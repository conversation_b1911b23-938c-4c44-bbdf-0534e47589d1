package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.spsdk.udpbase.bean.UdpUserBriefBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * HeirPosUserPrepareResp
 *
 * <AUTHOR> harleyge
 * @Date 25/7/24 2:22 pm
 */
@Data
public class HeirPosUserPrepareResp {
    private String posName;
    @Schema(description = "0:岗位，1：部门")
    private Integer posType;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long prepareLevelId;
    @Schema(description = "继任用户信息")
    private UdpUserBriefBean userInfo;
    @Schema(description = "处理状态：-1:未处理,0:暂不处理,1:已处理")
    private int modifyStatus;
    private List<HeirPrepare4Resp> prepareList;

    @Schema(description = "处理用户账号")
    private String optUsername;
    @Schema(description = "处理用户名")
    private String optFullname;
    @Schema(description = "处理时间")
    private Date optTime;
}
