package com.yxt.talent.bk.svc.heir.bean.req;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.Size;

@Data
@Schema(name = "继任准备度")
public class HeirPrepareReq {

    @Schema(description = "主键")
    private Long id;
    @Schema(description = "序号")
    @Range(min = 1, message = "apis.talentbk.userGroup.args.error")
    private Integer orderIndex;
    @Schema(description = "自定义名称（简体）")
    @Size(min = 1, max = 200, message = "apis.talentbk.userGroup.args.error")
    private String levelName1;
    @Schema(description = "自定义名称（英文）")
    @Size(max = 200, message = "apis.talentbk.userGroup.args.error")
    private String levelName2;
    @Schema(description = "自定义名称（繁体）")
    @Size(max = 200, message = "apis.talentbk.userGroup.args.error")
    private String levelName3;
    @Schema(description = "颜色设置")
    private String colorCode;
    @Schema(description = "备注")
    @Size(max = 200, message = "apis.talentbk.userGroup.args.error")
    private String remark;
}
