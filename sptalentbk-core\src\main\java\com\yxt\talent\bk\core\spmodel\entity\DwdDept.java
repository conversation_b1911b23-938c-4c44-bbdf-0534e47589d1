package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2024/7/31
 */
@Data
@TableName(value = "dwd_dept")
public class DwdDept {
    /**
     * 主键id
     */
    @TableField(value = "id")
    private String id;

    /**
     * 机构 id
     */
    @TableField(value = "org_id")
    private String orgId;

    @TableField(value = "dept_id")
    private String deptId;

    @TableField(value = "parent_id")
    private String parentId;

    @TableField(value = "third_dept_id")
    private String thirdDeptId;

    @TableField(value = "third_dept_name")
    private String thirdDeptName;

    @TableField(value = "id_full_path")
    private String idFullPath;

    @TableField(value = "routing_path")
    private String routingPath;

    @TableField(value = "routing_path_name")
    private String routingPathName;

    @TableField(value = "order_index")
    private String orderIndex;


}
