<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirPrepareHisMapper">

    <sql id="all_columns">
        id,
        org_id,
        pos_id,
        user_id,
        opt_user_id,
        modify_status,
        user_level_id,
        change_level_id,
        rule_data_id,
        match_data_id,
        create_user_id,
        create_time,
        update_user_id,
        update_time
    </sql>

    <select id="recentHis" resultType="com.yxt.talent.bk.core.heir.entity.HeirPrepareHisEntity">
        select <include refid="all_columns"/> from bk_heir_prepare_his
        where pos_id = #{posId} and user_id = #{userId} order by create_time desc limit 1
    </select>
</mapper>
