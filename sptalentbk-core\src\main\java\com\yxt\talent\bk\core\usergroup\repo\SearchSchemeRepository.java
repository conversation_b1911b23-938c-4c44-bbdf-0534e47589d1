package com.yxt.talent.bk.core.usergroup.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.DateUtil;
import com.yxt.talent.bk.core.usergroup.bean.SchemeRuleVO;
import com.yxt.talent.bk.core.usergroup.entity.SearchScheme;
import com.yxt.talent.bk.core.usergroup.mapper.SearchSchemeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description TODO
 *
 * <AUTHOR>
 * @Date 2023/8/16 16:07
 **/
@Slf4j
@Repository
public class SearchSchemeRepository extends ServiceImpl<SearchSchemeMapper, SearchScheme> {

    private LambdaQueryWrapper<SearchScheme> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public List<SearchScheme> listByOrgId(String orgId) {
        LambdaQueryWrapper<SearchScheme> wrapper = getQueryWrapper();
        wrapper.eq(SearchScheme::getOrgId, orgId);
        wrapper.eq(SearchScheme::getDeleted, YesOrNo.NO.getValue());
        return list(wrapper);
    }

    public SearchScheme findSchemeById(String orgId, Long id){
        LambdaQueryWrapper<SearchScheme> wrapper = getQueryWrapper();
        wrapper.eq(SearchScheme::getOrgId, orgId);
        wrapper.eq(SearchScheme::getId, id);
        wrapper.eq(SearchScheme::getDeleted, YesOrNo.NO.getValue());
        return getOne(wrapper);
    }

    public void removeScheme(String orgId, Long schemeId, String userId) {
        LambdaUpdateWrapper<SearchScheme> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SearchScheme::getOrgId, orgId);
        updateWrapper.eq(SearchScheme::getId, schemeId);
        updateWrapper.set(SearchScheme::getDeleted, YesOrNo.YES.getValue());
        updateWrapper.set(SearchScheme::getUpdateUserId, userId);
        updateWrapper.set(SearchScheme::getUpdateTime, DateUtil.currentTime());
        update(updateWrapper);
    }

    public List<SchemeRuleVO> findSchemeRuleVO(String orgId, String userId) {
        return baseMapper.findSchemeRuleVO(orgId, userId);
    }

}
