package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 筛选组名称
 */
@Data
@Schema(name = "筛选组名称")
public class FilterGroupNameBean {

    @Schema(description = "id")
    private String id;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "0-非默认筛选组，1-默认筛选组")
    private Integer groupType = 0;
}
