package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/8/1
 */
@Data
@TableName(value = "dws_user")
public class DwdUser {
    /**
     * 主键id
     */
    @TableField(value = "id")
    private String id;
    /**
     * 三方用户 id
     */
    @TableField(value = "third_user_id")
    private String thirdUserId;
    /**
     * 机构 id
     */
    @TableField(value = "org_id")
    private String orgId;

    @TableField(value = "user_id")
    private String userId;

    @TableField(value = "user_name")
    private String userName;

    @TableField(value = "full_name")
    private String fullName;

    @TableField(value = "third_dept_id")
    private String thirdDeptId;

    @TableField(value = "dept_id")
    private String deptId;

    @TableField(value = "third_dept_name")
    private String thirdDeptName;

    @TableField(value = "third_position_id")
    private String thirdPositionId;

    @TableField(value = "position_id")
    private String positionId;

    @TableField(value = "third_position_name")
    private String thirdPositionName;
}
