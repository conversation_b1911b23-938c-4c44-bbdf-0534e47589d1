package com.yxt.talent.bk.core.heir.repo;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserBean;
import com.yxt.talent.bk.core.heir.entity.HeirPosBenchmarkEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirPosBenchmarkMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * HeirPosBenchmarkRepository
 *
 * <AUTHOR> harleyge
 * @Date 6/5/24 10:41 am
 */
@Slf4j
@Repository
@AllArgsConstructor
public class HeirPosBenchmarkRepository extends ServiceImpl<HeirPosBenchmarkMapper, HeirPosBenchmarkEntity> {
    private final HeirPosBenchmarkMapper heirPosBenchmarkMapper;

    public List<HeirPosUserBean> posTopUsers(String orgId, List<String> posIds) {
        return TalentbkUtil.bkUdpTranslate(orgId, heirPosBenchmarkMapper.posTopUsers(posIds));
    }

    public List<HeirPosBenchmarkEntity> findByPosIds(String orgId, Collection<String> posIds) {
        if (CollectionUtils.isEmpty(posIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HeirPosBenchmarkEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(HeirPosBenchmarkEntity::getOrgId, orgId);
        queryWrapper.in(HeirPosBenchmarkEntity::getPosId, posIds);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<HeirPosBenchmarkEntity> getQueryWrapper() {
        LambdaQueryWrapper<HeirPosBenchmarkEntity> wrapper = new LambdaQueryWrapper<>();
        return wrapper.eq(HeirPosBenchmarkEntity::getDeleted, YesOrNo.NO.getValue());
    }
}
