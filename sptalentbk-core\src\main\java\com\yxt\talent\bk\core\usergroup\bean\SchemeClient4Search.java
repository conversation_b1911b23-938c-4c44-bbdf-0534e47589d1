package com.yxt.talent.bk.core.usergroup.bean;

import com.yxt.talent.bk.core.pool.bean.SearchDeptInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2023/8/16 14:48
 **/
@Data
public class SchemeClient4Search {

    @Schema(description = "搜索，姓名账号")
    private String keyword;

    @Schema(description = "部门id")
    List<SearchDeptInfo> depts;

    @Schema(description = "岗位id")
    private List<String> positionIds;

    @Schema(description = "职级id")
    private List<String> gradeIds;

    @Schema(description = "账号状态，-1全部,0-禁用，1-启用")
    private Integer status = -1;

    @Schema(description = "常用筛选方案id，不传时给空值")
    private Long schemeId;
}
