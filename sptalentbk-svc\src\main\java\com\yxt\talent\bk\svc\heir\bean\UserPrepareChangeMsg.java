package com.yxt.talent.bk.svc.heir.bean;

import com.google.common.collect.Maps;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.msgfacade.bean.MsgBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.core.heir.entity.HeirPosUserEntity;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.svc.common.SendMsgLangBase;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareResp;
import com.yxt.talent.bk.svc.heir.constants.HeirNoticeConstants;
import com.yxt.talent.bk.svc.udp.rpc.UdpRpc;
import jodd.util.StringPool;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * UserPrepareChangeMsg
 *
 * <AUTHOR> harleyge
 * @Date 10/5/24 11:22 am
 */
@Data
public class UserPrepareChangeMsg implements SendMsgLangBase {
    private MsgBean msgBean;
    private String posId;
    private int posType;
    private String posName;
    private String targetUserId;
    private String targetUserFullname;
    @Override
    public MsgBean msgBean() {
        if (msgBean.getParams() == null) {
            msgBean.setParams(Maps.newHashMap());
        }
        msgBean.getParams().put("{{successionname}}", StringUtils.isEmpty(posName) ? StringPool.DASH : posName);
        msgBean.getParams().put("{{targetFullname}}", targetUserFullname);
        return msgBean;
    }

    public String putMsgBean(HeirPosUserEntity heirPosUserEntity, List<String> remindUserIds,
                               UdpUserBriefBean udpUserBriefBean, String posName, HeirPrepareResp heirPrepareResp) {
        String jumpUrl = SpringContextHolder.getBean(UdpRpc.class).heirPrepareJump(heirPosUserEntity.getOrgId(),
                heirPosUserEntity.getPosId(), heirPosUserEntity.getUserId());
        MsgBean msgBean = new MsgBean();
        msgBean.setOrgId(heirPosUserEntity.getOrgId());
        msgBean.setUserIds(remindUserIds);
        msgBean.setTemplateCode(HeirNoticeConstants.MSG_CODE_PREPARE_CHANGE_NOTICE);
        msgBean.setTargetId(heirPosUserEntity.getId().toString());
        msgBean.setParams(getParams(jumpUrl,udpUserBriefBean,posName,heirPrepareResp));
        msgBean.setCreateUserId(TalentBkConstants.CODE_OPERATOR_ID);
        msgBean.setCreateFullname(TalentBkConstants.CODE_OPERATOR_ID);
        this.msgBean = msgBean;
        this.targetUserId = udpUserBriefBean.getId();
        this.targetUserFullname = udpUserBriefBean.getFullname();
        return jumpUrl;
    }

    private Map<String, String> getParams(String jumpUrl, UdpUserBriefBean udpUserBriefBean,
                                          String posName, HeirPrepareResp heirPrepareResp) {
        Map<String, String> params = Maps.newHashMap();
        params.put("{{fullname}}",udpUserBriefBean.getFullname());
        params.put("{{targetFullname}}",udpUserBriefBean.getFullname());
        params.put("{{successionname}}",StringUtils.isEmpty(posName) ? StringPool.DASH : posName);
        params.put("{{readinessname}}",heirPrepareResp.getLevelName1());
        params.put("{{url}}", jumpUrl);
        return params;
    }

    @Override
    public UdpLangUnitBean deptLangProperty() {
        if (posType == HeirPosTypeEnum.DEPT.getType()) {
            return new UdpLangUnitBean(posId, posName, this::setPosName);
        }
        return null;
    }

    @Override
    public UdpLangUnitBean positionLangProperty() {
        if (posType != HeirPosTypeEnum.DEPT.getType()) {
            return new UdpLangUnitBean(posId, posName, this::setPosName);
        }
        return null;
    }

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(targetUserId, targetUserFullname, this::setTargetUserFullname);
    }
}
