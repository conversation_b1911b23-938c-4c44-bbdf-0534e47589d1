package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * SkillModelMap4Get
 *
 * <AUTHOR>
 * @since 2021-05-25 17:33:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "获取能力模板-能力")
public class SkillModelMap4Get {
    @Schema(description = "id")
    private String id;

    @Schema(description = "能力id")
    private String skillId;

    @Schema(description = "排序")
    private Integer orderIndex;

    @Schema(description = "标准等级")
    private Integer standardLevel;

    @Schema(description = "等级名称")
    private String standardLevelStr;

    @Schema(description = "标准等级")
    private String standardLevelName;

    @Schema(description = "关联的能力")
    private Skill4Get skill;

    @Schema(description = "能力名称(冗余自能力)")
    private String name;



}
