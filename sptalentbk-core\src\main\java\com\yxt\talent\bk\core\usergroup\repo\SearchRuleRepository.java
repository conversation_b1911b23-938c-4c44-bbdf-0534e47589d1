package com.yxt.talent.bk.core.usergroup.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.core.usergroup.entity.SearchRule;
import com.yxt.talent.bk.core.usergroup.mapper.SearchRuleMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/17 11:45
 **/
@Repository
public class SearchRuleRepository extends ServiceImpl<SearchRuleMapper, SearchRule> {

    private LambdaQueryWrapper<SearchRule> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public SearchRule queryRuleById(String orgId, Long id) {
        LambdaQueryWrapper<SearchRule> queryWrapper = getQueryWrapper();
        queryWrapper.eq(SearchRule::getOrgId, orgId);
        queryWrapper.eq(SearchRule::getId, id);
        queryWrapper.eq(SearchRule::getDeleted, YesOrNo.NO.getValue());
        return getOne(queryWrapper);
    }

    public void deleteById(String orgId, String userId, Long id) {
        getBaseMapper().deleteLogicById(orgId, userId, id);
    }

    public SearchRule getBySearchRuleId(String orgId, Long searchRuleId) {
        LambdaQueryWrapper<SearchRule> query = new LambdaQueryWrapper<>();
        query.eq(SearchRule::getOrgId, orgId);
        query.eq(SearchRule::getId, searchRuleId);
        query.eq(SearchRule::getDeleted, YesOrNo.NO.getValue());
        return getOne(query);
    }

    public List<SearchRule> listByOrgId(String orgId) {
        LambdaQueryWrapper<SearchRule> queryWrapper = getQueryWrapper();
        queryWrapper.eq(SearchRule::getOrgId, orgId);
        queryWrapper.eq(SearchRule::getDeleted, YesOrNo.NO.getValue());
        return list(queryWrapper);
    }
}
