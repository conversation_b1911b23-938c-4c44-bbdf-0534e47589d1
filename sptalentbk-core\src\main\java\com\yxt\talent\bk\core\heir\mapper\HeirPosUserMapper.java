package com.yxt.talent.bk.core.heir.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserBean;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserBriefBean;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserIdDTO;
import com.yxt.talent.bk.core.heir.bean.HeirUserPosInfo;
import com.yxt.talent.bk.core.heir.bean.PosUserBriefBean;
import com.yxt.talent.bk.core.heir.bean.PosUserPrepareBean;
import com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosUserBean;
import com.yxt.talent.bk.core.heir.entity.HeirPosUserEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosUserExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * HeirPosUserMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 2:32 pm
 */
@Mapper
public interface HeirPosUserMapper extends BkBaseMapper<HeirPosUserEntity> {
    List<String> listExistUserId(@Param("orgId") String orgId, @Param("posId") String posId, @Param("userIds") List<String> userIds);

    List<HeirPosUserExt> listExistByIds(@Param("orgId") String orgId, @Param("ids") List<Long> ids);

    int batchUpdate(@Param("list") List<HeirPosUserEntity> list);

    IPage<HeirPosUserExt> list(Page page,
                               @Param("orgId") String orgId,
                               @Param("posId") String posId,
                               @Param("heirStatus") Integer heirStatus,
                               @Param("prepareLevelId") Long prepareLevelId);

    List<HeirPosUserExt> listByPosIds(@Param("orgId") String orgId,
                                      @Param("posIds") List<String> posIds);
    IPage<PosUserBriefBean> listValidBrief(Page page,
                                           @Param("orgId") String orgId,
                                           @Param("posId") String posId,
                                           @Param("heirStatus") Integer heirStatus);

    List<PosUserPrepareBean> posValidUserPrepare(@Param("orgId") String orgId,
                                                 @Param("posId") String posId);

    int posValidUserQty(@Param("orgId") String orgId,
                        @Param("posId") String posId);

    int batchUpdateUserPrepare(@Param("orgId") String orgId,
                               @Param("list") List<PosUserPrepareBean> list);

    List<HeirPosUserIdDTO> getIdByUserIds(@Param("orgId") String orgId,
                                          @Param("posId") String posId,
                                          @Param("userIds") List<String> userIds);

    void deleteByUserIds(@Param("orgId") String orgId,
        @Param("currentUserId") String userId,
        @Param("currentTime") Date currentTime,
        @Param("posId") String posId,
        @Param("userIds") List<String> userIds);

    List<String> queryUserPosIds(@Param("orgId") String orgId,
                                 @Param("userIds") List<String> userIds);

    Long selectUsePreparUser(@Param("prepareLevelId") Long prepareLevelId,@Param("orgId") String orgId);

    List<HeirPosUserEntity> selectShouldRemindUser(@Param("orgId") String orgId,@Param("posId") String posId);

    List<HeirPosUserBean> selectOnHeirByUserIds(@Param("orgId") String orgId,
                                                @Param("userIds") List<String> userIds);

    int batchQuitHeir(@Param("orgId") String orgId,
                      @Param("ids") List<Long> ids,
                      @Param("currentTime") Date currentTime);

    List<HeirPosUserBriefBean> listBriefByIds(List<Long> ids);

    IPage<DwdHeirPosUserBean> listPage4Open(Page page,
                                            @Param("orgId") String orgId);

    @Update("""
    update bk_heir_pos_user set modify_status = -1 where org_id = #{orgId}
    and pos_id = #{posId} and calc_level_id = #{calcLevelId} and deleted = 0 and modify_status = 0
    """)
    int resetModifyStatus(@Param("orgId") String orgId,
                          @Param("posId") String posId,
                          @Param("calcLevelId")Long calcLevelId);

    @Select("""
    select h.pos_type,if(h.pos_type = 0,p.name,d.name) as pos_name,u.prepare_level_id,u.modify_status from bk_heir_pos h
    join bk_heir_pos_user u on u.org_id = h.org_id and u.pos_id = h.id and u.user_id = #{userId} and u.deleted = 0
    left join udp_position p on p.id = h.id
    left join udp_dept d on d.id = h.id
    where h.id = #{posId} limit 1
    """)
    HeirUserPosInfo userPosPrepareInfo(@Param("posId") String posId, @Param("userId") String userId);
}
