package com.yxt.talent.bk.core.heir.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * HeirUserPosBriefBean
 *
 * <AUTHOR> geyan
 * @Date 31/8/23 6:23 pm
 */
@Data
public class HeirUserPosBriefBean implements UdpLangSupport {
    private String posId;
    @Schema(description = "0:岗位，1:部门")
    private Integer posType;
    private String name;
    private List<String> deptPath;
    private String prepareName;
    private String prepareColor;
    private String deptManagerName;
    @JsonIgnore
    private Long prepareLevelId;
    @JsonIgnore
    private String idFullPath;

    @Override
    public UdpLangUnitBean deptLangProperty() {
        if (posType != null && posType == HeirPosTypeEnum.DEPT.getType()) {
            return new UdpLangUnitBean(posId, name, this::setName);
        }
        return null;
    }
}
