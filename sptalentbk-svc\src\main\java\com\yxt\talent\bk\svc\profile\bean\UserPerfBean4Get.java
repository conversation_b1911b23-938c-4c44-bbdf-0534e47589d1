package com.yxt.talent.bk.svc.profile.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@Schema(name = "人才画像-绩效表现")
public class UserPerfBean4Get {

    @Schema(description = "绩效数据列表")
    private List<UserPerf4Get> list;

    @Schema(description = "绩效基础数据")
    private List<Perf4Get> perfs;

}
