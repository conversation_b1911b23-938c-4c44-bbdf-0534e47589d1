package com.yxt.talent.bk.core.tag.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import com.yxt.talent.bk.core.tag.mapper.TagValueMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class TagValueRepository extends ServiceImpl<TagValueMapper, TagValueEntity> {

    public TagValueEntity queryTagById(String orgId, String tagId) {
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagValueEntity::getId, tagId);
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        return getOne(wrapper);
    }

    public List<TagValueEntity> queryTagByIds(String orgId, Collection<String> tagValueIds) {
        if (CollectionUtils.isEmpty(tagValueIds)) {
            log.warn("LOG10120:org_id={}", orgId);
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.in(TagValueEntity::getId, tagValueIds);
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        List<TagValueEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }

    public List<TagValueEntity> findByTagIds(String orgId, Collection<String> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            log.warn("LOG10100:org_id={}", orgId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        wrapper.in(TagValueEntity::getTagId,tagIds);
        return list(wrapper);
    }

    public List<TagValueEntity> findTagValueOrgAndTagIds(Collection<String> orgIds, Collection<String> tagIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            log.warn("LOG10060:org_id={}", orgIds);
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(tagIds)) {
            log.warn("LOG10080:org_id={}", orgIds);
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.in(TagValueEntity::getOrgId, orgIds);
        wrapper.in(TagValueEntity::getTagId,tagIds);
        return list(wrapper);
    }

    public long countByTagIds(String orgId, Collection<String> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            log.warn("LOG10040:org_id={}", orgId);
            return 0;
        }
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        wrapper.in(TagValueEntity::getTagId,tagIds);
        return count(wrapper);
    }

    public Long countByTagValueNamesAndTagId(String orgId, Collection<String> valueNames, String tagId) {
        if (CollectionUtils.isEmpty(valueNames)) {
            log.warn("LOG10160:org_id={}, tagId={}", orgId, tagId);
            return 0L;
        }
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        wrapper.in(TagValueEntity::getValueName,valueNames);
        wrapper.eq(TagValueEntity::getTagId, tagId);
        return count(wrapper);
    }

    public List<TagValueEntity> listByTagValueNamesAndTagId(String orgId, Collection<String> valueNames, String tagId) {
        if (CollectionUtils.isEmpty(valueNames)) {
            log.warn("LOG10140:org_id={}, tagId={}", orgId, tagId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        wrapper.in(TagValueEntity::getValueName,valueNames);
        wrapper.eq(TagValueEntity::getTagId, tagId);
        return list(wrapper);
    }

    public List<TagValueEntity> getTagValuesByTagId(String orgId, String tagId){
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        wrapper.eq(TagValueEntity::getTagId, tagId);
        wrapper.orderByAsc(TagValueEntity::getOrderIndex);
        List<TagValueEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }

    public List<TagValueEntity> list(String orgId) {
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        return list(wrapper);
    }

    public boolean removeById(String orgId, String id) {
        LambdaQueryWrapper<TagValueEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(TagValueEntity::getId, id);
        queryWrapper.eq(TagValueEntity::getOrgId, orgId);
        return super.remove(queryWrapper);
    }

    public boolean removeByIds(String orgId, Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("LOG10140:org_id={}", orgId);
            return false;
        }
        LambdaQueryWrapper<TagValueEntity> queryWrapper = getQueryWrapper();
        queryWrapper.in(TagValueEntity::getId, ids);
        queryWrapper.eq(TagValueEntity::getOrgId, orgId);
        return super.remove(queryWrapper);
    }

    public List<TagValueEntity> listByTagId(String orgId, @NotNull(message = "param tagId can not be null!") String tagId) {
        LambdaQueryWrapper<TagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagValueEntity::getOrgId, orgId);
        wrapper.eq(TagValueEntity::getTagId, tagId);
        return list(wrapper);
    }

    private LambdaQueryWrapper<TagValueEntity> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }
}
