package com.yxt.talent.bk.svc.heir.bean.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Schema(name = "岗位可见人员列表响应")
public class PosPermissionListResp implements Serializable {

    private static final long serialVersionUID = -3727610214861763519L;

    @Schema(description = "账号")
    private String username;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "主部门全路径名称，例：研发中心->园区 ")
    private String deptName;

    @Schema(description = "用户id")
    private String userId;

}
