package com.yxt.talent.bk.svc.heir.bean.resp;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import com.yxt.talent.bk.svc.heir.bean.PrepareLevelBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class HeirPrepareCfgResp {

    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "准备度规则等级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long prepareLevelId;

    @Schema(description = "准备度规则等级名称")
    private String prepareLevelName;

    @Schema(description = "准备度规则配置")
    private LabelConditionJsonBean labelRuleGroupBean;

    private List<PrepareLevelBean> prepareLevelBeanList;

}
