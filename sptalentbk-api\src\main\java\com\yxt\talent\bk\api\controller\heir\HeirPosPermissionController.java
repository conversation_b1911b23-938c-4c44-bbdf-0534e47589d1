package com.yxt.talent.bk.api.controller.heir;

import com.alibaba.fastjson2.JSON;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.svc.heir.HeirPosPermissionService;
import com.yxt.talent.bk.svc.heir.bean.req.PosPermissionUserAddReq;
import com.yxt.talent.bk.svc.heir.bean.resp.PosPermissionListResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
* <AUTHOR>
*  @date 2023/8/16
**/
@Slf4j
@Tag(name = "继任权限")
@RestController
@RequestMapping("/mgr/heir/pos_permission")
@RequiredArgsConstructor
public class HeirPosPermissionController extends BaseController {

    private final HeirPosPermissionService heirPosPermissionService;

    @Operation(summary = "岗位权限启用状态", description = "返回值：0: 否，1: 是")
    @Parameters({         @Parameter(name = "posId", description = "继任id")     })
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    @GetMapping("/enable_status")
    public Integer enableStatus(@RequestParam("posId") String posId, @RequestParam("posType") Integer posType) {
        UserCacheBasic currentUser = getUserCacheBasic();
        return heirPosPermissionService.getEnableStatus(currentUser, posId, posType);
    }

    @Operation(summary = "启用停用岗位权限")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_PERM_ENABLE,
            paramExp = "{'posId':#posId,'permissionEnable':#enabled}")
    @Parameters({@Parameter(name = "posId", description = "继任id"),
            @Parameter(name = "enabled", description = "0: 否，1: 是")     })
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    @PostMapping("/enable")
    public void enable(@RequestParam("posId") String posId, @RequestParam("enabled") Integer enabled) {
        log.info("permission enable. posId: {} enabled: {}", posId, enabled);
        UserCacheBasic currentUser = getUserCacheBasic();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_PERMISSION_OPERATE, currentUser.getOrgId(), posId);
        CommonUtils.lockRun(lockKey,30, TimeUnit.SECONDS, () -> {
            heirPosPermissionService.enable(currentUser, posId, enabled);
        });
    }

    @Operation(summary = "岗位所有的可见人员")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/list_user")
    public CommonList<PosPermissionListResp> listUser(HttpServletRequest request, @RequestParam("posId") String posId) {
        UserCacheBasic currentUser = getUserCacheBasic();
        return heirPosPermissionService.listUser(currentUser, posId);
    }

    @Operation(summary = "保存岗位可见人员", description = "全量保存-不在userIds内的将会被删除")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_PERM_USER, paramExp = "#req.editParam4Log()")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    @PostMapping("/save_user")
    public void saveUser(@RequestBody @Validated PosPermissionUserAddReq req) {
        log.info("addPosPermissionUser. req: {}", JSON.toJSONString(req));
        UserCacheBasic currentUser = getUserCacheBasic();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_PERMISSION_OPERATE, currentUser.getOrgId(), req.getPosId());
        CommonUtils.lockRun(lockKey,30, TimeUnit.SECONDS, () -> {
            heirPosPermissionService.saveUser(currentUser, req);
        });
    }

}

