package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Data
public class UserRuleDetailTag4Get {
    @Schema(description = "标签ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tagId;
    @Schema(description = "标签名", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tagName;
    @Schema(description = "标签值的名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> tagValueNames;
}
