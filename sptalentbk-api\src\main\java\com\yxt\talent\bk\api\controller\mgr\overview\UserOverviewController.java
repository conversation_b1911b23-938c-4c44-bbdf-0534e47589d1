package com.yxt.talent.bk.api.controller.mgr.overview;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.persona.bean.UserOverviewBean;
import com.yxt.talent.bk.svc.persona.overview.UserOverviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@RestController
@RequestMapping("/mgr/user/overview")
@AllArgsConstructor
@Tag(name = "个人特征综述")
@Slf4j
public class UserOverviewController extends BaseController {
    private final UserOverviewService userOverviewService;

    @Deprecated
    @Operation(summary = "编辑用户特征综述")
    @PostMapping(value = "", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_OVERVIEW, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public void createDimension(@Valid @RequestBody UserOverviewBean userOverviewBean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        userOverviewService.addUserOverview(userDetail.getOrgId(), userDetail.getUserId(), userOverviewBean);
    }

    @Deprecated
    @Operation(summary = "查询用户特征综述")
    @Parameter(name = "userId", description = "用户id", in = ParameterIn.PATH)
    @GetMapping(value = "/{userId}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_OVERVIEW, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public UserOverviewBean getInfo(@PathVariable String userId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return userOverviewService.findUserOverview(userDetail.getOrgId(), userId);
    }
}
