<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdCerIssueHistoryMapper">
    <select id="countByUserId" resultType="int">
      select count(*) as totalCount
      from dwd_cer_issue_history a
      where a.org_id = '${orgId}'
        <if test="userId != null and userId != ''">
            and a.user_id = '${userId}'
        </if>
        <if test="userId == null or userId == ''">
            <!--@ignoreSql-->
            and 1 != 1
        </if>
        and a.deleted = 0
        and a.cer_status in (0, 2)
    </select>
</mapper>
