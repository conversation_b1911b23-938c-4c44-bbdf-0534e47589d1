package com.yxt.talent.bk.api.job;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupRepository;
import com.yxt.talent.bk.svc.usergroup.UserGroupService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Component
@Slf4j
@AllArgsConstructor
public class UserGroupCaculateHandler extends IJobHandler {

    private final UserGroupService userGroupService;
    private final UserGroupRepository userGroupRepository;

    @Override
    @XxlJob(value = "userGroupCaculate")
    public ReturnT<String> execute(String param) {
        try {
            log.info("开始计算");
            doCaculate();
        }catch (Exception e){
            log.error("userGroupCaculate error", e);
        }
        return ReturnT.SUCCESS;
    }

    private void doCaculate() {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取今天是星期几
        DayOfWeek dayOfWeek = today.getDayOfWeek();
        int weekdate = dayOfWeek.getValue();
        List<String> delayOrgIds = new ArrayList<>();
        int size = 1000;
        int current = 1;
        boolean flag = true;
        while (flag){
            Page<UserGroup> page = new Page();
            page.setCurrent(current);
            page.setSize(size);
            // 业务处理
            IPage<UserGroup> userGroups = userGroupRepository.selectList(page, weekdate, today.getDayOfMonth(), null);
            if (userGroups != null && CollectionUtils.isNotEmpty(userGroups.getRecords())){
                List<UserGroup> userGroupList = userGroups.getRecords();
                Map<String,List<UserGroup>> usergroupMap = userGroupList.stream().collect(Collectors.groupingBy(UserGroup::getOrgId));
                usergroupMap.forEach((orgId,list) -> {
                    log.info("计算 ID：{}",StreamUtil.mapList(list,UserGroup::getId));
                    if (userGroupService.canCanculete(orgId)){
                        list.forEach(userGroup -> userGroupService.caculateMembers(userGroup.getOrgId(), "job" ,userGroup.getId()));
                    }else {
                        delayOrgIds.add(orgId);
                    }
                });
            }
            if (userGroups != null && CollectionUtils.isNotEmpty(userGroups.getRecords()) && userGroups.getRecords().size() >= size){
                current ++;
                continue;
            }
            flag = false;
        }
        if (CollectionUtils.isNotEmpty(delayOrgIds)){
            log.info("延迟计算机构ID：{}", BeanHelper.bean2Json(delayOrgIds, JsonInclude.Include.ALWAYS));
            userGroupService.setCacuDelayOrgs(delayOrgIds);
        }
    }

}
