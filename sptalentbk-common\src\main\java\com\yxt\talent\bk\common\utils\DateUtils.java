package com.yxt.talent.bk.common.utils;

import com.yxt.common.Constants;
import com.yxt.common.util.DateUtil;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import lombok.extern.slf4j.Slf4j;

import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间处理工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/8/19 13:57
 */
@Slf4j
public final class DateUtils {
    private DateUtils() {
        //NO SONAR
    }

    public static final DateTimeFormatter FORMATTER_DATE_TIME = DateTimeFormatter.ofPattern(Constants.SDF_YEAR2SECOND);
    public static final DateTimeFormatter FORMATTER_DATE_TIME_YYYYMMDDHHMM = DateTimeFormatter
        .ofPattern(Constants.SDF_YEAR2MINUTE);
    public static final DateTimeFormatter FORMATTER_DATE_TIME_YYYYMMDDHHMMSS = DateTimeFormatter
        .ofPattern(Constants.SDF_YEAR2SECOND);
    public static final DateTimeFormatter FORMATTER_DATE_TIME_SHORT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter FORMATTER_DATE_TIME_MM = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    public static final DateTimeFormatter FORMATTER_DATE_TIME_SHORT_MILLIS = new DateTimeFormatterBuilder()
        .appendPattern("yyyyMMddHHmmss").appendValue(ChronoField.MILLI_OF_SECOND, 3).toFormatter();
    public static final DateTimeFormatter FORMATTER_DATE = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter FORMATTER_TIME = DateTimeFormatter.ofPattern("HHmmss");

    public static final String DATETIME_FORMAT_CHINESE = "yyyy年MM月dd日 HH时mm分";

    public static Date localDataTime2UData(
        @NotNull(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }


    public static String dateToString(Date date, DateTimeFormatter dateTimeFormatter) {
        return dateTimeFormatter.withZone(ZoneId.systemDefault()).format(date.toInstant());
    }

    public static String dateToString(Date date, String formatStr) {
        try {
            if (null != date) {
                return DateTimeFormatter.ofPattern(formatStr).withZone(ZoneId.systemDefault()).format(date.toInstant());
            }
        } catch (Exception e) {
            log.error("convert data error : {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取N年前的时间
     */
    public static Date getAgoDate(int n) {
        Calendar nd = Calendar.getInstance();
        nd.setTime(DateUtil.currentTime());
        nd.set(Calendar.YEAR, (nd.get(Calendar.YEAR) - n));
        return nd.getTime();
    }

    public static int getUserAge(Date birthday) {

        Calendar cal = Calendar.getInstance();
        cal.setTime(DateUtil.currentTime());
        if (cal.before(birthday)) {
            return 0;
        }
        //当前年份
        int yearNow = cal.get(Calendar.YEAR);
        //当前月份
        int monthNow = cal.get(Calendar.MONTH);
        //当前日期
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

        cal.setTime(birthday);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

        //计算整岁数
        int age = yearNow - yearBirth;
        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) {
                    //当前日期在生日之前，年龄减一
                    age--;
                }
            } else {
                //当前月份在生日之前，年龄减一
                age--;
            }
        }
        return age;
    }

    /**
     * 入职时间 2008-08-08 转 司龄 2.9年
     */
    public static String getEntryYear(Date date) {
        Calendar entryDate = Calendar.getInstance();
        entryDate.setTime(date);
        Calendar nowDate = Calendar.getInstance();
        nowDate.setTime(DateUtil.currentTime());

        int entryYear = entryDate.get(Calendar.YEAR);
        int nowYear = nowDate.get(Calendar.YEAR);
        int entryMonth = entryDate.get(Calendar.MONTH);
        int nowMonth = nowDate.get(Calendar.MONTH);
        int month = nowMonth - entryMonth;
        int year = nowYear - entryYear;

        if (month < 0) {
            month = (month + 12) % 12;
            year -= 1;
        }

        String rm = (month * 10 / 12) == 0 ? "" : ("." + (month * 10 / 12));
        return year + rm;
    }

    /**
     * 日期转换为String，格式为YYYYMMDDHHmmSS
     *
     * @return 格式化后的string
     */
    public static String localDateTimeToStr(LocalDateTime date) {
        return date.format(FORMATTER_DATE_TIME_YYYYMMDDHHMMSS);
    }

    public static String formatDate(LocalDateTime date) {
        return DateTimeFormatter.ofPattern(Constants.SDF_YEAR2SECOND).format(date);
    }

    public static String formatSimpleDate(LocalDateTime date) {
        return DateTimeFormatter.ofPattern(Constants.SDF_YEAR2DAY).format(date);
    }

    public static BigDecimal convertSecondsToMinutes(long seconds) {
        BigDecimal secondsBigDecimal = BigDecimal.valueOf(seconds);
        return secondsBigDecimal.divide(BigDecimal.valueOf(60), 1, RoundingMode.HALF_UP);

    }
}
