package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import com.yxt.spsdk.audit.base.AuditLogEntitySupport;
import lombok.Data;

/**
 * PoolUserAdd4Log
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 10:14 am
 */
@Data
public class PoolUserAdd4Log implements AuditLogEntitySupport {
    private String poolId;
    private String poolName;
    @AuditLogField(name = "人员列表", orderIndex = 0)
    private String addUserNames;
    @Override
    public String entityId() {
        return poolId;
    }

    @Override
    public String entityName() {
        return String.format("人才池-%s-添加人员", poolName);
    }
}
