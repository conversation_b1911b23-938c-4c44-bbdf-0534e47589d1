package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "人才池添加人员")
public class PoolUser4Add {
    @Schema(description = "人员id")
    private String userId;

    @Schema(description = "人才池id")
    @NotNull(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String poolId;

    @Schema(description = "选中人员id list")
    @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    @Size(message = BkApiErrorKeys.PARAM_SIZE_INVALID_MESSAGE, min = 1, max = 5000)
    private List<String> userIdList;

    private String orgId;
}
