package com.yxt.talent.bk.svc.tag.enums;

import lombok.Getter;

/**
 * 标签类型枚举类
 *
 * <AUTHOR>
 * @since 2022/8/11
 */
public enum TagTypeEnum {
    /**
     * 标签类型(0-普通标签,1-分层标签)
     */
    TYPE_0(0, "普通标签"), TYPE_1(1, "分层标签");

    @Getter
    private int type;

    @Getter
    private String name;

    TagTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

}
