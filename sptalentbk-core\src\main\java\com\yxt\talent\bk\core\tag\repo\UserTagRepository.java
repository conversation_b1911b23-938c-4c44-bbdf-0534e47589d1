package com.yxt.talent.bk.core.tag.repo;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.core.tag.entity.UserTagEntity;
import com.yxt.talent.bk.core.tag.mapper.UserTagMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2022/8/15
 */
@Repository
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class UserTagRepository extends ServiceImpl<UserTagMapper, UserTagEntity> {
    private final UserTagMapper userTagMapper;

    public void deleteByTagIdsAndUserIds(String orgId,
            @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) Collection<String> tagIds,
            @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) Collection<String> userIds,
            String operatorId) {
        userTagMapper
                .delUserTag(orgId, tagIds, userIds, operatorId, DateUtils.localDataTime2UData(LocalDateTime.now()));
    }
}
