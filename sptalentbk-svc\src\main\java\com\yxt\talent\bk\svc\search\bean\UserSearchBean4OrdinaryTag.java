package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "人才搜索-主页-列表搜索入参-企业自建的普通标签")
public class UserSearchBean4OrdinaryTag {
    @Schema(description = "标签key",example = "02b4bc1a-51a8-4acf-bf74-499450211eba")
    private String itemKey;
    @Schema(description = "搜索逻辑【0-且,1-或】",example = "1")
    private Integer logic = 1;
    @Schema(description="标签值",example = "卓越班组长")
    private String value;
}
