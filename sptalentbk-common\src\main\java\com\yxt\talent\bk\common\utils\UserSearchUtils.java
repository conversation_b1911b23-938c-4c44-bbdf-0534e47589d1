package com.yxt.talent.bk.common.utils;

import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.util.DateUtil;
import com.yxt.talent.bk.common.bean.UserSearchDateRangeBean;
import com.yxt.talent.bk.common.constants.UserSearchConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Map;

@Slf4j
public final class UserSearchUtils {
    private UserSearchUtils() {
        //NO SONAR
    }

    /**
     * 将年龄、工龄范围，转为日期范围
     *
     * @param range
     * @return
     */
    public static UserSearchDateRangeBean rangeToDate(String range, boolean isOffset) {

        UserSearchDateRangeBean dateRange = new UserSearchDateRangeBean();
        String[] split = range.split("-");
        if (split.length <= 0) {
            return null;
        }

        try {

            if (StringUtils.isNotBlank(split[0])) {
                Integer stInt = Integer.valueOf(split[0]);
                Date agoDate = DateUtils.getAgoDate(stInt);
                // 设置为结束日期（split[0]）
                dateRange.setEndDate(DateUtil.formatSimpleDate(agoDate));
            }

            if (split.length > 1 && StringUtils.isNotBlank(split[1])) {
                Integer enInt = Integer.valueOf(split[1]);
                if (isOffset) {
                    // 0-17 、 18-22 , 少了17-18之间的，所以要加1个偏移量
                    enInt = enInt + 1;
                }
                Date agoDate = DateUtils.getAgoDate(enInt);
                // 设置为开始日期（split[1]）
                dateRange.setStartDate(DateUtil.formatSimpleDate(agoDate));
            }

        } catch (NumberFormatException e) {
            log.error(UserSearchUtils.class.getName() + "." + "rangeToDate 【" + range + "】 转换失败 -> ", e);
        }

        return dateRange;
    }

    /**
     * 将生日转换为年龄段
     * @param date
     * @return
     */
    public static String dateToAgeTag(Date date) {

        int userAge = DateUtils.getUserAge(date);
        Map<String, String> ageRangeKVEnumMap = UserSearchConstants.getAgeRangeKVEnumMap();
        for(Map.Entry<String, String> entry  : ageRangeKVEnumMap.entrySet()){
            String[] split = entry.getKey().split("-");
            if (split.length <= 0) {
                continue;
            }

            Integer stInt = Integer.valueOf(split[0]);
            Integer enInt = Integer.MAX_VALUE;
            if (split.length > 1 && StringUtils.isNotBlank(split[1])) {
                enInt = Integer.valueOf(split[1]);
            }

            if(userAge >= stInt && userAge <= enInt){
                return  entry.getValue();
            }
        }

        return null;
    }

    /**
     * 将数值转换为百分比标签
     * @param val
     * @return
     */
    public static String valueToRatio(String val) {
        return val+"%";
    }

    /**
     * 将数值转换为 连续学习X周
     */
    public static String valueToUnspacecLrn(String tagName, String val){
        return tagName.replace("N",val);
    }


    /**
     * 生成ES层级的属性 <br>
     * 示例 ： userlabels.age
     *
     * @param name
     * @return
     */
    public static String generateEsPropertiesKey(String name) {
        if (UserSearchConstants.DIRECTLY.contains(name)) {
            return name;
        } else {
            return UserSearchConstants.EsLabelLevelName.DIYLABELS.getValue() + "." + name;
        }
    }

    /**
     * 生成Es分页信息
     *
     * @param pageRequest
     * @return
     */
    public static org.springframework.data.domain.PageRequest generatePageable(PageRequest pageRequest) {
        return org.springframework.data.domain.PageRequest
                .of((int) pageRequest.getCurrent() - 1, (int) pageRequest.getSize());
    }

    /**
     * 获取部门短名称
     *
     * @param deptPathName
     * @return
     */
    public static String getShortDeptName(String deptPathName) {
        if (StringUtils.isBlank(deptPathName)) {
            return deptPathName;
        }
        String[] split = deptPathName.split(UserSearchConstants.DEPT_PATH_SPLIT_STR);
        if (null != split && split.length > 0) {
            return split[split.length - 1];
        }
        return deptPathName;
    }

}
