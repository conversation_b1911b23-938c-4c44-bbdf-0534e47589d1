package com.yxt.talent.bk.svc.tag;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.*;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.UserSearchConstants;
import com.yxt.talent.bk.common.es.ESQueryConveter;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.tag.bean.TagInfoBean;
import com.yxt.talent.bk.core.tag.bean.TagValueBean;
import com.yxt.talent.bk.core.tag.bean.UserBaseInfoBean;
import com.yxt.talent.bk.core.tag.bean.UserTagBaseBean;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.entity.UserTagEntity;
import com.yxt.talent.bk.core.tag.mapper.TagMapper;
import com.yxt.talent.bk.core.tag.mapper.TagValueMapper;
import com.yxt.talent.bk.core.tag.mapper.UserTagMapper;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.UserTagRepository;
import com.yxt.talent.bk.svc.base.BkConfigService;
import com.yxt.talent.bk.svc.tag.bean.*;
import com.yxt.talent.bk.svc.tag.enums.TagSourceEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户标签相关service
 *
 * <AUTHOR>
 * @since 2022/8/8
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserTagService {
    private static final String F_DIY_LABELS = "diylabels";

    private final ElasticsearchRestTemplate elasticsearchRestTemplate;
    private final TagRepository tagRepository;
    private final TagMapper tagMapper;
    private final TagValueMapper tagValueMapper;
    private final UserTagRepository userTagRepository;
    private final UserTagMapper userTagMapper;
    private final BkConfigService bkConfigService;
    private final ESQueryConveter esQueryConveter;

    public IPage<UserBaseInfoBean> listPageTagValueUser(com.yxt.common.pojo.api.PageRequest pageRequest, String orgId,
            String keyword, String tagId, String tagValueId) {
        IPage<UserBaseInfoBean> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
                pageRequest.getCurrent(), pageRequest.getSize());
        return userTagMapper.listPageTagUser(page, orgId, keyword, tagId, tagValueId);
    }

    public List<UserBaseInfoBean> listTagValueUser(String orgId, String keyword, String tagId) {
        return userTagMapper.listTagUser(orgId, keyword, tagId);
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void delUserTag(String orgId, Collection<String> tagIds, Collection<String> userIds, String operatorId) {
        Date now = DateUtil.currentTime();
        userTagMapper.delUserTag(orgId, tagIds, userIds, operatorId, now);
    }

    public Map<String, List<UserTagBaseBean>> getTagUserMap(String orgId, Collection<String> tagIds,
            Collection<String> userIds) {
        if (CollectionUtils.isEmpty(tagIds) || CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        List<UserTagBaseBean> userTagBaseBeans = userTagMapper.listUserTag(orgId, tagIds, userIds);
        if (CollectionUtils.isNotEmpty(userTagBaseBeans)) {
            return userTagBaseBeans.stream().collect(Collectors.groupingBy(UserTagBaseBean::getTagId));
        }
        return Maps.newHashMap();
    }

    public List<UserTagEntity> initUserTagList(String orgId, String tagId, List<UserTagBaseBean> existsUserTagBaseBeans,
            List<String> userIds, String operatorId) {
        Date now = DateUtil.currentTime();
        List<String> tagUserIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(existsUserTagBaseBeans)) {
            tagUserIds = StreamUtil.mapList(existsUserTagBaseBeans, UserTagBaseBean::getUserId);
        }
        List<String> insertUserIds = TalentbkUtil.calculateDifferenceUserIds(userIds, tagUserIds);
        log.debug("LOG10640:{}", insertUserIds);
        if (CollectionUtils.isNotEmpty(insertUserIds)) {
            return generateUserTags(orgId, tagId, insertUserIds, now, operatorId);
        }
        return Lists.newArrayList();
    }

    private List<UserTagEntity> generateUserTags(String orgId, String tagId, List<String> userIds, Date now,
            String operatorId) {
        List<UserTagEntity> userTagList = Lists.newArrayListWithExpectedSize(userIds.size());
        userIds.forEach(userId -> {
            UserTagEntity userTag = new UserTagEntity();
            userTag.setId(ApiUtil.getUuid());
            userTag.setOrgId(orgId);
            userTag.setUserId(userId);
            userTag.setTagId(tagId);
            userTag.setCreateTime(now);
            userTag.setCreateUserId(operatorId);
            userTag.setUpdateTime(now);
            userTag.setUpdateUserId(operatorId);
            userTagList.add(userTag);
        });
        return userTagList;
    }

    public void batchSave(List<UserTagEntity> userTagEntityList) {
        userTagRepository.saveBatch(userTagEntityList);
    }

    public UserAllTagInfoBean getEsUserTagInfo(String userId, String orgId) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(UserSearchConstants.ES_ORG_ID, orgId))
                .must(QueryBuilders.termQuery(UserSearchConstants.ES_USER_ID, userId));
        NativeSearchQuery query = new NativeSearchQueryBuilder().withQuery(queryBuilder).build();
        log.debug("LOG11210:{}{}", System.lineSeparator(), esQueryConveter.extractJson(query));
        List<UserAllTagInfoBean> tagInfoBeans = elasticsearchRestTemplate.queryForList(query, UserAllTagInfoBean.class);
        if (CollectionUtils.isEmpty(tagInfoBeans)) {
            return new UserAllTagInfoBean();
        }
        return tagInfoBeans.get(0);
    }

    /**
     * 查询指定自建标签查询用户分页列表
     *
     * @param orgId      机构id
     * @param keyword    搜索关键字(姓名帐号模糊搜索)
     * @param tagId      标签id
     * @param tagValueId 标签值id
     * @param current    当前第几页
     * @param size       每页大小
     * @return 用户信息
     */
    public PagingList<UserBaseInfoBean> listPageEsTagUser(String orgId, String keyword, String tagId, String tagValueId,
            long current, long size) {
        TagEntity tag = getTag(orgId, tagId);
        if (Objects.isNull(tag)) {
            // 没找到对应的标签 报错
            throw new ApiException("1");
        }
        List<TagValueBean> tagValueBeanList = tagValueMapper.listTagValueByTagId(orgId, tagId);
        QueryBuilder queryBuilder = getQueryBuilder(orgId, keyword, tagId, tagValueId, tagValueBeanList,
                tag.getSource());
        Pageable pageable = PageRequest.of((int) current - 1, (int) size);
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().withQuery(queryBuilder)
                .withPageable(pageable).build();
        log.debug("LOG11260:{}{}", System.lineSeparator(), esQueryConveter.extractJson(nativeSearchQuery));
        Page<UserBaseInfoBean> page = elasticsearchRestTemplate.queryForPage(nativeSearchQuery, UserBaseInfoBean.class);
        List<UserBaseInfoBean> userBaseInfoBeans = page.getContent();
        if (CollectionUtils.isNotEmpty(userBaseInfoBeans)) {
            if (Objects.equals(TagSourceEnum.SOURCE_1.getType(), tag.getSource())) {
                // 自建标签
                Map<String, String> tagValueNameMap = StreamUtil
                        .list2map(tagValueBeanList, TagValueBean::getId, TagValueBean::getValueName);
                userBaseInfoBeans.forEach(userBase -> {
                    Map<String, Set<String>> diylabels = userBase.getDiylabels();
                    if (MapUtils.isEmpty(diylabels)) {
                        return;
                    }
                    diylabels.forEach((userTagId, tagValueIdSet) -> {
                        if (!Objects.equals(tagId, userTagId)) {
                            return;
                        }
                        tagValueIdSet.forEach(userTagValueId -> userBase.getTagValueNameList()
                                .add(tagValueNameMap.get(userTagValueId)));
                    });
                });
            } else {
                userBaseInfoBeans
                        .forEach(userBase -> userBase.getTagValueNameList().addAll(userBase.getLeadershipStyle()));
            }
        }
        PagingList<UserBaseInfoBean> pagingList = new PagingList<>();
        // 设置返回值
        pagingList.setDatas(userBaseInfoBeans);
        // 设置分页信息
        long offset = (current - 1) * size;
        Paging paging = new Paging(size, offset, page.getTotalPages(), page.getTotalElements());
        pagingList.setPaging(paging);
        return pagingList;
    }

    /**
     * 拼接es查询条件
     *
     * @param orgId            机构id
     * @param keyword          关键字
     * @param tagId            标签id
     * @param tagValueId       标签值id
     * @param tagValueBeanList 标签值信息
     * @param source           标签来源 0-内置,1-自建,2-固定
     * @return 查询条件
     */
    private QueryBuilder getQueryBuilder(String orgId, String keyword, String tagId, String tagValueId,
            List<TagValueBean> tagValueBeanList, Integer source) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(UserSearchConstants.ES_ORG_ID, orgId));
        if (StringUtils.isNotBlank(keyword)) {
            String likeQuery = TalentBkConstants.MARK_1 + keyword + TalentBkConstants.MARK_1;
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.should(QueryBuilders.wildcardQuery(UserSearchConstants.ES_CN_NAME, likeQuery));
            boolQueryBuilder.should(QueryBuilders.wildcardQuery(UserSearchConstants.ES_USER_NAME, likeQuery));
            queryBuilder.must(boolQueryBuilder);
        }
        Set<String> tagValueIdSet = Sets.newHashSet();
        Set<String> tagValueNameSet = Sets.newHashSet();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(tagValueId)) {
            // 标签值id为空，则需要查询所有标签值id或者标签值名称
            if (CollectionUtils.isEmpty(tagValueBeanList)) {
                throw new ApiException("2");
            }
            if (Objects.equals(TagSourceEnum.SOURCE_1.getType(), source)) {
                // 自建标签，取标签值id
                tagValueIdSet = StreamUtil.map2set(tagValueBeanList, TagValueBean::getId);
            } else {
                tagValueNameSet = StreamUtil.map2set(tagValueBeanList, TagValueBean::getValueName);
            }
        } else {
            tagValueIdSet.add(tagValueId);
            tagValueNameSet.add(tagValueId);
        }
        if (Objects.equals(TagSourceEnum.SOURCE_1.getType(), source)) {
            // 自建标签
            tagValueIdSet.forEach(condition -> boolQueryBuilder.should(QueryBuilders.termQuery(
                UserSearchConstants.EsLabelLevelName.DIYLABELS.getName() + TalentBkConstants.MARK_2 + tagId,
                    condition)));
        } else {
            tagValueNameSet.forEach(tagValueName -> boolQueryBuilder
                    .should(QueryBuilders.termQuery(UserSearchConstants.ES_LEADERSHIP_STYLE, tagValueName)));
        }
        queryBuilder.must(boolQueryBuilder);
        return queryBuilder;
    }

    private Map<String, List<TagValueBean>> getTagValueMap(String orgId, Set<String> tagValueIds) {
        List<TagValueBean> tagValueBeanList = tagValueMapper.listTagValue(orgId, tagValueIds);
        if (CollectionUtils.isNotEmpty(tagValueBeanList)) {
            return tagValueBeanList.stream().collect(Collectors.groupingBy(TagValueBean::getTagId));
        }
        return Maps.newHashMap();
    }

    /**
     * 手动给用户打标签
     *
     * @param orgId       机构id
     * @param userTagBean 标签信息
     */
    public void tagUser(String orgId, UserTagBean userTagBean) {
        List<String> userIds = userTagBean.getUserIds();
        List<TagBean> tagBeanList = userTagBean.getTagBeanList();
        Map<String, TagBean> tagBeanMap = StreamUtil.list2map(tagBeanList, TagBean::getTagId);
        List<TagInfoBean> tagInfoBeans = tagMapper.listTag(orgId, userTagBean.listAllTagId());
        if (CollectionUtils.isEmpty(tagInfoBeans)) {
            return;
        }
        // 1、暂时不考虑查不到用户的情况
        NativeSearchQuery query = new NativeSearchQueryBuilder().withIds(userIds).build();
        log.debug("LOG11220:{}{}", System.lineSeparator(), esQueryConveter.extractJson(query));
        List<EsUserBean> tagBeans = elasticsearchRestTemplate.multiGet(query, EsUserBean.class);
        Map<String, EsUserBean> esUserMap = StreamUtil.list2map(tagBeans, EsUserBean::getUserId);
        if (esUserMap.size() < 100) {
            log.debug("LOG10700:{}", BeanHelper.bean2Json(esUserMap));
        }

        List<UpdateQuery> queries = Lists.newArrayListWithCapacity(userIds.size());
        userIds.forEach(userId -> {
            EsUserBean esUserBean = esUserMap.get(userId);
            if (Objects.isNull(esUserBean)) {
                log.info("LOG10710:{}", userId);
                return;
            }
            Map<String, Object> result = getUpdateMap(esUserBean, tagInfoBeans, tagBeanMap);
            log.debug("LOG10720:{}", BeanHelper.bean2Json(result));
            if (MapUtils.isNotEmpty(result)) {
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.doc(result);
                UpdateQuery updateQuery =
                    new UpdateQueryBuilder().withId(userId).withIndexName(bkConfigService.getIndexName())
                        .withType(TalentBkConstants.ES_TYPE).build();
                updateQuery.setUpdateRequest(updateRequest);
                log.debug("LOG10740:{}", updateQuery.getUpdateRequest().toString());
                queries.add(updateQuery);
            }
        });
        if (CollectionUtils.isNotEmpty(queries)) {
            // 更新用户标签
            elasticsearchRestTemplate.bulkUpdate(queries);
        }
    }

    /**
     * 删除单个用户的标签信息
     *
     * @param orgId       机构id
     * @param userId      用户id
     * @param userTagBean 待删除标签信息
     */
    public void delSingleUserTag(String orgId, String userId, UserTagBean userTagBean) {
        List<TagInfoBean> tagInfoBeans = tagMapper.listTag(orgId, userTagBean.listDelTagId());
        if (CollectionUtils.isEmpty(tagInfoBeans)) {
            return;
        }
        NativeSearchQuery query = new NativeSearchQueryBuilder().withIds(Lists.newArrayList(userId)).build();
        log.debug("LOG11230:{}{}", System.lineSeparator(), esQueryConveter.extractJson(query));
        List<EsUserBean> tagBeans = elasticsearchRestTemplate.multiGet(query, EsUserBean.class);
        Map<String, EsUserBean> esUserMap = StreamUtil.list2map(tagBeans, EsUserBean::getUserId);

        List<UpdateQuery> queries = Lists.newArrayList();
        EsUserBean esUserBean = esUserMap.get(userId);
        if (Objects.isNull(esUserBean)) {
            return;
        }
        Map<String, Object> result = getDelUserTagMap(esUserBean, tagInfoBeans, userTagBean.getDelTagBeanMap());
        if (MapUtils.isNotEmpty(result)) {
            UpdateRequest updateRequest = new UpdateRequest();
            updateRequest.doc(result);
            UpdateQuery updateQuery = new UpdateQueryBuilder().withId(userId).withIndexName(bkConfigService.getIndexName())
                    .withType(TalentBkConstants.ES_TYPE).build();
            updateQuery.setUpdateRequest(updateRequest);
            queries.add(updateQuery);
        }
        if (CollectionUtils.isNotEmpty(queries)) {
            // 更新用户标签
            elasticsearchRestTemplate.bulkUpdate(queries);
        }
    }

    private Map<String, Object> getDelUserTagMap(EsUserBean esUserBean, List<TagInfoBean> tagInfoBeans,
            Map<String, TagBean> delTagBeanMap) {
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Set<String>> diylabels = getMap(esUserBean.getDiylabels());
        tagInfoBeans.parallelStream().forEach(tagInfo -> {
            if (!Objects.equals(TagSourceEnum.SOURCE_1.getType(), tagInfo.getTagSource())) {
                // 不是自建标签不处理
                return;
            }
            String tagId = tagInfo.getId();
            TagBean delTagBean = delTagBeanMap.get(tagId);
            if (Objects.nonNull(delTagBean)) {
                // 删除自建标签值
                Set<String> valueSet = diylabels.get(tagId);
                if (CollectionUtils.isNotEmpty(valueSet)) {
                    valueSet.removeAll(delTagBean.getTagValueIdList());
                }
            }
        });
        if (MapUtils.isNotEmpty(diylabels)) {
            result.put(F_DIY_LABELS, diylabels);
        }
        return result;
    }

    private Map<String, Set<String>> getMap(Map<String, Set<String>> diylabels) {
        if (MapUtils.isEmpty(diylabels)) {
            diylabels = Maps.newHashMap();
        }
        return diylabels;
    }

    /**
     * @param esUserBean
     * @param tagInfoBeans
     * @param tagBeanMap
     * @return
     */
    private Map<String, Object> getUpdateMap(EsUserBean esUserBean, List<TagInfoBean> tagInfoBeans,
            Map<String, TagBean> tagBeanMap) {
        Map<String, Set<String>> diylabels = getMap(esUserBean.getDiylabels());
        Map<String, Object> result = Maps.newHashMap();
        // 内置标签map
        Map<String, Set<String>> valueMap = Maps.newConcurrentMap();
        tagInfoBeans.stream().forEach(tagInfo -> {
            if (!Objects.equals(TagSourceEnum.SOURCE_1.getType(), tagInfo.getTagSource())) {
                // 不是自建标签不处理
                return;
            }
            String tagId = tagInfo.getId();
            TagBean tagBean = tagBeanMap.get(tagId);
            Set<String> list;
            if (Objects.nonNull(tagBean)) {
                // 新增自建标签值
                list = diylabels.get(tagId);
                list = handleDiyLabelTagIdSet(list, tagBean.getTagValueIdList(), tagInfo.getValueChooseModel());
                valueMap.put(tagId, list);
            }
        });
        if (MapUtils.isNotEmpty(valueMap)) {
            result.put(F_DIY_LABELS, valueMap);
        }
        return result;
    }

    private Set<String> handleDiyLabelTagIdSet(Set<String> list, List<String> tagValueIdList,
            Integer valueChooseModel) {
        if (CollectionUtils.isEmpty(list)) {
            list = Sets.newHashSet();
            list.addAll(tagValueIdList);
        } else {
            if (Objects.equals(0, valueChooseModel)) {
                // 单选
                list.clear();
                list.addAll(tagValueIdList);
            } else if (Objects.equals(1, valueChooseModel)) {
                // 多选
                list.addAll(tagValueIdList);
            }
        }
        return list;
    }

    private void handleLeadershipStyleTagNameSet(Set<String> list, List<TagValueBean> tagValueBeanList) {
        Set<String> tagValueNames = StreamUtil.map2set(tagValueBeanList, TagValueBean::getValueName);
        if (CollectionUtils.isEmpty(list)) {
            list = Sets.newHashSet();
            list.addAll(tagValueNames);
        } else {
            list.addAll(tagValueNames);
        }
    }

    /**
     * 删除用户标签
     *
     * @param orgId          机构id
     * @param delUserTagBean 标签信息
     * @param operatorId     操作人id
     */
    public void delEsUserTag(String orgId, DelUserTagBean delUserTagBean, String operatorId) {
        log.debug("delUserTag orgId=[{}] operatorId=[{}]", orgId, operatorId);
        String tagId = delUserTagBean.getTagId();
        TagEntity tag = getTag(orgId, tagId);
        List<String> userIdList = delUserTagBean.getUserIdList();
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().withIds(userIdList).build();
        log.debug("LOG11270:{}{}", System.lineSeparator(), esQueryConveter.extractJson(nativeSearchQuery));
        List<EsUserBean> esUserBeans = elasticsearchRestTemplate.multiGet(nativeSearchQuery, EsUserBean.class);
        if (CollectionUtils.isEmpty(esUserBeans)) {
            return;
        }
        List<UpdateQuery> queries = Lists.newArrayListWithCapacity(esUserBeans.size());
        esUserBeans.forEach(esUser -> {
            if (StringUtils.isBlank(esUser.getOrgId()) || StringUtils.isBlank(esUser.getUserId())) {
                return;
            }
            Map<String, Object> result = getDelUserTagMap(esUser, tag, delUserTagBean.getTagValueIdSet());
            if (MapUtils.isNotEmpty(result)) {
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.doc(result);
                UpdateQuery updateQuery =
                    new UpdateQueryBuilder().withId(esUser.getUserId()).withIndexName(bkConfigService.getIndexName())
                        .withType(TalentBkConstants.ES_TYPE).build();
                updateQuery.setUpdateRequest(updateRequest);
                queries.add(updateQuery);
            }
        });
        if (CollectionUtils.isNotEmpty(queries)) {
            elasticsearchRestTemplate.bulkUpdate(queries);
        }
    }

    private Map<String, Object> getDelUserTagMap(EsUserBean esUserBean, TagEntity tag, Set<String> tagValueIdSet) {
        Map<String, Object> result = Maps.newHashMap();
        if (Objects.equals(TagSourceEnum.SOURCE_1.getType(), tag.getSource())) {
            // 自建标签
            Map<String, Set<String>> diylabels = getMap(esUserBean.getDiylabels());
            Set<String> valueSet = diylabels.get(tag.getId());
            if (CollectionUtils.isNotEmpty(valueSet)) {
                valueSet.removeAll(tagValueIdSet);
            }
            result.put(F_DIY_LABELS, diylabels);
        }
        return result;
    }

    private TagEntity getTag(String orgId, String tagId) {
        TagEntity tag = tagRepository.queryTagById(orgId, tagId);
        Validate.isNotNull(tag, BkApiErrorKeys.ERROR_KEY_TAG_IS_NULL);
        return tag;
    }

}
