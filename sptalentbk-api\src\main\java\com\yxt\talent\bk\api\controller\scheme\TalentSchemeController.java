package com.yxt.talent.bk.api.controller.scheme;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.bk.api.component.TalentSchemeComponent;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkAuthCodes;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.usergroup.bean.Scheme4Search;
import com.yxt.talent.bk.core.usergroup.bean.SchemeBean;
import com.yxt.talent.bk.svc.scheme.bean.Scheme4List;
import com.yxt.talent.bk.svc.scheme.bean.SearchSchemeBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Description 人才列表
 * <AUTHOR>
 * @Date 2023/8/16 13:56
 **/

@RestController
@RequestMapping("/mgr/scheme")
@AllArgsConstructor
@Tag(name = "人才列表")
@Slf4j
public class TalentSchemeController extends BaseController {

    private final TalentSchemeComponent schemeComponent;

    @Operation(summary = "人才搜索列表")
    @PostMapping(value = "/user/list", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_SCHEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public PagingList<SchemeBean> findTalentScheme(HttpServletRequest request, @RequestBody Scheme4Search bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return schemeComponent.findTaletScheme(
                request, ApiUtil.getPageRequest(request), userDetail.getOrgId(), bean, userDetail);
    }

    @Operation(summary = "筛选方案列表")
    @GetMapping(value = "/list", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_SCHEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN,
            codes = {TalentBkAuthCodes.BK_FILE_SEARCH_TAGFILTER})
    public CommonList<Scheme4List> findSchemeList() {
        UserCacheDetail userDetail = getUserCacheDetail();
        List<Scheme4List> schemeList = schemeComponent.findSchemeList(userDetail.getOrgId(), userDetail.getUserId());
        return new CommonList<>(schemeList);
    }

    @Operation(summary = "筛选方案明细")
    @GetMapping(value = "/rule/{schemeId}", consumes = Constants.MEDIATYPE)
    @Parameters({             @Parameter(name = "schemeId", description = "筛选方案id")})
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_SCHEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public SearchRuleBean findSchemeDetail(@PathVariable Long schemeId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return schemeComponent.findSchemeDetail(userDetail.getOrgId(), schemeId);
    }


    @Operation(summary = "创建筛选方案")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.USER_SEARCH_SCHEME_CREATE, paramExp = "#bean")
    @PostMapping(value = "/rule", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_SCHEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public SearchSchemeBean createSchemeRule(@Validated @RequestBody SearchSchemeBean bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return schemeComponent.createSchemeRule(userDetail.getOrgId(), userDetail.getUserId(), bean);
    }

    @Operation(summary = "编辑筛选方案明细")
    @PutMapping(value = "/rule", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_SCHEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public void editSchemeRule(@Validated @RequestBody SearchSchemeBean bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        schemeComponent.editSchemeRule(userDetail.getOrgId(), userDetail.getUserId(), bean);
    }

    @Operation(summary = "删除筛选方案")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.USER_SEARCH_SCHEME_DEL, paramExp = "#schemeId")
    @DeleteMapping(value = "/{schemeId}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_SCHEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public void deleteSchemeRule(@PathVariable Long schemeId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        schemeComponent.deleteScheme(userDetail.getOrgId(), userDetail.getUserId(), schemeId);
    }


    @Operation(summary = "导出人员列表")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.USER_LIST_EXPORT)
    @PostMapping(value = "/export", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_SCHEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN,
            codes = {TalentBkAuthCodes.BK_FILE_SEARCH_EXPORT})
    public Map<String, String> export(@RequestBody Scheme4Search bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return schemeComponent.export(userDetail, bean);
    }
}
