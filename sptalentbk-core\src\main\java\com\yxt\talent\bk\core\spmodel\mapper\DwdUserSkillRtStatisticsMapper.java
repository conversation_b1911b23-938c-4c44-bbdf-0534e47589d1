package com.yxt.talent.bk.core.spmodel.mapper;

import com.yxt.talent.bk.core.dashboard.bean.DeptUserSkillRtReachDTO;
import com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO;
import com.yxt.talent.bk.core.dashboard.bean.GroupUserSkillRtReachDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DwdDeptMapper
 *
 * <AUTHOR> harleyge
 * @Date 13/6/24 5:16 pm
 */
@Mapper
public interface DwdUserSkillRtStatisticsMapper {

    List<DeptUserSkillRtReachDTO> getDeptUserSkillReachCount(@Param("orgId") String orgId,
            @Param("deptIds") List<String> deptIds, @Param("groupIds") List<Long> groupIds);

    List<GroupUserSkillRtReachDTO> getGroupUserSkillReachCount(@Param("orgId") String orgId,
            @Param("deptIds") List<String> deptIds, @Param("groupIds") List<Long> groupIds);

    long getDeptGroupAllCount(@Param("orgId") String orgId, @Param("deptIds") List<String> deptIds,
            @Param("groupIds") List<Long> groupIds);

    List<DwsUserSkillRtDTO> getPageByDeptGroup(@Param("orgId") String orgId,
            @Param("deptIds") List<String> deptIds, @Param("groupIds") List<Long> groupIds,
            @Param("offset") long offset, @Param("limitSize") long limit);

    List<DwsUserSkillRtDTO> getAllByDeptGroup(@Param("orgId") String orgId, @Param("deptIds") List<String> deptIds,
            @Param("groupIds") List<Long> groupIds);

    List<DwsUserSkillRtDTO> getUserSkillRtByUserIds(@Param("orgId") String orgId,
            @Param("userIds") List<String> userIds);

    DwsUserSkillRtDTO getAllByDeptGroupLimitOne(@Param("orgId") String orgId,
            @Param("deptIds") List<String> deptIds, @Param("groupIds") List<Long> groupIds);

    DwsUserSkillRtDTO getUserSkillRtByUserIdsOne(@Param("orgId") String orgId,
            @Param("userIds") List<String> userIds);
}
