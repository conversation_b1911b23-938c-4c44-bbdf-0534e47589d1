package com.yxt.talent.bk.svc.strategy;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.ExcelUtil;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
@AllArgsConstructor
public class PoolReadiness4ErrorStrategy implements OutputStrategy {
    private final I18nComponent i18nComponent;
    private static final String[] EXPORT_KEYS = {"fullName", "userName", "readinessName","errMsg"};

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        Map<String, Object> headerMap = PoolReadinessTempStrategry.baseHeaderMap();
        headerMap.put("errMsg",
                i18nComponent.getI18nValue("apis.talentbk.pool.user.export.header.errMsg"));
        ExcelUtil.exportWithTemplate(
                headerMap,
                data, filePath, ExportConstants.POOL_READONESST_ERROR_TEMP_FILE_PATH);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE).appCode(ModuleConstants.APP_CODE)
                .moduleCode(ModuleConstants.MODULE_CODE).fileName(fileName)
                .name(i18nComponent.getI18nValue(ExportConstants.POOL_READONESS_IMPORT_ERROR_NAME)).build();
    }
}
