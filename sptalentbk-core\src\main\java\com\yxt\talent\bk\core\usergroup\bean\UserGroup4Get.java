package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: sptalentbkapi
 * @description: 群组创建 Bean
 **/
@Data
public class UserGroup4Get {

    /**
     * 唯一 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "更新时使用，唯一 ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * 群组类型（1:静态，2：动态）
     */
    @Schema(description = "群组类型（1:静态，2：动态）", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer groupType;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String groupName;

    /**
     * 是否启用，（0：未启用，1：已启用）
     */
    @Schema(description = "是否启用，（0：未启用，1：已启用）,默认未启用", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer enabled = 0;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String groupDesc;

    @Schema(description = "覆盖人数", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer userCount;

    @Schema(description = "创建时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "计算时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime  caculateTime;

    @Schema(description = "负责人", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> userManagers;

}
