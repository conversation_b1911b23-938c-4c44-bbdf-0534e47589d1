package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 学员能力结果数据(DwdUserEvalSkillRt)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 17:34:43
 */
@Data
@TableName(value = "dwd_user_eval_skill_rt")
public class DwdUserEvalSkillRt {
    /**
     * id
     */
    @TableField(value = "id")
    private String id;
    /**
     * 0: 记录有效,1：记录无效
     */
    @TableField(value = "dw_status")
    private Integer dwStatus;
    /**
     * 能力ID
     */
    @TableField(value = "skill_id")
    private String skillId;
    /**
     * 被评估人ID
     */
    @TableField(value = "user_id")
    private String userId;
    /**
     * 能力得分
     */
    @TableField(value = "stan_score")
    private BigDecimal stanScore;
    /**
     * 能力得分（10分制）
     */
    @TableField(value = "ten_score")
    private BigDecimal tenScore;
    /**
     * 评估方案类型(1-行为,2-问卷,3-人格,4-多次行为评估)
     */
    @TableField(value = "evaluation_type")
    private Integer evaluationType;
    /**
     * 能力优劣势 1 优势项 2 待提升项
     */
    @TableField(value = "conclusion")
    private Integer conclusion;
    /**
     * 能力等级
     */
    @TableField(value = "skill_level_name")
    private String skillLevelName;
    /**
     * 机构ID
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 第三方用户ID
     */
    @TableField(value = "third_user_id")
    private String thirdUserId;
    /**
     * 第三方部门ID
     */
    @TableField(value = "third_dept_id")
    private String thirdDeptId;
    /**
     * 用户姓名
     */
    @TableField(value = "full_name")
    private String fullName;
    /**
     * 人员状态：-1-删除，0-禁用，1-启用
     */
    @TableField(value = "user_status")
    private Integer userStatus;

    /**
     * 能力类型：0-通用能力 1-专业能力 2-第三方能力
     */
    @TableField(value = "skill_type")
    private Integer skillType;
    /**
     * 能力层级：仅倍智能力有此数据
     */
    @TableField(value = "skill_tier")
    private Integer skillTier;
    /**
     * 能力模型id
     */
    @TableField(value = "skill_model_id")
    private String skillModelId;
    /**
     * 能力名称
     */
    @TableField(value = "skill_name")
    private String skillName;

    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime creatTime;

}
