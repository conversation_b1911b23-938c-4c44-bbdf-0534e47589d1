package com.yxt.talent.bk.svc.pool.bean.export;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.ExcelUtil;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.utils.FillBeanUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 *  人才池人员导出模板
 * <AUTHOR>
 * @since 2022/9/19 13:40
 * @version 1.0
 */
@Component
@AllArgsConstructor
public class PoolUserInfoExportStrategy implements OutputStrategy {

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        ExcelUtil.exportWithTemplate(FillBeanUtil.generationFillBenList(data), filePath, ExportConstants.POOL_USER_INFO_EXPORT_TEMPLATE_PATH);

        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE).appCode(ModuleConstants.APP_CODE)
                .moduleCode(ModuleConstants.MODULE_CODE).fileName(fileName).name("人才池详情导出_").build();
    }
}
