package com.yxt.talent.bk.core.heir.ext;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * HeirPosUserEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:54 am
 */
@Getter
@Setter
public class HeirPosUserExt implements Serializable, UdpLangSupport {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "账号")
    private String username;

    @Schema(description = "姓名")
    private String fullName;

    private String positionId;
    @Schema(description = "主岗位名称")
    private String positionName;

    private String deptId;
    @Schema(description = "主部门全路径名称，例：研发中心->园区 ")
    private String deptName;

    @Schema(description = "用户状态：用于标识当前用户的状态(0-禁用,1-启用)")
    private Integer status;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "继任id")
    private String posId;

    @Schema(description = "准备度规则等级id")
    private Long prepareLevelId;

    @Schema(description = "计算的准备度id")
    private Long calcLevelId;

    @Schema(description = "0:进行中，1:已退出")
    private Integer heirStatus;

    @Schema(description = "退出原因")
    private String quitReason;

    @Schema(description = "退出时间")
    private Date exitTime;

    @Schema(description = "用户是否被删除")
    private Integer userDeleted;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(userId, fullName, this::setFullName);
    }

    @Override
    public UdpLangUnitBean deptLangProperty() {
        return new UdpLangUnitBean(deptId, deptName, this::setDeptName);
    }

    @Override
    public UdpLangUnitBean positionLangProperty() {
        return new UdpLangUnitBean(positionId, positionName, this::setPositionName);
    }
}
