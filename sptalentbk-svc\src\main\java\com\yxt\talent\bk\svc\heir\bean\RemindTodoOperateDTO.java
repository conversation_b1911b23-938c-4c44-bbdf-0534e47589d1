package com.yxt.talent.bk.svc.heir.bean;

import com.yxt.talent.bk.core.heir.bean.RemindTodoParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * RemindTodoOperateDTO
 *
 * <AUTHOR> geyan
 * @Date 27/9/23 10:32 am
 */
@Data
public class RemindTodoOperateDTO {
    private boolean doneTodo;
    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "部门或者岗位id")
    private String posId;

    @Schema(description = "提醒人")
    private String remindUserId;

    @Schema(description = "处理目标用户")
    private String targetUserId;

    private Long targetRecordId;
    private String jumpUrl;

    private RemindTodoParam todoParam;
}
