package com.yxt.talent.bk.common.enums;

public enum LevelTypeEnum {
    /**
     * 风险类型
     */
    PREPARE("继任准备度", 0),
    RISK("继任风险规则", 1) ;

    private String name;

    private Integer type;

    LevelTypeEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }
}
