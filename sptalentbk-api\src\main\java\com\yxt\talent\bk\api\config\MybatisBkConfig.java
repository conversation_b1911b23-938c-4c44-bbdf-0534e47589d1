package com.yxt.talent.bk.api.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.yxt.common.interceptor.DbHintInterceptor;
import com.yxt.common.interceptor.DbhintPaginationInnerInterceptor;
import com.yxt.spmodel.facade.service.SpmodelSqlService;
import com.yxt.spsdk.common.interceptor.MybatisLogInterceptor;
import com.yxt.spsdk.spmodel.bean.SpmodelQueryConfig;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @since 2023/1/16
 */
@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
@MapperScan(value = {"com.yxt.spsdk.**.mapper", "com.yxt.talent.bk.core.*.mapper"}, sqlSessionFactoryRef = MybatisBkConfig.BK_SQL_SESSION_FACTORY)
public class MybatisBkConfig extends BaseMybatisConfig {

    public static final String BK_SQL_SESSION_FACTORY = "bkSqlSessionFactory";

    @Bean(BK_SQL_SESSION_FACTORY)
    public SqlSessionFactory sqlSessionFactory(@Qualifier("bkDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
        factory.setDataSource(dataSource);
        factory.setVfs(SpringBootVFS.class);
        factory.setTypeAliasesPackage("com.yxt.talent.bk.core.*.entity");
        factory.setMapperLocations(resolveMapperLocations("classpath*:mapper/**/*Mapper*.xml"));
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(dbhintPaginationInnerInterceptor());
        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        factory.setPlugins(new DbHintInterceptor(), mybatisPlusInterceptor, new MybatisLogInterceptor());
        applyConfiguration(factory);

        return factory.getObject();
    }

    @Bean
    public DbhintPaginationInnerInterceptor dbhintPaginationInnerInterceptor() {
        DbhintPaginationInnerInterceptor dbhintPaginationInnerInterceptor =
                new DbhintPaginationInnerInterceptor();
        dbhintPaginationInnerInterceptor.setDbType(DbType.MYSQL);
        // 关闭MP left join的自动优化
        dbhintPaginationInnerInterceptor.setOptimizeJoin(false);
        return dbhintPaginationInnerInterceptor;
    }

    @Bean
    public SpmodelQueryConfig spmodelQueryConfig(SpmodelSqlService spmodelSqlService) {
        return new SpmodelQueryConfig("com.yxt.talent.bk.core.spmodel.mapper", spmodelSqlService);
    }

    @Bean("bkSqlSessionTemplate")
    public SqlSessionTemplate bkSqlSessionTemplate(
        @Qualifier(BK_SQL_SESSION_FACTORY) SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean("bkBatchSqlSession")
    public SqlSessionTemplate bkBatchSqlSession(@Qualifier(BK_SQL_SESSION_FACTORY) SqlSessionFactory sqlSessionFactory) {
        // Mysql, rewriteBatchedStatements=true
        return new SqlSessionTemplate(sqlSessionFactory, ExecutorType.BATCH);
    }

}
