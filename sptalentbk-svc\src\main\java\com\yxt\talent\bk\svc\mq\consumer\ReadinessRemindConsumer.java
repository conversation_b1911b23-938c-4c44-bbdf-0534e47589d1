package com.yxt.talent.bk.svc.mq.consumer;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPrepareCfgMapper;
import com.yxt.talent.bk.svc.heir.ReadinessComputeService;
import com.yxt.talent.bk.svc.mq.constant.HeirRocketMqConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = HeirRocketMqConstants.GROUP_PREFIX + HeirRocketMqConstants.TOPIC_HEIR_READINESS_REMIND,     topic = HeirRocketMqConstants.TOPIC_HEIR_READINESS_REMIND, consumeThreadNumber = 3, consumeTimeout = 30)
@RequiredArgsConstructor
public class ReadinessRemindConsumer implements RocketMQListener<HeirPosEntity> {

    private final RocketMQTemplate rocketMQTemplate;

    private final ReadinessComputeService readinessComputeService;

    private final HeirPosMapper heirPosMapper;
    @Override
    public void onMessage(HeirPosEntity heirPosEntity) {
        try {
            log.info("readinessRemindConsumer start heirPosEntity:{}",JSON.toJSONString(heirPosEntity));
            int version = heirPosEntity.getNextRemindVersion();
            HeirPosEntity posEntity = heirPosMapper.selectById(heirPosEntity.getId());
            if (version != posEntity.getNextRemindVersion()) {
                log.info("readinessRemindConsumer version change posId:{}",heirPosEntity.getId());
                //版本号发生变化，数据丢弃
                return;
            }
            Date nextRemindTime = heirPosEntity.getNextRemindTime();
            if (nextRemindTime !=null && (nextRemindTime.getTime()- System.currentTimeMillis())/1000< ReadinessComputeService.FLOAT_SEC) {
                log.info("readinessRemindConsumer sendPrepareChangeUser heirPosEntity:{}",JSON.toJSONString(heirPosEntity));
                readinessComputeService.sendPrepareChangeUser(heirPosEntity);
                readinessComputeService.setRemindNextExecutionTime(heirPosEntity);
            }else{
                log.info("readinessRemindConsumer syncDelaySend posId:{},topic:{}", heirPosEntity.getId(),
                        HeirRocketMqConstants.TOPIC_HEIR_READINESS_COMPUTE);
                rocketMQTemplate.syncDelaySend(HeirRocketMqConstants.TOPIC_HEIR_READINESS_REMIND, JSON.toJSONString(heirPosEntity),
                        readinessComputeService.calcDelayLevel(nextRemindTime).getDelayLevel());
            }
        } catch (Exception e) {
            log.error("readinessRemindConsumer failed posId {}", heirPosEntity.getId(), e);
        }
    }
}
