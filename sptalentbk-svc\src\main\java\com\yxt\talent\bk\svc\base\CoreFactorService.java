package com.yxt.talent.bk.svc.base;

import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.coreapi.client.bean.em.OrgFactorStateEnum;
import com.yxt.coreapi.client.bean.sale.OrgVerifyFactorBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 后端去版本改造
 * 前提：去版本是基于2.0企业设置，首先企业要是2.0企业，1.0企业不存在去版本，依旧使用1.0机构参的形式
 * 此类维护的数据来自产品经理，并维护在运营平台
 * 去版本PRD：https://lanhuapp.com/web/#/item/project/product?type=notifyFile&docType=axure&docId=d705e2cd-e433-4c95-a3d6-733494a5fec4&image_id=d705e2cd-e433-4c95-a3d6-733494a5fec4&vidBindFile=d705e2cd-e433-4c95-a3d6-733494a5fec4&pid=bfbbf08a-d781-43ae-a946-385f165f2f0a&project_id=bfbbf08a-d781-43ae-a946-385f165f2f0a&versionId=408b741c-b90e-4656-9bdc-110740c99a5c&vid=5bc542b9-18c8-4ee7-9536-e6276ce40d49&from=search&pageId=3ed1126f939b42deb2c5fc3282f7bd4c
 * 去版本方案设计：https://confluence.yunxuetang.com.cn/pages/viewpage.action?pageId=68624868
 * <p>
 * 一、运营平台：
 * >1、开发环境：https://123.yunxuetang.com.cn
 * >2、预发布环境：
 * >3、产线环境：
 * <p>
 * 二、人才发展要素(要素可能是应用APP，模块MODEL或者能力SKILL)
 * >1、人才发展基础(gwnl_basic)
 * >2、人才发展测训(gwnl_cx)
 * >3、人才发展高级(gwnl_pro)
 * <p>
 * 三、人才发展要素包
 * >1、基础版（gwnl_basic）
 * >2、测训版（gwnl_basic+gwnl_cx）
 * >3、高级版（gwnl_basic+gwnl_cx+gwnl_pro）
 * <p>
 * 四、使用方法
 * >1、方法中需要硬编码的部分可以只用coreApiFacade中的verifyOrgFactors方法用于鉴权，具体返回参照verifyOrgFactors文档使用说明。
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CoreFactorService {
    /**
     * 应用ID,分别对应人才发展基础、人才发展测训、人才发展高级
     */
    private static final String APP_GWNL_BASIC = "gwnl_basic";
    private static final String APP_GWNL_CX = "gwnl_cx";
    private static final String APP_GWNL_PRO = "gwnl_pro";
    /**
     * 盘点要素
     */
    private static final String APP_GWNL_RV = "gwnl_talentrv";
    /**
     * 人才继任、人才池要素
     */
    private static final String APP_GWNL_POOL = "gwnl_succession";
    /**
     * 集团版
     */
    private static final String APP_GWNL_GROUP = "gwnl_group_server";
    /**
     * isv
     */
    private static final String APP_GWNL_ISV = "gwnl_isv";
    private static final List<String> APP_GWNL_FACTOR_LIST = Lists
            .newArrayList(APP_GWNL_BASIC, APP_GWNL_CX, APP_GWNL_PRO, APP_GWNL_GROUP, APP_GWNL_ISV);
    /**
     * 模块ID,是否开通证书，调查，线下/混合式培训，胜任力评估
     */
    private static final String MODEL_CERTIFICATE = "certificate";
    private static final String MODEL_SURVEY = "survey_mgmt";
    private static final String MODEL_O2O_TRAIN = "o2o_train";
    private static final String MODEL_COMPETENCE_EVAL = "competence_eval";
    /**
     * 自定义orgEdition（0-未开通人才发展，1-基础版，2-测训版，3-高级版）
     */
    private static final int ORG_EDITION_NONE = 0;
    private static final int ORG_EDITION_BASIC = 1;
    private static final int ORG_EDITION_CX = 2;
    private static final int ORG_EDITION_PRO = 3;

    private final CoreRpcService v2CoreRpc;

    private Map<String, OrgVerifyFactorBean> getOrgVerifyFactorBeanMap(String orgId, List<String> factorCodes) {
        List<OrgVerifyFactorBean> orgVerifyFactorBeans = v2CoreRpc.verifyOrgFactors(orgId, factorCodes);
        return StreamUtil.list2map(orgVerifyFactorBeans, OrgVerifyFactorBean::getFactorCode);
    }

    public OrgVerifyFactorBean getVerifyFactorBean(String orgId, String factorCode) {
        List<OrgVerifyFactorBean> verifyFactorBean = v2CoreRpc
                .verifyOrgFactors(orgId, Lists.newArrayList(factorCode));
        if (CollectionUtils.isNotEmpty(verifyFactorBean)) {
            return verifyFactorBean.get(0);
        } else {
            return new OrgVerifyFactorBean();
        }
    }

    private boolean isFactorPurchasedEnable(int state) {
        return state == OrgFactorStateEnum.PURCHASED_NOT_EXPIRED.getState();
    }


    /**
     * 是否是基础 2.0专用
     * 这里判断要素包中开且只开了人才发展基础要素包，则为基础版
     * <p>
     * 1、免费版需要去除岗位配置的<批量导入>功能
     * 2、免费版只能进行三级模式配置
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是基础版，false-不是基础版
     */
    public boolean isBasicV2(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap = getOrgVerifyFactorBeanMap(orgId,
                APP_GWNL_FACTOR_LIST);
        log.info("LOG50004:获取要素判断，coreapi返回：{}", BeanHelper.bean2Json(orgVerifyFactorBeanMap));
        return isBasic(orgVerifyFactorBeanMap);
    }

    /**
     * 是否是基础
     * 这里判断要素包中开且只开了人才发展基础要素包，则为基础版
     * <p>
     * 1、免费版需要去除岗位配置的<批量导入>功能
     * 2、免费版只能进行三级模式配置
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是基础版，false-不是基础版
     */
    public boolean isBasic(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap = getOrgVerifyFactorBeanMap(orgId,
                APP_GWNL_FACTOR_LIST);
        return isBasic(orgVerifyFactorBeanMap);
    }

    private boolean isBasic(Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap) {
        int basicState = orgVerifyFactorBeanMap.get(APP_GWNL_BASIC).getFactorState();
        int cxState = orgVerifyFactorBeanMap.get(APP_GWNL_CX).getFactorState();
        int proState = orgVerifyFactorBeanMap.get(APP_GWNL_PRO).getFactorState();
        return isFactorPurchasedEnable(basicState) && !isFactorPurchasedEnable(cxState) && !isFactorPurchasedEnable(
                proState);
    }

    /**
     * 是否是测训版 2.0专用
     * 要素包中开了人才发展测训要素包且未开人才发展收费要素包，则为测训版
     * <p>
     * 1、测训版需要隐藏能力模型库的新建模型和批量发布功能
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是测训版，false-不是测训版
     */
    public boolean isCxV2(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap = getOrgVerifyFactorBeanMap(orgId,
                APP_GWNL_FACTOR_LIST);
        log.info("LOG50005:获取要素判断，coreapi返回：{}", BeanHelper.bean2Json(orgVerifyFactorBeanMap));
        return isCx(orgVerifyFactorBeanMap);
    }

    /**
     * 是否是测训版
     * 要素包中开了人才发展测训要素包且未开人才发展收费要素包，则为测训版
     * <p>
     * 1、测训版需要隐藏能力模型库的新建模型和批量发布功能
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是测训版，false-不是测训版
     */
    public boolean isCx(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap = getOrgVerifyFactorBeanMap(orgId,
                APP_GWNL_FACTOR_LIST);
        log.info("获取测训要素判断，coreapi返回：{}", BeanHelper.bean2Json(orgVerifyFactorBeanMap));
        return isCx(orgVerifyFactorBeanMap);
    }

    private boolean isCx(Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap) {
        int cxState = orgVerifyFactorBeanMap.get(APP_GWNL_CX).getFactorState();
        int proState = orgVerifyFactorBeanMap.get(APP_GWNL_PRO).getFactorState();
        return isFactorPurchasedEnable(cxState) && !isFactorPurchasedEnable(proState);
    }

    /**
     * 是否是高级版 2.0 专用
     * 要素包中开了人才发展收费要素包，则为高级版
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是高级版，false-不是高级版
     */
    public boolean isProV2(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap = getOrgVerifyFactorBeanMap(orgId,
                APP_GWNL_FACTOR_LIST);
        log.info("LOG50006:获取要素判断，coreapi返回：{}", BeanHelper.bean2Json(orgVerifyFactorBeanMap));
        return isPro(orgVerifyFactorBeanMap);
    }

    /**
     * 是否是高级版
     * 要素包中开了人才发展收费要素包，则为高级版
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是高级版，false-不是高级版
     */
    public boolean isPro(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap = getOrgVerifyFactorBeanMap(orgId,
                APP_GWNL_FACTOR_LIST);
        return isPro(orgVerifyFactorBeanMap);
    }

    private boolean isPro(Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap) {
        int proState = orgVerifyFactorBeanMap.get(APP_GWNL_PRO).getFactorState();
        return isFactorPurchasedEnable(proState);
    }

    /**
     * 是否开通人才发展
     * 只要开通了任意一个要素都算开通人才发展
     *
     * @param orgId 机构id
     * @return true-开通了人才发展，false-没开通人才发展
     */
    public boolean isOpenTalent(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap = getOrgVerifyFactorBeanMap(orgId,
                APP_GWNL_FACTOR_LIST);
        int basicState = orgVerifyFactorBeanMap.get(APP_GWNL_BASIC).getFactorState();
        int cxState = orgVerifyFactorBeanMap.get(APP_GWNL_CX).getFactorState();
        int proState = orgVerifyFactorBeanMap.get(APP_GWNL_PRO).getFactorState();
        int groupState = orgVerifyFactorBeanMap.get(APP_GWNL_GROUP).getFactorState();
        int isvState = orgVerifyFactorBeanMap.get(APP_GWNL_ISV).getFactorState();
        return isFactorPurchasedEnable(basicState) || isFactorPurchasedEnable(cxState) || isFactorPurchasedEnable(
                proState) || isFactorPurchasedEnable(groupState) || isFactorPurchasedEnable(isvState);
    }

    /**
     * 获取机构开通的
     * 此接口只提供给2.0机构使用
     *
     * @param orgId 机构id
     * @return 0-未开通，1-基础版，2-测训版，3-高级版
     */
    public int getOrgEdition(String orgId) {
        int orgEdition = ORG_EDITION_NONE;
        if (isProV2(orgId)) {
            return ORG_EDITION_PRO;
        }
        if (isCxV2(orgId)) {
            return ORG_EDITION_CX;
        }
        if (isBasicV2(orgId)) {
            return ORG_EDITION_BASIC;
        }
        return orgEdition;
    }

    /**
     * 是否开通证书模块
     * <p>
     * 需要校验的地方：
     * 1、任职资格的<添加>和<编辑>证书
     * 2、测训项目<添加>和<编辑>证书，
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-开通证书模块，false-未开通证书模块
     */
    public boolean isOpenCertificate(String orgId) {
        return verifyFactor(orgId, MODEL_CERTIFICATE);
    }

    /**
     * 是否开通调查模块
     * <p>
     * 需要校验的地方
     * 1、学习方案添加调查
     * 2、学习方案模版增加调查
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-开通调查模块，false-未开通调查模块
     */
    public boolean isOpenSurvey(String orgId) {
        return verifyFactor(orgId, MODEL_SURVEY);
    }

    /**
     * 是否开通线下/混合式培训模块
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     * <p>
     * 需要校验的地方
     * 1、学习方案添加面授任务或者面授课程
     *
     * @param orgId 机构id
     * @return true-开通线下/混合式培训，false-未开通线下/混合式培训
     */
    public boolean isOpenO2OTrain(String orgId) {
        return verifyFactor(orgId, MODEL_O2O_TRAIN);
    }

    /**
     * 是否开通胜任力评估
     * <p>
     * 1.0机构默认是开通的，走机构参，不走去版本判别
     * <p>
     * 需要校验的地方
     * 1、档案中心获取胜任力评估数据
     *
     * @param orgId 机构id
     * @return true-开通胜任力评估，false-未开通胜任力评估
     */
    public boolean isOpenCompetenceEval(String orgId) {
        return verifyFactor(orgId, MODEL_COMPETENCE_EVAL);
    }

    /**
     * 判断某一个要素是否开启
     *
     * @param orgId  机构id
     * @param factor 要素code，可以是应用、模块、能力任意要素中的一个
     * @return true-要素正常使用，false-要素不可使用
     */
    private boolean verifyFactor(String orgId, String factor) {
        OrgVerifyFactorBean verifyFactorBean = getVerifyFactorBean(orgId, factor);
        log.info("获取测评要素判断，coreapi返回：{}", BeanHelper.bean2Json(verifyFactorBean));
        int isOpenFactor = verifyFactorBean.getFactorState();
        return isFactorPurchasedEnable(isOpenFactor);
    }

    /**
     * 是否开通盘点
     * @param orgId
     * @return
     */
    public boolean isOpenRv(String orgId) {
         return verifyFactor(orgId, APP_GWNL_RV);
    }

    /**
     * 是否付费集团版
     * @param orgId 机构id
     * @return true-是付费版，false-不是付费版
     */

    public boolean isGroupServer(String orgId) {
            Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap = getOrgVerifyFactorBeanMap(orgId,
                    Lists.newArrayList(APP_GWNL_GROUP));
            return isGroupServer(orgVerifyFactorBeanMap);
    }

    private boolean isGroupServer(Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap) {
        int proState = orgVerifyFactorBeanMap.get(APP_GWNL_GROUP).getFactorState();
        return isFactorPurchasedEnable(proState);
    }
}
