package com.yxt.talent.bk.core.search.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.search.entity.SearchFilterGroup;
import com.yxt.talent.bk.core.search.mapper.SearchFilterGroupMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Deprecated
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class SearchFilterGroupRepository extends ServiceImpl<SearchFilterGroupMapper, SearchFilterGroup> {
    public List<SearchFilterGroup> findGroupNameByOrgId(String orgId) {
        LambdaQueryWrapper<SearchFilterGroup> queryWrapper = new QueryWrapper<SearchFilterGroup>().lambda();
        queryWrapper.eq(SearchFilterGroup::getOrgId, orgId);
        queryWrapper.orderByDesc(SearchFilterGroup::getGroupType);
        queryWrapper.orderByDesc(SearchFilterGroup::getCreateTime);
        return list(queryWrapper);
    }

    public List<SearchFilterGroup> findGroupNamesByOrgId(String orgId) {
        return baseMapper.findGroupNamesByOrgId(orgId);
    }

    public SearchFilterGroup findGroupById(String orgId, String id) {
        LambdaQueryWrapper<SearchFilterGroup> queryWrapper = new LambdaQueryWrapper<SearchFilterGroup>();
        queryWrapper.eq(SearchFilterGroup::getOrgId, orgId);
        queryWrapper.eq(SearchFilterGroup::getId, id);
        return getOne(queryWrapper);
    }

    public void updateGroupName(String orgId, SearchFilterGroup bean) {
        LambdaUpdateWrapper<SearchFilterGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SearchFilterGroup::getOrgId, orgId);
        updateWrapper.eq(SearchFilterGroup::getId,bean.getId());
        updateWrapper.set(SearchFilterGroup::getGroupName, bean.getGroupName());
        updateWrapper.set(SearchFilterGroup::getUpdateUserId, bean.getUpdateUserId());
        updateWrapper.set(SearchFilterGroup::getUpdateTime, bean.getUpdateTime());
        update(updateWrapper);
    }

    /**
     * 校验名称是否重复
     *
     * @param orgId 机构id
     * @param name 名称
     * @param groupId
     * @return
     */
    public long chkGroupName(String orgId, String name, String groupId) {
        LambdaQueryWrapper<SearchFilterGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SearchFilterGroup::getOrgId, orgId);
        queryWrapper.eq(SearchFilterGroup::getGroupName, name);
        if (StringUtils.isNotEmpty(groupId)) {
            queryWrapper.ne(SearchFilterGroup::getId, groupId);
        }
        return count(queryWrapper);
    }
}
