package com.yxt.talent.bk.core.search.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 透视维度
 */
@Data
@NoArgsConstructor
public class UserSearchAnalyseDimensionBean {
    /**
     * 透视项key
     */
    private String itemKey;
    /**
     * 透视项名称
     */
    private String itemName;
    /**
     * 透视项Id
     */
    private String itemValueId;
    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 如部门，搜索条件中选择了部门，则透视按照已选择部门（根结点）进行，这里就是搜索条件中所选择的标签值 （岗位、职级、客户自定义普通标签等亦如此）
     */
    private List<String> values;
}
