package com.yxt.talent.bk.svc.profile.bean;

import com.google.common.collect.Sets;
import com.yxt.spsdk.audit.annotations.AuditLogField;
import com.yxt.spsdk.audit.base.AuditLogDynamicBean;
import lombok.Data;

import java.util.Set;

@Data
public class PortraitConfigLog implements AuditLogDynamicBean {
    private int updateClient;

    @AuditLogField(name = "是否开启可见", orderIndex = 0, fieldKey = "managerRange")
    private String managerRange;
    @AuditLogField(name = "可见范围", orderIndex = 1, fieldKey = "clientRangeComplex")
    private String clientRangeComplex;
    @AuditLogField(name = "白名单员工", orderIndex = 2, fieldKey = "whiteUser")
    private String whiteUser;
    @AuditLogField(name = "展示模块", orderIndex = 3, fieldKey = "showCfg")
    private String showCfg;
    @Override
    public Set<String> logFieldKeys() {
        return updateClient == 1 ? Sets.newHashSet("clientRangeComplex", "whiteUser", "showCfg")
                : Sets.newHashSet("managerRange", "showCfg");
    }
}
