package com.yxt.talent.bk.svc.heir.bean;

import com.yxt.spsdk.audit.base.AuditLogEntitySupport;
import lombok.Data;

/**
 * HeirPos4Log
 *
 * <AUTHOR> geyan
 * @Date 19/3/24 10:00 am
 */
@Data
public class HeirPosDel4Log implements AuditLogEntitySupport {
    private String id;
    private String posName;

    @Override
    public String entityId() {
        return id;
    }

    @Override
    public String entityName() {
        return String.format("继任地图-%s-删除", posName);
    }
}
