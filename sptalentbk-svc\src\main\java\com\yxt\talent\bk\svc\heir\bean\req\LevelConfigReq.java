package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
public class LevelConfigReq {

    @Size(message = BkApiErrorKeys.PARAM_SIZE_INVALID_MESSAGE, min = 1, max = 10)
    private List<HeirPrepareReq> heirPrepareReqList;

    private List<HeirRiskReq> heirRiskReqList;

    @Schema(description = "学员端是否展示相关信息（0：不展示；1：展示）")
    private int showFlag;
}
