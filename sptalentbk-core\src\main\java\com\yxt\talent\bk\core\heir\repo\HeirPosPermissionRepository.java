package com.yxt.talent.bk.core.heir.repo;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.heir.entity.HeirPosPermissionEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosPermissionExt;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPermissionMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * HeirPosPermissionRepository
 *
 * <AUTHOR> harleyge
 * @Date 6/5/24 10:32 am
 */
@Slf4j
@Repository
@AllArgsConstructor
public class HeirPosPermissionRepository extends ServiceImpl<HeirPosPermissionMapper, HeirPosPermissionEntity> {
    private final HeirPosPermissionMapper heirPosPermissionMapper;

    public List<HeirPosPermissionExt> list(String orgId, String posId) {
        return TalentbkUtil.bkUdpTranslate(orgId, true,
                heirPosPermissionMapper.list(orgId, posId));
    }

    public List<HeirPosPermissionEntity> findByPosIds(String orgId, Collection<String> posIds) {
        if (CollectionUtils.isEmpty(posIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HeirPosPermissionEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(HeirPosPermissionEntity::getOrgId, orgId);
        queryWrapper.in(HeirPosPermissionEntity::getPosId, posIds);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<HeirPosPermissionEntity> getQueryWrapper() {
        LambdaQueryWrapper<HeirPosPermissionEntity> wrapper = new LambdaQueryWrapper<>();
        return wrapper.eq(HeirPosPermissionEntity::getDeleted, YesOrNo.NO.getValue());
    }
}
