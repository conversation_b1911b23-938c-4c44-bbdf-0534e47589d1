package com.yxt.talent.bk.svc.tag.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;
import java.util.List;

/***
 * <AUTHOR>
 * @since 2022/8/10 16:10
 */
@Data
@Schema(name = "标签创建/编辑传值对象")
public class Tag4Create {

    @Schema(description = "标签id")
    private String id;

    @Schema(description = "标签名称")
    @Size(max = 20, min = 1, message = "apis.talentbk.tag.tagName.size")
    private String tagName;

    @Schema(description = "标签分类id")
    private String catalogId;

    @Schema(description = "标签类型:0-普通标签,1-分层标签")
    private Integer tagType;

    @Schema(description = "创建方式(0-静态,1-规则,2-模型)")
    private Integer createType;

    @Schema(description = "标签来源(2-固定,0-内置,1-自建)")
    private Integer source;

    @Schema(description = "标签定义")
    private String description;

    @Schema(description = "分层标签值列表")
    private List<TagValue4Create> tagValueList;

    @Schema(description = "选择模式:0-单选,1-多选")
    private Integer valueChooseModel;
}
