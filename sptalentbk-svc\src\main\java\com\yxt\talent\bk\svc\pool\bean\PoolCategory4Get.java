package com.yxt.talent.bk.svc.pool.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Schema(description = "人才池分类详情返回")
public class PoolCategory4Get implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="主键;id主键")
    private String id;
    @Schema(description="机构号;机构id")
    private String orgId;
    @Schema(description="分类名称;名称")
    private String name;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
}
