package com.yxt.talent.bk.svc.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.svc.common.DemoCopyService;
import com.yxt.talentbkfacade.bean.SpDemoInitStatus4Mq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * BkDemoInitCopyConsumer
 *
 * <AUTHOR> harleyge
 * @Date 13/9/24 3:44 pm
 */
@Component
@AllArgsConstructor
@Slf4j
@RocketMQMessageListener(consumerGroup = TalentBkRocketMqConstant.GROUP_PREFIX + TalentBkRocketMqConstant.TOPIC_SPMODEL_ORG_COPY_SUCCESS,         topic = TalentBkRocketMqConstant.TOPIC_SPMODEL_ORG_COPY_SUCCESS, consumeThreadNumber = 2, consumeTimeout = 30)
public class SpmDemoInitStatusConsumer implements RocketMQListener<String> {
    private final DemoCopyService demoCopyService;
    @Override
    public void onMessage(String message) {
        try {
            String orgId = JSON.parseObject(message).getString("orgId");
            demoCopyService.demoCopyBizDone(orgId,
                    SpDemoInitStatus4Mq.bizCode("spmodel", SpDemoInitStatus4Mq.STAGE_COPY),
                    YesOrNo.YES.getValue());
        } catch (Exception e) {
            log.error("SpmDemoInitStatusConsumer fail msg {}", JSON.toJSONString(message), e);
        }
    }
}
