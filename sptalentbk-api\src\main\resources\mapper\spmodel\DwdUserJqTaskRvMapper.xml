<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdUserJqTaskRvMapper">
<!--    <select id="listByUserId" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdUserJqTaskRv">-->
<!--        select jq_task_name, jq_task_result, t1.update_time as update_time  from dwd_user_jq_task_rv t1-->
<!--            join dwd_position t2 on t1.third_position_id = t2.third_position_id-->
<!--        where t1.org_id = '${orgId}'-->
<!--          and t1.third_user_id = '${userId}'-->
<!--          and t2.position_id = '${positionId}'-->
<!--          and t1.deleted = 0-->
<!--        order by t1.update_time desc-->
<!--    </select>-->

    <select id="listByUserId" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdUserJqTaskRv">
        select indicator_name AS jq_task_name,
               qualified AS jq_task_result,
               update_time as update_time
        from dwd_user_indicator_result
        where org_id = '${orgId}'
        and user_id = '${userId}'
        and indicator_category = 4
        and deleted = 0
        order by update_time desc
    </select>

    <select id="getUsedRtModelListByUserId"
            resultType="com.yxt.talent.bk.core.dashboard.bean.PersonalEvalModelSimpleVO">
        select distinct model_id as modelId
        from dwd_user_indicator_result
        where org_id = '${orgId}'
          and user_id = '${userId}'
          and indicator_category = 4
          and deleted = 0
    </select>

</mapper>
