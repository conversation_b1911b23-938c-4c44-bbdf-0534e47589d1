package com.yxt.talent.bk.core.tag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.tag.bean.TagValueBean;
import com.yxt.talent.bk.core.tag.bean.TagValueBean4UserSearch;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface TagValueMapper extends BaseMapper<TagValueEntity> {
    /**
     * 根据orgId 和 tagKey，返回维度值
     */
    List<TagValueEntity> findByOrgIdAndTagKey(@Param("orgId") String orgId, @Param("tagKey")String tagKey);

    /**
     * 根据orgId 和 tagKey，返回维度值
     */
    List<TagValueBean4UserSearch> findByOrgIdAndTagKeys(@Param("orgId") String orgId, @Param("tagKeys")Collection<String> tagKeys);

    /**
     * 获取标签值信息
     * @param orgId 机构id
     * @param tagValueIds 标签值id列表
     * @return 标签值信息
     */
    List<TagValueBean> listTagValue(@Param("orgId") String orgId, @Param("tagValueIds") Collection<String> tagValueIds);

    /**
     * 获取标签值信息
     * @param orgId 机构id
     * @param tagId 标签id
     * @return 标签值信息
     */
    List<TagValueBean> listTagValueByTagId(@Param("orgId") String orgId, @Param("tagId") String tagId);

}
