package com.yxt.talent.bk.svc.profile;

import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.profile.entity.PortraitWhite;
import com.yxt.talent.bk.core.profile.mapper.PortraitWhiteMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class UserProfileWhiteService {
    private final PortraitWhiteMapper portraitWhiteMapper;

    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void upDateWhiteUsers(List<String> updateUsers, String orgId, String operatorId) {
        try {
            if (CollectionUtils.isNotEmpty(updateUsers)) {
                List<PortraitWhite> dbAllWhiteUsers = portraitWhiteMapper.queryAllByOrgId(orgId);
                List<String> dbAllIds = dbAllWhiteUsers.stream().map(PortraitWhite::getUserId).toList();
                List<String> dbDisAbleIds = dbAllWhiteUsers.stream()
                        .filter(dbUser -> dbUser.getWhiteEnable() == YesOrNo.NO.getValue())
                        .map(PortraitWhite::getUserId).toList();
                //需要新增的白名单
                List<String> insertList = updateUsers.stream().filter(userId -> !dbAllIds.contains(userId)).toList();
                //需要更新为启用的白名单
                List<String> enableList = updateUsers.stream().filter(dbDisAbleIds::contains).toList();
                //需要更新为禁用的白名单
                List<String> disEnableList = dbAllIds.stream().filter(userId -> !updateUsers.contains(userId)).toList();
                if(CollectionUtils.isNotEmpty(insertList)){
                    List<PortraitWhite> portraitWhites = new ArrayList<>();
                    insertList.forEach(userId -> {
                        PortraitWhite portraitWhite = new PortraitWhite();
                        portraitWhite.setId(ApiUtil.getUuid());
                        portraitWhite.setOrgId(orgId);
                        portraitWhite.setUserId(userId);
                        portraitWhite.setWhiteEnable(YesOrNo.YES.getValue());
                        EntityUtil.setCreateInfo(operatorId, portraitWhite);
                        portraitWhites.add(portraitWhite);
                    });
                    portraitWhiteMapper.insertBatch(portraitWhites);
                }

                if(CollectionUtils.isNotEmpty(enableList)){
                    portraitWhiteMapper.updateEnableByUserIds(orgId, enableList, YesOrNo.YES.getValue());
                }

                if(CollectionUtils.isNotEmpty(disEnableList)){
                    portraitWhiteMapper.updateEnableByUserIds(orgId, disEnableList, YesOrNo.NO.getValue());
                }

            }else {
                portraitWhiteMapper.updateAllDisEnable(orgId);
            }

        } catch (Exception e) {
            log.info("upDateWhiteUsers orgId:{} error", orgId, e);
        }
    }

}
