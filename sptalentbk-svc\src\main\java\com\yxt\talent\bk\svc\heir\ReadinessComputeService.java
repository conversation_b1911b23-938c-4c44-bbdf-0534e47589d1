package com.yxt.talent.bk.svc.heir;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.pojo.IdName;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.msgfacade.bean.MsgBean;
import com.yxt.usdk.components.rocketmq.common.MessageDelayLevel;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserIdDTO;
import com.yxt.talent.bk.core.heir.bean.RemindTodoParam;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosUserEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPrepareRemindUserEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPrepareRemindUserMapper;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.heir.bean.CalcCycleBean;
import com.yxt.talent.bk.svc.heir.bean.HeirPos4Notify;
import com.yxt.talent.bk.svc.heir.bean.UserPrepareChangeMsg;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareResp;
import com.yxt.talent.bk.svc.heir.component.HeirPosTypeSelector;
import com.yxt.talent.bk.svc.heir.constants.HeirNoticeConstants;
import com.yxt.talent.bk.svc.heir.enums.CycleEnum;
import com.yxt.talent.bk.svc.heir.util.MessageUtil;
import com.yxt.talent.bk.svc.mq.constant.HeirRocketMqConstants;
import com.yxt.talent.bk.svc.udp.rpc.UdpRpc;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReadinessComputeService {

    /**
     * 触发浮动最大秒数
     */
    public static final int FLOAT_SEC = 30;

    private final HeirPosUserMapper heirPosUserMapper;

    private final RocketMQTemplate rocketMQTemplate;

    private final HeirPrepareRemindUserMapper heirPrepareRemindUserMapper;

    private final UdpRpc udpRpc;
    private final UdpLiteUserRepository udpLiteUserRepository;

    private final HeirOrgLevelConfigService heirOrgLevelConfigService;

    private final MessageUtil messageUtil;

    private final HeirPosMapper heirPosMapper;
    private final HeirPosTypeSelector heirPosTypeSelector;

    public void readinessCompute(){
        HeirPosPrepareCfgService posPrepareCfgService = SpringContextHolder.getBean(HeirPosPrepareCfgService.class);
        //查询出当天需要计算的数据
        List<HeirPosEntity> heirPosEntities = heirPosMapper.selectNeedReadinessCompute();
        if (CollectionUtils.isNotEmpty(heirPosEntities)) {
            for (HeirPosEntity heirPosEntity : heirPosEntities) {
                Date nextCalcTime = heirPosEntity.getNextCalcTime();
                if (nextCalcTime !=null && (nextCalcTime.getTime()- System.currentTimeMillis())/1000<FLOAT_SEC) {
                    posPrepareCfgService.calcPosUserPrepare(heirPosEntity.getOrgId(), heirPosEntity.getId(), heirPosEntity.getPosType());
                    setComputeNextExecutionTime(heirPosEntity);
                }else{
                    rocketMQTemplate.syncDelaySend(HeirRocketMqConstants.TOPIC_HEIR_READINESS_COMPUTE, JSON.toJSONString(heirPosEntity),
                        calcDelayLevel(nextCalcTime).getDelayLevel());
                }
            }

        }
    }

    public void readinessRemind() {
        //查询出当天需要通知的数据
        List<HeirPosEntity> heirPosEntities = heirPosMapper.selectNeedReadinessRemind();
        if (CollectionUtils.isNotEmpty(heirPosEntities)) {
            for (HeirPosEntity posEntity : heirPosEntities) {
                Date nextRemindTime = posEntity.getNextRemindTime();
                if (nextRemindTime !=null && (nextRemindTime.getTime()- System.currentTimeMillis())/1000<FLOAT_SEC) {
                    sendPrepareChangeUser(posEntity);
                    setRemindNextExecutionTime(posEntity);
                }else{
                    rocketMQTemplate.syncDelaySend(HeirRocketMqConstants.TOPIC_HEIR_READINESS_REMIND, JSON.toJSONString(posEntity),
                        calcDelayLevel(nextRemindTime).getDelayLevel());
                }
            }
        }
    }

    public void setComputeNextExecutionTime(HeirPosEntity heirPosEntity) {
        HeirPosEntity heirPos = new HeirPosEntity();
        heirPos.setId(heirPosEntity.getId());
        String calcCycle = heirPosEntity.getCalcCycle();
        CalcCycleBean calcCycleBean = JSON.parseObject(calcCycle, CalcCycleBean.class);
        if (CycleEnum.EVERYDAY.getCode().equals(calcCycleBean.getCycle())) {
            heirPos.setNextCalcTime(DateUtil.offsetDay(heirPosEntity.getNextCalcTime(),1));
        } else if (CycleEnum.EVERYWEEK.getCode().equals(calcCycleBean.getCycle())) {
            heirPos.setNextCalcTime(DateUtil.offsetWeek(heirPosEntity.getNextCalcTime(),1));
        } else if (CycleEnum.EVERYMONTH.getCode().equals(calcCycleBean.getCycle())) {
            heirPos.setNextCalcTime(DateUtil.offsetMonth(heirPosEntity.getNextCalcTime(),1));
        }
        heirPos.init(null);
        heirPosMapper.updateById(heirPos);
    }

    public void setRemindNextExecutionTime(HeirPosEntity heirPosEntity) {
        HeirPosEntity posEntity = new HeirPosEntity();
        posEntity.setId(heirPosEntity.getId());
        String remindCycle = heirPosEntity.getRemindCycle();
        CalcCycleBean remindCycleBean = JSON.parseObject(remindCycle, CalcCycleBean.class);
        if (CycleEnum.EVERYDAY.getCode().equals(remindCycleBean.getCycle())) {
            posEntity.setNextRemindTime(DateUtil.offsetDay(heirPosEntity.getNextRemindTime(),1));
        }else if (CycleEnum.EVERYWEEK.getCode().equals(remindCycleBean.getCycle())) {
            posEntity.setNextRemindTime(DateUtil.offsetWeek(heirPosEntity.getNextRemindTime(),1));
        }else if (CycleEnum.EVERYMONTH.getCode().equals(remindCycleBean.getCycle())) {
            posEntity.setNextRemindTime(DateUtil.offsetMonth(heirPosEntity.getNextRemindTime(),1));
        }
        posEntity.init(null);
        heirPosMapper.updateById(posEntity);
    }

    public void sendPrepareChangeUser(HeirPosEntity heirPosEntity) {
        //继任准备度通知人员
        List<HeirPrepareRemindUserEntity> remindUserEntityList = heirPrepareRemindUserMapper.selectUserIdsByPrepareCfgId(
            (heirPosEntity.getId()));
        if (CollectionUtils.isNotEmpty(remindUserEntityList)) {
            String posName = heirPosTypeSelector.getPosNameById(heirPosEntity.getOrgId(), heirPosEntity.getId(), heirPosEntity.getPosType());
            log.info("sendPrepareChangeUser remindUserEntityList:{}",JSON.toJSONString(remindUserEntityList));
            List<String> remindUserIds = remindUserEntityList.stream().map(HeirPrepareRemindUserEntity::getUserId)
                .collect(Collectors.toList());
            String orgId = heirPosEntity.getOrgId();
            List<HeirPosUserEntity> heirPosUserEntities = heirPosUserMapper.selectShouldRemindUser(orgId,heirPosEntity.getId());
            if (CollectionUtils.isNotEmpty(heirPosUserEntities)) {
                log.info("sendPrepareChangeUser heirPosUserEntities:{}",JSON.toJSONString(heirPosUserEntities));
                //查准备度
                List<HeirPrepareResp> heirPrepareData = heirOrgLevelConfigService.getHeirPrepareData(orgId);
                Map<Long, String> prepareNameMap = StreamUtil.list2map(heirPrepareData,
                    HeirPrepareResp::getId, HeirPrepareResp::getLevelName1);
                log.info("sendPrepareChangeUser heirPrepareData:{}",JSON.toJSONString(heirPrepareData));
                //查询部门或岗位名称
                List<IdName> idNames = udpRpc.queryPositionNameByIds(orgId,
                    heirPosUserEntities.stream().map(HeirPosUserEntity::getPosId)
                        .collect(Collectors.toList()));
                log.info("sendPrepareChangeUser posId:{},idNames:{}",heirPosEntity.getId(),JSON.toJSONString(idNames));
                List<UdpUserBriefBean> userBriefBeans = BeanCopierUtil.convertList(
                    heirPosUserEntities,
                    posUser -> {
                        UdpUserBriefBean userBrief = new UdpUserBriefBean();
                        userBrief.setId(posUser.getUserId());
                        return userBrief;
                    });
                //查人员姓名
                udpLiteUserRepository.fillUserInfo(orgId, userBriefBeans, UdpUserBriefBean::getId, (userBrief, udpUser) -> {
                    userBrief.setFullname(udpUser.getFullname());
                }, Lists.newArrayList("fullname"));
                log.info("sendPrepareChangeUser posId:{},userBriefBeans:{}",heirPosEntity.getId(),JSON.toJSONString(userBriefBeans));
                List<HeirPosUserIdDTO> targetUserIds = new ArrayList<>(heirPosUserEntities.size());
                for (HeirPosUserEntity heirPosUserEntity : heirPosUserEntities) {
                    UdpUserBriefBean udpUserBriefBean = userBriefBeans.stream().filter(
                        userBriefBean -> StringUtils.equals(userBriefBean.getId(),
                            heirPosUserEntity.getUserId())).findFirst().orElse(new UdpUserBriefBean());
                    HeirPrepareResp heirPrepareResp = heirPrepareData.stream().filter(
                            heirPrepare -> heirPrepare.getId().equals(heirPosUserEntity.getCalcLevelId()))
                        .findFirst().orElse(new HeirPrepareResp());
                    UserPrepareChangeMsg changeMsg = new UserPrepareChangeMsg();
                    changeMsg.setPosId(heirPosEntity.getId());
                    changeMsg.setPosType(heirPosEntity.getPosType());
                    changeMsg.setPosName(posName);
                    String jumpUrl = changeMsg.putMsgBean(heirPosUserEntity,remindUserIds,udpUserBriefBean,posName,heirPrepareResp);
                    messageUtil.sendTemMsg(changeMsg);

                    //发送待办的参数
                    HeirPosUserIdDTO userIdDTO = new HeirPosUserIdDTO();
                    userIdDTO.setId(heirPosUserEntity.getId());
                    userIdDTO.setUserId(heirPosUserEntity.getUserId());
                    userIdDTO.setJumpUrl(jumpUrl);
                    userIdDTO.setTodoParam(new RemindTodoParam());
                    userIdDTO.getTodoParam().setPrepareId(heirPosUserEntity.getCalcLevelId());
                    userIdDTO.getTodoParam().setPrepareName(prepareNameMap.get(heirPosUserEntity.getCalcLevelId()));
                    targetUserIds.add(userIdDTO);
                }

                HeirPos4Notify pos4Notify = new HeirPos4Notify();
                pos4Notify.setPosId(heirPosEntity.getId());
                pos4Notify.setPosType(heirPosEntity.getPosType());
                pos4Notify.setPosName(posName);
                HeirPosUserService posUserService = SpringContextHolder.getBean(HeirPosUserService.class);
                posUserService.asyncBatchCreateTodo(orgId, pos4Notify, targetUserIds);
            }
        }
    }

    public MessageDelayLevel calcDelayLevel(Date triggerTime) {
        long remindSec = (triggerTime.getTime() - System.currentTimeMillis())/1000;
        if (remindSec >= 120*60) {
            return MessageDelayLevel.TWO_HOURS;
        }
        if (remindSec >= 60*60) {
            return MessageDelayLevel.ONE_HOUR;
        }
        if (remindSec >= 30*60) {
            return MessageDelayLevel.THIRTY_MINUTES;
        }
        if (remindSec >= 20*60) {
            return MessageDelayLevel.TWENTY_MINUTES;
        }
        if (remindSec >= 10*60) {
            return MessageDelayLevel.TEN_MINUTES;
        }
        if (remindSec >= 9*60) {
            return MessageDelayLevel.NINE_MINUTES;
        }
        if (remindSec >= 8*60) {
            return MessageDelayLevel.EIGHT_MINUTES;
        }
        if (remindSec >= 7*60) {
            return MessageDelayLevel.SEVEN_MINUTES;
        }
        if (remindSec >= 6*60) {
            return MessageDelayLevel.SIX_MINUTES;
        }
        if (remindSec >= 5*60) {
            return MessageDelayLevel.FIVE_MINUTES;
        }
        if (remindSec >= 4*60) {
            return MessageDelayLevel.FOUR_MINUTES;
        }
        if (remindSec >= 3*60) {
            return MessageDelayLevel.THREE_MINUTES;
        }
        if (remindSec >= 2*60) {
            return MessageDelayLevel.TWO_MINUTES;
        }
        if (remindSec >= 60) {
            return MessageDelayLevel.ONE_MINUTE;
        }
        if (remindSec >= 30) {
            return MessageDelayLevel.THIRTY_SECONDS;
        }
        if (remindSec >= 10) {
            return MessageDelayLevel.TEN_SECONDS;
        }
        if (remindSec >= 5) {
            return MessageDelayLevel.FIVE_SECONDS;
        }
        return MessageDelayLevel.ONE_SECOND;
    }

}
