package com.yxt.talent.bk.svc.profile.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@Schema(name = "人才画像-人员标签")
public class UserTag4Get {

    @Schema(description = "标签分类code")
    private String labelCategorySystemCode;

    @Schema(description = "标签值列表")
    private List<TagValue4Get> tagValues;
}
