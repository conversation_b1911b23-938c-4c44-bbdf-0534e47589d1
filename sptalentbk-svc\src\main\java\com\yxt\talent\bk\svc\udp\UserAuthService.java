package com.yxt.talent.bk.svc.udp;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.common.Constants;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.util.BeanHelper;
import com.yxt.coreapi.client.bean.dataauth.DataAuthPermissionBean;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.svc.base.CoreRpcService;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean4Dept;
import com.yxt.talent.bk.svc.udp.rpc.UdpEsUserSearchRpc;
import com.yxt.talent.bk.svc.udp.rpc.UdpRpc;
import com.yxt.udpfacade.bean.dept.ManagedDeptBean;
import com.yxt.udpfacade.bean.user.es.DeptSearchBean;
import com.yxt.udpfacade.bean.user.es.search.EsUserInfoVo;
import com.yxt.udpfacade.bean.user.es.search.EsUserSearchParam;
import com.yxt.udpfacade.enums.SourceFromEnum;
import com.yxt.udpfacade.service.UdpFacade;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class UserAuthService {

    private final CoreRpcService coreRpcService;
    private final UdpEsUserSearchRpc v2UdpEsUserSearchRpc;
    private final RedisRepository talentRedisRepository;
    private final UdpFacade udpFacade;
    private final UdpRpc udpRpc;


    /**
     * 缓存时间 2小时
     */
    private static final long CACHE_TIME = 5;

    public UserAuthBean verifyPermission(UserBasicBean userBasic, String navCode, String dataPermsCode,
            List<String> verifyUserIds) {
        return coreRpcService.verifyPermission(userBasic, navCode, dataPermsCode, verifyUserIds);
    }

    /**
     * 获取我管辖的部门ids
     *
     * @param orgId              机构id
     * @param userId             用户id
     * @param navCode            导航code
     * @param dataPermissionCode 权限code
     */
    public UserAuthBean4Dept getAuthDeptIds(String orgId, String userId, String navCode, String dataPermissionCode) {
        UserAuthBean4Dept deptAuthBean = new UserAuthBean4Dept();
        // 加缓冲
        //        String cacheKey = String.format(TalentBkRedisKeys.CACHE_KEY_UDP_DEPT_AUTH_NAVCODE, orgId, userId, navCode,
        //                dataPermissionCode);
        //        String valueByKey = talentRedisRepository.getValueByKey(cacheKey);
        //        if (StringUtils.isNotEmpty(valueByKey)) {
        //            deptAuthBean = BeanHelper.json2Bean(valueByKey, UserAuthBean4Dept.class);
        //            return deptAuthBean;
        //        }

        List<String> deptIds = new ArrayList<>();
        log.info("getAuthDeptIds orgId={},userId={},navCode={},dataPermissionCode={}", orgId, userId, navCode,
                dataPermissionCode);
        // 2.0 权限范围内的
        DataAuthPermissionBean dataAuthPermissionBean = coreRpcService.getUserDataPermission(orgId, userId, navCode,
                dataPermissionCode);
        log.info("getAuthDeptIds dataAuthPermissionBean={}", JSON.toJSONString(dataAuthPermissionBean));
        if (1 == dataAuthPermissionBean.getIsAllOrgPermission()) {
            deptAuthBean.setIsAll(1);
        } else {
            deptIds = dataAuthPermissionBean.getDeptIds();
        }
        deptAuthBean.setDeptIds(deptIds);
        // 设置缓存并设置时间
        //        talentRedisRepository.setValue(cacheKey, BeanHelper.bean2Json(deptAuthBean), CACHE_TIME, TimeUnit.HOURS);
        return deptAuthBean;
    }

    /**
     * 获取我管辖的用户ids
     *
     * @param orgId              机构id
     * @param userId             用户id
     * @param navCode            导航code
     * @param dataPermissionCode 权限code
     */
    public UserAuthBean getAuthUserIds(String orgId, String userId, String navCode, String dataPermissionCode) {
        UserAuthBean userAuthBean = new UserAuthBean();
        // 加缓冲
        String cacheKey = String.format(TalentBkRedisKeys.CACHE_KEY_UDP_USER_AUTH_NAVCODE, orgId, userId, navCode,
                dataPermissionCode);
        String valueByKey = talentRedisRepository.getValueByKey(cacheKey);
        if (StringUtils.isNotEmpty(valueByKey)) {
            userAuthBean = BeanHelper.json2Bean(valueByKey, UserAuthBean.class);
            return userAuthBean;
        }

        List<String> userIds = new ArrayList<>();
        // 2.0 权限范围内的
        DataAuthPermissionBean dataAuthPermissionBean = coreRpcService.getUserDataPermission(orgId, userId, navCode,
                dataPermissionCode);
        log.debug("LOG11500:orgId={}, userId={}, navCode={}, dataPermissionCode={}, dataAuthPermissionBean={}", orgId,
                userId, navCode, dataPermissionCode, BeanHelper.bean2Json(dataAuthPermissionBean));
        if (1 == dataAuthPermissionBean.getIsAllOrgPermission()) {
            userAuthBean.setAdmin(1);
        } else {
            List<String> deptIds = dataAuthPermissionBean.getDeptIds();
            //查询部门下的人员信息
            if (CollectionUtils.isNotEmpty(deptIds)) {

                EsUserSearchParam searchParam = new EsUserSearchParam();
                searchParam.setOrgId(orgId);
                searchParam.setSourceFrom(SourceFromEnum.TALENT.getValue());
                searchParam.setIsOpenLimit(true);

                // 如果部门过多时候，不一定会全部返回
                List<EsUserInfoVo> esUserInfoVos = this.getEsUserListByDeptIds(searchParam, deptIds);
                if (CollectionUtils.isNotEmpty(esUserInfoVos)) {
                    userIds.addAll(esUserInfoVos.stream().map(EsUserInfoVo::getId).collect(Collectors.toList()));
                }
            }

            if (CollectionUtils.isNotEmpty(dataAuthPermissionBean.getUserIds())) {
                userIds.addAll(dataAuthPermissionBean.getUserIds());
            }

        }

        userAuthBean.setUserIds(userIds);
        // 设置缓存并设置时间
        talentRedisRepository.setValue(cacheKey, BeanHelper.bean2Json(userAuthBean), CACHE_TIME, TimeUnit.MINUTES);
        return userAuthBean;
    }

    public List<String> mergeAuthDeptUser(String orgId, List<String> userIds, List<String> deptIds) {
        if (CollectionUtils.isNotEmpty(deptIds)) {
            EsUserSearchParam searchParam = new EsUserSearchParam();
            searchParam.setOrgId(orgId);
            searchParam.setSourceFrom(SourceFromEnum.TALENT.getValue());
            searchParam.setIsOpenLimit(true);
            // 如果部门过多时候，不一定会全部返回
            List<EsUserInfoVo> esUserInfoVos = this.getEsUserListByDeptIds(searchParam, deptIds);
            if (CollectionUtils.isNotEmpty(esUserInfoVos)) {
                Set<String> newUserIds = new HashSet<>(esUserInfoVos.size() + CollectionUtils.size(userIds));
                esUserInfoVos.stream().map(EsUserInfoVo::getId).forEach(newUserIds::add);
                if (CollectionUtils.isNotEmpty(userIds)) {
                    newUserIds.addAll(userIds);
                }
                return newUserIds.stream().collect(Collectors.toList());
            }
        }
        return userIds;
    }

    /**
     * 用户端 <br> 我直属 & 我管辖的  3a098a6e-45b3-4f1e-8aba-8725a951a396
     *
     * @param orgId     机构id
     * @param userId    用户id
     * @param isManager rue 我直属、false 我管辖
     * @return
     */
    public UserAuthBean getClientAuthUserIds(String orgId, String userId, boolean isManager) {
        log.info(" 用户端我直属&我管辖的 --> " + userId + " - " + isManager);
        UserAuthBean userAuthBean = new UserAuthBean();

        // 加缓冲
        String cacheKey = String.format(TalentBkRedisKeys.CACHE_KEY_UDP_USER_AUTH_CLIENT, userId, isManager);
        String valueByKey = talentRedisRepository.getValueByKey(cacheKey);
        if (StringUtils.isNotEmpty(valueByKey)) {
            userAuthBean = BeanHelper.json2Bean(valueByKey, UserAuthBean.class);
            return userAuthBean;
        }

        List<String> userIds = doGetClientAuthUserIdsForV2(isManager, orgId, userId);
        userAuthBean.setUserIds(userIds);
        log.info(" 用户端我直属&我管辖的2.0 --> " + userId + " 管辖用户数：" + userIds.size());

        // 设置缓存并设置时间
        talentRedisRepository.setValue(cacheKey, BeanHelper.bean2Json(userAuthBean), CACHE_TIME, TimeUnit.HOURS);

        return userAuthBean;
    }


    private List<String> doGetClientAuthUserIdsForV2(boolean isManager, String orgId, String userId) {
        if (isManager) {
            // 直属员工
            return udpFacade.managerSubMember(orgId, userId).getDatas();
        } else {
            // 我管辖的
            //获取负责的部门list
            List<ManagedDeptBean> deptBeans = udpRpc.findManagedDeptUsers(orgId, userId);
            log.info(" 用户端我直属&我管辖的2.0 --> " + userId + " 管辖部门数：" + deptBeans.size());
            if (CollectionUtils.isNotEmpty(deptBeans)) {
                List<String> deptIds = deptBeans.stream().map(ManagedDeptBean::getDeptId).collect(Collectors.toList());
                EsUserSearchParam searchCriteria = new EsUserSearchParam();
                searchCriteria.setSourceFrom(SourceFromEnum.TALENT.getValue());
                searchCriteria.setIsOpenLimit(true);
                searchCriteria.setOrgId(orgId);

                List<EsUserInfoVo> esUserInfoVos = this.getEsUserListByDeptIds(searchCriteria, deptIds);
                if (CollectionUtils.isNotEmpty(esUserInfoVos)) {
                    return esUserInfoVos.stream().map(EsUserInfoVo::getId).collect(Collectors.toList());
                }
            }

        }
        return new ArrayList<>();
    }

    private List<EsUserInfoVo> getEsUserListByDeptIds(EsUserSearchParam searchParam, List<String> deptIds) {
        //deptSearchList 入参udp限制最多1024
        //循环调用udp接口，每次限制入参500个部门id
        List<EsUserInfoVo> result = Lists.newArrayList();
        List<List<String>> splitList = Lists.partition(deptIds, 500);
        for (List<String> childList : splitList) {
            List<DeptSearchBean> deptSearchList = new ArrayList<>();
            childList.forEach(deptId -> {
                DeptSearchBean bean = new DeptSearchBean();
                bean.setOrgId(searchParam.getOrgId());
                bean.setIncludeSubDept(0); // 设置不包含子部门，因为权限那边设置为包含子部门会返回所有子部门的id,所以设置为0。
                bean.setDeptId(deptId);
                deptSearchList.add(bean);
            });
            searchParam.setDeptSearchList(deptSearchList);
            searchParam.setProductCode(Constants.XXV2_PRODUCT_CODE);
            List<EsUserInfoVo> esUserInfoVos = v2UdpEsUserSearchRpc.search4List(searchParam);
            if (CollectionUtils.isNotEmpty(esUserInfoVos)) {
                result.addAll(esUserInfoVos);
            }
        }
        return result;
    }

}
