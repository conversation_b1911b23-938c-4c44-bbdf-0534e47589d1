<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>sptalentbk-parent</artifactId>
        <groupId>com.yxt</groupId>
        <version>6.5.1-jdk17</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>sptalentbk-svc</artifactId>
    <version>6.5.1-jdk17</version>

    <dependencies>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentbk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>udpapi-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>bifrost-sdk-udp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentapi-facade</artifactId>
            <version>4.6.3-jdk17</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yxt</groupId>
                    <artifactId>waf-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>spevalapi-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yxt</groupId>
                    <artifactId>waf-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>spmodelapi-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yxt</groupId>
                    <artifactId>waf-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentsdapi-facade</artifactId>
            <version>5.9-jdk17</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yxt</groupId>
                    <artifactId>waf-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>msg-facade</artifactId>
            <version>2.11.7-jdk17</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yxt</groupId>
                    <artifactId>waf-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.21</version>
        </dependency>
    </dependencies>


</project>
