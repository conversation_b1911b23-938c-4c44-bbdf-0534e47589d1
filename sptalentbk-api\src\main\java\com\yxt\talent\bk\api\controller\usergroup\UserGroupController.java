package com.yxt.talent.bk.api.controller.usergroup;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StringUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.spsdk.audit.annotations.EasyAuditLogSelect;
import com.yxt.spsdk.udpbase.bean.UdpUserBriefBean;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkAuthCodes;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.usergroup.bean.GroupMemberVO;
import com.yxt.talent.bk.core.usergroup.bean.SchemeBean;
import com.yxt.talent.bk.core.usergroup.bean.UserGroup4Get;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupBean;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupDetail4Get;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupEnableBean;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupModulePageVO;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupPreviewBean;
import com.yxt.talent.bk.core.usergroup.bean.UserRuleDetailTag4Get;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import com.yxt.talent.bk.svc.usergroup.UserGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Tag(name = "用户群组")
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mgr/usergroup")
public class UserGroupController extends BaseController {

    private final UserGroupService userGroupService;

    @Operation(summary = "查询群组列表")
    @Parameters({@Parameter(name = "limit", description = "每页记录数. 默认值为20.", in = ParameterIn.QUERY),
            @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "
                    + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY)})
    @GetMapping(value = "/list")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<UserGroup4Get> list(@RequestParam String keyword) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return userGroupService.find4Page(ApiUtil.getPageRequest(getRequest()), userDetail.getOrgId(), keyword,
                userDetail);
    }

    @Operation(summary = "新增群组")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.USER_GROUP_CREATE, paramExp = "#userGroupBean")
    @PostMapping(value = "", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN, codes = {
            TalentBkAuthCodes.BK_GROUP_OPERATION_ADD})
    public void add(@RequestBody @Validated UserGroupBean userGroupBean) {
        userGroupService.validateRule(userGroupBean);
        UserCacheBasic userCache = getUserCacheBasic();
        UserGroup userGroup = userGroupService.createUserGroup(userCache.getOrgId(), userCache.getUserId(),
                userGroupBean);
        userGroupBean.setId(userGroup.getId());
    }

    @Operation(summary = "群组预览")
    @PostMapping(value = "preview", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<SchemeBean> preview(HttpServletRequest request,
            @RequestBody @Validated UserGroupPreviewBean userGroupPreviewBean) {
        int offset = StringUtil.str2Int(request.getParameter(Constants.PARAM_NAME_OFFSET), 0);
        int limit = StringUtil.str2Int(request.getParameter(Constants.PARAM_NAME_LIMIT), Constants.DEFAULT_LIMIT);
        UserCacheBasic userCache = getUserCacheBasic();
        return userGroupService.previewUserGroup(userCache.getOrgId(), offset, limit, userGroupPreviewBean);
    }

    @Operation(summary = "删除群组")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.USER_GROUP_DELETE, paramExp = "#id")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN, codes = {
            TalentBkAuthCodes.BK_GROUP_OPERATION_DEL})
    public void delete(@PathVariable Long id) {
        UserCacheBasic userCache = getUserCacheBasic();
        userGroupService.deleteUserGroup(userCache.getOrgId(), userCache.getUserId(), id);
    }

    @Operation(summary = "禁用，启用")
    @Auditing
    @EasyAuditLogSelect({
            @EasyAuditLog(value = AuditLogConstants.USER_GROUP_ENABLE, paramExp = "#id", conditionExp = "#userGroupEnableBean.enabled == 1"),
            @EasyAuditLog(value = AuditLogConstants.USER_GROUP_DISABLE, paramExp = "#id", conditionExp = "#userGroupEnableBean.enabled != 1")})
    @PutMapping(value = "/enable/{id}", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void enableGroup(@PathVariable Long id, @RequestBody @Validated UserGroupEnableBean userGroupEnableBean) {
        // 禁用的时候，被其他业务引用，需要判断
        UserCacheBasic userCache = getUserCacheBasic();
        userGroupService.setUserGroupEnable(userCache.getOrgId(), userCache.getUserId(), id,
                userGroupEnableBean.getEnabled());
    }

    @Operation(summary = "重新计算")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.USER_GROUP_CALC, paramExp = "#id")
    @PutMapping(value = "/caclulate/{id}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN, codes = {
            TalentBkAuthCodes.BK_GROUP_OPERATION_RECALCULATE})
    public void caclulate(@PathVariable Long id) {
        UserCacheBasic userCache = getUserCacheBasic();
        userGroupService.caculateMembers(userCache.getOrgId(), userCache.getUserId(), id);
    }

    @Operation(summary = "查看详情")
    @GetMapping(value = "/detail/{id}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public UserGroupDetail4Get getDetail(@PathVariable Long id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return userGroupService.getDetail(userCache.getOrgId(), id);
    }

    @Operation(summary = "群组人员导出")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.USER_GROUP_EXPORT, paramExp = "#id")
    @PostMapping(value = "/{id}/users/export")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN, codes = {
            TalentBkAuthCodes.BK_GROUP_OPERATION_DOWNLOADS})
    public Map<String, String> exportGroupUserInfo(@PathVariable Long id) {
        UserCacheDetail userCache = getUserCacheDetail();
        UserGroup userGroup = userGroupService.checkAndGetById(userCache.getOrgId(), id);
        return userGroupService.exportGroupUserInfo(userGroup, userCache);
    }

    @Operation(summary = "群组人员列表")
    @GetMapping(value = "/{id}/members")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<GroupMemberVO> groupUserList(@PathVariable Long id) {
        UserCacheBasic userCache = getUserCacheBasic();
        UserGroup userGroup = userGroupService.checkAndGetById(userCache.getOrgId(), id);
        PageRequest pageRequest = ApiUtil.getPageRequest(getRequest());
        return userGroupService.groupMemberList(userGroup, userCache, pageRequest);
    }

    @Operation(summary = "群组人员动态列")
    @GetMapping(value = "/{id}/dynamictags")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public List<UserRuleDetailTag4Get> dynamictags(@PathVariable Long id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return userGroupService.getDynamicTags(userCache.getOrgId(), id);
    }


    @Operation(summary = "提供前端组件选择群组-权限范围")
    @GetMapping(value = "/module/page")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<UserGroupModulePageVO> pageUserGroupForModule(
            @RequestParam(required = false, defaultValue = "") String groupName,
            @RequestParam(required = false, defaultValue = "") String navCode,
            @RequestParam(required = false, defaultValue = "") String dataPermissionCode) {
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        return userGroupService.pageUserGroupForModule(userBasic, groupName,navCode,dataPermissionCode);
    }

}
