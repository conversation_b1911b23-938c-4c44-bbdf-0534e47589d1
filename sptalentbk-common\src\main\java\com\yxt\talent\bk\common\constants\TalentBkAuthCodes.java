package com.yxt.talent.bk.common.constants;

/***
 * <AUTHOR>
 * @since 2022/8/25 16:40
 */
public class TalentBkAuthCodes {
    private TalentBkAuthCodes() {
    }

    public static final String BK_AUTH_CODE_ALL = "*";

    /**
     * 查看标签
     */
    public static final String BK_AUTH_CODE_TAG_VIEW = "sp_gwnl_file_tag|sp_file_tag_view";

    /**
     * 新建标签
     */
    public static final String BK_AUTH_CODE_TAG_ADD = "sp_gwnl_file_tag|sp_file_tag_add";

    /**
     * 编辑标签
     */
    public static final String BK_AUTH_CODE_TAG_EDIT = "sp_gwnl_file_tag|sp_file_tag_edit";

    /**
     * 删除标签
     */
    public static final String BK_AUTH_CODE_TAG_DELETE = "sp_gwnl_file_tag|sp_file_tag_delete";

    /**
     * 启用/禁用标签
     */
    public static final String BK_AUTH_CODE_TAG_STATUS = "sp_gwnl_file_tag|sp_file_tag_open";
    /**
     * 贴标签 有贴标签权限或者新建标签权限
     */
    public static final String BK_AUTH_CODE_TAG_USER = "sp_gwnl_file_search|sp_file_tag_make";

    /**
     * 人才搜索列表浏览权限
     */
    public static final String BK_AUTH_CODE_PORTRAIT_VIEW = "sp_gwnl_file_search|sp_portrait_view";

    /**
     * 人才搜索列表导出
     */
    public static final String BK_AUTH_CODE_USER_EXPORT = "sp_gwnl_file_search|sp_file_user_export";

    /**
     * 人才池，人员导入
     */
    public static final String BK_AUTH_CODE_POOL_IMPORT = "sp_gwnl_succession_pool|sp_pool_user_import";

    /**
     * 人才池，人员导出
     */
    public static final String BK_AUTH_CODE_POOL_EXPORT = "sp_gwnl_succession_pool|sp_pool_user_export";

    /**
     * 人才池创建
     */
    public static final String BK_AUTH_CODE_POOL_CREATE = "sp_gwnl_succession_pool|sp_pool_new";

    /**
     * 人才池删除
     */
    public static final String BK_AUTH_CODE_POOL_DEL = "sp_gwnl_succession_pool|sp_pool_view_del";


    public static final String PORTRAIT_VIEW = "sp_gwnl_file_search|sp_portrait_view";


    /**
     * 人才列表导出
     */
    public static final String BK_FILE_SEARCH_EXPORT = "sp_gwnl_file_search|sp_file_search_operation_import";
    /**
     * 标签筛选
     */
    public static final String BK_FILE_SEARCH_TAGFILTER = "sp_gwnl_file_search|sp_file_search_operation_tagfilter";
    /**
     * 人才比对
     */
    public static final String BK_FILE_SEARCH_COMPARE = "sp_gwnl_file_search|sp_file_talentlist_compare";
    /**
     * 过滤禁用
     */
    public static final String BK_FILE_SEARCH_FILTERSWITCH = "sp_gwnl_file_search|sp_file_talentlist_filterswitch";
    /**
     * 查看详情
     */
    public static final String BK_FILE_SEARCH_TALENTDETAIL = "sp_gwnl_file_search|sp_file_talentlist_talentdetail";

    /**
     * 人才群组-新建群组
     */
    public static final String BK_GROUP_OPERATION_ADD = "sp_gwnl_file_group|sp_file_talentgroup_operation_add";
    /**
     * 群组删除
     */
    public static final String BK_GROUP_OPERATION_DEL = "sp_gwnl_file_group|sp_file_talentgroup_operation_del";
    /**
     * 群组查看
     */
    public static final String BK_GROUP_OPERATION_DETAILS = "sp_gwnl_file_group|sp_file_talentgroup_operation_details";
    /**
     * 群组下载
     */
    public static final String BK_GROUP_OPERATION_DOWNLOADS = "sp_gwnl_file_group|sp_file_talentgroup_operation_download";
    /**
     * 重新计算
     */
    public static final String BK_GROUP_OPERATION_RECALCULATE = "sp_gwnl_file_group|sp_file_talentgroup_operation_recalculate";

    public static final String GROUP_BOARD_NAV = "sp_talent_group_label";

    public static final String GROUP_BOARD_GROUP_DATA_PERMISSION = "tg_board_group";

    public static final String GROUP_BOARD_DEPT_DATA_PERMISSION = "tg_board_dept";

    public static final String POOL_DATA_PERMISSION_VIEW = "sp_pool_view_extent";
    public static final String POOL_DATA_PERMISSION_ADD = "sp_pool_user_add_extent";
    public static final String POOL_NAV_CODE = "sp_gwnl_succession_pool";

    public static final String SP_SUCCESSION_MAP = "sp_succession_map";
    public static final String SUCCESSION_DEPT = "succession_dept";
}
