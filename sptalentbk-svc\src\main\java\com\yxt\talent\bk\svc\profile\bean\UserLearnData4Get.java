package com.yxt.talent.bk.svc.profile.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(name = "人才画像-学习数据")
public class UserLearnData4Get {

    @Schema(description = "学习时长")
    private BigDecimal learningTime = new BigDecimal(0);

    @Schema(description = "学分")
    private BigDecimal score = new BigDecimal(0);

    @Schema(description = "积分")
    private BigDecimal point = new BigDecimal(0);

    @Schema(description = "证书数量")
    int certificates;

    @Schema(description = "项目数量")
    int projects;

    @Schema(description = "课程数量")
    int courses;

    @Schema(description = "考试数量")
    int exams;
}
