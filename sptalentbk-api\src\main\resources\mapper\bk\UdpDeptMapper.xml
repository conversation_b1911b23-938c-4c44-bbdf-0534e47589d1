<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.udp.mapper.UdpDeptMapper">

    <select id="getNameById" resultType="com.yxt.talent.bk.common.bean.udp.UdpLangDeptBean">
        select id,name from udp_dept where org_id = #{orgId} and id = #{id} and deleted = 0
    </select>

    <select id="queryIdFullPathByIds" resultType="com.yxt.talent.bk.core.udp.bean.DeptFullPathBean">
        select id,id_full_path from udp_dept where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryNameByIds" resultType="com.yxt.spsdk.common.bean.SpIdNameBean">
        select id,name from udp_dept where
        <choose>
            <when test="ids != null and ids.size > 0">
                id in
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=2
            </otherwise>
        </choose>
    </select>

    <select id="getRoutingPathById" resultType="java.lang.String">
        select routing_path from udp_dept where org_id = #{orgId} and id = #{id} and deleted = 0
    </select>
</mapper>
