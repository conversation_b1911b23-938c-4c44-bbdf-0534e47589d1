package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "标签列表，供勾选")
public class TagBean4Select {
    @Schema(description = "id")
    private String tagId;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "分类名称")
    private String catalogName;

    @Schema(description = "标签分组，默认-1,0-普通标签，1-人才盘点下标签")
    private Integer flag =-1;

    @Schema(description = "是否分层标签，0-否，1-是")
    private Integer tagType = 0;
}
