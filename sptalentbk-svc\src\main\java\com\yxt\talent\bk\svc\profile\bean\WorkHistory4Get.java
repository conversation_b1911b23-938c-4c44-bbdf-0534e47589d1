package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.common.Constants;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlOrder;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Setter
@Getter
@SqlTable("dwd_work_history")
@Schema(name = "人才画像-外部工作经历")
public class WorkHistory4Get {

    @Schema(description = "主键Id")
    private String id;

    @Schema(description = "绚星2.0平台用户id")
    private String userId;

    @JsonIgnore
    @Schema(description = "三方用户id")
    private String thirdUserId;

    @Schema(description = "开始时间")
    @SqlOrder(order = 1, direction = SqlOrder.DESC)
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime endTime;

    @SqlIgnore
    @Schema(description = "工作月份")
    private int workMonth;

    @Schema(description = "公司名称")
    private String corpName;

    @Schema(description = "任职部门名称")
    private String deptName;

    @Schema(description = "任职岗位名称")
    private String positionName;

    public int getWorkMonth() {
        if (this.endTime == null) {
            this.endTime = LocalDateTime.now();
        }
        if (this.startTime == null) {
            this.startTime = LocalDateTime.now();
        }
        long between = Math.abs(ChronoUnit.MONTHS.between(startTime, endTime));
        between = between < 1 ? 1 : between;
        return (int) between;
    }

}
