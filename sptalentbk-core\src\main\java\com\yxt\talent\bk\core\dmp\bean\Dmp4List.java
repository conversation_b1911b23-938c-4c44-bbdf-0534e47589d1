package com.yxt.talent.bk.core.dmp.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description TODO
 *
 * <AUTHOR>
 * @Date 2023/8/31 20:35
 **/
@Data
public class Dmp4List {

    @Schema(description = "项目id")
    private String id;

    @Schema(description = "动态匹配项目名称")
    private String dmpName;

    @Schema(description = "封面图片地址")
    private String coverUrl;

    @Schema(description = "岗位id")
    private String positionId;


    @Schema(description = "岗位名称")
    private String positionName;


    @Schema(description = "负责人")
    private List<String> owners;

    @Schema(description = "关联维度数")
    private int itemNum;

    @Schema(description = "开始时间")
    private Date startTime;


    @Schema(description = "结束时间")
    private Date endTime;


    @Schema(description = "盘点人数")
    private int rvNum;

    @Schema(description = "项目状态,0-未发布草稿，1-已发布但未开始，2-已发布进行中，3-已暂停，4-已结束")
    private int dmpStatus;

    @Schema(description = "任务进度")
    private int progress;
}
