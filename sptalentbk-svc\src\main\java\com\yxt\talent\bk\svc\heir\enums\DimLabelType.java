package com.yxt.talent.bk.svc.heir.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 标签类型：1-指标 2-标签
 */
@Getter
@AllArgsConstructor
public enum DimLabelType {

    /**
     * 指标
     */
    INDICATOR(1, "指标"),
    /**
     * 标签
     */
    LABEL(2, "标签");

    private final Integer code;
    private final String name;

    public static boolean isIndicator(Integer code) {
        return Objects.equals(INDICATOR.getCode(), code);
    }

    public static boolean isLabel(Integer code) {
        return Objects.equals(LABEL.getCode(), code);
    }

}
