package com.yxt.talent.bk.svc.common;

import com.yxt.common.exception.ApiException;
import com.yxt.export.DlcComponent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/4/3 9:54
 */
@Component
@AllArgsConstructor
@Slf4j
public class CommonService {

    private final DlcComponent dlcComponent;

    public String getDownloadUrl(String fileId, String errorKey) {
        String downloadUrl = dlcComponent.getDownloadUrl(fileId);
        log.debug("get downloadUrl by fileId: {}, {}", fileId, downloadUrl);
        if (StringUtils.isBlank(downloadUrl)) {
            throw new ApiException(errorKey);
        }
        return downloadUrl;
    }

}
