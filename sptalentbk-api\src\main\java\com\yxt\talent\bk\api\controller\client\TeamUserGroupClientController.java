package com.yxt.talent.bk.api.controller.client;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.Validate;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.core.usergroup.bean.GroupMemberVO;
import com.yxt.talent.bk.core.usergroup.bean.TeamUserUgroupCreateCmd;
import com.yxt.talent.bk.core.usergroup.bean.UserDeptUGroupVO;
import com.yxt.talent.bk.core.usergroup.bean.UserDeptUgroup4Get;
import com.yxt.talent.bk.svc.usergroup.UserGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/team/user/ugroup")
@Tag(name = "我的团队-人才看板-组织人才库")
public class TeamUserGroupClientController extends BaseController {
    private final UserGroupService userGroupService;

    @Operation(summary = "保存团队管理者的人才库")
    @PostMapping(value = "", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void add(@RequestBody TeamUserUgroupCreateCmd teamUserUgroupCreateCmd) {
        Validate.isNotBlank(teamUserUgroupCreateCmd.getDeptId(), "apis.talentbk.userGroup.deptId.notblank");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        String userId = userCache.getUserId();
        // 2种情况: 1-直接新增 2-从其他部门复制
        if (StringUtils.isNotBlank(teamUserUgroupCreateCmd.getFromDeptId())) {
            userGroupService.copyUserDeptUgroup(orgId, userId, teamUserUgroupCreateCmd);
        } else {
            userGroupService.createUserDeptUgroup(orgId, userId, teamUserUgroupCreateCmd);
        }
    }

    @Operation(summary = "查看人才库详情")
    @GetMapping(value = "/{id}", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public UserDeptUGroupVO getDetail(@PathVariable Long id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return userGroupService.getUserDeptUgroup(userCache.getOrgId(), id);
    }

    @Operation(summary = "编辑团队管理者的人才库")
    @PutMapping(value = "/{id}", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void edit(@PathVariable Long id,
                     @RequestBody TeamUserUgroupCreateCmd teamUserUgroupCreateCmd) {
        Validate.isNotBlank(teamUserUgroupCreateCmd.getDeptId(), "apis.talentbk.userGroup.deptId.notblank");
        userGroupService.validateRule(teamUserUgroupCreateCmd);
        UserCacheBasic userCache = getUserCacheBasic();
        userGroupService.updateUserDeptUgroup(userCache.getOrgId(), userCache.getUserId(), id, teamUserUgroupCreateCmd);
    }

    @Operation(summary = "删除团队管理者的人才库")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void delete(@PathVariable Long id) {
        UserCacheBasic userCache = getUserCacheBasic();
        userGroupService.deleteUserDeptUgroup(userCache.getOrgId(), userCache.getUserId(), id);
    }

    @Operation(summary = "人才库明细")
    @GetMapping(value = "/{id}/members")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<GroupMemberVO> getUserDeptUgroupMembers(@PathVariable Long id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return userGroupService.getUserDeptUgroupMembers(id, userCache);
    }

    @Operation(summary = "人才库列表")
    @GetMapping(value = "/{deptId}/list")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<UserDeptUgroup4Get> getUserDeptUgroupList(@PathVariable String deptId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        List<UserDeptUgroup4Get> records =
                userGroupService.findUserDeptUgroupPage(userDetail.getOrgId(), userDetail.getUserId(), deptId);
        return new CommonList<>(records);
    }

}
