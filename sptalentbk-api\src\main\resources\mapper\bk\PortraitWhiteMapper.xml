<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.profile.mapper.PortraitWhiteMapper">

    <resultMap type="com.yxt.talent.bk.core.profile.entity.PortraitWhite" id="BkPortraitWhiteMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="whiteEnable" column="white_enable" jdbcType="INTEGER"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUserId" column="update_user_id" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <select id="queryAllByOrgId" resultMap="BkPortraitWhiteMap">
        select
            id, org_id, user_id, white_enable
        from bk_portrait_white
        where org_id = #{orgId}
    </select>


    <select id="queryEnableByOrgId" resultMap="BkPortraitWhiteMap">
        select
          id, org_id, user_id, white_enable
        from bk_portrait_white
        where org_id = #{orgId} and white_enable = #{enable}
    </select>

    <!--统计总行数-->
    <select id="findEnableByUserIds" resultType="int">
        select count(1)
        from bk_portrait_white
        where org_id = #{orgId} and user_id = #{userId} and white_enable = 1
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into bk_portrait_white(id, org_id, user_id, white_enable, create_user_id, create_time, update_user_id, update_time)
        values (#{id}, #{orgId}, #{userId}, #{whiteEnable}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bk_portrait_white(id, org_id, user_id, white_enable, create_user_id, create_time, update_user_id, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.id}, #{entity.orgId}, #{entity.userId}, #{entity.whiteEnable}, #{entity.createUserId}, #{entity.createTime}, #{entity.updateUserId}, #{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bk_portrait_white(id, org_id, user_id, white_enable, create_user_id, create_time, update_user_id, update_time, db_create_time, db_update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.orgId}, #{entity.userId}, #{entity.whiteEnable}, #{entity.createUserId}, #{entity.createTime}, #{entity.updateUserId}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        id = values(id),
        org_id = values(org_id),
        user_id = values(user_id),
        white_enable = values(white_enable),
        create_user_id = values(create_user_id),
        create_time = values(create_time),
        update_user_id = values(update_user_id),
        update_time = values(update_time)
    </insert>


    <update id="updateEnableByUserIds">
        update bk_portrait_white
        set white_enable = #{enable}
        where org_id = #{orgId} and user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateAllDisEnable">
        update bk_portrait_white
        set white_enable = 0
        where org_id = #{orgId}
    </update>

</mapper>

