package com.yxt.talent.bk.svc.udp.rpc;

import com.yxt.common.pojo.api.PagingList;
import com.yxt.udpfacade.bean.user.es.search.EsGroupSearchCriteria;
import com.yxt.udpfacade.bean.user.es.search.EsUserInfoVo;
import com.yxt.udpfacade.bean.user.es.search.EsUserSearchParam;
import com.yxt.udpfacade.service.UdpEsUserSearchFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class UdpEsUserSearchRpc {
    private final UdpEsUserSearchFacade udpEsUserSearchFacade;

    public PagingList<EsUserInfoVo> search4Page(EsUserSearchParam searchParam) {
        return udpEsUserSearchFacade.search4Page(searchParam);
    }

    public List<EsUserInfoVo> search4List(EsUserSearchParam searchParam) {
        return udpEsUserSearchFacade.search4List(searchParam);
    }

    public long search4Count(EsUserSearchParam searchParam) {
        return udpEsUserSearchFacade.search4Count(searchParam);
    }

    public EsGroupSearchCriteria searchEsUserGroupCriteria(EsGroupSearchCriteria searchParam) {
        return udpEsUserSearchFacade.searchEsUserGroupCriteria(searchParam);
    }

}
