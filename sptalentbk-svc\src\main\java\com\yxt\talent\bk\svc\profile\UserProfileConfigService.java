package com.yxt.talent.bk.svc.profile;

import com.alibaba.fastjson.JSON;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.EntityUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.profile.entity.PortraitOrgConfig;
import com.yxt.talent.bk.core.profile.entity.PortraitWhite;
import com.yxt.talent.bk.core.profile.mapper.PortraitOrgConfigMapper;
import com.yxt.talent.bk.core.profile.mapper.PortraitWhiteMapper;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.profile.bean.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class UserProfileConfigService {
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final PortraitOrgConfigMapper portraitOrgConfigMapper;
    private final PortraitWhiteMapper portraitWhiteMapper;

    private final UserProfileWhiteService userProfileWhiteService;
    private final ILock lockService;

    @DbHintMaster
    public PortraitConfig4Get getPortraitConfig(String orgId, String userId) {
        PortraitConfig4Get portraitConfig = new PortraitConfig4Get();
        try {
            PortraitOrgConfig portraitOrgConfig = getPortraitOrgConfig(orgId, userId);
            BeanHelper.copyProperties(portraitOrgConfig, portraitConfig);
            portraitConfig.setClientCfgDto(parseOrInitClientCfg(portraitOrgConfig.getClientShowCfg()));
            portraitConfig.setManagerCfgDto(parseOrInitClientCfg(portraitOrgConfig.getManagerShowCfg()));
            //查询白名单人员
            if (portraitOrgConfig.getWhiteRange() == 1) {
                List<PortraitWhite> portraitWhites = portraitWhiteMapper.queryEnableByOrgId(orgId,
                        YesOrNo.YES.getValue());
                if (CollectionUtils.isNotEmpty(portraitWhites)) {
                    List<String> userIds = portraitWhites.stream().map(PortraitWhite::getUserId).toList();
                    List<UdpLiteUser> udpUserInfo = udpLiteUserRepository.getUdpUserInfo(orgId, userIds);
                    portraitConfig.setWhiteUsers(BeanCopierUtil.convertList(udpUserInfo, UdpLiteUser.class, SimpleUserBean.class));
                }
            }
            portraitConfig.setClientRangeComplex(transferAsClientRangeComplex(portraitOrgConfig));
        } catch (Exception e) {
            log.info("getPortraitConfig orgId:{}, error", orgId, e);
        }
        return portraitConfig;
    }

    public PortraitConfig4Update getPortraitConfig4Log(String orgId, boolean queryClientCfg) {
        PortraitConfig4Update update = new PortraitConfig4Update();
        PortraitOrgConfig portraitOrgConfig = getPortraitOrgConfig(orgId, null);
        BeanHelper.copyProperties(portraitOrgConfig, update);
        update.setClientCfgDto(parseOrInitClientCfg(portraitOrgConfig.getClientShowCfg()));
        update.setManagerCfgDto(parseOrInitClientCfg(portraitOrgConfig.getManagerShowCfg()));
        if (queryClientCfg && portraitOrgConfig.getWhiteRange() == 1) {
            update.setUserIds(portraitWhiteMapper.queryEnableUserIdByOrgId(orgId));
        }
        update.setClientRangeComplex(transferAsClientRangeComplex(portraitOrgConfig));
        return update;
    }

    private Integer transferAsClientRangeComplex(PortraitOrgConfig portraitOrgConfig) {
        Integer clientRange = Optional.ofNullable(portraitOrgConfig.getClientRange()).orElse(0);
        if (clientRange == 1) {
            return 1;
        } else {
            if (Optional.ofNullable(portraitOrgConfig.getWhiteRange()).orElse(0) == 0) {
                return 0;
            } else {
                return 2;
            }
        }
    }

    private PortraitOrgConfig getPortraitOrgConfig(String orgId, String userId) {
        String redisLockKey = String.format(TalentBkRedisKeys.LOCK_KEY_PORTRAIT_SETTING, orgId);
        if (lockService.tryLock(redisLockKey, TalentBkRedisKeys.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                PortraitOrgConfig portraitOrgConfig = portraitOrgConfigMapper.queryByOrgId(orgId);
                //初始化portraitConfig
                if (null == portraitOrgConfig) {
                    PortraitOrgConfig initConfig = new PortraitOrgConfig();
                    initConfig.setId(ApiUtil.getUuid());
                    initConfig.setOrgId(orgId);
                    initConfig.setClientRange(0);
                    initConfig.setManagerRange(1);
                    initConfig.setWhiteRange(0);
                    EntityUtil.setCreateInfo(userId, initConfig);
                    portraitOrgConfigMapper.insert(initConfig);
                    portraitOrgConfig = portraitOrgConfigMapper.queryByOrgId(orgId);
                    log.info("initPortraitConfig portraitOrgConfig:{}", portraitOrgConfig);
                }
                return portraitOrgConfig;
            }catch (Exception e){
                log.info("getPortraitOrgConfig orgId:{}, error", orgId, e);
            }finally {
                lockService.unLock(redisLockKey);
            }
        }
        return new PortraitOrgConfig();
    }


    public void updatePortraitConfig(PortraitConfig4Update portraitConfig4Update, String operatorId) {
        PortraitOrgConfig portraitConfig = new PortraitOrgConfig();
        BeanHelper.copyProperties(portraitConfig4Update, portraitConfig);
        Integer clientRangeComplex = portraitConfig4Update.getClientRangeComplex();
        if (clientRangeComplex != null) {
            if (clientRangeComplex == 0) {
                portraitConfig.setClientRange(0);
                portraitConfig.setWhiteRange(0);
            } else if (clientRangeComplex == 1) {
                portraitConfig.setClientRange(1);
                portraitConfig.setWhiteRange(0);
            } else {
                portraitConfig.setClientRange(0);
                portraitConfig.setWhiteRange(1);
            }
        }
        portraitConfig.setClientShowCfg(TalentbkUtil.toJson(portraitConfig4Update.getClientCfgDto()));
        portraitConfig.setManagerShowCfg(TalentbkUtil.toJson(portraitConfig4Update.getManagerCfgDto()));
        EntityUtil.setUpdatedInfo(operatorId, portraitConfig);
        portraitOrgConfigMapper.update(portraitConfig);
        if(portraitConfig.getWhiteRange() != null
                && portraitConfig.getWhiteRange() == YesOrNo.YES.getValue()) {
            List<String> userIds = portraitConfig4Update.getUserIds();
            userProfileWhiteService.upDateWhiteUsers(userIds, portraitConfig4Update.getOrgId(), operatorId);
        }
    }

    @DbHintMaster
    public PortraitConfigBean getClientPortraitConfig(String orgId, String userId) {
        PortraitConfigBean portraitConfigBean = new PortraitConfigBean();
        try {
            PortraitOrgConfig portraitOrgConfig = getPortraitOrgConfig(orgId, userId);
            if(portraitOrgConfig.getClientRange() == YesOrNo.YES.getValue()){
                portraitConfigBean.setShowPortrait(YesOrNo.YES.getValue());
            }else if(portraitOrgConfig.getClientRange() == YesOrNo.NO.getValue() && portraitOrgConfig.getWhiteRange() == YesOrNo.YES.getValue()){
                int enableCount = portraitWhiteMapper.findEnableByUserIds(orgId, userId);
                if(enableCount == 1){
                    portraitConfigBean.setShowPortrait(YesOrNo.YES.getValue());
                }
            }
            if (portraitConfigBean.getShowPortrait() == YesOrNo.YES.getValue()) {
                portraitConfigBean.setShowCfg(parseOrInitClientCfg(portraitOrgConfig.getClientShowCfg()));
            }
        }catch (Exception e){
            log.info("getClientPortraitConfig orgId:{} userId:{}, error", orgId, userId, e);
        }
        return portraitConfigBean;
    }

    @DbHintMaster
    public PortraitConfigBean getManagerPortraitConfig(String orgId, String userId) {
        PortraitConfigBean portraitConfigBean = new PortraitConfigBean();
        try {
            PortraitOrgConfig portraitOrgConfig = getPortraitOrgConfig(orgId, userId);
            if(portraitOrgConfig.getManagerRange() == YesOrNo.YES.getValue()){
                portraitConfigBean.setShowPortrait(YesOrNo.YES.getValue());
            }
            if (portraitConfigBean.getShowPortrait() == YesOrNo.YES.getValue()) {
                portraitConfigBean.setShowCfg(parseOrInitClientCfg(portraitOrgConfig.getManagerShowCfg()));
            }
        }catch (Exception e){
            log.info("getManagerPortraitConfig orgId:{} userId:{}, error", orgId, userId, e);
        }
        return portraitConfigBean;
    }

    public void init(Set<String> orgIds){
         List<PortraitOrgConfig> portraitOrgConfigList = new ArrayList<>();
         if(CollectionUtils.isNotEmpty(orgIds)){
             Map<String, PortraitOrgConfig> configMap = new HashMap<>();
             List<PortraitOrgConfig> dbPortraitOrgConfigs = portraitOrgConfigMapper.listByOrgIds(new ArrayList<>(orgIds));
             if(CollectionUtils.isNotEmpty(dbPortraitOrgConfigs)){
                 configMap = StreamUtil.list2map(dbPortraitOrgConfigs,
                         PortraitOrgConfig::getOrgId);
             }
             Map<String, PortraitOrgConfig> finalConfigMap = configMap;
             orgIds.forEach(orgId -> {
                 if(!finalConfigMap.containsKey(orgId)){
                     PortraitOrgConfig initConfig = new PortraitOrgConfig();
                     initConfig.setId(ApiUtil.getUuid());
                     initConfig.setOrgId(orgId);
                     initConfig.setClientRange(1);
                     initConfig.setManagerRange(1);
                     initConfig.setWhiteRange(0);
                     EntityUtil.setCreateInfo("system", initConfig);
                     portraitOrgConfigList.add(initConfig);
                 }
             });
         }
         log.info("initPortraitConfig size={}", portraitOrgConfigList.size());
         if(CollectionUtils.isNotEmpty(portraitOrgConfigList)){
             BatchOperationUtil.batchExecute(portraitOrgConfigList, 200, portraitOrgConfigMapper::insertBatch);
         }
        log.info("initPortraitConfig end");
    }

    private PortraitShowCfgDto parseOrInitClientCfg(String cfgJson) {
        PortraitShowCfgDto cfgDto = CommonUtils.tryParseObject(cfgJson, PortraitShowCfgDto.class);
        if (cfgDto == null) {
            cfgDto = new PortraitShowCfgDto();
            cfgDto.setUserBasic(YesOrNo.YES.getValue());
            cfgDto.setUserAbility(YesOrNo.YES.getValue());
            cfgDto.setUserPerf(YesOrNo.YES.getValue());
            cfgDto.setJobFeatureEval(YesOrNo.YES.getValue());
            cfgDto.setUserJob(YesOrNo.YES.getValue());
            cfgDto.setInnerJob(YesOrNo.YES.getValue());
            cfgDto.setOuterJob(YesOrNo.YES.getValue());
            cfgDto.setUserAward(YesOrNo.YES.getValue());
            cfgDto.setSpEval(YesOrNo.YES.getValue());
            cfgDto.setPositionMatch(YesOrNo.YES.getValue());
            cfgDto.setRvCell(YesOrNo.YES.getValue());
            cfgDto.setHeirTree(YesOrNo.YES.getValue());
            cfgDto.setUserPool(YesOrNo.YES.getValue());
            cfgDto.setStudySummary(YesOrNo.YES.getValue());
            cfgDto.setO2oStudy(YesOrNo.YES.getValue());
            cfgDto.setKngStudy(YesOrNo.YES.getValue());
            cfgDto.setExam(YesOrNo.YES.getValue());
            cfgDto.setIdp(YesOrNo.YES.getValue());
        }
        return cfgDto;
    }
}
