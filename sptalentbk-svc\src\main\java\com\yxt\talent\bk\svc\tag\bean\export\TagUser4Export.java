package com.yxt.talent.bk.svc.tag.bean.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * : 标签值导出人员 数据结构
 * <AUTHOR>
 * @since 2022/8/10 16:27
 */
@Data
@NoArgsConstructor
public class TagUser4Export {


    @Schema(description = "用户账号")
    private String username;

    @Schema(description = "用户姓名")
    private String fullname;

    @Schema(description = "用户账号（用户名）")
    private String userAndFullName;

    @Schema(description = "帐号状态 0-禁用 1-启用")
    private int status;

    @Schema(description = "帐号状态 0-禁用 1-启用" + "excel 专用字段慎用")
    private String statusStr;

    @Schema(description = "部门名称")
    private String departmentName;

    @Schema(description = "岗位名称")
    private String positionName;

    @Schema(description = "标签值")
    private String tagValues;

    @Schema(description = "标签值名称列表")
    private List<String> tagValueNameList;

    @Schema(description = "标签类型:0-普通标签,1-分层标签")
    private Integer tagType;

    public String getStatusStr() {
        // (0-禁用,1-启用,2已删除)
        if (this.status == 1) {
            setStatusStr("已启用");
            return this.statusStr;
        } else if (this.status == 2) {
            setStatusStr("已删除");
            return this.statusStr;
        } else {
            setStatusStr("已禁用");
            return this.statusStr;
        }
    }
}
