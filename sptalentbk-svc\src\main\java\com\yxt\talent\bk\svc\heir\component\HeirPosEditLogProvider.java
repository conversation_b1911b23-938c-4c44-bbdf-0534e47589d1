package com.yxt.talent.bk.svc.heir.component;

import com.yxt.common.pojo.IdName;
import com.yxt.common.util.StreamUtil;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.enums.HeirRiskRuleTypeEnum;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.udp.bean.UdpPositionBean;
import com.yxt.talent.bk.svc.common.enums.AuditLogPointEnum;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.talent.bk.svc.heir.HeirOrgLevelConfigService;
import com.yxt.talent.bk.svc.heir.bean.HeirPos4Log;
import com.yxt.talent.bk.svc.heir.bean.req.HeirPosEditBean;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirRiskResp;
import com.yxt.talent.bk.svc.udp.UdpQueryService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * HeirPosEditLogProvider
 *
 * <AUTHOR> geyan
 * @Date 19/3/24 10:09 am
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirPosEditLogProvider implements AuditLogDataProvider<HeirPosEditBean, HeirPos4Log> {
    private static final String KEY_RISK_LEVELS = "risk_levels";
    private final HeirPosRepository heirPosRepository;
    private final UdpQueryService udpQueryService;
    private final HeirOrgLevelConfigService heirOrgLevelConfigService;

    @Override
    public HeirPosEditBean convertParam(Object param, AuditLogBasicBean logBasic) {
        if (param instanceof String) {
            HeirPosEditBean editBean = new HeirPosEditBean();
            editBean.setId((String) param);
            return editBean;
        }
        return AuditLogDataProvider.super.convertParam(param, logBasic);
    }

    @Override
    public HeirPos4Log before(HeirPosEditBean param, AuditLogBasicBean logBasic) {
        HeirPosEntity heirPos = heirPosRepository.selectById(param.getId());
        HeirPos4Log ret = new HeirPos4Log();
        ret.setId(param.getId());
        if (heirPos != null) {
            ret.setHeirTargetQty(heirPos.getHeirTargetQty());
            ret.setRiskRuleType(heirPos.getRiskRuleType());
            ret.setRiskLevelId(heirPos.getRiskLevelId());
            fillPosLogDetail(logBasic, ret);
        }
        return ret;
    }

    @Override
    public HeirPos4Log after(HeirPosEditBean param, AuditLogBasicBean logBasic) {
        HeirPos4Log ret = new HeirPos4Log();
        ret.setId(param.getId());
        ret.setParentPosId(param.getParentPosId());
        ret.setHeirTargetQty(param.getHeirTargetQty());
        ret.setRiskRuleType(param.getRiskRuleType());
        ret.setRiskLevelId(param.getRiskLevelId());
        fillPosLogDetail(logBasic, ret);
        return ret;
    }

    @Override
    public Pair<String, String> entityInfo(HeirPosEditBean param, HeirPos4Log beforeObj, HeirPos4Log afterObj, AuditLogBasicBean logBasic) {
        String posName = CommonUtils.firstNotEmpty(beforeObj, afterObj, HeirPos4Log::getPosName);
        if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_DEL_POS)) {
            return Pair.of(param.getId(), String.format("继任地图-%s-删除", posName));
        } else {
            return Pair.of(param.getId(), String.format("继任地图-%s-岗位设置", posName));
        }
    }

    private void fillPosLogDetail(AuditLogBasicBean logBasic, HeirPos4Log posLog) {
        String orgId = logBasic.getOrgId();
        if (HeirRiskRuleTypeEnum.MANUAL.getType() == posLog.getRiskRuleType()
                && posLog.getRiskLevelId() != null) {
            HeirRiskResp riskResp = IArrayUtils.getFirstMatch(
                    logBasic.cacheData(KEY_RISK_LEVELS, () -> heirOrgLevelConfigService.getHeirRiskData(orgId)),
                    item -> posLog.getRiskLevelId().equals(item.getId()));
            posLog.setRiskLevelName(Optional.ofNullable(riskResp).map(HeirRiskResp::getLevelName1).orElse(null));
        }
        List<String> posIds = Lists.newArrayList(posLog.getId());
        if (StringUtils.isNotEmpty(posLog.getParentPosId())) {
            posIds.add(posLog.getParentPosId());
        }
        Map<String, String> positionMap = StreamUtil.list2map(udpQueryService.listPositionByIds(orgId, posIds),
                IdName::getId, IdName::getName);
        posLog.setParentPosName(positionMap.get(posLog.getParentPosId()));
        posLog.setPosName(positionMap.get(posLog.getId()));
        posLog.setRiskRuleTypeName(HeirRiskRuleTypeEnum.getNameByType(posLog.getRiskRuleType()));
    }
}
