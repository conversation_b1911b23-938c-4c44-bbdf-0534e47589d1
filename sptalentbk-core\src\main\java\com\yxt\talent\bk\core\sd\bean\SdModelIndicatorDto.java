package com.yxt.talent.bk.core.sd.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public abstract class SdModelIndicatorDto {

    @Schema(description = "类型，0-普通、1-能力、2-技能、3-知识、4-任务  5-分类（暂未使用）")
    private Integer itemType;

    @Schema(description = "具体值，普通与分类类型为文本，其余为id")
    private String itemValue;

    @Schema(description = "标准等级")
    private Integer standardLevel;

    @Schema(description = "权重百分比")
    private BigDecimal weight;

    @Schema(description = "知识掌握度")
    private String kngDictName;

    @Schema(description = "任务熟练度")
    private String rtDictName;

    @Schema(description = "分类优秀等级")
    private String excellentLevelStr;

    @Schema(description = "分类标准等级")
    private String standardLevelStr;

    @Schema(description = "指标库id，为方便统一字段取值准备")
    private String itemId;

    @Schema(description = "任务名称")
    private String rtName;
    @Schema(description = "任务描述")
    private String rtDesc;

    @Schema(description = "等级")
    private int level;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "描述")
    private String description;

    private String parentName;
    public abstract void setChildren(List children);
}
