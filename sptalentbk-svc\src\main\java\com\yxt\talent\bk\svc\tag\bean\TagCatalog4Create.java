package com.yxt.talent.bk.svc.tag.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 分类crud页面交互Vo
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Data
@NoArgsConstructor
public class TagCatalog4Create {

    @Schema(description = "分类表主键(区分更新与新增)")
    private String id;

    @Schema(description = "分类名称")
    private String catalogName;

    @Schema(description = "类型(0-标签类型)")
    private Integer catalogType;

    @Schema(description = "排序")
    private Integer orderIndex;

}
