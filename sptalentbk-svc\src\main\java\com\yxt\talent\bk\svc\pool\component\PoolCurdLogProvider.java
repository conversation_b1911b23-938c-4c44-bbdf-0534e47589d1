package com.yxt.talent.bk.svc.pool.component;

import com.yxt.auditlog.consts.AuditConsts;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.bk.core.pool.bean.Pool4AuditLog;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * PoolCurdLogProvider
 *
 * <AUTHOR> geyan
 * @Date 15/3/24 2:01 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class PoolCurdLogProvider implements AuditLogDataProvider<String, Pool4AuditLog> {
    private final PoolRepository poolRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;
    @Override
    public Pool4AuditLog before(String pollId, AuditLogBasicBean logBasic) {
        return getAuditLogData(pollId,!AuditConsts.DELETE.equals(logBasic.getLogPoint().getAuditAction()));
    }

    @Override
    public Pool4AuditLog after(String pollId, AuditLogBasicBean logBasic) {
        return getAuditLogData(pollId,!AuditConsts.DELETE.equals(logBasic.getLogPoint().getAuditAction()));
    }

    @Override
    public Pair<String, String> entityInfo(String poolId, Pool4AuditLog beforeObj, Pool4AuditLog afterObj, AuditLogBasicBean logBasic) {
        String poolName = StringPool.EMPTY;
        if (beforeObj != null) {
            poolName = beforeObj.getPoolName();
        } else if (afterObj != null) {
            poolName = afterObj.getPoolName();
        }
        if (StringUtils.isEmpty(poolName)) {
            poolName = poolRepository.getNameById(poolId);
        }
        return Pair.of(poolId, "人才池-" + poolName);
    }

    private Pool4AuditLog getAuditLogData(String poolId, boolean needDetail) {
        if (StringUtils.isEmpty(poolId)) {
            return null;
        }
        Pool4AuditLog data = poolRepository.getById4AuditLog(poolId, needDetail);
        if (data != null) {
            data.setMgtUserNames(udpLiteUserRepository.userNames4Log(data.getOrgId(), data.getMgtUserIds()));
        }
        return data;
    }
}
