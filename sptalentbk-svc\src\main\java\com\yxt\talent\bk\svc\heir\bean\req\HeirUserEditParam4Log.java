package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.core.heir.bean.HeirPosUserBriefBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * HeirUserEditParam4Log
 *
 * <AUTHOR> geyan
 * @Date 19/3/24 3:36 pm
 */
@Data
public class HeirUserEditParam4Log {
    private String posId;

    @Schema(description = "待修改的记录id（最多1000个）")
    private List<Long> ids;
    @Schema(description = "准备度规则等级id（为0表示没有准备度）")
    private Long prepareLevelId;
    @Schema(description = "继任状态：0:进行中，1:已退出")
    private Integer heirStatus;

    @Schema(description = "退出原因")
    private String quitReason;

    private List<HeirPosUserBriefBean> posUsers;
    private String prepareLevelName;
}
