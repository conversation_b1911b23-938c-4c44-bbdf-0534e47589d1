package com.yxt.talent.bk.core.pool.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.pool.bean.Pool4AuditLog;
import com.yxt.talent.bk.core.pool.entity.Pool;
import com.yxt.talent.bk.core.pool.mapper.PoolMapper;
import com.yxt.talent.bk.core.pool.mapper.PoolMgrMapper;
import com.yxt.talentbkfacade.bean.StrIdNameBean;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class PoolRepository extends ServiceImpl<PoolMapper, Pool> {

    private static final Logger log1 = LoggerFactory.getLogger(PoolRepository.class);
    @Autowired
    private PoolMgrMapper poolMgtMapper;

    public Pool getById(String id, String orgId) {
        LambdaQueryWrapper<Pool> queryWrapper = getQueryWrapper();
        queryWrapper.eq(Pool::getId, id);
        queryWrapper.eq(Pool::getOrgId, orgId);
        queryWrapper.eq(Pool::getDeleted,0);
        return getOne(queryWrapper);
    }

    public List<Pool> list(String orgId) {
        LambdaQueryWrapper<Pool> queryWrapper = getQueryWrapper();
        queryWrapper.eq(Pool::getOrgId, orgId);
        queryWrapper.eq(Pool::getDeleted, YesOrNo.NO.getValue());
        return list(queryWrapper);
    }

    public boolean removeById(String orgId, String id) {
        LambdaQueryWrapper<Pool> queryWrapper = getQueryWrapper();
        queryWrapper.eq(Pool::getId, id);
        queryWrapper.eq(Pool::getOrgId, orgId);
        return super.remove(queryWrapper);
    }

    public long countByCatalogId(String orgId, String catalogId) {
        LambdaQueryWrapper<Pool> queryWrapper = getQueryWrapper();
        queryWrapper.eq(Pool::getCatalogId, catalogId);
        queryWrapper.eq(Pool::getOrgId, orgId);
        return count(queryWrapper);
    }

    public List<Pool> findPoolNameByPoolIds(String orgId, Collection<String> poolIds) {
        if (CollectionUtils.isEmpty(poolIds)) {
            log1.warn("LOG10290:orgId={}", orgId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<Pool> queryWrapper = getQueryWrapper();
        queryWrapper.eq(Pool::getOrgId, orgId);
        queryWrapper.in(Pool::getId,poolIds);
        queryWrapper.select(Pool::getId,Pool::getPoolName);
        return list(queryWrapper);
    }

    public Pool4AuditLog getById4AuditLog(String id, boolean needDetail) {
        Pool4AuditLog pool4Log = baseMapper.getById4AuditLog(id);
        if (pool4Log != null && needDetail) {
            pool4Log.setMgtUserIds(poolMgtMapper.poolMgtUserIds(pool4Log.getOrgId(), id));
        }
        return pool4Log;
    }

    public String getNameById(String id) {
        return baseMapper.getNameById(id);
    }

    public List<StrIdNameBean> getNameByIds(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return baseMapper.queryPoolNameByIds(ids);
    }

    private LambdaQueryWrapper<Pool> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }
}
