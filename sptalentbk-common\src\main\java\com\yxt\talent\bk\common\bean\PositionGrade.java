package com.yxt.talent.bk.common.bean;

import com.yxt.common.Constants;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 岗位职级表
 *
 * <AUTHOR>
 * @since 2020-08-24 18:10:22
 */
@Data
public class PositionGrade {
    /**
     * 企业岗位职级表主键
     */
	private String id;
    /**
     * 所属企业id
     */
	private String orgId;
    /**
     * 职级名称
     */
	private String name;
    /**
     * 第三方系统ID
     */
	private String thirdId;
    /**
     * 排序
     */
	private Integer orderIndex;
    /**
     * 创建者uuid
     */
	private String createUserId;
    /**
     * 创建时间
     */
	@DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
	private Date createTime;
    /**
     * 更新者uuid
     */
	private String updateUserId;
    /**
     * 修改时间
     */
	@DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
	private Date updateTime;
    /**
     * 删除标记(0-否,1-是)
     */
	private Integer deleted;
}
