package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.SnowFlowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirLangI18nEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:40 am
 */
@Data
@TableName("bk_heir_lang_i18n")
public class HeirLangI18nEntity extends SnowFlowIdEntity {
    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "一组多语言关联id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_ORG_LEVEL_CFG_ID)
    private Long groupId;

    @Schema(description = "如:cn,en,tc")
    private String langCode;

    @Schema(description = "多语言内容")
    private String langValue;
}
