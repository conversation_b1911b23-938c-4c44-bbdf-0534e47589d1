package com.yxt.talent.bk.svc.tag.bean.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * : 标签值导入人员 数据结构
 * <AUTHOR>
 * @since 2022/8/10 16:27
 */
@Data
@NoArgsConstructor
public class TagValue4Export {

    @Schema(description = "0:普通标签，1：分层标签", example = "0")
    private Integer tagType;

    @Schema(description = "标签值选择类型(0-单选,1-多选)", example = "0")
    private Integer valueChooseModel;

    @Schema(description = "表格数据载体(普通标签)")
    private List<TagExportData4Single> dataList4Single;

    @Schema(description = "表格数据载体(分层标签)")
    private List<TagExportData4Mul> dataList4Mul;

    @Data
    @NoArgsConstructor
    public static class TagExportData4Single {

        @Schema(description = "用户账号")
        private String username;

        @Schema(description = "用户姓名")
        private String fullName;

        @Schema(description = "错误信息")
        private String errorMsg = "";

    }

    @Data
    @NoArgsConstructor
    public static class TagExportData4Mul{

        @Schema(description = "用户id")
        private String userId;

        @Schema(description = "用户账号")
        private String username;

        @Schema(description = "用户姓名")
        private String fullName;

        @Schema(description = "标签值")
        private String tagValue;

        @Schema(description = "错误信息")
        private String errorMsg = "";

    }
}
