package com.yxt.talent.bk.svc.common;

import com.yxt.msgfacade.bean.GroupUserInfo;
import com.yxt.msgfacade.bean.Todo4Create;
import com.yxt.msgfacade.bean.Todo4DeleteBatch;
import com.yxt.msgfacade.bean.Todo4Done;
import com.yxt.msgfacade.bean.Todo4DoneBatch;
import com.yxt.msgfacade.service.TodoFacade;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * TodoRpc
 *
 * <AUTHOR> geyan
 * @Date 7/10/23 4:21 pm
 */
@Component
@AllArgsConstructor
@Slf4j
public class TodoRpc {
    private final TodoFacade todoFacade;

    public void create(Todo4Create todo4Create) {
        todoFacade.create(todo4Create);
    }

    public void done(Todo4Done todo4Done) {
        todoFacade.done(todo4Done);
    }

    public void batchDone(Todo4DoneBatch doneBatch) {
        todoFacade.doneBatch(doneBatch);
    }

    public void deleteBatch(Todo4DeleteBatch todo4DeleteBatch) {
        todoFacade.deleteBatch(todo4DeleteBatch);
    }

    public GroupUserInfo buildUser(String orgId, String... userIds) {
        GroupUserInfo userInfo = new GroupUserInfo();
        userInfo.setOrgId(orgId);
        userInfo.setUserIds(Arrays.asList(userIds));
        return userInfo;
    }
}
