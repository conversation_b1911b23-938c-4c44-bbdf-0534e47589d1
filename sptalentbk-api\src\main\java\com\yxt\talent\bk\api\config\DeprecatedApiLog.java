package com.yxt.talent.bk.api.config;

import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import jakarta.servlet.http.HttpServletRequest;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Optional;

/**
 * DeprecatedApiLog
 *
 * <AUTHOR> harleyge
 * @Date 9/4/24 5:20 pm
 */
@Slf4j
@Aspect
@Component
public class DeprecatedApiLog {
    @Autowired
    private AuthService authService;

    @Pointcut("execution(public * com.yxt.talent.bk.api.controller..*(..)) && @annotation(deprecated)")
    public void pointcut(Deprecated deprecated) {
        //NO SONAR
    }

    @Around(value = "pointcut(deprecated)")
    public Object around(ProceedingJoinPoint joinPoint, Deprecated deprecated) throws Throwable {
        try {
            return joinPoint.proceed();
        } finally {
            try {
                Class targetClazz = joinPoint.getTarget().getClass();
                Method method = ((MethodSignature)joinPoint.getSignature()).getMethod();
                StringBuilder methodSign = new StringBuilder(targetClazz.getSimpleName() + StringPool.DOT + method.getName());
                methodSign.append(StringPool.LEFT_BRACKET);
                Class<?>[] parameterTypes = method.getParameterTypes();
                if (parameterTypes != null) {
                    for (int i = 0; i < parameterTypes.length; i++) {
                        if (i > 0) {
                            methodSign.append(StringPool.COMMA);
                        }
                        methodSign.append(parameterTypes[i].getSimpleName());
                    }
                }
                methodSign.append(StringPool.RIGHT_BRACKET);
                HttpServletRequest request = ApiUtil.getRequestByContext();
                String orgId = Optional.ofNullable(authService.getUserCacheBasic(request)).map(UserCacheBasic::getOrgId)
                        .orElse(StringPool.EMPTY);
                log.info("deprecatedApiCall {} orgId {} product {} source {}", methodSign, orgId,
                        request.getHeader("x-yxt-product"), request.getHeader("source"));
            } catch (Exception e) {
                log.error("deprecatedApiCall log failed", e);
            }
        }
    }
}
