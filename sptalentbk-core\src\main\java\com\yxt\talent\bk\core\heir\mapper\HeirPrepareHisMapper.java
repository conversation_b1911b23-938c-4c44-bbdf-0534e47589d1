package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.entity.HeirPrepareHisEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * HeirPrepareHisMapper
 *
 * <AUTHOR> ha<PERSON><PERSON>
 * @Date 24/7/24 10:06 am
 */
@Mapper
public interface HeirPrepareHisMapper extends BkBaseMapper<HeirPrepareHisEntity> {

    HeirPrepareHisEntity recentHis(String posId, String userId);
}
