package com.yxt.talent.bk.core.pool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.pool.entity.PoolReadiness;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @since 2022/09/08
 */
@Mapper
public interface PoolReadinessMapper extends BaseMapper<PoolReadiness> {

    @Select("select readiness_name from bk_pool_readiness where id = #{id}")
    String getNameById(@Param("id") String id);
}
