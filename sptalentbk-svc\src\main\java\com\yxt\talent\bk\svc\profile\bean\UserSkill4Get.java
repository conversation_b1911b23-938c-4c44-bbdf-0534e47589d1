package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(name = "人才画像-个人能力")
public class UserSkill4Get {

    @Schema(description = "能力id")
    private String skillId;

    @Schema(description = "能力名称")
    private String skillName;

    @Schema(description = "能力分数（10分制）")
    private BigDecimal tenScore;

    @Schema(description = "是否达标：0-不达标 1-达标【原能力优劣势 1 优势项 2 待提升项】")
    private Integer conclusion;

    @Schema(description = "能力模型id")
    private String skillModelId;

    @Schema(description = "测评结果生成时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime creatTime;

    private int orderIndex;
}
