package com.yxt.talent.bk.core.heir.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirPosUserPrepareBean
 *
 * <AUTHOR> geyan
 * @Date 19/8/23 4:28 pm
 */
@Data
public class PosUserPrepareBean {
    private Long id;
    private String userId;
    @Schema(description = "计算的准备度id")
    private Long calcLevelId;
    private boolean needUpdate;
    private boolean calcMatched;
}
