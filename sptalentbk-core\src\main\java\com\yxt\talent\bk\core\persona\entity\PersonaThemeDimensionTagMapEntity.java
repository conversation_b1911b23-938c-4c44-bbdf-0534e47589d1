package com.yxt.talent.bk.core.persona.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("bk_persona_theme_dimension_tag_map")
public class PersonaThemeDimensionTagMapEntity extends CreatorEntity {
    /**
     * 维度标签关系主键
     */
    @TableId
    private String id;
    /**
     * 机构id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * 画像主题id
     */
    @TableField("theme_id")
    private String themeId;
    /**
     * '维度id'
     */
    @TableField("dimension_id")
    private String dimensionId;
    /**
     * 标签id
     */
    @TableField("tag_id")
    private String tagId;
}
