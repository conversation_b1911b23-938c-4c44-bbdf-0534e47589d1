package com.yxt.talent.bk.common.constants;

import lombok.experimental.UtilityClass;

import java.util.Locale;

@UtilityClass
public final class TalentBkRedisKeys {
    public static final String TALENTBK_THEME_DIMENSION_TAG_INIT_LOCK_KEY = "talentbk:theme:dimension:tag:key:%s";
    public static final String CACHE_KEY_USER_SEARCH_RESULT_EXPORT = "talentbk:user:search:result.export:%s:%s";
    public static final String CACHE_KEY_CATALOG_POOL_DEFAULT_SAVE = "talentbk:catalog:pool:default.save:%s";
    public static final String TALENTBK_TAG_VALUE_USER_EXPORT_CACHE_KEY = "talentbk:tag:value:user.export:%s:%s";
    public static final String TALENTBK_POOL_USER_INFO_EXPORT_CACHE_KEY = "talentbk:pool:user.info.export:%s:%s";
    public static final String TALENTBK_TAG_VALUE_USER_IMPORT_CACHE_KEY = "talentbk:tag:value:user.import:%s:%s";
    public static final String CACHE_KEY_UDP_USER_AUTH_NAVCODE = "talentbk:udp:user:auth:client:%s:%s:%s:%s";
    public static final String CACHE_KEY_UDP_DEPT_AUTH_NAVCODE = "talentbk:udp:dept:auth:client:%s:%s:%s:%s";
    public static final String CACHE_KEY_UDP_USER_AUTH_CLIENT = "talentbk:udp:user:auth:client:%s:%s";
    public static final String CACHE_KEY_USER_SEARCH_AGGREGATED = "talentbk:user:search:aggregated:%s:%s";
    /**
     * 人才池准备度相关
     */
    public static final String CACHE_KEY_POOL_READINESS_CREATE = "talentbk:pool:readiness:readiness.create:%s";
    public static final String CACHE_KEY_POOL_READINESS_DELETE = "talentbk:pool:readiness:readiness.delete:%s";
    public static final String CACHE_KEY_POOL_READINESS_INIT = "talentbk:pool:readiness:readiness.init:%s";
    /**
     * 人才池相关
     */
    public static final String CACHE_KEY_POOL_RESULT_EXPORT = "talentbk:pool:result.export:%s:%s";
    /**
     * 新增用户标签，按照机构级别加锁
     */
    public static final String CACHE_KEY_ORG_ADD_USER_TAG = "talent:user:tag:org:add:%s";
    /**
     * 删除用户标签，按照机构级别加锁
     */
    public static final String CACHE_KEY_ORG_DEL_USER_TAG = "talent:user:tag:org:delete:%s";
    /**
     * 初始化 筛选组数据，加锁
     */
    public static final String TALENTBK_TAG_INIT_GROUP_CACHE_KEY = "talentbk:tag:value:init.group:%s";
    /**
     * 初始化 维度选项加锁
     */
    public static final String TALENTBK_INIT_DIMENSION_ITEM_LOCK_KEY = "talentbk:init:dimension:item:%s";
    /**
     * 初始化 筛选勾选项加锁 FilterItem
     */
    public static final String TALENTBK_INIT_FILTER_ITEM_LOCK_KEY = "talentbk:init:filter:item:%s";
    /**
     * 初始化 搜索维度
     */
    public static final String TALENTBK_INIT_DIMENSION_SEARCH_LOCK_KEY = "talentbk:init:dimension:search:%s:%s";
    /**
     * 人才池人员导入
     */
    public static final String TALENTBK_POOL_USER_IMPORT_CACHE_KEY = "talentbk:pool:user.import:%s:%s";
    /**
     * 人才池准备度模板
     */
    public static final String POOL_READINESS_IMPORT_TEMP = "talentbk:pool:readiness.import:%s:%s";
    public static final String CACHE_KEY_POOL_READINESS_IMPORT = "talentbk:pool:readiness:import:%s:%s";
    public static final int MAX_LEASE_TIME = 100;
    /**
     * 人才池添加人员防止重复添加
     */
    public static final String TALENTBK_POOL_ADD_USER_CACHE_KEY = "talentbk:pool:add:user:%s:%s";

    /**
     * 人才继任：继任者操作 加锁
     */
    public static final String CACHE_KEY_HEIR_POS_USER_OPERATE = "talentbk:heir:pos:user:operate:%s:%s";
    /**
     * 人才继任：继任权限操作 加锁
     */
    public static final String CACHE_KEY_HEIR_POS_PERMISSION_OPERATE = "talentbk:heir:pos:permission:operate:%s:%s";
    /**
     * 人才继任：继任标杆用户操作 加锁
     */
    public static final String CACHE_KEY_HEIR_POS_BENCHMARK_OPERATE = "talentbk:heir:pos:benchmark:operate:%s";

    /**
     * 人才继任：继任有效用户数量计算
     */
    public static final String CACHE_KEY_HEIR_POS_CALCULATE_VALID_QTY = "talentbk:heir:pos:calculate:valid:qty:%s";

    public static final String CACHE_KEY_SCHEME_USER_OPERATE = "talentbk:scheme:user:operate:%s:%s";

    public static final String CACHE_KEY_USER_GROUP_EXPORT = "talentbk:user:group:export:%s:%s";
    public static final String CACHE_KEY_SAVE_CONFIG_BASE = "talentbk:save:config:base:%s";
    /**
     * 待办处理key
     */
    public static final String LOCK_KEY_HEIR_REMIND_TODO_OPT = "talentbk:lock:heir:remind:todo:opt:%s";

    public static final String CACHE_KEY_USER_GROUP_CACULATE_DELAY_ORG = "talentbk:user:group:caculate:delayorgs";

    /**
     * 人才画像高级设置key
     */
    public static final String LOCK_KEY_PORTRAIT_SETTING = "talentbk:lock:portrait:setting:%s";

    public static final String CACHE_KEY_SAVE_LOG_LAST_DING = "talentbk:save:log:last:ding";

    /**
     * param orgId
     */
    public static final String CACHE_KEY_HEIR_CLIENT_SHOW = "talentbk:heir:client:show:%s";
    public static final String CACHE_KEY_DASHBOARD_DEPT_DIM_EXPORT = "talentbk:lock:dashboard:dept:dim:%s:%s";

    public static final String CACHE_KEY_DASHBOARD_GROUP_DIM_EXPORT = "talentbk:lock:dashboard:group:dim:%s:%s";

    public static final String CACHE_KEY_DASHBOARD_PERSONAL_DIM_EXPORT = "talentbk:lock:dashboard:personal:dim:%s:%s";

    //重复消息key
    public static final String CACHE_MQ_DUPLICATE_MESSAGE = "talentbk:mq:duplicate:message:%s";

    public static final String CACHE_COPY_ORG_DATA = "talentbk:copy:org:data:%s";
    public static final String CACHE_COPY_ORG_IDMAP = "talentbk:copy:org:idmap:%s";

    public static final String CACHE_LOCK_ORG_COPY_RESULT = "talentbk:lock:org:copy:result:%s";
    public static final String CACHE_ORG_COPY_RESULT = "talentbk:org:copy:result:%s";
    public static final String CACHE_DEMO_COPY_ORG_SET = "talentbk:demo:copy:org:set";

    /**
     * userId + resourceCode
     */
    public static final String CACHE_LOCK_RESOURCE_TRANSFER = "talentbk:lock:resource:transfer:%s:%s";
}
