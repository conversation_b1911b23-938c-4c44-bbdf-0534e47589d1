package com.yxt.talent.bk.svc.tag.bean.imports;

import com.yxt.talent.bk.common.imports.ImportRequestBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * : 标签值导入人员 数据结构
 * <AUTHOR>
 * @since 2022/8/10 16:27
 */
@Data
public class TagValue4Import {

    @Schema(description = "标签id")
    private String tagId;

    @Schema(description = "表格数据载体(普通标签)")
    private ImportRequestBean importRequestBean;
}
