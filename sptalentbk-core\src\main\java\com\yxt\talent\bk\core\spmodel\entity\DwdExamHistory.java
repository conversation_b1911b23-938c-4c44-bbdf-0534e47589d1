package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * 考试历史表(DwdExamHistory)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 11:00:16
 */
@Data
@TableName(value = "dwd_exam_history")
public class DwdExamHistory {
    /**
     * 主键id
     */
    @TableField(value = "id")
    private String id;
    /**
     * 三方用户 id
     */
    @TableField(value = "third_user_id")
    private String thirdUserId;

    @TableField(value = "user_id")
    private String userId;

    /**
     * 机构 id
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 考试id
     */
    @TableField(value = "arrange_id")
    private String arrangeId;
    /**
     * 考试名称
     */
    @TableField(value = "arrange_name")
    private String arrangeName;
    /**
     * 考试状态，0-正考通过，1-补考通过，2-未通过，3-未提交，4-未批阅
     */
    @TableField(value = "uem_final_passed")
    private Integer uemFinalPassed;

    /**
     * 最高分考试提交记录ID
     */
    @TableField(value = "best_ue_id")
    private String bestUeId;

    /**
     * 维度类型：0：普通考试；1：循环考试；2：考试下的批次。 档案导入默认为0
     */
    @TableField(value = "dimension_type")
    private Integer dimensionType;

    /**
     * 考试类型，0、普通考试；1、项目考试；2、课程包考试；3、练习（practise）；4、人才发展；5、面授
     */
    @TableField(value = "arrange_type")
    private Integer arrangeType;

    /**
     * 批次考试所属循环考试ID， ote_arrange_batch.arrange_id
     */
    @TableField(value = "batch_arrange_id")
    private String batchArrangeId;

    /**
     * 最高分
     */
    @TableField(value = "highest_score")
    private BigDecimal highestScore;
    /**
     * 是否删除(0-否,1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

}
