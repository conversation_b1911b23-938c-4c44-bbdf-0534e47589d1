package com.yxt.talent.bk.svc.heir.enums;

import com.yxt.common.util.DateUtil;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.svc.heir.bean.RuleComparisonBean;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;
import java.util.function.Function;

@Getter
@AllArgsConstructor
public enum ComparisonOperator {
    /**
     * 比较符
     */
    EQUAL(1, "等于", compareBean -> {
        Object ruleValue = compareBean.getRuleValue();
        String userIndicatorValue = compareBean.getUserIndicatorValue();
        return doEqual(ruleValue, userIndicatorValue);
    }),
    NOT_EQUAL(2, "不等于", compareBean -> {
        Object ruleValue = compareBean.getRuleValue();
        String userIndicatorValue = compareBean.getUserIndicatorValue();
        return !doEqual(ruleValue, userIndicatorValue);
    }),
    GREATER_THAN(3, "大于", compareBean -> {
        Object ruleValue = compareBean.getRuleValue();
        String userIndicatorValue = compareBean.getUserIndicatorValue();
        boolean isSatisfied = false;
        if (ruleValue instanceof Number) {
            // 不论整形还是浮点型，都转成浮点型进行比较，使用BigDecimal比较浮点
            BigDecimal ruleBigDecimal = new BigDecimal(ruleValue.toString());
            BigDecimal userIndicatorBigDecimal = new BigDecimal(userIndicatorValue);
            isSatisfied = userIndicatorBigDecimal.compareTo(ruleBigDecimal) > 0;
        } else if (ruleValue instanceof String) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) > 0;
        } else if (ruleValue instanceof Boolean) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) > 0;
        } else if (ruleValue instanceof Date) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtil.formatDate((Date) ruleValue)) > 0 ||
                    userIndicatorValue.compareTo(DateUtil.formatSimpleDate((Date) ruleValue)) > 0;
        } else if (ruleValue instanceof LocalDateTime) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtils.formatDate((LocalDateTime) ruleValue)) > 0 ||
                    userIndicatorValue.compareTo(DateUtils.formatSimpleDate((LocalDateTime) ruleValue)) >
                            0;
        }
        return isSatisfied;
    }),
    LESS_THAN(4, "小于", compareBean -> {
        Object ruleValue = compareBean.getRuleValue();
        String userIndicatorValue = compareBean.getUserIndicatorValue();
        boolean isSatisfied = false;
        if (ruleValue instanceof Number) {
            // 不论整形还是浮点型，都转成浮点型进行比较，使用BigDecimal比较浮点
            BigDecimal ruleBigDecimal = new BigDecimal(ruleValue.toString());
            BigDecimal userIndicatorBigDecimal = new BigDecimal(userIndicatorValue);
            isSatisfied = userIndicatorBigDecimal.compareTo(ruleBigDecimal) < 0;
        } else if (ruleValue instanceof String) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) < 0;
        } else if (ruleValue instanceof Boolean) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) < 0;
        } else if (ruleValue instanceof Date) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtil.formatDate((Date) ruleValue)) < 0 ||
                    userIndicatorValue.compareTo(DateUtil.formatSimpleDate((Date) ruleValue)) < 0;
        } else if (ruleValue instanceof LocalDateTime) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtils.formatDate((LocalDateTime) ruleValue)) < 0 ||
                    userIndicatorValue.compareTo(DateUtils.formatSimpleDate((LocalDateTime) ruleValue)) <
                            0;
        }
        return isSatisfied;
    }),
    GREATER_THAN_OR_EQUAL(5, "大于等于", compareBean -> {
        Object ruleValue = compareBean.getRuleValue();
        String userIndicatorValue = compareBean.getUserIndicatorValue();
        boolean isSatisfied = false;
        if (ruleValue instanceof Number) {
            // 不论整形还是浮点型，都转成浮点型进行比较，使用BigDecimal比较浮点
            BigDecimal ruleBigDecimal = new BigDecimal(ruleValue.toString());
            BigDecimal userIndicatorBigDecimal = new BigDecimal(userIndicatorValue);
            isSatisfied = userIndicatorBigDecimal.compareTo(ruleBigDecimal) >= 0;
        } else if (ruleValue instanceof String) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) >= 0;
        } else if (ruleValue instanceof Boolean) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) >= 0;
        } else if (ruleValue instanceof Date) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtil.formatDate((Date) ruleValue)) >= 0 ||
                    userIndicatorValue.compareTo(DateUtil.formatSimpleDate((Date) ruleValue)) >= 0;
        } else if (ruleValue instanceof LocalDateTime) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtils.formatDate((LocalDateTime) ruleValue)) >= 0 ||
                    userIndicatorValue.compareTo(DateUtils.formatSimpleDate((LocalDateTime) ruleValue)) >=
                            0;
        }
        return isSatisfied;
    }),
    LESS_THAN_OR_EQUAL(6, "小于等于", compareBean -> {
        Object ruleValue = compareBean.getRuleValue();
        String userIndicatorValue = compareBean.getUserIndicatorValue();
        boolean isSatisfied = false;
        if (ruleValue instanceof Number) {
            // 不论整形还是浮点型，都转成浮点型进行比较，使用BigDecimal比较浮点
            BigDecimal ruleBigDecimal = new BigDecimal(ruleValue.toString());
            BigDecimal userIndicatorBigDecimal = new BigDecimal(userIndicatorValue);
            isSatisfied = userIndicatorBigDecimal.compareTo(ruleBigDecimal) <= 0;
        } else if (ruleValue instanceof String) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) <= 0;
        } else if (ruleValue instanceof Boolean) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) <= 0;
        } else if (ruleValue instanceof Date) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtil.formatDate((Date) ruleValue)) <= 0 ||
                    userIndicatorValue.compareTo(DateUtil.formatSimpleDate((Date) ruleValue)) <= 0;
        } else if (ruleValue instanceof LocalDateTime) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtils.formatDate((LocalDateTime) ruleValue)) <= 0 ||
                    userIndicatorValue.compareTo(DateUtils.formatSimpleDate((LocalDateTime) ruleValue)) <=
                            0;
        }
        return isSatisfied;
    }),
    CONTAINS_ANY(7, "包含其一", null),
    NOT_CONTAINS(8, "不包含", null),
    ALL_CONTAINS(9, "都包含", null),
    TEXT_CONTAINS(10, "文本包含", compareBean -> {
        Object ruleValue = compareBean.getRuleValue();
        String userIndicatorValue = compareBean.getUserIndicatorValue();
        boolean isSatisfied = false;
        if (ruleValue instanceof String) {
            isSatisfied = userIndicatorValue.contains(ruleValue.toString());
        }
        return isSatisfied;
    }),
    NOT_TEXT_MATCHES(11, "文本不包含", compareBean -> {
        Object ruleValue = compareBean.getRuleValue();
        String userIndicatorValue = compareBean.getUserIndicatorValue();
        boolean isSatisfied = false;
        if (ruleValue instanceof String) {
            isSatisfied = !userIndicatorValue.contains(ruleValue.toString());
        }
        return isSatisfied;
    });

    private final int code;
    private final String description;
    private final Function<RuleComparisonBean, Boolean> indicatorCompare;

    public static ComparisonOperator getByCode(Integer code) {
        for (ComparisonOperator comparisonOperator : ComparisonOperator.values()) {
            if (Objects.equals(comparisonOperator.getCode(), code)) {
                return comparisonOperator;
            }
        }
        return null;
    }

    /**
     * 是否是否定运算符
     */
    public boolean isNegative() {
        return this == NOT_EQUAL || this == NOT_CONTAINS || this == NOT_TEXT_MATCHES;
    }

    public boolean isNotContains() {
        return this == NOT_CONTAINS;
    }

    public boolean isAllContains() {
        return this == ALL_CONTAINS;
    }

    private static boolean doEqual(Object ruleValue, String userIndicatorValue) {
        boolean isSatisfied;
        if (ruleValue instanceof Number) {
            // 不论整形还是浮点型，都转成浮点型进行比较，使用BigDecimal比较浮点
            BigDecimal ruleBigDecimal = new BigDecimal(ruleValue.toString());
            BigDecimal userIndicatorBigDecimal = new BigDecimal(userIndicatorValue);
            isSatisfied = userIndicatorBigDecimal.compareTo(ruleBigDecimal) == 0;
        } else if (ruleValue instanceof String) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) == 0;
        } else if (ruleValue instanceof Boolean) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) == 0;
        } else if (ruleValue instanceof Date) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtil.formatDate((Date) ruleValue)) == 0 ||
                    userIndicatorValue.compareTo(DateUtil.formatSimpleDate((Date) ruleValue)) == 0;
        } else if (ruleValue instanceof LocalDateTime) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtils.formatDate((LocalDateTime) ruleValue)) == 0 ||
                    userIndicatorValue.compareTo(DateUtils.formatSimpleDate((LocalDateTime) ruleValue)) == 0;
        } else {
            isSatisfied = false;
        }
        return isSatisfied;
    }
}
