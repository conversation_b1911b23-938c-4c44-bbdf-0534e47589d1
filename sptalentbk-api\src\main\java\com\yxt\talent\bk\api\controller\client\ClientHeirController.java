package com.yxt.talent.bk.api.controller.client;

import cn.hutool.core.lang.Pair;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.bk.api.component.TalentSchemeComponent;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.usergroup.bean.Scheme4Search;
import com.yxt.talent.bk.core.usergroup.bean.SchemeBean;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.HeirPosUserService;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareDumpParam;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPosUserPrepareResp;
import com.yxt.talent.bk.svc.heir.bean.resp.OpenHeirPosResp;
import com.yxt.talent.bk.svc.heir.bean.resp.OpenHeirPosUserResp;
import com.yxt.talent.bk.svc.heir.component.HeirPosUserComponent;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description 继任学员端
 *
 * <AUTHOR>
 * @Date 2023/11/15 14:00
 **/

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/heir/")
@Tag(name = "继任学员端")
public class ClientHeirController extends BaseController {

    private final TalentSchemeComponent talentSchemeComponent;
    private final HeirPosService heirPosService;
    private final HeirPosUserComponent heirPosUserComponent;
    private final HeirPosUserService heirPosUserService;

    @Operation(summary = "部门成员列表")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/user/list", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public PagingList<SchemeBean> getUserBasic(HttpServletRequest request, @RequestBody Scheme4Search bean) {
        UserCacheBasic userCache = getUserCacheBasic();
        return talentSchemeComponent.findUser4Client(request, ApiUtil.getPageRequest(request), userCache, bean);
    }

    @Operation(summary = "继任brief列表(T+1)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/brief_list")
    public List<OpenHeirPosResp> allHeir(@RequestParam int posType,
                                         @RequestParam(required = false) String deptId) {
        TalentbkUtil.isUuid(deptId, "apis.talentbk.param.error");
        String orgId = getUserCacheDetail().getOrgId();
        return heirPosService.allHeir4Model(orgId, deptId, posType);
    }

    @Operation(summary = "(继任用户/标杆用户)brief列表(T+1)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/user_brief_list")
    public List<OpenHeirPosUserResp> heirUserList(@RequestParam String posId,
                                                  @RequestParam int userType,
                                                  @RequestParam(required = false) String deptId) {
        TalentbkUtil.isUuid(posId, "apis.talentbk.param.error");
        TalentbkUtil.isUuid(deptId, "apis.talentbk.param.error");
        String orgId = getUserCacheBasic().getOrgId();
        return heirPosService.heirUser4Model(orgId, posId, userType, deptId);
    }

    @Operation(summary = "用户继任准备度详情")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/user_prepare_detail")
    public HeirPosUserPrepareResp userPrepareDetail(@RequestParam String posId,
                                                    @RequestParam String userId) {
        String orgId = getUserCacheBasic().getOrgId();
        return heirPosUserComponent.userPrepareResp(orgId, posId, userId);
    }

    @Operation(summary = "用户继任准备度处理")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/user_prepare_handle")
    public void userPrepareHandle(@RequestParam String posId,
                                  @RequestParam String userId,
                                  @RequestParam(required = false) Long prepareLevelId) {
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheBasic());
        HeirPrepareDumpParam dumpParam = heirPosUserComponent.userPrepare4Dump(userBasic.getOrgId(), posId, userId);
        heirPosUserService.prepareUrgeHandle(userBasic, posId, userId, prepareLevelId, dumpParam);
    }
}
