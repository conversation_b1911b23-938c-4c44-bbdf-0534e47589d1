package com.yxt.talent.bk.svc.heir;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import com.yxt.spmodel.facade.bean.rule.ExecuteRuleVO;
import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import com.yxt.spmodel.facade.bean.rule.RuleFeignCreateParam;
import com.yxt.spmodel.facade.service.SpmodelIndicatorsService;
import com.yxt.spmodel.facade.service.SpmodelLabelService;
import com.yxt.spmodel.facade.service.SpmodelRuleService;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.heir.bean.HeirPrepareCfgDataBean;
import com.yxt.talent.bk.core.heir.bean.PosUserPrepareBean;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPrepareRemindUserEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosExt;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPrepareCfgMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPrepareRemindUserMapper;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.common.bean.BkLabelConditionBean;
import com.yxt.talent.bk.svc.heir.bean.CalcCycleBean;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareRuleBean;
import com.yxt.talent.bk.svc.heir.bean.PrepareLevelBean;
import com.yxt.talent.bk.svc.heir.bean.PrepareRuleCfgDTO;
import com.yxt.talent.bk.svc.heir.bean.req.PrepareCfgReq;
import com.yxt.talent.bk.svc.heir.bean.req.PrepareCycleReq;
import com.yxt.talent.bk.svc.heir.bean.resp.CycleAndRemindUserResp;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareCfgResp;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareResp;
import com.yxt.talent.bk.svc.heir.enums.CycleEnum;
import com.yxt.talent.bk.svc.heir.enums.LogicEnum;
import com.yxt.talent.bk.svc.mq.constant.HeirRocketMqConstants;
import com.yxt.talent.bk.svc.udp.rpc.UdpRpc;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class HeirPosPrepareCfgService {

    private final HeirPosPrepareCfgMapper heirPosPrepareCfgMapper;
    private final HeirPrepareRemindUserMapper heirPrepareRemindUserMapper;
    private final HeirPosUserMapper heirPosUserMapper;
    private final HeirPosRepository heirPosRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final HeirOrgLevelConfigService heirOrgLevelConfigService;
    private final AuthService authService;
    private final SpmodelLabelService spmodelLabelService;
    private final SpmodelIndicatorsService spmodelIndicatorsService;
    private final UdpRpc udpRpc;
    private final HeirPosService heirPosService;
    private final HeirPosMapper heirPosMapper;
    private final RocketMQTemplate rocketMQTemplate;
    private final ReadinessComputeService readinessComputeService;
    private final SpmodelRuleCalcService spmodelRuleCalcService;
    private final SpmodelRuleService spmodelRuleService;


    @DbHintMaster
    public String saveprepareCfg(UserCacheBasic userCacheBasic, PrepareCfgReq prepareCfgReq) {
        log.info("HeirPosPrepareCfgService saveprepareCfg start userCacheBasic:{},prepareCfgReq:{}",
            JSON.toJSONString(userCacheBasic), JSON.toJSONString(prepareCfgReq));
        String orgId = userCacheBasic.getOrgId();
        if (prepareCfgReq.getId() == null) {
            int posType = heirPosRepository.getPosType(prepareCfgReq.getPosId());
            //新增
            HeirPosPrepareCfgEntity heirPosPrepareCfgEntity = getHeirPosPrepareCfgEntity(userCacheBasic, prepareCfgReq, null);
            heirPosPrepareCfgEntity.setPosType(posType);
            heirPosPrepareCfgMapper.insert(heirPosPrepareCfgEntity);
            return heirPosPrepareCfgEntity.getId().toString();
        } else {
            HeirPosPrepareCfgEntity dbCfgEntity = heirPosPrepareCfgMapper.selectById(prepareCfgReq.getId());
            if (dbCfgEntity == null) {
                return StringPool.EMPTY;
            }
            Long cfgRuleId = dbCfgEntity.getRuleCfgId();
            String oldRuleCfgMd5 = dbCfgEntity.getRuleCfgMd5();
            if (StringUtils.isEmpty(oldRuleCfgMd5)) {
                oldRuleCfgMd5 = TalentbkUtil.labelRuleGroupMd5(Optional.ofNullable(tryGetRuleCfgId(dbCfgEntity.getRuleCfgId(), dbCfgEntity.getRuleCfgData()))
                        .map(queryRuleId -> spmodelRuleService.getRule(queryRuleId, orgId, TalentBkConstants.APP_CODE))
                        .map(ExecuteRuleVO::getRuleConfig).orElse(null));
            }
            //更新
            HeirPosPrepareCfgEntity heirPosPrepareCfgEntity = getHeirPosPrepareCfgEntity(userCacheBasic, prepareCfgReq, cfgRuleId);
            heirPosPrepareCfgEntity.setId(prepareCfgReq.getId());
            if (StringUtils.isNotEmpty(oldRuleCfgMd5) && !oldRuleCfgMd5.equals(heirPosPrepareCfgEntity.getRuleCfgMd5())) {
                heirPosUserMapper.resetModifyStatus(orgId, dbCfgEntity.getPosId(), dbCfgEntity.getPrepareLevelId());
            }
            heirPosPrepareCfgMapper.updateRuleCfgDataByPrimaryKey(heirPosPrepareCfgEntity);
            return prepareCfgReq.getId().toString();
        }
    }

    @DbHintMaster
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void saveprepareCycle(UserCacheBasic userCacheBasic, PrepareCycleReq prepareCycleReq) {
        log.info("HeirPosPrepareCfgService saveprepareCycle start userCacheBasic:{},prepareCycleReq:{}",
            JSON.toJSONString(userCacheBasic), JSON.toJSONString(prepareCycleReq));
        String orgId = userCacheBasic.getOrgId();
        HeirPosExt heirPosExt = heirPosMapper.getById(orgId, prepareCycleReq.getPosId());
        if (heirPosExt == null) {
            heirPosService.init(userCacheBasic,prepareCycleReq.getPosId(),1);
        }
        HeirPosEntity entity = new HeirPosEntity();
        entity.setId(prepareCycleReq.getPosId());
        entity.setOrgId(orgId);
        entity.setCalcCycle(prepareCycleReq.getCalcCycleBean() == null ? null : JSON.toJSONString(prepareCycleReq.getCalcCycleBean()));
        Date nextExecutionTime = getNextExecutionTime(prepareCycleReq.getCalcCycleBean());
        entity.setNextCalcTime(nextExecutionTime);
        entity.setRemindCycle(prepareCycleReq.getRemindCycleBean() == null ? null : JSON.toJSONString(prepareCycleReq.getRemindCycleBean()));
        Date nextExecutionRemindTime = getNextExecutionTime(prepareCycleReq.getRemindCycleBean());
        entity.setNextRemindTime(nextExecutionRemindTime);
        entity.init(userCacheBasic.getUserId());
        heirPosMapper.update(entity);
        heirPrepareRemindUserMapper.deleteUsers(orgId,prepareCycleReq.getPosId());
        List<String> reqUserIds = prepareCycleReq.getUserIds();
        if (CollectionUtils.isNotEmpty(reqUserIds)) {
            List<HeirPrepareRemindUserEntity> remindUserEntityList = Lists.newArrayList();
            for (String reqUserId : reqUserIds) {
                HeirPrepareRemindUserEntity remindUserEntity = new HeirPrepareRemindUserEntity();
                remindUserEntity.init(userCacheBasic.getOrgId(), userCacheBasic.getUserId());
                remindUserEntity.setPosId(prepareCycleReq.getPosId());
                remindUserEntity.setUserId(reqUserId);
                remindUserEntityList.add(remindUserEntity);
            }
            heirPrepareRemindUserMapper.batchInsert(remindUserEntityList);
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //如果发送时间是今天且时间是当前时间往后的，要发送mq
                SpringContextHolder.getBean(HeirPosPrepareCfgService.class)
                        .sendCycleMq(prepareCycleReq, nextExecutionTime, nextExecutionRemindTime);
            }
        });
    }

    @Async
    @DbHintMaster
    public void sendCycleMq(PrepareCycleReq prepareCycleReq, Date nextExecutionTime, Date nextExecutionRemindTime) {
        String format = DateUtil.format(nextExecutionTime, DatePattern.NORM_DATE_FORMAT);
        HeirPosEntity posEntity = heirPosMapper.selectById(prepareCycleReq.getPosId());
        String orgId = posEntity.getOrgId();
        log.info("sendCycleMq orgId:{},prepareCycleReq:{},nextExecutionTime:{},nextExecutionRemindTime:{}", orgId,
            JSON.toJSONString(prepareCycleReq), DateUtil.format(nextExecutionTime, DatePattern.NORM_DATETIME_FORMAT),
            DateUtil.format(nextExecutionRemindTime, DatePattern.NORM_DATETIME_FORMAT));
        if (DateUtil.today().equals(format) && DateUtil.date().before(nextExecutionTime)) {
            if (nextExecutionTime !=null && (nextExecutionTime.getTime()- System.currentTimeMillis())/1000< ReadinessComputeService.FLOAT_SEC) {
                log.info("sendCycleMq calculateReadinessRules posEntity:{}",JSON.toJSONString(posEntity));
                readinessComputeService.setComputeNextExecutionTime(posEntity);
            }else{
                log.info("sendCycleMq syncDelaySend posId:{},topic:{}",posEntity.getId(),HeirRocketMqConstants.TOPIC_HEIR_READINESS_COMPUTE);
                rocketMQTemplate.syncDelaySend(HeirRocketMqConstants.TOPIC_HEIR_READINESS_COMPUTE, JSON.toJSONString(posEntity),
                    readinessComputeService.calcDelayLevel(nextExecutionTime).getDelayLevel());
            }
        }
        String formatRemindTime = DateUtil.format(nextExecutionRemindTime, DatePattern.NORM_DATE_FORMAT);
        if (DateUtil.today().equals(formatRemindTime) && DateUtil.date().before(nextExecutionRemindTime)){
            if (nextExecutionRemindTime !=null && (nextExecutionRemindTime.getTime()- System.currentTimeMillis())/1000< ReadinessComputeService.FLOAT_SEC) {
                log.info("sendCycleMq sendPrepareChangeUser posEntity:{}",JSON.toJSONString(posEntity));
                readinessComputeService.sendPrepareChangeUser(posEntity);
                readinessComputeService.setRemindNextExecutionTime(posEntity);
            }else{
                log.info("sendCycleMq syncDelaySend posId:{},topic:{}",posEntity.getId(),HeirRocketMqConstants.TOPIC_HEIR_READINESS_REMIND);
                rocketMQTemplate.syncDelaySend(HeirRocketMqConstants.TOPIC_HEIR_READINESS_REMIND, JSON.toJSONString(posEntity),
                    readinessComputeService.calcDelayLevel(nextExecutionRemindTime).getDelayLevel());
            }
        }
    }

    @NotNull
    private HeirPosPrepareCfgEntity getHeirPosPrepareCfgEntity(UserCacheBasic userCacheBasic,
        PrepareCfgReq prepareCfgReq, Long cfgRuleId) {
        HeirPosPrepareCfgEntity heirPosPrepareCfgEntity = new HeirPosPrepareCfgEntity();
        heirPosPrepareCfgEntity.init(userCacheBasic.getOrgId(), userCacheBasic.getUserId());
        heirPosPrepareCfgEntity.setPosId(prepareCfgReq.getPosId());
        heirPosPrepareCfgEntity.setPrepareLevelId(prepareCfgReq.getPrepareLevelId());
        heirPosPrepareCfgEntity.setDeleted(YesOrNo.NO.getValue());
        PrepareRuleCfgDTO cfgDTO = new PrepareRuleCfgDTO();
        LabelConditionJsonBean ruleConfig = null;
        if (prepareCfgReq.getLabelRuleGroupBean() != null) {
            if (CollectionUtils.isEmpty(prepareCfgReq.getLabelRuleGroupBean().getConditions())) {
                //清空配置spmodelRuleService.updateRule调用会失败，兼容处理
                cfgDTO.setEmptyCfg(true);
            } else {
                if (prepareCfgReq.getLabelRuleGroupBean().getLogic() == null) {
                    prepareCfgReq.getLabelRuleGroupBean().setLogic(LogicEnum.AND.getCode());
                }
                IArrayUtils.forEach(prepareCfgReq.getLabelRuleGroupBean().getConditions(), labelCondition -> {
                    if (labelCondition.getLogic() == null) {
                        labelCondition.setLogic(LogicEnum.AND.getCode());
                    }
                });
                RuleFeignCreateParam param = new RuleFeignCreateParam();
                param.setOrgId(userCacheBasic.getOrgId());
                param.setUserId(userCacheBasic.getUserId());
                param.setAppCode(TalentBkConstants.APP_CODE);
                param.setRuleConfig(prepareCfgReq.getLabelRuleGroupBean());
                if (cfgRuleId == null) {
                    cfgRuleId = spmodelRuleService.createRule(param);
                } else {
                    spmodelRuleService.updateRule(cfgRuleId, param);
                }
                ruleConfig = param.getRuleConfig();
            }
        }
        cfgDTO.setId(cfgRuleId);
        heirPosPrepareCfgEntity.setRuleCfgId(cfgRuleId);
        heirPosPrepareCfgEntity.setRuleCfgData(JSON.toJSONString(cfgDTO));
        heirPosPrepareCfgEntity.setRuleCfgMd5(TalentbkUtil.labelRuleGroupMd5(ruleConfig));
        return heirPosPrepareCfgEntity;
    }

    /**
     * 获取规则id
     * @param cfgRuleId
     * @param ruleCfgData
     * @return
     */
    public Long tryGetRuleCfgId(Long cfgRuleId, String ruleCfgData) {
        if (cfgRuleId == null) {
            return null;
        }
        if (StringUtils.isNotEmpty(ruleCfgData) && JSON.parseObject(ruleCfgData, PrepareRuleCfgDTO.class).isEmptyCfg()) {
            return null;
        }
        return cfgRuleId;
    }

    private Date getNextExecutionTime(CalcCycleBean calcCycleBean) {
        if (StringUtils.isBlank(calcCycleBean.getRemindTime())) {
            calcCycleBean.setRemindTime("00:00:00");
        }
        if (CycleEnum.EVERYDAY.getCode().equals(calcCycleBean.getCycle())) {
            //每天执行
            Date todayDate = getTodayDate(calcCycleBean.getRemindTime());
            if (todayDate.getTime() < System.currentTimeMillis()) {
                return getTargetDateTime(calcCycleBean, DateUtil.tomorrow());
            } else {
                return getTargetDateTime(calcCycleBean, DateUtil.date());
            }
        } else if (CycleEnum.EVERYWEEK.getCode().equals(calcCycleBean.getCycle())) {
            //当天是周几
            int count = DateUtil.dayOfWeek(DateUtil.date());
            // 周日=1 周一=2 周二=3 。。 周六=7
            //处理成正常的周几
            int day = count - 1;
            if (day == 0) {
                day = 7;
            }
            if (day == calcCycleBean.getDay()) {
                //如果设置的是当天
                Date todayDate = getTodayDate(calcCycleBean.getRemindTime());
                if (todayDate.getTime() < System.currentTimeMillis()) {
                    //过了执行时间，拿下周的时间
                    return DateUtil.offsetWeek(todayDate, 1);
                } else {
                    //没过执行时间
                    return getTargetDateTime(calcCycleBean, DateUtil.date());
                }
            } else if (day < calcCycleBean.getDay()) {
                //如果设置的时间在今天后面
                Date todayDate = getTodayDate(calcCycleBean.getRemindTime());
                return DateUtil.offsetDay(todayDate, calcCycleBean.getDay() - day);
            } else {
                //如果设置的时间在今天之前
                Date todayDate = getTodayDate(calcCycleBean.getRemindTime());
                return DateUtil.offsetDay(todayDate, 7 - (day - calcCycleBean.getDay()));
            }
        } else if (CycleEnum.EVERYMONTH.getCode().equals(calcCycleBean.getCycle())) {
            //每月执行
            //今天是几号
            int dayOfMonth = DateUtil.thisDayOfMonth();
            if (dayOfMonth == calcCycleBean.getDay()) {
                Date todayDate = getTodayDate(calcCycleBean.getRemindTime());
                if (todayDate.getTime() < System.currentTimeMillis()) {
                    //过了执行时间，拿下月的时间
                    return DateUtil.offsetMonth(todayDate, 1);
                } else {
                    //没过执行时间,就是今天的执行时间
                    return todayDate;
                }
            } else if (dayOfMonth < calcCycleBean.getDay()) {
                //取当月
                return getCurrentMonthDate(calcCycleBean);
            } else {
                //取下月
                return DateUtil.offsetMonth(getCurrentMonthDate(calcCycleBean), 1);
            }
        }
        return getTargetDateTime(calcCycleBean, DateUtil.date());
    }

    @NotNull
    private static DateTime getCurrentMonthDate(CalcCycleBean calcCycleBean) {
        String format = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_FORMAT);
        String substring = format.substring(0, format.lastIndexOf("-"));
        if (calcCycleBean.getDay() < 10) {
            substring = substring + "-0" + calcCycleBean.getDay();
        } else {
            substring = substring + "-" + calcCycleBean.getDay();
        }
        DateTime parse = DateUtil
            .parse(substring + " " + calcCycleBean.getRemindTime(), DatePattern.NORM_DATETIME_FORMAT);
        return parse;
    }

    @NotNull
    private DateTime getTargetDateTime(CalcCycleBean calcCycleBean, Date date) {
        String dateStr = DateUtil.format(date, DatePattern.NORM_DATE_FORMAT) + " " + calcCycleBean.getRemindTime();
        return DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_FORMAT);
    }

    private Date getTodayDate(String remindTime) {
        String today = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_FORMAT) + " " + remindTime;
        return DateUtil.parse(today, DatePattern.NORM_DATETIME_FORMAT);
    }

    public HeirPrepareCfgResp getPrepareCfgDetail(String orgId, String posId, Long prepareLevelId) {
        log.info("HeirPosPrepareCfgService getPrepareCfgDetail start orgId:{},posId:{},prepareLevelId:{}", orgId, posId,
            prepareLevelId);
        List<HeirPrepareResp> heirPrepareData = heirOrgLevelConfigService.getHeirPrepareData(orgId);
        if (CollectionUtils.isEmpty(heirPrepareData)) {
            return null;
        }
        if (prepareLevelId == null) {
            return null;
        }
        HeirPosPrepareCfgEntity prepareCfgDetail = heirPosPrepareCfgMapper
            .getPrepareCfgDetail(orgId, posId, prepareLevelId);
        HeirPrepareCfgResp heirPrepareCfgResp = new HeirPrepareCfgResp();
        heirPrepareCfgResp.setPrepareLevelId(prepareLevelId);
        setPrepareLevelBeanList(orgId,heirPrepareData,heirPrepareCfgResp);
        if (prepareCfgDetail == null) {
            return heirPrepareCfgResp;
        }
        Long ruleCfgId = tryGetRuleCfgId(prepareCfgDetail.getRuleCfgId(), prepareCfgDetail.getRuleCfgData());
        if (ruleCfgId != null) {
            ExecuteRuleVO ruleVO = spmodelRuleService.getRule(ruleCfgId, orgId, TalentBkConstants.APP_CODE);
            if (ruleVO != null) {
                heirPrepareCfgResp.setLabelRuleGroupBean(ruleVO.getRuleConfig());
            }
        }
        BeanCopierUtil.copy(prepareCfgDetail, heirPrepareCfgResp, false);
        return heirPrepareCfgResp;
    }

    public CycleAndRemindUserResp getCycleAndRemindUser(UserCacheBasic userCacheBasic, String posId) {
        log.info("HeirPosPrepareCfgService getDetailFirst start,userCacheBasic:{},posId:{}", userCacheBasic, posId);
        String orgId = userCacheBasic.getOrgId();
        HeirPosEntity posEntity = heirPosMapper.selectById(posId);
        if (posEntity == null || posEntity.getDeleted()==1) {
            return null;
        }
        CycleAndRemindUserResp cycleAndRemindUserResp = new CycleAndRemindUserResp();
        cycleAndRemindUserResp.setId(posId);
        cycleAndRemindUserResp.setCalcCycleBean(posEntity.getCalcCycle()==null?null:JSON.parseObject(posEntity.getCalcCycle(),CalcCycleBean.class));
        cycleAndRemindUserResp.setRemindCycleBean(posEntity.getRemindCycle()==null?null:JSON.parseObject(posEntity.getRemindCycle(),CalcCycleBean.class));
        List<UdpUserBriefBean> userBriefBeans = BeanCopierUtil
            .convertList(heirPrepareRemindUserMapper.selectUserIdsByPrepareCfgId(posId), posUser -> {
                UdpUserBriefBean userBrief = new UdpUserBriefBean();
                userBrief.setId(posUser.getUserId());
                return userBrief;
            });
        udpLiteUserRepository.fillUserInfo(orgId, userBriefBeans, UdpUserBriefBean::getId, (userBrief, udpUser) -> {
            userBrief.setUsername(udpUser.getUsername());
            userBrief.setFullname(udpUser.getFullname());
        }, Lists.newArrayList("fullname", "username"));
        cycleAndRemindUserResp.setUdpUserBriefBeans(userBriefBeans);
        return cycleAndRemindUserResp;
    }

    private void setPrepareLevelBeanList(String orgId, List<HeirPrepareResp> heirPrepareData, HeirPrepareCfgResp prepareCfgDetail) {
        log.info("setPrepareLevelBeanList start");
        Locale locale = authService.getLocale();
        List<Long> plIds = heirPrepareData.stream().map(HeirPrepareResp::getId).collect(Collectors.toList());
        List<Long> list = heirPosPrepareCfgMapper.selectContainPrepareCfgDetail(orgId, plIds);
        log.info("setPrepareLevelBeanList list:{}", JSON.toJSONString(list));
        List<PrepareLevelBean> prepareLevelBeanList = Lists.newArrayList();
        for (HeirPrepareResp heirPrepare : heirPrepareData) {
            PrepareLevelBean prepareLevelBean = new PrepareLevelBean();
            prepareLevelBean.setPrepareLevelId(heirPrepare.getId());
            prepareLevelBean.setPrepareLevelStatus(0);
            if (Locale.SIMPLIFIED_CHINESE.equals(locale)) {
                prepareLevelBean.setPrepareLevelName(heirPrepare.getLevelName1());
            } else if (Locale.TRADITIONAL_CHINESE.equals(locale)) {
                prepareLevelBean.setPrepareLevelName(heirPrepare.getLevelName3());
            } else if (Locale.ENGLISH.equals(locale)) {
                prepareLevelBean.setPrepareLevelName(heirPrepare.getLevelName2());
            } else{
                prepareLevelBean.setPrepareLevelName(heirPrepare.getLevelName1());
            }
            prepareLevelBeanList.add(prepareLevelBean);
        }
        IArrayUtils.forEach(list, plId -> {
            for (PrepareLevelBean prepareLevelBean : prepareLevelBeanList) {
                if (prepareLevelBean.getPrepareLevelId().equals(plId)) {
                    prepareLevelBean.setPrepareLevelStatus(1);
                }
            }
        });
        prepareCfgDetail.setPrepareLevelBeanList(prepareLevelBeanList);
        Optional<HeirPrepareResp> first = heirPrepareData.stream()
            .filter(heirPrepareResp -> heirPrepareResp.getId().equals(prepareCfgDetail.getPrepareLevelId()))
            .findFirst();
        if (Locale.SIMPLIFIED_CHINESE.equals(locale)) {
            prepareCfgDetail.setPrepareLevelName(first.map(HeirPrepareResp::getLevelName1).orElse(null));
        } else if (Locale.TRADITIONAL_CHINESE.equals(locale)) {
            prepareCfgDetail.setPrepareLevelName(first.map(HeirPrepareResp::getLevelName3).orElse(null));
        } else if (Locale.ENGLISH.equals(locale)) {
            prepareCfgDetail.setPrepareLevelName(first.map(HeirPrepareResp::getLevelName2).orElse(null));
        } else{
            prepareCfgDetail.setPrepareLevelName(first.map(HeirPrepareResp::getLevelName1).orElse(null));
        }
    }

    public void calcPosUserPrepare(String orgId, String posId, int posType) {
        List<IdName> posNames;
        if (posType == HeirPosTypeEnum.POSITION.getType()) {
            posNames = udpRpc.queryPositionNameByIds(orgId, Lists.newArrayList(posId));
        } else {
            posNames = udpRpc.queryDeptNameByIds(orgId, Lists.newArrayList(posId));
        }
        if (CollectionUtils.isEmpty(posNames)) {
            log.info("calcPosUserPrepare posId is not exist posId {} posType {}", posId, posType);
            return;
        }
        newCalcPrepareId(orgId, posId);
    }

    private void newCalcPrepareId(String orgId, String posId) {
        List<HeirPrepareResp> prepareList = heirOrgLevelConfigService.getHeirPrepareData(orgId);
        if (CollectionUtils.isEmpty(prepareList)) {
            return;
        }
        //按顺序组装出准备等级对应的配置
        List<HeirPrepareCfgDataBean> cfgList = heirPosPrepareCfgMapper.getPrepareCfgData(orgId, posId,
            prepareList.stream().map(HeirPrepareResp::getId).collect(Collectors.toList()));
        List<HeirPrepareRuleBean> calcList = new ArrayList<>();
        for (HeirPrepareResp levelBriefBean : prepareList) {
            HeirPrepareCfgDataBean cfgData = IArrayUtils
                .getFirstMatch(cfgList, item -> levelBriefBean.getId().equals(item.getPrepareLevelId()));
            if (cfgData != null && cfgData.getRuleCfgId() != null) {
                Long ruleCfgId = tryGetRuleCfgId(cfgData.getRuleCfgId(), cfgData.getRuleCfgData());
                if (ruleCfgId == null) {
                    continue;
                }
                ExecuteRuleVO ruleVO = spmodelRuleService.getRule(cfgData.getRuleCfgId(), orgId, TalentBkConstants.APP_CODE);
                if (ruleVO == null || ruleVO.getRuleConfig() == null) {
                    continue;
                }
                HeirPrepareRuleBean ruleBean = new HeirPrepareRuleBean();
                ruleBean.setOrderIndex(levelBriefBean.getOrderIndex());
                ruleBean.setPrepareLevelId(levelBriefBean.getId());
                ruleBean.setRuleCondition(ruleVO.getRuleConfig());
                calcList.add(ruleBean);
            }
        }
        List<PosUserPrepareBean> posUsers = heirPosUserMapper.posValidUserPrepare(orgId, posId);
        if (CollectionUtils.isEmpty(posUsers)) {
            log.info("calcPosUserPrepare orgId {} posId {} noPosUser", orgId, posId);
            return;
        }
        calcPrepareIdAndSave(orgId, posUsers, calcList);
    }

    private void calcPrepareIdAndSave(String orgId,
                                      List<PosUserPrepareBean> posUsers,
                                      List<HeirPrepareRuleBean> calcList) {
        BatchOperationUtil.batchExecute(posUsers, 200, batchUser -> {
            for (HeirPrepareRuleBean ruleBean : calcList) {
                //等级从高到低，高的满足了，就不需要计算后续的
                List<PosUserPrepareBean> needCalcUsers = batchUser.stream()
                        .filter(item -> !item.isCalcMatched()).collect(Collectors.toList());
                spmodelRuleCalcService.calcRuleMatch(orgId, BkLabelConditionBean.createBy(ruleBean.getRuleCondition()),
                        needCalcUsers, PosUserPrepareBean::getUserId, (posUser, matched) -> {
                            if (matched == null) {
                                return;
                            }
                            posUser.setCalcMatched(matched);
                            if (matched && (posUser.getCalcLevelId() == null || !ruleBean.getPrepareLevelId()
                                    .equals(posUser.getCalcLevelId()))) {
                                //如果计算值和数据库不一致则标记为需要更新
                                posUser.setCalcLevelId(ruleBean.getPrepareLevelId());
                                posUser.setNeedUpdate(true);
                            }
                        });
            }
            List<PosUserPrepareBean> updateBatchUser = batchUser.stream()
                    .filter(posUser -> {
                        if (!posUser.isCalcMatched()) {
                            //没一个匹配则calcLevelId设为null
                            posUser.setCalcLevelId(null);
                        }
                        return posUser.isNeedUpdate() || !posUser.isCalcMatched();
                    })
                    .collect(Collectors.toList());
            if (!updateBatchUser.isEmpty()) {
                heirPosUserMapper.batchUpdateUserPrepare(orgId, updateBatchUser);
            }
        });
    }
}
