package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.svc.heir.bean.CfgValueBean;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.Size;

@Data
@Schema(name = "继任风险规则")
public class HeirRiskReq {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "序号")
    @Range(min = 1, message = "apis.talentbk.userGroup.args.error")
    private Integer orderIndex;

    @Schema(description = "风险等级描述（简体）")
    @Size(min = 1, max = 200, message = "apis.talentbk.userGroup.args.error")
    private String levelName1;

    @Schema(description = "风险等级描述（英文）")
    @Size(max = 200, message = "apis.talentbk.userGroup.args.error")
    private String levelName2;

    @Schema(description = "风险等级描述（繁体）")
    @Size(max = 200, message = "apis.talentbk.userGroup.args.error")
    private String levelName3;

    @Schema(description = "备注")
    @Size(max = 200, message = "apis.talentbk.userGroup.args.error")
    private String remark;

    @Schema(description = "颜色设置")
    private String colorCode;

    @Schema(description = "自动判定规则")
    private CfgValueBean cfgValueBean;
}
