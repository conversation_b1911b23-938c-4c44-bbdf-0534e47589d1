package com.yxt.talent.bk.svc.heir.component;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spmodel.facade.bean.rule.ExecuteRuleVO;
import com.yxt.spmodel.facade.service.SpmodelRuleService;
import com.yxt.spsdk.udpbase.QueryUdpUtils;
import com.yxt.spsdk.udpbase.bean.UdpUserBriefBean;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.core.heir.bean.HeirPrepareCfgDataBean;
import com.yxt.talent.bk.core.heir.bean.HeirUserPosInfo;
import com.yxt.talent.bk.core.heir.entity.HeirPrepareHisEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPrepareCfgMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPrepareHisMapper;
import com.yxt.talent.bk.core.heir.mapper.PackageDataMapper;
import com.yxt.talent.bk.core.heir.repo.PackageDataRepository;
import com.yxt.talent.bk.svc.common.bean.BkLabelConditionBean;
import com.yxt.talent.bk.svc.common.bean.BkLabelRuleInfo;
import com.yxt.talent.bk.svc.heir.HeirOrgLevelConfigService;
import com.yxt.talent.bk.svc.heir.HeirPosPrepareCfgService;
import com.yxt.talent.bk.svc.heir.SpmodelRuleCalcService;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepare4Dump;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareDumpParam;
import com.yxt.talent.bk.svc.heir.bean.HeirUserPrepareMatch4Dump;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPosUserPrepareResp;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepare4Resp;
import com.yxt.talent.bk.svc.heir.enums.PkgBizTypeEnum;
import com.yxt.talent.bk.svc.heir.enums.PosUserModifyStatusEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * HeirPosUserComponent
 *
 * <AUTHOR> harleyge
 * @Date 25/7/24 2:20 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirPosUserComponent {
    private final HeirOrgLevelConfigService heirOrgLevelConfigService;
    private final HeirPosPrepareCfgService heirPosPrepareCfgService;
    private final HeirPrepareHisMapper heirPrepareHisMapper;
    private final PackageDataMapper packageDataMapper;
    private final HeirPosPrepareCfgMapper heirPosPrepareCfgMapper;
    private final HeirPosUserMapper heirPosUserMapper;
    private final PackageDataRepository packageDataRepository;
    private final SpmodelRuleService spmodelRuleService;
    private final SpmodelRuleCalcService spmodelRuleCalcService;
    private final AuthService authService;

    public HeirPrepareDumpParam userPrepare4Dump(String orgId, String posId, String userId) {
        HeirUserPosInfo userPosInfo = heirPosUserMapper.userPosPrepareInfo(posId, userId);
        if (userPosInfo == null) {
            throw new ApiException(BkApiErrorKeys.HEIR_USER_DELETE);
        }
        if (!PosUserModifyStatusEnum.INIT.getValue().equals(userPosInfo.getModifyStatus())) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_HEIR_USER_PREPARE_HANDLED);
        }
        HeirPrepareDumpParam dumpParam = new HeirPrepareDumpParam();
        HeirUserPrepareMatch4Dump userMatchResult = new HeirUserPrepareMatch4Dump();
        userMatchResult.setUserMatchList(new ArrayList<>());
        List<HeirPrepare4Dump> retList = BeanCopierUtil.convertList(heirOrgLevelConfigService.getHeirPrepareData(orgId), prepareCfg -> {
            HeirPrepare4Dump prepare = new HeirPrepare4Dump();
            prepare.setId(prepareCfg.getId());
            prepare.setLevelName1(prepareCfg.getLevelName1());
            prepare.setLevelName2(prepareCfg.getLevelName2());
            prepare.setLevelName3(prepareCfg.getLevelName3());
            prepare.setColorCode(prepareCfg.getColorCode());
            return prepare;
        });
        if (CollectionUtils.isNotEmpty(retList)) {
            List<HeirPrepareCfgDataBean> cfgList = heirPosPrepareCfgMapper.getPrepareCfgData(orgId, posId,
                    retList.stream().map(HeirPrepare4Dump::getId).collect(Collectors.toList()));
            for (HeirPrepare4Dump prepare4Dump : retList) {
                HeirPrepareCfgDataBean cfgData = IArrayUtils
                        .getFirstMatch(cfgList, item -> prepare4Dump.getId().equals(item.getPrepareLevelId()));
                BkLabelConditionBean ruleConfig = tryGetRuleCfg(orgId, cfgData);
                if (ruleConfig != null) {
                    prepare4Dump.setRuleConfig(ruleConfig);
                    spmodelRuleCalcService.calcRuleMatch(orgId, ruleConfig, Lists.newArrayList(userId), item -> item, (queryUserId, matched) -> {
                        prepare4Dump.setLevelMatched(CommonUtils.bool2Int(matched));
                    });
                }
                prepare4Dump.forEachRule(ruleInfo -> {
                    BkLabelRuleInfo ruleMatchResult = new BkLabelRuleInfo();
                    ruleMatchResult.setId(ruleInfo.getId());
                    ruleMatchResult.setUserMatchRule(ruleInfo.getUserMatchRule());
                    ruleMatchResult.setUserValues(ruleInfo.getUserValues());
                    userMatchResult.getUserMatchList().add(ruleMatchResult);
                    //清除用户匹配结果
                    ruleInfo.setUuid(null);
                    ruleInfo.setUserMatchRule(null);
                    ruleInfo.setUserValues(null);
                });
            }
            boolean matched = false;
            for (HeirPrepare4Dump prepare4Resp : retList) {
                if (!matched && prepare4Resp.getLevelMatched() == YesOrNo.YES.getValue()) {
                    matched = true;
                    //第一个匹配的就是最高级的准备度
                    userMatchResult.setPrepareLevelId(prepare4Resp.getId());
                }
                //清除LevelMatched值，retList只存规则相关数据
                prepare4Resp.setLevelMatched(YesOrNo.NO.getValue());
            }
        }
        Long cfgDataId = packageDataRepository.savePackage(orgId, PkgBizTypeEnum.HEIR_RULE_CFG.getCode(), posId,
                JSON.toJSONString(retList));
        dumpParam.setRuleDataId(cfgDataId);
        dumpParam.setRuleMatchResult(JSON.toJSONString(userMatchResult));
        dumpParam.setPrepareLevelIds(BeanCopierUtil.convertList(retList, HeirPrepare4Dump::getId));
        return dumpParam;
    }

    public HeirPosUserPrepareResp userPrepareResp(String orgId, String posId, String userId) {
        Locale locale = authService.getLocale();
        HeirPosUserPrepareResp resp = new HeirPosUserPrepareResp();
        UdpUserBriefBean userInfo = QueryUdpUtils.getSpUserInfo(orgId, userId,
                UdpUserBriefBean::getUsername, UdpUserBriefBean::getFullname,
                UdpUserBriefBean::getSex,UdpUserBriefBean::getImgUrl,
                UdpUserBriefBean::getDeptId,UdpUserBriefBean::getDeptName,
                UdpUserBriefBean::getPositionId, UdpUserBriefBean::getPositionName);
        resp.setUserInfo(userInfo);
        HeirUserPosInfo userPosInfo = heirPosUserMapper.userPosPrepareInfo(posId, userId);
        if (userPosInfo == null) {
            throw new ApiException(BkApiErrorKeys.HEIR_USER_DELETE);
        }
        resp.setPosType(userPosInfo.getPosType());
        resp.setPosName(userPosInfo.getPosName());
        resp.setPrepareLevelId(userPosInfo.getPrepareLevelId());
        resp.setModifyStatus(userPosInfo.getModifyStatus());
        if (PosUserModifyStatusEnum.INIT.getValue().equals(userPosInfo.getModifyStatus())) {
            //还没确认修改
            resp.setPrepareList(BeanCopierUtil.convertList(heirOrgLevelConfigService.getHeirPrepareData(orgId), prepareCfg -> {
                HeirPrepare4Resp prepare = new HeirPrepare4Resp();
                prepare.setId(prepareCfg.getId());
                prepare.setLevelName(prepareCfg.queryLevelName(locale));
                prepare.setColorCode(prepareCfg.getColorCode());
                return prepare;
            }));
            calcPrepareLevelMatch(orgId, posId, userId, resp.getPrepareList());
        } else {
            HeirPrepareHisEntity changeHis = heirPrepareHisMapper.recentHis(posId, userId);
            //已确认修改
            if (changeHis != null) {
                resp.setPrepareLevelId(changeHis.getUserLevelId());
                List<HeirPrepare4Dump> prepareList = CommonUtils.parseArray(packageDataMapper.getDataById(changeHis.getRuleDataId()), HeirPrepare4Dump.class);
                HeirUserPrepareMatch4Dump userMatchResult = CommonUtils.parseObject(packageDataMapper.getDataById(changeHis.getMatchDataId()),
                        HeirUserPrepareMatch4Dump.class, new HeirUserPrepareMatch4Dump());
                //暂不调整时，设置为原来的levelId，确认调整就用调整的levelId
                Long setPrepareLevelId = changeHis.getChangeLevelId() != null ? changeHis.getChangeLevelId() : changeHis.getUserLevelId();
                Map<Long, BkLabelRuleInfo> matchRuleMap = CommonUtils.list2map(userMatchResult.getUserMatchList(), BkLabelRuleInfo::getId);
                prepareList.forEach(prepare -> prepare.forEachRule(rule -> {
                    BkLabelRuleInfo ruleMatch = matchRuleMap.get(rule.getId());
                    if (ruleMatch != null) {
                        rule.setUserValues(ruleMatch.getUserValues());
                        rule.setUserMatchRule(ruleMatch.getUserMatchRule());
                    }
                }));
                resp.setPrepareList(BeanCopierUtil.convertList(prepareList, prepareDump -> {
                    HeirPrepare4Resp prepare = new HeirPrepare4Resp();
                    prepare.setId(prepareDump.getId());
                    prepare.setLevelName(prepareDump.queryLevelName(locale));
                    prepare.setColorCode(prepareDump.getColorCode());
                    prepare.setRuleConfig(prepareDump.getRuleConfig());
                    prepare.setLevelMatched(CommonUtils.bool2Int(Objects.equals(prepareDump.getId(), setPrepareLevelId)));
                    return prepare;
                }));
                //操作人信息填充
                Optional.ofNullable(QueryUdpUtils.getSpUserInfo(orgId, changeHis.getOptUserId(),
                        UdpUserBriefBean::getUsername, UdpUserBriefBean::getFullname)).ifPresent(optUser -> {
                    resp.setOptUsername(optUser.getUsername());
                    resp.setOptFullname(optUser.getFullname());
                });
                resp.setOptTime(changeHis.getCreateTime());
            }
        }
        //需求要反转顺序，并剔除没有配置的准备度
        IArrayUtils.remove(resp.getPrepareList(), prepare -> prepare.getRuleConfig() == null);
        if (resp.getPrepareList() != null) {
            Collections.reverse(resp.getPrepareList());
        }
        return resp;
    }

    private void calcPrepareLevelMatch(String orgId, String posId, String userId, List<HeirPrepare4Resp> prepareList) {
        if (CollectionUtils.isEmpty(prepareList)) {
            return;
        }
        List<HeirPrepareCfgDataBean> cfgList = heirPosPrepareCfgMapper.getPrepareCfgData(orgId, posId,
                prepareList.stream().map(HeirPrepare4Resp::getId).collect(Collectors.toList()));
        for (HeirPrepare4Resp prepare4Resp : prepareList) {
            HeirPrepareCfgDataBean cfgData = IArrayUtils
                    .getFirstMatch(cfgList, item -> prepare4Resp.getId().equals(item.getPrepareLevelId()));
            BkLabelConditionBean ruleConfig = tryGetRuleCfg(orgId, cfgData);
            if (ruleConfig != null) {
                prepare4Resp.setRuleConfig(ruleConfig);
                spmodelRuleCalcService.calcRuleMatch(orgId, ruleConfig, Lists.newArrayList(userId), item -> item, (queryUserId, matched) -> {
                    prepare4Resp.setLevelMatched(CommonUtils.bool2Int(matched));
                });
            }
        }
        boolean matched = false;
        for (HeirPrepare4Resp prepare4Resp : prepareList) {
            if (matched) {
                prepare4Resp.setLevelMatched(YesOrNo.NO.getValue());
            } else if (prepare4Resp.getLevelMatched() == YesOrNo.YES.getValue()) {
                matched = true;
            }
        }
    }

    private BkLabelConditionBean tryGetRuleCfg(String orgId, HeirPrepareCfgDataBean cfgData) {
        if (cfgData != null && cfgData.getRuleCfgId() != null) {
            Long ruleCfgId = heirPosPrepareCfgService.tryGetRuleCfgId(cfgData.getRuleCfgId(), cfgData.getRuleCfgData());
            if (ruleCfgId == null) {
                return null;
            }
            ExecuteRuleVO ruleVO = spmodelRuleService.getRule(cfgData.getRuleCfgId(), orgId, TalentBkConstants.APP_CODE);
            if (ruleVO == null || ruleVO.getRuleConfig() == null) {
                return null;
            }
            return BkLabelConditionBean.createBy(ruleVO.getRuleConfig());
        }
        return null;
    }
}
