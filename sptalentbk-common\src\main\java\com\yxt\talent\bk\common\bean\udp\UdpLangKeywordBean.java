package com.yxt.talent.bk.common.bean.udp;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * UdpKeywordQuery
 *
 * <AUTHOR> harleyge
 * @Date 7/5/24 1:52 pm
 */
@Data
public class UdpLangKeywordBean {
    private boolean enabled;
    private List<String> userIds;
    private List<String> positionIds;

    public UdpLangKeywordBean rebuildIdsIfEnabled(boolean forUser, boolean forPosition) {
        if (enabled) {
            if (forUser && CollectionUtils.isEmpty(userIds)) {
                userIds = Lists.newArrayList("miss0000-0000-0000-0000-000000000000");
            }
            if (forPosition && CollectionUtils.isEmpty(positionIds)) {
                positionIds = Lists.newArrayList("miss0000-0000-0000-0000-000000000000");
            }
        }
        return this;
    }
}
