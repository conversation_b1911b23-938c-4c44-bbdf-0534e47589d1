<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.usergroup.mapper.UserDeptUgroupMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup">
        <!--@mbg.generated-->
        <!--@Table bk_user_dept_ugroup-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="dept_id" jdbcType="CHAR" property="deptId"/>
        <result column="ugroup_id" jdbcType="BIGINT" property="ugroupId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        org_id,
        user_id,
        dept_id,
        ugroup_id,
        deleted,
        create_user_id,
        create_time,
        update_user_id,
        update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from bk_user_dept_ugroup
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from bk_user_dept_ugroup
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup">
        <!--@mbg.generated-->
        insert into bk_user_dept_ugroup (id, org_id, user_id, dept_id,
                                         ugroup_id, deleted, create_user_id,
                                         create_time, update_user_id, update_time)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=CHAR}, #{userId,jdbcType=CHAR},
                #{deptId,jdbcType=CHAR},
                #{ugroupId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT},
                #{createUserId,jdbcType=CHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR},
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective"
            parameterType="com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup">
        <!--@mbg.generated-->
        insert into bk_user_dept_ugroup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orgId != null and orgId != ''">
                org_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id,
            </if>
            <if test="ugroupId != null">
                ugroup_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null and orgId != ''">
                #{orgId,jdbcType=CHAR},
            </if>
            <if test="userId != null and userId != ''">
                #{userId,jdbcType=CHAR},
            </if>
            <if test="deptId != null and deptId != ''">
                #{deptId,jdbcType=CHAR},
            </if>
            <if test="ugroupId != null">
                #{ugroupId,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUserId != null and createUserId != ''">
                #{createUserId,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup">
        <!--@mbg.generated-->
        update bk_user_dept_ugroup
        <set>
            <if test="orgId != null and orgId != ''">
                org_id = #{orgId,jdbcType=CHAR},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=CHAR},
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id = #{deptId,jdbcType=CHAR},
            </if>
            <if test="ugroupId != null">
                ugroup_id = #{ugroupId,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup">
        <!--@mbg.generated-->
        update bk_user_dept_ugroup
        set org_id         = #{orgId,jdbcType=CHAR},
            user_id        = #{userId,jdbcType=CHAR},
            dept_id        = #{deptId,jdbcType=CHAR},
            ugroup_id      = #{ugroupId,jdbcType=BIGINT},
            deleted        = #{deleted,jdbcType=TINYINT},
            create_user_id = #{createUserId,jdbcType=CHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            update_user_id = #{updateUserId,jdbcType=CHAR},
            update_time    = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bk_user_dept_ugroup
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="ugroup_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.ugroupId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into bk_user_dept_ugroup
        (id, org_id, user_id, dept_id, ugroup_id, deleted, create_user_id, create_time,
         update_user_id,
         update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=CHAR}, #{item.userId,jdbcType=CHAR},
             #{item.deptId,jdbcType=CHAR}, #{item.ugroupId,jdbcType=BIGINT},
             #{item.deleted,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup">
        <!--@mbg.generated-->
        insert into bk_user_dept_ugroup
        (id, org_id, user_id, dept_id, ugroup_id, deleted, create_user_id, create_time,
         update_user_id,
         update_time)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=CHAR}, #{userId,jdbcType=CHAR},
                #{deptId,jdbcType=CHAR},
                #{ugroupId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT},
                #{createUserId,jdbcType=CHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR},
                #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update id             = #{id,jdbcType=BIGINT},
                                org_id         = #{orgId,jdbcType=CHAR},
                                user_id        = #{userId,jdbcType=CHAR},
                                dept_id        = #{deptId,jdbcType=CHAR},
                                ugroup_id      = #{ugroupId,jdbcType=BIGINT},
                                deleted        = #{deleted,jdbcType=TINYINT},
                                create_user_id = #{createUserId,jdbcType=CHAR},
                                create_time    = #{createTime,jdbcType=TIMESTAMP},
                                update_user_id = #{updateUserId,jdbcType=CHAR},
                                update_time    = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective"
            parameterType="com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup">
        <!--@mbg.generated-->
        insert into bk_user_dept_ugroup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orgId != null and orgId != ''">
                org_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id,
            </if>
            <if test="ugroupId != null">
                ugroup_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null and orgId != ''">
                #{orgId,jdbcType=CHAR},
            </if>
            <if test="userId != null and userId != ''">
                #{userId,jdbcType=CHAR},
            </if>
            <if test="deptId != null and deptId != ''">
                #{deptId,jdbcType=CHAR},
            </if>
            <if test="ugroupId != null">
                #{ugroupId,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUserId != null and createUserId != ''">
                #{createUserId,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null and orgId != ''">
                org_id = #{orgId,jdbcType=CHAR},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=CHAR},
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id = #{deptId,jdbcType=CHAR},
            </if>
            <if test="ugroupId != null">
                ugroup_id = #{ugroupId,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectByOrgIdAndId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from bk_user_dept_ugroup
        where org_id = #{orgId,jdbcType=CHAR}
          and id = #{id,jdbcType=BIGINT}
          and deleted = 0
    </select>

    <select id="countByUserIdAndDeptId" resultType="int">
        select count(*)
        from bk_user_dept_ugroup a
        where a.org_id = #{orgId,jdbcType=CHAR}
        and a.user_id = #{userId,jdbcType=CHAR}
        and a.dept_id = #{deptId,jdbcType=CHAR}
        and a.deleted = 0
    </select>

    <select id="selectStatisticByUserIdAndDeptId"
            resultType="com.yxt.talent.bk.core.usergroup.bean.UserDeptUgroup4Get">
        select
          a.id, b.group_name as name, count(distinct c.user_id) as userCnt
        from bk_user_dept_ugroup a
            left join bk_user_group b on a.ugroup_id = b.id and b.deleted = 0
            left join bk_user_group_member c on c.group_id = b.id and c.deleted = 0
        where a.org_id = #{orgId,jdbcType=CHAR}
          and a.user_id = #{userId,jdbcType=CHAR}
          and a.dept_id = #{deptId,jdbcType=CHAR}
          and a.deleted = 0
        group by a.id, b.group_name
    </select>

    <select id="selectByUserIdAndDeptId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from bk_user_dept_ugroup
        where org_id = #{orgId,jdbcType=CHAR}
          and user_id = #{userId,jdbcType=CHAR}
          and dept_id = #{deptId,jdbcType=CHAR}
          and deleted = 0
    </select>
</mapper>
