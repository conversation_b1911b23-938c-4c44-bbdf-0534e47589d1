package com.yxt.talent.bk.core.spmodel.mapper;

import com.yxt.talent.bk.core.dashboard.bean.PersonalEvalModelSimpleVO;
import com.yxt.talent.bk.core.spmodel.entity.DwdUserEvalSkillRt;
import com.yxt.talent.bk.core.spmodel.entity.DwdUserIndicatorResult;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 学员能力结果数据(DwdUserEvalSkillRt)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 17:34:43
 */
public interface DwdUserEvalSkillRtMapper {

    List<String> queryModelIdByUserId(@Param("orgId") String orgId, @Param("userId") String userId);

    List<DwdUserEvalSkillRt> queryByUserIdAndModelId(@Param("orgId") String orgId, @Param("userId") String userId,
            @Param("modelId") String modelId);

    List<PersonalEvalModelSimpleVO> getUsedSkillModelListByUserId(@Param("orgId") String orgId,
                                                                  @Param("userId") String userId);

    List<DwdUserIndicatorResult> queryAllByUserIdAndModelIds(@Param("orgId") String orgId,
                                                             @Param("userId") String userId,
                                                             @Param("modelIds") Collection<String> modelIds);

}

