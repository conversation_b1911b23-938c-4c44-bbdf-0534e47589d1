package com.yxt.talent.bk.svc.tag.bean;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.common.util.StreamUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户贴标签bean
 *
 * <AUTHOR>
 * @since 2022/8/8
 */
@Data
@NoArgsConstructor
public class UserTagBean {

    @Schema(description = "用户id列表")
    @NotEmpty(message = "")
    private List<String> userIds;

    @Schema(description = "标签信息")
    private List<TagBean> tagBeanList = Lists.newArrayList();

    @Schema(description = "待删除标签信息")
    private List<TagBean> delTagBeanList;

    /**
     * 获取所有tagValueId
     *
     * @return 标签值id列表
     */
    public Set<String> listTagValueId() {
        if (CollectionUtils.isNotEmpty(tagBeanList)) {
            Set<String> tagValueIdSet = Sets.newConcurrentHashSet();
            tagBeanList.parallelStream().forEach(tagBean -> tagValueIdSet.addAll(tagBean.getTagValueIdList()));
            return tagValueIdSet;
        }
        return Sets.newHashSet();
    }

    /**
     * 获取待删除标签id集合
     *
     * @return 待删除标签集合
     */
    public Set<String> listDelTagId() {
        if (CollectionUtils.isNotEmpty(delTagBeanList)) {
            return StreamUtil.map2set(delTagBeanList, TagBean::getTagId);
        }
        return Sets.newHashSet();
    }

    /**
     * 获取待删除标签map
     *
     * @return map
     */
    public Map<String, TagBean> getDelTagBeanMap() {
        if (CollectionUtils.isNotEmpty(delTagBeanList)) {
            return StreamUtil.list2map(delTagBeanList, TagBean::getTagId);
        }
        return Maps.newHashMap();
    }

    /**
     * 获取所有标签id集合
     *
     * @return 标签id集合
     */
    public Set<String> listAllTagId() {
        Set<String> set = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(delTagBeanList)) {
            set.addAll(StreamUtil.map2set(delTagBeanList, TagBean::getTagId));
        }
        if (CollectionUtils.isNotEmpty(tagBeanList)) {
            set.addAll(StreamUtil.map2set(tagBeanList, TagBean::getTagId));
        }
        return set;
    }

}
