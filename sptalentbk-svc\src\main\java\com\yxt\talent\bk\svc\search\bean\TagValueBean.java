package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Schema(name = "分层标签值")
public class TagValueBean {

    @Schema(description = "分层标签id")
    private String tagValueId;

    @Schema(description = "分层标签名称")
    private String tagValueName;
}
