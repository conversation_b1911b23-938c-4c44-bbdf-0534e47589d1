package com.yxt.talent.bk.core.tag.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TagItemFilterBean {

    private String id;

    private String orgId;

    private String itemKey;
    /**
     * 标签名称
     */
    private String itemName;

    /**
     * 标签分类名称
     */
    private String itemCatalogName;

    /**
     * 排序
     */
    private int orderIndex;

    /**
     * 标签表中的标签名称
     */
    private String tagRealName;
}
