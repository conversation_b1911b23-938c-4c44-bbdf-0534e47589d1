package com.yxt.talent.bk.core.usergroup.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.usergroup.entity.SearchSetting;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SearchSettingMapper extends BaseMapper<SearchSetting> {
    int updateBatch(List<SearchSetting> list);

    int updateBatchSelective(List<SearchSetting> list);

    int batchInsert(@Param("list") List<SearchSetting> list);

    int insertOrUpdate(SearchSetting record);

    int insertOrUpdateSelective(SearchSetting record);
}