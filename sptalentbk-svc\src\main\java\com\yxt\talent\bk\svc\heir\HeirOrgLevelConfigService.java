package com.yxt.talent.bk.svc.heir;

import com.alibaba.fastjson.JSON;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.enums.LangCodeEnum;
import com.yxt.talent.bk.common.enums.LevelTypeEnum;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.core.component.RedisComponent;
import com.yxt.talent.bk.core.heir.entity.HeirLangI18nEntity;
import com.yxt.talent.bk.core.heir.entity.HeirOrgConfigEntity;
import com.yxt.talent.bk.core.heir.entity.HeirOrgLevelCfgEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirLangI18nMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirOrgLevelCfgMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import com.yxt.talent.bk.core.heir.repo.HeirOrgConfigRepository;
import com.yxt.talent.bk.svc.heir.bean.CfgValueBean;
import com.yxt.talent.bk.svc.heir.bean.req.HeirPrepareReq;
import com.yxt.talent.bk.svc.heir.bean.req.HeirRiskReq;
import com.yxt.talent.bk.svc.heir.bean.req.LevelConfigReq;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareResp;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirRiskResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class HeirOrgLevelConfigService {

    private final HeirOrgLevelCfgMapper heirOrgLevelCfgMapper;
    private final HeirLangI18nMapper heirLangI18nMapper;
    private final HeirPosUserMapper heirPosUserMapper;
    private final HeirOrgConfigRepository heirOrgConfigRepository;
    private final RedisComponent redisComponent;

    @DbHintMaster
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void saveLevelConfig(UserCacheBasic userCacheBasic, LevelConfigReq levelConfigReq,boolean flag) {
        String key = String.format(TalentBkRedisKeys.CACHE_KEY_SAVE_CONFIG_BASE, userCacheBasic.getOrgId());
        CommonUtils.tryLockRun(key,0,8,TimeUnit.SECONDS,()->{
            log.info("HeirOrgLevelConfigService saveLevelConfig,userCacheBasic:{},levelConfigReq:{}",
                JSON.toJSONString(userCacheBasic), JSON.toJSONString(userCacheBasic));
            String orgId = userCacheBasic.getOrgId();
            String userId = userCacheBasic.getUserId();
            List<HeirPrepareReq> heirPrepareReqList = levelConfigReq.getHeirPrepareReqList();
            //校验名称
            checkPrepareReqLevelName(heirPrepareReqList);
            List<HeirRiskReq> heirRiskReqList = levelConfigReq.getHeirRiskReqList();
            //校验名称
            checkRiskReqLevelName(heirRiskReqList);
            //处理学员端配置
            HeirOrgConfigEntity heirOrgConfigEntity = heirOrgConfigRepository.getByOrgId(orgId);
            if (Objects.isNull(heirOrgConfigEntity)) {
                HeirOrgConfigEntity config = new HeirOrgConfigEntity();
                config.init(orgId,userId);
                config.setShowFlag(0);
                heirOrgConfigRepository.insert(config);
            }
            if (flag) {
                HeirOrgConfigEntity config = new HeirOrgConfigEntity();
                config.setOrgId(orgId);
                config.setShowFlag(levelConfigReq.getShowFlag());
                heirOrgConfigRepository.updateShowFlag(config);
                //清除缓存
                redisComponent.removeKey(String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_CLIENT_SHOW, orgId));
            }
            List<HeirOrgLevelCfgEntity> allRiskList = Lists.newArrayList();
            List<HeirOrgLevelCfgEntity> saveList = Lists.newArrayList();
            List<HeirOrgLevelCfgEntity> updateList = Lists.newArrayList();
            List<HeirLangI18nEntity> i18nEntities = Lists.newArrayList();
            IArrayUtils.forEach(heirPrepareReqList, heirPrepareReq -> {
                if (heirPrepareReq.getId() != null) {
                    HeirOrgLevelCfgEntity heirOrgLevelCfgEntity = new HeirOrgLevelCfgEntity();
                    heirOrgLevelCfgEntity.setId(heirPrepareReq.getId());
                    heirOrgLevelCfgEntity.setColorCode(heirPrepareReq.getColorCode());
                    heirOrgLevelCfgEntity.setRemark(heirPrepareReq.getRemark());
                    heirOrgLevelCfgEntity.setOrderIndex(heirPrepareReq.getOrderIndex());
                    heirOrgLevelCfgEntity.init(orgId, userId);
                    log.info("HeirOrgLevelConfigService heirOrgLevelCfgEntity:{}",JSON.toJSONString(heirOrgLevelCfgEntity));
                    heirOrgLevelCfgMapper.updateById(heirOrgLevelCfgEntity);
                    heirLangI18nMapper.deleteByGroupId(orgId, heirPrepareReq.getId());
                    log.info("HeirOrgLevelConfigService deleteByGroupId over,orgId:{},groupId:{}", orgId,
                            heirPrepareReq.getId());
                    extracted(orgId, userId, i18nEntities, heirPrepareReq);
                }else{
                    HeirOrgLevelCfgEntity heirOrgLevelCfgEntity = new HeirOrgLevelCfgEntity();
                    heirOrgLevelCfgEntity.init(orgId, userId);
                    heirOrgLevelCfgEntity.setOrderIndex(heirPrepareReq.getOrderIndex());
                    heirOrgLevelCfgEntity.setLevelType(LevelTypeEnum.PREPARE.getType());
                    heirOrgLevelCfgEntity.setLevelName("");
                    heirOrgLevelCfgEntity.setColorCode(heirPrepareReq.getColorCode());
                    heirOrgLevelCfgEntity.setRemark(heirPrepareReq.getRemark());
                    heirOrgLevelCfgEntity.setDeleted(YesOrNo.NO.getValue());
                    saveList.add(heirOrgLevelCfgEntity);
                    heirPrepareReq.setId(heirOrgLevelCfgEntity.getId());
                    extracted(orgId, userId, i18nEntities, heirPrepareReq);
                }
            });
            IArrayUtils.forEach(heirRiskReqList, heirRiskReq -> {
                if (heirRiskReq.getId() != null) {
                    HeirOrgLevelCfgEntity heirOrgLevelCfgEntity = new HeirOrgLevelCfgEntity();
                    heirOrgLevelCfgEntity.setId(heirRiskReq.getId());
                    heirOrgLevelCfgEntity.setRemark(heirRiskReq.getRemark());
                    heirOrgLevelCfgEntity.setColorCode(heirRiskReq.getColorCode());
                    heirOrgLevelCfgEntity.setCfgValue(Optional.ofNullable(heirRiskReq.getCfgValueBean())
                            .map(JSON::toJSONString).orElse(null));
                    heirOrgLevelCfgEntity.setOrderIndex(heirOrgLevelCfgMapper.getOrderIndexById(heirRiskReq.getId()));
                    heirOrgLevelCfgEntity.init(orgId, userId);
                    log.info("HeirOrgLevelConfigService heirRiskReqList not empty heirOrgLevelCfgEntity:{}",
                            JSON.toJSONString(heirOrgLevelCfgEntity));
                    updateList.add(heirOrgLevelCfgEntity);
                    allRiskList.add(heirOrgLevelCfgEntity);
                    heirLangI18nMapper.deleteByGroupId(orgId, heirRiskReq.getId());
                    log.info(
                            "HeirOrgLevelConfigService heirRiskReqList not empty deleteByGroupId over,orgId:{},groupId:{}",
                            orgId, heirRiskReq.getId());
                    extracted(orgId, userId, i18nEntities, heirRiskReq);
                }else{
                    HeirOrgLevelCfgEntity heirOrgLevelCfgEntity = new HeirOrgLevelCfgEntity();
                    heirOrgLevelCfgEntity.init(orgId, userId);
                    heirOrgLevelCfgEntity.setOrderIndex(heirRiskReq.getOrderIndex());
                    heirOrgLevelCfgEntity.setLevelType(LevelTypeEnum.RISK.getType());
                    heirOrgLevelCfgEntity.setLevelName("");
                    heirOrgLevelCfgEntity.setColorCode(heirRiskReq.getColorCode());
                    heirOrgLevelCfgEntity.setRemark(heirRiskReq.getRemark());
                    heirOrgLevelCfgEntity.setCfgValue(Optional.ofNullable(heirRiskReq.getCfgValueBean())
                            .map(JSON::toJSONString).orElse(null));
                    heirOrgLevelCfgEntity.setDeleted(YesOrNo.NO.getValue());
                    saveList.add(heirOrgLevelCfgEntity);
                    allRiskList.add(heirOrgLevelCfgEntity);
                    heirRiskReq.setId(heirOrgLevelCfgEntity.getId());
                    extracted(orgId, userId, i18nEntities, heirRiskReq);
                }
            });
            calcRiskRange(allRiskList);
            updateList.forEach(heirOrgLevelCfgMapper::updateById);
            log.info("HeirOrgLevelConfigService saveList:{}", JSON.toJSONString(saveList));
            BatchOperationUtil.batchSave(saveList, heirOrgLevelCfgMapper::batchInsert);
            log.info("HeirOrgLevelConfigService i18nEntities:{}", JSON.toJSONString(i18nEntities));
            BatchOperationUtil.batchSave(i18nEntities, heirLangI18nMapper::batchInsert);
        });
    }

    public void fixRiskRange(String orgId) {
        List<HeirOrgLevelCfgEntity> heirRiskData = heirOrgLevelCfgMapper.getHeirPrepareData(orgId,LevelTypeEnum.RISK.getType());
        calcRiskRange(heirRiskData);
        for (HeirOrgLevelCfgEntity heirRisk : heirRiskData) {
            HeirOrgLevelCfgEntity updateRiskRange = new HeirOrgLevelCfgEntity();
            updateRiskRange.setId(heirRisk.getId());
            updateRiskRange.setNumRangeMax(heirRisk.getNumRangeMax());
            updateRiskRange.setNumRangeMin(heirRisk.getNumRangeMin());
            updateRiskRange.setUpdateTime(DateUtil.currentTime());
            heirOrgLevelCfgMapper.updateById(updateRiskRange);
        }
    }

    private void calcRiskRange(List<HeirOrgLevelCfgEntity> heirRiskReqList) {
        if (CollectionUtils.isEmpty(heirRiskReqList)) {
            return;
        }
        //范围从小到大
        IArrayUtils.sortListDesc(heirRiskReqList, HeirOrgLevelCfgEntity::getOrderIndex);
        Map<Integer, BigDecimal> riskPtgMap = new HashMap<>(heirRiskReqList.size());
        for (int i = 0; i < heirRiskReqList.size(); i++) {
            Integer riskPtgIdx = i;
            Optional.ofNullable(JSON.parseObject(heirRiskReqList.get(i).getCfgValue(), CfgValueBean.class))
                    .map(CfgValueBean::getValue).ifPresent(riskPtg -> riskPtgMap.put(riskPtgIdx, riskPtg));
        }
        for (int i = 0; i < heirRiskReqList.size(); i++) {
            HeirOrgLevelCfgEntity cfgEntity = heirRiskReqList.get(i);
            cfgEntity.setNumRangeMin(riskPtgMap.get(i));
            if (i >= heirRiskReqList.size() - 1) {
                //设置最大值,字段DECIMAL(8,2)
                cfgEntity.setNumRangeMax(BigDecimal.valueOf(99_9999));
            } else {
                cfgEntity.setNumRangeMax(riskPtgMap.get(i + 1));
            }
        }
    }

    public Integer heirClientShow(String orgId) {
        String cacheKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_CLIENT_SHOW, orgId);
        return redisComponent.cacheValue(cacheKey, () -> {
            HeirOrgConfigEntity heirOrgConfigEntity = heirOrgConfigRepository.getByOrgId(orgId);
            return Optional.ofNullable(heirOrgConfigEntity).map(HeirOrgConfigEntity::getShowFlag)
                    .orElse(YesOrNo.NO.getValue());
        }, Integer.class, 1, TimeUnit.HOURS);
    }

    private void checkRiskReqLevelName(List<HeirRiskReq> heirRiskReqList) {
        if (CollectionUtils.isNotEmpty(heirRiskReqList) && heirRiskReqList.size() >= 2) {
            List<String> list1 = heirRiskReqList.stream().map(HeirRiskReq::getLevelName1)
                .collect(Collectors.toList());
            doCheckLevelName(list1, BkApiErrorKeys.POOL_RISK_CANNOT_SAME_LEVEL_NAME);
            List<String> list2 = heirRiskReqList.stream().map(HeirRiskReq::getLevelName2)
                .filter(levelName -> StringUtils.isNotBlank(levelName)).collect(Collectors.toList());
            if (list2.size()>=2) {
                doCheckLevelName(list2, BkApiErrorKeys.POOL_RISK_CANNOT_SAME_LEVEL_NAME);
            }
            List<String> list3 = heirRiskReqList.stream().map(HeirRiskReq::getLevelName3)
                .filter(levelName -> StringUtils.isNotBlank(levelName)).collect(Collectors.toList());
            if (list3.size()>=2) {
                doCheckLevelName(list3, BkApiErrorKeys.POOL_RISK_CANNOT_SAME_LEVEL_NAME);
            }
            List<BigDecimal> collect = heirRiskReqList.stream().filter(riskReq -> riskReq.getCfgValueBean() != null)
                .map(riskReq -> riskReq.getCfgValueBean().getValue()).filter(Objects::nonNull)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect) && collect.size()>=2) {
                doCheckCfgValue(collect);
            }
        }
    }

    private void doCheckCfgValue(List<BigDecimal> list) {
        for (int i = 0; i < list.size()-1; i++) {
            BigDecimal temp = list.get(i);
            for (int j = i+1; j < list.size(); j++){
                if (temp.compareTo(list.get(j))==0) {
                    throw new ApiException(BkApiErrorKeys.POOL_RISK_CANNOT_SAME_VALUE);
                }
            }
        }
    }

    private void checkPrepareReqLevelName(List<HeirPrepareReq> heirPrepareReqList) {
        if (CollectionUtils.isNotEmpty(heirPrepareReqList) && heirPrepareReqList.size() >= 2) {
            List<String> list1 = heirPrepareReqList.stream().map(HeirPrepareReq::getLevelName1)
                .collect(Collectors.toList());
            doCheckLevelName(list1, BkApiErrorKeys.POOL_PREPARE_CANNOT_SAME_LEVEL_NAME);
            List<String> list2 = heirPrepareReqList.stream().map(HeirPrepareReq::getLevelName2)
                .filter(levelName -> StringUtils.isNotBlank(levelName)).collect(Collectors.toList());
            if (list2.size()>=2) {
                doCheckLevelName(list2, BkApiErrorKeys.POOL_PREPARE_CANNOT_SAME_LEVEL_NAME);
            }
            List<String> list3 = heirPrepareReqList.stream().map(HeirPrepareReq::getLevelName3)
                .filter(levelName -> StringUtils.isNotBlank(levelName)).collect(Collectors.toList());
            if (list3.size()>=2) {
                doCheckLevelName(list3, BkApiErrorKeys.POOL_PREPARE_CANNOT_SAME_LEVEL_NAME);
            }
        }
    }

    private void doCheckLevelName(List<String> list,String exceptionKey) {
        for (int i = 0; i < list.size()-1; i++) {
            String temp = list.get(i);
            for (int j = i+1; j < list.size(); j++){
                if (StringUtils.equals(temp,list.get(j))) {
                    throw new ApiException(exceptionKey);
                }
            }
        }
    }

    private void extracted(String orgId, String userId, List<HeirLangI18nEntity> i18nEntities,
        HeirRiskReq heirRiskReq) {
        if (StringUtils.isNotBlank(heirRiskReq.getLevelName1())) {
            HeirLangI18nEntity heirLangI18nEntity = getHeirLangI18nEntity(orgId, userId,
                heirRiskReq.getId(), heirRiskReq.getLevelName1(),LangCodeEnum.CN.getType());
            i18nEntities.add(heirLangI18nEntity);
        }
        if (StringUtils.isNotBlank(heirRiskReq.getLevelName2())) {
            HeirLangI18nEntity heirLangI18nEntity = getHeirLangI18nEntity(orgId, userId,
                heirRiskReq.getId(), heirRiskReq.getLevelName2(),LangCodeEnum.EN.getType());
            i18nEntities.add(heirLangI18nEntity);
        }
        if (StringUtils.isNotBlank(heirRiskReq.getLevelName3())) {
            HeirLangI18nEntity heirLangI18nEntity = getHeirLangI18nEntity(orgId, userId,
                heirRiskReq.getId(), heirRiskReq.getLevelName3(),LangCodeEnum.TC.getType());
            i18nEntities.add(heirLangI18nEntity);
        }
    }

    private void extracted(String orgId, String userId, List<HeirLangI18nEntity> i18nEntities,
        HeirPrepareReq heirPrepareReq) {
        log.info("extracted orgId:{},userId:{},i18nEntities:{},heirPrepareReq:{}", orgId, userId,
            JSON.toJSONString(i18nEntities), JSON.toJSONString(heirPrepareReq));
        if (StringUtils.isNotBlank(heirPrepareReq.getLevelName1())) {
            HeirLangI18nEntity heirLangI18nEntity = getHeirLangI18nEntity(orgId, userId,
                heirPrepareReq.getId(), heirPrepareReq.getLevelName1(),LangCodeEnum.CN.getType());
            i18nEntities.add(heirLangI18nEntity);
        }
        if (StringUtils.isNotBlank(heirPrepareReq.getLevelName2())) {
            HeirLangI18nEntity heirLangI18nEntity = getHeirLangI18nEntity(orgId, userId,
                heirPrepareReq.getId(), heirPrepareReq.getLevelName2(),LangCodeEnum.EN.getType());
            i18nEntities.add(heirLangI18nEntity);
        }
        if (StringUtils.isNotBlank(heirPrepareReq.getLevelName3())) {
            HeirLangI18nEntity heirLangI18nEntity = getHeirLangI18nEntity(orgId, userId,
                heirPrepareReq.getId(), heirPrepareReq.getLevelName3(),LangCodeEnum.TC.getType());
            i18nEntities.add(heirLangI18nEntity);
        }
        log.info("extracted i18nEntities:{}",JSON.toJSONString(i18nEntities));
    }

    @NotNull
    private HeirLangI18nEntity getHeirLangI18nEntity(String orgId, String userId,
        Long groupId,String levelName,String langCode) {
        HeirLangI18nEntity heirLangI18nEntity = new HeirLangI18nEntity();
        heirLangI18nEntity.init(orgId, userId);
        heirLangI18nEntity.setGroupId(groupId);
        heirLangI18nEntity.setLangCode(langCode);
        heirLangI18nEntity.setLangValue(levelName);
        return heirLangI18nEntity;
    }

    public HeirPrepareResp getHeirPrepareData(String orgId, Long prepareId) {
        if (prepareId == null) {
            return null;
        }
        List<HeirPrepareResp> prepareList = getHeirPrepareData(orgId);
        return IArrayUtils.getFirstMatch(prepareList, item -> prepareId.equals(item.getId()));
    }
    public List<HeirPrepareResp> getHeirPrepareData(String orgId) {
        log.info("HeirOrgLevelConfigService getHeirPrepareData start");
        List<HeirPrepareResp> list = Lists.newArrayList();
        List<HeirOrgLevelCfgEntity> heirPrepareData = heirOrgLevelCfgMapper.getHeirPrepareData(
            orgId,LevelTypeEnum.PREPARE.getType());
        if (CollectionUtils.isNotEmpty(heirPrepareData)) {
            List<Long> ids = heirPrepareData.stream().map(HeirOrgLevelCfgEntity::getId)
                .collect(Collectors.toList());
            List<HeirLangI18nEntity> i18nEntities = heirLangI18nMapper.selectByGroupIds(orgId, ids);
            for (HeirOrgLevelCfgEntity heirOrgLevelCfgEntity : heirPrepareData) {
                HeirPrepareResp heirPrepareResp = new HeirPrepareResp();
                BeanCopierUtil.copy(heirOrgLevelCfgEntity,heirPrepareResp,false);
                List<HeirLangI18nEntity> collect = i18nEntities.stream()
                    .filter(heirLangI18nEntity -> heirLangI18nEntity.getGroupId().equals(heirOrgLevelCfgEntity.getId()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    for (HeirLangI18nEntity heirLangI18nEntity : collect) {
                        if (LangCodeEnum.CN.getType().equals(heirLangI18nEntity.getLangCode())) {
                            heirPrepareResp.setLevelName1(heirLangI18nEntity.getLangValue());
                        }else if (LangCodeEnum.EN.getType().equals(heirLangI18nEntity.getLangCode())) {
                            heirPrepareResp.setLevelName2(heirLangI18nEntity.getLangValue());
                        }else if (LangCodeEnum.TC.getType().equals(heirLangI18nEntity.getLangCode())) {
                            heirPrepareResp.setLevelName3(heirLangI18nEntity.getLangValue());
                        }
                    }
                }
                list.add(heirPrepareResp);
            }
            return list;
        }else{
            //生成默认的准备度规则
            createConfig(orgId);
            String key = String.format(TalentBkRedisKeys.CACHE_KEY_SAVE_CONFIG_BASE, orgId);
            return CommonUtils.tryLockReturn(key,8,8,TimeUnit.SECONDS,()-> {
                return getHeirPrepareData(orgId);
            });
        }
    }

    private void createConfig(String orgId) {
        LevelConfigReq levelConfigReq = new LevelConfigReq();
        List<HeirPrepareReq> heirPrepareReqList = Lists.newArrayList();
        List<HeirRiskReq> heirRiskReqList = Lists.newArrayList();
        heirPrepareReqList.add(getHeirPrepareReq(1, "准备就绪", "Ready", "準備就緒", "#52C41A"));
        heirPrepareReqList.add(getHeirPrepareReq(2, "高准备度", "High readiness", "高準備度", "#436BFF"));
        heirPrepareReqList.add(getHeirPrepareReq(3, "中准备度", "In readiness", "中準備度", "#FA8C16"));
        heirPrepareReqList.add(getHeirPrepareReq(4, "低准备度", "Low readiness", "低準備度", "#BFBFBF"));
        heirRiskReqList.add(getHeirRiskReq(1,"低","low","#52C41A",new BigDecimal("80")));
        heirRiskReqList.add(getHeirRiskReq(2,"中","normal","#FA8C16",new BigDecimal("40")));
        heirRiskReqList.add(getHeirRiskReq(3,"高","high","#F5222D",new BigDecimal("0")));
        levelConfigReq.setHeirPrepareReqList(heirPrepareReqList);
        levelConfigReq.setHeirRiskReqList(heirRiskReqList);
        UserCacheBasic userCacheBasic = new UserCacheBasic();
        userCacheBasic.setOrgId(orgId);
        userCacheBasic.setUserId(TalentBkConstants.CODE_OPERATOR_ID);
        saveLevelConfig(userCacheBasic,levelConfigReq,false);
    }

    private HeirRiskReq getHeirRiskReq(int sortIndex, String levelName,String english, String colorCode, BigDecimal bigDecimal) {
        HeirRiskReq heirRiskReq = new HeirRiskReq();
        heirRiskReq.setOrderIndex(sortIndex);
        heirRiskReq.setLevelName1(levelName);
        heirRiskReq.setLevelName2(english);
        heirRiskReq.setLevelName3(levelName);
        heirRiskReq.setColorCode(colorCode);
        heirRiskReq.setRemark("");
        CfgValueBean cfgValueBean = new CfgValueBean();
        cfgValueBean.setValue(bigDecimal);
        heirRiskReq.setCfgValueBean(cfgValueBean);
        return heirRiskReq;
    }

    private HeirPrepareReq getHeirPrepareReq(int sortIndex, String levelName1, String levelName2, String levelName3,
        String colorCode) {
        HeirPrepareReq heirPrepareReq = new HeirPrepareReq();
        heirPrepareReq.setOrderIndex(sortIndex);
        heirPrepareReq.setLevelName1(levelName1);
        heirPrepareReq.setLevelName2(levelName2);
        heirPrepareReq.setLevelName3(levelName3);
        heirPrepareReq.setColorCode(colorCode);
        heirPrepareReq.setRemark("");
        return heirPrepareReq;
    }

    public List<HeirRiskResp> getHeirRiskData(String orgId) {
        log.info("HeirOrgLevelConfigService getHeirRiskData start");
        List<HeirRiskResp> list = Lists.newArrayList();
        List<HeirOrgLevelCfgEntity> heirRiskData = heirOrgLevelCfgMapper.getHeirPrepareData(orgId,LevelTypeEnum.RISK.getType());
        if (CollectionUtils.isNotEmpty(heirRiskData)) {
            List<Long> ids = heirRiskData.stream().map(HeirOrgLevelCfgEntity::getId)
                .collect(Collectors.toList());
            List<HeirLangI18nEntity> i18nEntities = heirLangI18nMapper.selectByGroupIds(orgId, ids);
            for (HeirOrgLevelCfgEntity heirOrgLevelCfgEntity : heirRiskData) {
                HeirRiskResp heirRiskResp = new HeirRiskResp();
                BeanCopierUtil.copy(heirOrgLevelCfgEntity,heirRiskResp,false);
                String cfgValue = heirOrgLevelCfgEntity.getCfgValue();
                if (StringUtils.isNotBlank(cfgValue)) {
                    CfgValueBean cfgValueBean = JSON.parseObject(cfgValue, CfgValueBean.class);
                    heirRiskResp.setCfgValueBean(cfgValueBean);
                }
                List<HeirLangI18nEntity> collect = i18nEntities.stream()
                    .filter(heirLangI18nEntity -> heirLangI18nEntity.getGroupId().equals(heirOrgLevelCfgEntity.getId()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    for (HeirLangI18nEntity heirLangI18nEntity : collect) {
                        if (LangCodeEnum.CN.getType().equals(heirLangI18nEntity.getLangCode())) {
                            heirRiskResp.setLevelName1(heirLangI18nEntity.getLangValue());
                        }else if (LangCodeEnum.EN.getType().equals(heirLangI18nEntity.getLangCode())) {
                            heirRiskResp.setLevelName2(heirLangI18nEntity.getLangValue());
                        }else if (LangCodeEnum.TC.getType().equals(heirLangI18nEntity.getLangCode())) {
                            heirRiskResp.setLevelName3(heirLangI18nEntity.getLangValue());
                        }
                    }
                }
                list.add(heirRiskResp);
            }
            return list;
        }else{
            createConfig(orgId);
            String key = String.format(TalentBkRedisKeys.CACHE_KEY_SAVE_CONFIG_BASE, orgId);
            return CommonUtils.tryLockReturn(key,8,8,TimeUnit.SECONDS,()-> {
                return getHeirRiskData(orgId);
            });
        }
    }

    @DbHintMaster
    public void deleteById(Long id,String orgId) {
        log.info("HeirOrgLevelConfigService deleteById start,id:{},orgId:{}",id,orgId);
        Long puId = heirPosUserMapper.selectUsePreparUser(id, orgId);
        if (puId != null) {
            throw new ApiException(BkApiErrorKeys.POOL_READINESS_DELETE_CONFIRM);
        }
        List<Long> longs = heirOrgLevelCfgMapper.selectPrepareLevel(orgId);
        if (CollectionUtils.isNotEmpty(longs) && longs.size()==1 && longs.get(0).equals(id)) {
            //剩下最后一条数据，不能删除
            throw new ApiException(BkApiErrorKeys.POOL_PREPARE_DELETE_CONFIRM);
        }
        heirOrgLevelCfgMapper.deleteById(id);
        heirLangI18nMapper.deleteByGroupId(orgId, id);
    }

    public int getStudentConfig(String orgId) {
        HeirOrgConfigEntity heirOrgConfigEntity = heirOrgConfigRepository.getByOrgId(orgId);
        if (Objects.isNull(heirOrgConfigEntity)) {
            return 0;
        }
        return heirOrgConfigEntity.getShowFlag();
    }
}
