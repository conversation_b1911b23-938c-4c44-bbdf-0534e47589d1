package com.yxt.talent.bk.api.controller.profile;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.svc.profile.UserProfileService;
import com.yxt.talent.bk.svc.profile.bean.CareerHistory4Get;
import com.yxt.talent.bk.svc.profile.bean.RewardPunishmentHistory4Get;
import com.yxt.talent.bk.svc.profile.bean.TrainingHistory4Get;
import com.yxt.talent.bk.svc.profile.bean.UserBasic4Get;
import com.yxt.talent.bk.svc.profile.bean.UserCourse4Get;
import com.yxt.talent.bk.svc.profile.bean.UserExam4Get;
import com.yxt.talent.bk.svc.profile.bean.UserLearnData4Get;
import com.yxt.talent.bk.svc.profile.bean.UserModel4Get;
import com.yxt.talent.bk.svc.profile.bean.UserPerfBean4Get;
import com.yxt.talent.bk.svc.profile.bean.UserPerfType4Get;
import com.yxt.talent.bk.svc.profile.bean.UserRvTask4Get;
import com.yxt.talent.bk.svc.profile.bean.UserSkill4Get;
import com.yxt.talent.bk.svc.profile.bean.UserTag4Get;
import com.yxt.talent.bk.svc.profile.bean.WorkHistory4Get;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/mgr/portrait/{userId}")
@Tag(name = "人才画像")
public class UserPortraitController extends BaseController {
    private final UserProfileService userProfileService;

    @Operation(summary = "人员基本信息")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/basic", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public UserBasic4Get getUserBasic(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getUserBasic(orgId, userId);
    }

    @Operation(summary = "用户标签")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/tag", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public List<UserTag4Get> getUserTags(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getUserTags(orgId, userId);
    }

    @Operation(summary = "内部任职履历")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/career", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public List<CareerHistory4Get> getCareerHistory(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getCareerHistory(orgId, userId);
    }

    @Operation(summary = "外部工作经历")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/work", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public List<WorkHistory4Get> getWorkHistory(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getWorkHistory(orgId, userId);
    }

    @Operation(summary = "学习培训")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/training", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public PagingList<TrainingHistory4Get> getTrainingHistory(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return userProfileService.getTrainingHistoryPage(orgId, userId, paging);
    }

    @Operation(summary = "奖惩信息历史")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/rp", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public PagingList<RewardPunishmentHistory4Get> getRewardPunishmentHistory(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return userProfileService.getRewardPunishmentHistoryPage(orgId, userId, paging);
    }

    @Operation(summary = "绩效表现")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/perf", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public UserPerfBean4Get getUserPerf(@PathVariable String userId,
        @RequestParam(value = "cycle", defaultValue = "2") Integer cycle,
        @RequestParam(value = "limit", defaultValue = "6") Integer limit) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        UserPerfBean4Get userPerfBean4Get = new UserPerfBean4Get();
        String orgId = userCache.getOrgId();
        userPerfBean4Get.setList(userProfileService.getUserPerf(orgId, userId, cycle, limit));
        userPerfBean4Get.setPerfs(userProfileService.getAllPerf(orgId));
        return userPerfBean4Get;
    }

    @Operation(summary = "个人绩效类型")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/perftype", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public List<UserPerfType4Get> getUserPerfType(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getUserPerfType(orgId,userId);
    }

    @Operation(summary = "个人能力")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/skillInfo", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public List<UserSkill4Get> getUserSkillInfo(@PathVariable String userId , @RequestParam(value = "modelId", defaultValue = "", required = false) String modelId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        TalentbkUtil.isUuid(modelId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getUserSkillInfo(orgId, userId, modelId);
    }


    @Operation(summary = "个人能力-模型列表")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/skillmodel", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public List<UserModel4Get> getUserModelList(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getUserModelList(orgId, userId);
    }

    @Operation(summary = "职责任务")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/rvtaskinfo", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public UserRvTask4Get getRvTaskInfo(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getRvTaskInfo(orgId, userId);
    }

    @Operation(summary = "职业特征评估")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/characteristic", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public List<UserTag4Get> getUserCharacteristic(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getUserCharacteristic(orgId, userId);
    }

    @Operation(summary = "学习数据概览")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/learndata", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public UserLearnData4Get getUserLearnData(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileService.getUserLearnData(orgId, userId);
    }

    @Operation(summary = "课程学习记录")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/coursedata", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public PagingList<UserCourse4Get> getUserCourseData(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return userProfileService.getUserCourseData(orgId, userId, paging);
    }

    @Operation(summary = "参与考试记录")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/examdata", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameter(name = "userId", description = "用户id", required = true, in = ParameterIn.PATH)
    public PagingList<UserExam4Get> getUserExamData(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return userProfileService.getUserExamData(orgId, userId, paging);
    }

}
