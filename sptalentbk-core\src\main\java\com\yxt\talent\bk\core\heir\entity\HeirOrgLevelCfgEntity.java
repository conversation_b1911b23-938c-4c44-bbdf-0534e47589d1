package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.SnowFlowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * HeirOrgLevelCfgEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:47 am
 */
@Data
@TableName("bk_heir_org_level_cfg")
public class HeirOrgLevelCfgEntity extends SnowFlowIdEntity {
    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "0:继任准备度,1:继任风险规则")
    private Integer levelType;

    @Schema(description = "等级名称")
    private String levelName;

    @Schema(description = "排序号")
    private Integer orderIndex;

    @Schema(description = "颜色编码")
    private String colorCode;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "配置数据")
    private String cfgValue;

    @Schema(description = "数值范围最大(<this")
    private BigDecimal numRangeMax;

    @Schema(description = "数值范围最小(>=this")
    private BigDecimal numRangeMin;

    @Override
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = BkDemoConstants.BK_ORG_LEVEL_CFG_ID)
    public Long getId() {
        return super.getId();
    }
}
