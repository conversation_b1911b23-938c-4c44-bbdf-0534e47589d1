package com.yxt.talent.bk.core.udp.bean;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * UdpUserBriefBean
 *
 * <AUTHOR> geyan
 * @Date 18/8/23 10:22 am
 */
@Data
public class UdpUserBriefBean implements UdpLangSupport {
    private String id;

    private String username;

    private String fullname;

    private String deptId;

    private String deptName;

    private String userId;

    private String thirdUserId;

    private String positionId;

    @Schema(description = "主岗位名称")
    private String positionName;

    /**
     * 职级名称
     */
    private String gradeName;

    private String imgUrl;
    private Integer status;
    private Integer deleted;
    /**
     * 是否是奇点用户
     */
    private boolean spUser;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(id, fullname, this::setFullname);
    }

    @Override
    public UdpLangUnitBean deptLangProperty() {
        return new UdpLangUnitBean(deptId, deptName, this::setDeptName);
    }

    @Override
    public UdpLangUnitBean positionLangProperty() {
        return new UdpLangUnitBean(positionId, positionName, this::setPositionName);
    }
}
