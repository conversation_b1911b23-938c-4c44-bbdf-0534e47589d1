package com.yxt.talent.bk.core.usergroup.repo;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupManagerBean;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupManager;
import com.yxt.talent.bk.core.usergroup.mapper.UserGroupManagerMapper;
import lombok.AllArgsConstructor;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * UserGroupManagerRepository
 *
 * <AUTHOR> harleyge
 * @Date 30/4/24 6:13 pm
 */
@Repository
@AllArgsConstructor
public class UserGroupManagerRepository extends ServiceImpl<UserGroupManagerMapper, UserGroupManager> {
    private final UserGroupManagerMapper userGroupManagerMapper;

    private LambdaQueryWrapper<UserGroupManager> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public List<UserGroupManagerBean> findList(String orgId, List<Long> groupIds) {
        return TalentbkUtil.bkUdpTranslate(orgId, userGroupManagerMapper.findList(orgId, groupIds));
    }

    /**
     * 剔除禁用和我的团队创建的
     *
     * @param orgId
     * @return
     */
    public List<String> findAllMgrUserIds(String orgId) {
        return getBaseMapper().findAllMgrUserIds(orgId);
        //        LambdaQueryWrapper<UserGroupManager> queryWrapper = getQueryWrapper();
        //        queryWrapper.eq(UserGroupManager::getOrgId, orgId);
        //        queryWrapper.eq(UserGroupManager::getDeleted, YesOrNo.NO.getValue());
        //        queryWrapper.select(UserGroupManager::getUserId);
        //        List<UserGroupManager> userGroupList = list(queryWrapper);
        //        if (CollectionUtils.isEmpty(userGroupList)) {
        //            return new ArrayList<>();
        //        }
        //        return userGroupList.stream().map(UserGroupManager::getUserId).filter(StringUtils::isNotBlank).distinct()
        //                .collect(Collectors.toList());
    }

    public List<UserGroupManager> listByGroupIds(String orgId, Collection<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<UserGroupManager> query = getQueryWrapper();
        query.eq(UserGroupManager::getOrgId, orgId);
        query.in(UserGroupManager::getGroupId, groupIds);
        query.eq(UserGroupManager::getDeleted, YesOrNo.NO.getValue());
        return list(query);
    }
}
