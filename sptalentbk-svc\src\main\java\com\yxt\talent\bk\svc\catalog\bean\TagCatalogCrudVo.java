package com.yxt.talent.bk.svc.catalog.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 分类crud页面交互Vo
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Data
@NoArgsConstructor
public class TagCatalogCrudVo {

    @Schema(description = "分类表主键(区分更新与新增)")
    private String id;

    private String orgId;

    @Schema(description = "父分类id")
    private String parentId;

    @Schema(description = "必：分类名称")
    private String catalogName;

    @Schema(description = "类型(0-标签类型)")
    private Integer catalogType;

    @Schema(description = "排序")
    private Integer orderIndex;

}
