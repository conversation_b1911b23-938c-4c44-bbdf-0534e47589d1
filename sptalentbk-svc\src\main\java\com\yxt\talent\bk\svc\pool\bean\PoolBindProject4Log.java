package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import com.yxt.spsdk.audit.base.AuditLogEntitySupport;
import lombok.Data;

/**
 * PoolBindProject4Log
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 2:53 pm
 */
@Data
public class PoolBindProject4Log implements AuditLogEntitySupport {
    private String poolId;
    private String poolName;
    @AuditLogField(name = "培养项目", orderIndex = 0)
    private String projectNames;
    @AuditLogField(name = "加入项目人员", orderIndex = 1)
    private String userNames;

    @Override
    public String entityId() {
        return poolId;
    }

    @Override
    public String entityName() {
        return String.format("人才池-%s-关联培养项目", poolName);
    }
}
