<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.profile.mapper.PortraitOrgConfigMapper">

    <!--查询单个-->
    <select id="queryByOrgId" resultType="com.yxt.talent.bk.core.profile.entity.PortraitOrgConfig">
        select
          id, org_id, client_range, white_range, manager_range,client_show_cfg,manager_show_cfg, create_user_id, create_time, update_user_id, update_time, db_create_time, db_update_time
        from bk_portrait_org_config
        where org_id = #{orgId}
    </select>

    <select id="listByOrgIds" resultType="com.yxt.talent.bk.core.profile.entity.PortraitOrgConfig">
        select
            id, org_id, client_range, white_range, manager_range,client_show_cfg,manager_show_cfg, create_user_id, create_time, update_user_id, update_time, db_create_time, db_update_time
        from bk_portrait_org_config
        where org_id in
        <foreach collection="orgIds" item="item" index="index" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into bk_portrait_org_config(id, org_id, client_range, white_range, manager_range, create_user_id, create_time, update_user_id, update_time)
        values (#{id}, #{orgId}, #{clientRange}, #{whiteRange}, #{managerRange}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bk_portrait_org_config(id, org_id, client_range, white_range, manager_range, create_user_id, create_time, update_user_id, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.id}, #{entity.orgId}, #{entity.clientRange}, #{entity.whiteRange}, #{entity.managerRange}, #{entity.createUserId}, #{entity.createTime}, #{entity.updateUserId}, #{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bk_portrait_org_config(id, org_id, client_range, white_range, manager_range, create_user_id, create_time, update_user_id, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.orgId}, #{entity.clientRange}, #{entity.whiteRange}, #{entity.managerRange}, #{entity.createUserId}, #{entity.createTime}, #{entity.updateUserId}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        id = values(id),
        org_id = values(org_id),
        client_range = values(client_range),
        white_range = values(white_range),
        manager_range = values(manager_range),
        create_user_id = values(create_user_id),
        create_time = values(create_time),
        update_user_id = values(update_user_id),
        update_time = values(update_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bk_portrait_org_config
        <set>
            <if test="clientRange != null">
                client_range = #{clientRange},
            </if>
            <if test="whiteRange != null">
                white_range = #{whiteRange},
            </if>
            <if test="managerRange != null">
                manager_range = #{managerRange},
            </if>
            <if test="clientShowCfg != null and clientShowCfg != ''">
                client_show_cfg = #{clientShowCfg},
            </if>
            <if test="managerShowCfg != null and managerShowCfg != ''">
                manager_show_cfg = #{managerShowCfg},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>


</mapper>

