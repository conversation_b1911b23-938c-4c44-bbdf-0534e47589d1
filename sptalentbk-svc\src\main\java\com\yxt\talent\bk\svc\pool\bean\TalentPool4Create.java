package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
@Getter
@Setter
@Schema(description = "人才池创建请求")
public class TalentPool4Create implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="人才池名称;名称", requiredMode = Schema.RequiredMode.REQUIRED )
    private String name;
    @Schema(description="实际人数")
    private Long actualPerson;
    @Schema(description="期望人数")
    private Long expectedPerson;
    @Schema(description="分类")
    private List<AmSlDrawer4ReqDTO> catagory;
    @Schema(description="管理者", requiredMode = Schema.RequiredMode.REQUIRED )
    private List<AmSlDrawer4ReqDTO> manager;
    @Schema(description="备注")
    private String remark;
    @Schema(description="饱和度")
    private Integer saturability;
}
