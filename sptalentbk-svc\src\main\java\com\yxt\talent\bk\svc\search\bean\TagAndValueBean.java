package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "用户关联标签")
public class TagAndValueBean {

    @Schema(description = "标签id,标签key值")
    private String tagId;

    @Schema(description = "分层标签值，值可能为空")
    private List<TagValueBean> tagValueBeans;
}
