<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.udp.mapper.UdpLiteUserMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.udp.entity.UdpLiteUser">
    <!--@mbg.generated-->
    <!--@Table udp_lite_user_sp-->
    <result column="id" jdbcType="CHAR" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="fullname" jdbcType="VARCHAR" property="fullname" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="user_no" jdbcType="VARCHAR" property="userNo" />
    <result column="third_user_id" jdbcType="VARCHAR" property="thirdUserId" />
    <result column="sex" jdbcType="TINYINT" property="sex" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="dept_id" jdbcType="CHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="manager_id" jdbcType="CHAR" property="managerId" />
    <result column="manager_fullname" jdbcType="VARCHAR" property="managerFullname" />
    <result column="grade_id" jdbcType="CHAR" property="gradeId" />
    <result column="position_id" jdbcType="CHAR" property="positionId" />
    <result column="position_name" jdbcType="VARCHAR" property="positionName" />
    <result column="grade_name" jdbcType="VARCHAR" property="gradeName" />
    <result column="dept_manager_id" jdbcType="CHAR" property="deptManagerId" />
    <result column="dept_manager_fullname" jdbcType="VARCHAR" property="deptManagerFullname" />
    <result column="mobile_validated" jdbcType="TINYINT" property="mobileValidated" />
    <result column="email_validated" jdbcType="TINYINT" property="emailValidated" />
    <result column="locale" jdbcType="VARCHAR" property="locale" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="timezone" jdbcType="VARCHAR" property="timezone" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="hire_date" jdbcType="TIMESTAMP" property="hireDate" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, username, fullname, img_url, email, mobile, user_no, third_user_id, sex,
    `status`, dept_id, dept_name, manager_id, manager_fullname, grade_id, position_id,
    position_name, grade_name, dept_manager_id, dept_manager_fullname, mobile_validated,
    email_validated, `locale`, create_time, update_time, deleted, timezone, region, hire_date,
    area_code
  </sql>

    <select id="findUserPage" resultType="com.yxt.talent.bk.core.usergroup.bean.SchemeBean">
        select
            id userId,
            fullname ,
            username ,
            dept_id,
            dept_name deptName,
            position_id,
            position_name positionName,
            status ,
            hire_date hireDate,
            grade_name gradeName,
            img_url imgUrl
        from
            udp_lite_user_sp u
        where u.org_id = #{orgId}
            and u.deleted = 0
            <if test="param.status != null">
                and u.status = #{param.status}
            </if>
            <if test="param.positionIds != null and param.positionIds.size() > 0">
                and u.position_id in
                <foreach collection="param.positionIds" item="positionId" open="(" close=")" separator=",">#{positionId}</foreach>
            </if>
            <if test="param.gradeIds != null and param.gradeIds.size() > 0">
                and u.grade_id in
                <foreach collection="param.gradeIds" item="gradeId" open="(" close=")" separator=",">#{gradeId}</foreach>
            </if>
            <if test="param.userIds != null and param.userIds.size() > 0">
                and u.id in
                <foreach collection="param.userIds" item="userId" open="(" close=")" separator=",">#{userId}</foreach>
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and u.dept_id in
                <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">#{deptId}</foreach>
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                <choose>
                    <when test="param.keywordLangMatch.enabled">
                        AND ((u.username like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\')
                        <if test="param.keywordLangMatch.userIds != null and param.keywordLangMatch.userIds.size() > 0">
                            or u.id in
                            <foreach collection="param.keywordLangMatch.userIds" item="userId" open="(" separator="," close=")">
                                #{userId}
                            </foreach>
                        </if>
                        )
                    </when>
                    <otherwise>
                        and ((u.username like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\')
                        or (u.fullname like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\'))
                    </otherwise>
                </choose>
            </if>
    </select>

    <select id="findNamesById" resultType="com.yxt.talent.bk.common.bean.udp.UdpLangUserBean">
        select id,fullname from udp_lite_user_sp where org_id = #{orgId}
        <if test="(userIds != null and userIds.size()>0)">
            AND id in
            <foreach collection="userIds" item="uid" open="(" close=")" separator=",">
                #{uid}
            </foreach>
        </if>
    </select>



    <select id="existUserIds" resultType="string">
        select id from udp_lite_user_sp where org_id = #{orgId}
        and id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByUserIds" resultType="com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean">
        select id,
        <if test="queryFields != null and queryFields.size() > 0">
            <foreach collection="queryFields" item="item" separator="," close=",">
                ${item}
            </foreach>
        </if>
        deleted from udp_lite_user_sp where org_id = #{orgId}
        and id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryPositionUserQty" resultType="com.yxt.talent.bk.core.udp.bean.UdpPositionBean">
        select position_id as id,count(*) as user_count from udp_lite_user_sp
        where org_id = #{orgId} and deleted = 0 and status = 1
        and position_id in
        <foreach collection="positionIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by position_id
    </select>

    <select id="listByIds" resultType="com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean">
        select id, username, fullname from udp_lite_user_sp where org_id = #{orgId} and id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryLocaleByIds" resultType="com.yxt.talent.bk.core.udp.bean.UdpUserLocaleBean">
        select id, locale from udp_lite_user_sp where org_id = #{orgId} and id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findUser4Export" resultType="com.yxt.talent.bk.core.usergroup.bean.SchemeUser4Export">
        select
        id,
        fullname ,
        username ,
        dept_id,
        dept_name deptName,
        position_id,
        position_name positionName,
        status ,
        hire_date hireDate,
        grade_name gradeName
        from
        udp_lite_user_sp u
        where u.org_id = #{orgId}
        and u.deleted = 0
        <if test="param.status != null">
            and u.status = #{param.status}
        </if>
        <if test="param.positionIds != null and param.positionIds.size() > 0">
            and u.position_id in
            <foreach collection="param.positionIds" item="positionId" open="(" close=")" separator=",">#{positionId}</foreach>
        </if>
        <if test="param.gradeIds != null and param.gradeIds.size() > 0">
            and u.grade_id in
            <foreach collection="param.gradeIds" item="gradeId" open="(" close=")" separator=",">#{gradeId}</foreach>
        </if>
        <if test="deptIdCon != null">
            <trim prefix="and (" prefixOverrides="or" suffix=")">
                <if test="deptIdCon.deptIds != null and deptIdCon.deptIds.size() > 0">
                    or u.dept_id in
                    <foreach collection="deptIdCon.deptIds" item="deptId" open="(" close=")" separator=",">#{deptId}</foreach>
                </if>
                <if test="deptIdCon.parentDeptIds != null and deptIdCon.parentDeptIds.size() > 0">
                    or
                    (select 1 from udp_dept_relation where org_id = u.org_id and dept_id = u.dept_id and parent_id in
                    <foreach collection="deptIdCon.parentDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </trim>
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            <choose>
                <when test="param.keywordLangMatch.enabled">
                    AND ((u.username like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\')
                    <if test="param.keywordLangMatch.userIds != null and param.keywordLangMatch.userIds.size() > 0">
                        or u.id in
                        <foreach collection="param.keywordLangMatch.userIds" item="userId" open="(" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                    and ((u.username like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\')
                    or (u.fullname like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\'))
                </otherwise>
            </choose>
        </if>

    </select>

  <select id="findByOrgIdAndUserId" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from udp_lite_user_sp
      where org_id = #{orgId} and id = #{userId}
    </select>

    <select id="listIdByPosition" resultType="java.lang.String">
        select id from udp_lite_user_sp where org_id = #{orgId} and position_id = #{positionId}
        and id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="briefPage" resultType="com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean">
        select id,username,fullname,dept_id,dept_name,position_id,position_name,img_url,`status`
        from udp_lite_user_sp u
        where org_id = #{orgId}
        <if test="q.positionId != null and q.positionId != ''">
            and position_id = #{q.positionId}
        </if>
        <if test="q.fullname != null and q.fullname != ''">
            <choose>
                <when test="q.keywordLangMatch.enabled">
                    <if test="q.keywordLangMatch.userIds != null and q.keywordLangMatch.userIds.size() > 0">
                        and id in
                        <foreach collection="q.keywordLangMatch.userIds" item="userId" open="(" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                </when>
                <otherwise>
                    and fullname like CONCAT('%',#{q.fullname},'%') ESCAPE '\\'
                </otherwise>
            </choose>
        </if>
        <if test="q.status != null">
            and `status` = #{q.status}
        </if>
        <if test="q.deptId != null and q.deptId != ''">
            and exists (
              select 1 from udp_dept_relation t where t.org_id = #{orgId} and t.parent_id = #{q.deptId} and t.dept_id = u.dept_id
            )
        </if>
        and deleted = 0
    </select>

    <select id="findUserSchemePage" resultType="com.yxt.talent.bk.core.usergroup.bean.SchemeBean">
        select
        id userId,
        fullname ,
        username ,
        dept_id,
        dept_name deptName,
        position_id,
        position_name positionName,
        status ,
        hire_date hireDate,
        grade_name gradeName,
        img_url imgUrl
        from
        udp_lite_user_sp u
        where u.org_id = #{orgId}
        and u.deleted = 0
        <if test="param.status != null">
            and u.status = #{param.status}
        </if>
        <if test="param.positionIds != null and param.positionIds.size() > 0">
            and u.position_id in
            <foreach collection="param.positionIds" item="positionId" open="(" close=")" separator=",">#{positionId}</foreach>
        </if>
        <if test="param.gradeIds != null and param.gradeIds.size() > 0">
            and u.grade_id in
            <foreach collection="param.gradeIds" item="gradeId" open="(" close=")" separator=",">#{gradeId}</foreach>
        </if>
        <if test="param.userIds != null and param.userIds.size() > 0">
            and u.id in
            <foreach collection="param.userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="deptIdCon != null">
            <trim prefix="and (" prefixOverrides="or" suffix=")">
                <if test="deptIdCon.deptIds != null and deptIdCon.deptIds.size() > 0">
                    or u.dept_id in
                    <foreach collection="deptIdCon.deptIds" item="deptId" open="(" close=")" separator=",">#{deptId}</foreach>
                </if>
                <if test="deptIdCon.parentDeptIds != null and deptIdCon.parentDeptIds.size() > 0">
                    or
                    exists (select 1 from udp_dept_relation where org_id = u.org_id and dept_id = u.dept_id and parent_id in
                    <foreach collection="deptIdCon.parentDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </trim>
        </if>

        <if test="param.keyword != null and param.keyword != ''">
            <choose>
                <when test="param.keywordLangMatch.enabled">
                    AND ((u.username like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\')
                    <if test="param.keywordLangMatch.userIds != null and param.keywordLangMatch.userIds.size() > 0">
                        or u.id in
                        <foreach collection="param.keywordLangMatch.userIds" item="userId" open="(" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                    and ((u.username like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\')
                    or (u.fullname like CONCAT('%', #{param.keyword} ,'%') ESCAPE '\\'))
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="listUser4Group" resultType="com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean">
        select id,username,fullname,status,dept_id,dept_name,position_id,position_name,grade_name
        from udp_lite_user_sp u
        where u.org_id = #{orgId}
        <include refid="queryUser4GroupCondition"/>
    </select>

    <select id="listUserIds4Group" resultType="string">
        select id from udp_lite_user_sp u
        where u.org_id = #{orgId}
        <include refid="queryUser4GroupCondition"/>
    </select>

    <sql id="queryUser4GroupCondition">
        and u.deleted = 0
        <if test="param.status != null">
            and u.status = #{param.status}
        </if>
        <if test="param.deptIds != null and param.deptIds.size() > 0">
            and (u.dept_id in
            <foreach collection="param.deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            <if test="param.parentDeptIds != null and param.parentDeptIds.size() > 0">
                or
                (select 1 from udp_dept_relation where org_id = u.org_id and dept_id = u.dept_id and parent_id in
                <foreach collection="param.parentDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            )
        </if>
        <if test="param.positionIds != null and param.positionIds.size() > 0">
            and u.position_id in
            <foreach collection="param.positionIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.gradeIds != null and param.gradeIds.size() > 0">
            and u.grade_id in
            <foreach collection="param.gradeIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.userIds != null and param.userIds.size() > 0">
            and u.id in
            <foreach collection="param.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>
