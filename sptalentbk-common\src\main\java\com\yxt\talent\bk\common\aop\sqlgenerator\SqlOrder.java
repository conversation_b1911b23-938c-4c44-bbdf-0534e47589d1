package com.yxt.talent.bk.common.aop.sqlgenerator;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 指定排序字段
 */
@Documented
@Target(value = ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SqlOrder {

    String DESC = "desc";

    String ASC = "asc";

    @AliasFor("direction")
    int value() default 0;

    /**
     * 排序值
     * @return
     */
    @AliasFor("value")
    int order() default 0;

    /**
     * 排序方向
     * @return
     */
    String direction() default ASC;

}
