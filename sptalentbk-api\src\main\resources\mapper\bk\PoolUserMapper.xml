<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.pool.mapper.PoolUserMapper">
    <select id="page" resultType="com.yxt.talent.bk.core.pool.bean.PoolUser4List">
        SELECT
        <include refid="pool_user_base_column"/>
        ,
        <include refid="udp_user_base_column"/>
        , u.img_url, pr.order_index, pr.id as readinessId, pr.readiness_name
        FROM
            bk_pool_user pu
            LEFT JOIN udp_lite_user_sp u
        ON pu.user_id = u.id
            AND pu.org_id = u.org_id
            LEFT JOIN bk_pool_readiness pr ON pu.org_id = pr.org_id
            AND pu.readiness_id = pr.id
        WHERE
            pu.event_pool_out = 0
          AND pu.org_id = #{param.orgId}
          AND pu.pool_id = #{param.poolId}
        <if test="param.keyword != null and param.keyword != ''">
            <choose>
                <when test="param.keywordLangMatch.enabled">
                    AND (u.username like CONCAT('%'
                    , #{param.keyword}
                    , '%') ESCAPE '\\'
                    <if test="param.keywordLangMatch.userIds != null and param.keywordLangMatch.userIds.size() > 0">
                        or u.id in
                        <foreach collection="param.keywordLangMatch.userIds" item="userId" open="(" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                    <if test="param.keywordLangMatch.positionIds != null and param.keywordLangMatch.positionIds.size() > 0">
                        or u.position_id in
                        <foreach collection="param.keywordLangMatch.positionIds" item="positionId" open="(" separator="," close=")">
                            #{positionId}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                    AND (u.username like CONCAT('%'
                    , #{param.keyword}
                    , '%') ESCAPE '\\'
                    or u.fullname like CONCAT('%'
                    , #{param.keyword}
                    , '%') ESCAPE '\\'
                    or u.position_name like CONCAT('%'
                    , #{param.keyword}
                    , '%') ESCAPE '\\')
                </otherwise>
            </choose>
        </if>
        and u.deleted = 0
        <if test="param.deptIdCon != null">
            <trim prefix="and (" prefixOverrides="or" suffix=")">
                <if test="param.deptIdCon.deptIds != null and param.deptIdCon.deptIds.size() > 0">
                    or u.dept_id in
                    <foreach collection="param.deptIdCon.deptIds" item="deptId" open="(" close=")" separator=",">#{deptId}</foreach>
                </if>
                <if test="param.deptIdCon.parentDeptIds != null and param.deptIdCon.parentDeptIds.size() > 0">
                    or
                    (select 1 from udp_dept_relation where org_id = u.org_id and dept_id = u.dept_id and parent_id in
                    <foreach collection="param.deptIdCon.parentDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </trim>
        </if>

        <if test="param.userIdList != null and param.userIdList.size() > 0">
            AND u.id in
            <foreach collection="param.userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>

        <if test="param.readinessId != null and param.readinessId != ''">
            AND pu.readiness_id = #{param.readinessId}
        </if>
        <if test="param.orderColumn != null and param.orderColumn != ''">
            <choose>
                <when test="param.orderColumn == 'order_index'">
                    <if test="param.orderBy != null and param.orderBy != ''">
                        <choose>
                            <when test="param.orderBy == 1">
                                ORDER BY pr.order_index DESC
                            </when>
                            <otherwise>
                                <!--@ignoreSql-->
                                ORDER BY pr.order_index
                            </otherwise>
                        </choose>
                    </if>
                </when>
                <otherwise>
                    <if test="param.orderBy != null and param.orderBy != ''">
                        <choose>
                            <when test="param.orderBy == 1">
                                <!--@ignoreSql-->
                                ORDER BY pu.create_time DESC
                            </when>
                            <otherwise>
                                <!--@ignoreSql-->
                                ORDER BY pu.create_time
                            </otherwise>
                        </choose>
                    </if>
                </otherwise>
            </choose>
        </if>
        <!-- 默认情况排序 -->
        <if test="(param.orderColumn == null or param.orderColumn == '') and (param.orderBy == null or param.orderBy == '')">
            <!--@ignoreSql-->
            ORDER BY pu.create_time DESC
        </if>
    </select>

    <sql id="pool_user_base_column">
        <!--@ignoreSql-->
        pu.id, pu.org_id, pu.pool_id, pu.user_id, pu.create_time
    </sql>

    <sql id="udp_user_base_column">
        <!--@ignoreSql-->
        u.username, u.fullname, u.user_no, u.dept_id, u.dept_name, u.position_id, u.position_name, u.grade_id, u.grade_name
    </sql>

    <select id="findUserIdByPoolIdAndUserIds" resultType="java.lang.String">
        <if test="poolId != null and poolId != '' and userIds != null and userIds.size > 0">
            SELECT pu.user_id
            FROM
            bk_pool_user pu
            WHERE
            pu.org_id = #{orgId}
            AND pu.event_pool_out = 0
            AND pu.pool_id = #{poolId}
            AND pu.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="findPoolUser4Import" resultType="com.yxt.talent.bk.core.pool.bean.PoolUser4Import">
        select
        id,
        username as userName,
         fullname as fullName,
         status
        from udp_lite_user_sp a
        where org_id = #{orgId}
        and a.username in
        <foreach collection="userNames" item="userName" open="(" separator="," close=")">
            #{userName}
        </foreach>
        and a.deleted = 0
    </select>

    <select id="findAllPoolUser" resultType="java.lang.String">
        select user_id from bk_pool_user bpu
        where
        org_id = #{orgId}
        and pool_id = #{poolId}
        and event_pool_out = 0
    </select>

    <select id="findPoolUserExperienceByUserIds" resultType="com.yxt.talent.bk.core.pool.bean.PoolUserExperienceInfo4List">
        SELECT
            t1.pool_id AS poolId,
            t2.pool_name,
            t1.create_time AS joinTime,
            t1.out_time  AS outTime,
            t1.readiness_id AS readinessId,
            t1.event_pool_out  AS eventPoolOut,
            t1.remark AS remark
        FROM bk_pool_user t1
        JOIN bk_pool t2 ON t1.org_id = t2.org_id AND t1.pool_id = t2.id
        WHERE t1.org_id = #{orgId}
        AND t1.user_id = #{userId}
        AND t1.recently = 1
        AND t2.deleted = 0
        <if test="statusList != null and statusList.size() > 0">
            AND event_pool_out IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY t1.create_time
        <choose>
            <when test=" direction != null and direction == 'asc' ">
                <!--@ignoreSql-->
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>

    </select>

    <update id="updateRecentlyBy">
        UPDATE bk_pool_user
        SET recently = 0,
        update_user_id = #{currentUserId},
        update_time = #{updateTime}
        WHERE org_id = #{orgId}
        AND pool_id = #{poolId}
        AND recently = 1
        AND user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <select id="distinctOrgId" resultType="java.lang.String">
        select distinct org_id from bk_pool_user
    </select>

    <select id="hasUserPoolIds" resultType="string">
        select distinct pu.pool_id from bk_pool_user pu
        left join udp_lite_user_sp u on u.org_id = pu.org_id and u.id = pu.user_id and u.deleted = 0
        where u.id is null and pu.event_pool_out = 0 and pu.org_id = #{orgId} and pu.user_id in
        <foreach collection="userIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <delete id="removePoolUser">
        delete from bk_pool_user where org_id = #{orgId} and event_pool_out = 0 and user_id in
        <foreach collection="userIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="userPoolList" resultType="com.yxt.talent.bk.core.pool.bean.UserPoolInfoBean">
        select pu.pool_id,pm.pool_name,event_pool_out,pu.create_time,pu.out_time,pr.readiness_name from bk_pool_user pu
        join bk_pool pm on pm.id = pu.pool_id and pm.deleted = 0
        left join bk_pool_readiness pr on pr.id = pu.readiness_id
        where pu.recently = 1 and pu.org_id = #{orgId} and pu.user_id = #{userId}
        order by pu.create_time desc
    </select>

    <select id="pageUserIds" resultType="java.lang.String">
        select distinct pu.user_id from bk_pool_user pu
        join udp_lite_user_sp u on u.id = pu.user_id and u.org_id = #{orgId} and u.deleted = 0 and u.status = 1
        where pu.org_id = #{orgId}
        and pu.event_pool_out = 0 and pu.pool_id in
        <foreach collection="poolIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="poolEnableUserQty" resultType="com.yxt.talent.bk.core.pool.bean.PoolUserQtyBean">
        select pool_id,count(pu.user_id) user_qty from bk_pool_user pu
        join udp_lite_user_sp u on u.id = pu.user_id and u.org_id = #{orgId} and u.deleted = 0 and u.status = 1
        where pu.org_id = #{orgId}
        and pu.event_pool_out = 0 and pu.pool_id in
        <foreach collection="poolIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by pool_id
    </select>

    <select id="inPoolsRefTarget" resultType="com.yxt.talent.bk.core.pool.bean.PoolUserTargetIdDto">
        select pu.user_id,pj.target_id from bk_pool_user pu
        join bk_pool p on p.id = pu.pool_id and p.deleted = 0
        join bk_project_target_map pj on pj.org_id = pu.org_id and pj.project_id = p.id and pj.project_type = 0
        and pj.target_id in
        <foreach collection="targetIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        where pu.org_id = #{orgId} and event_pool_out = 0
        and pu.user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
