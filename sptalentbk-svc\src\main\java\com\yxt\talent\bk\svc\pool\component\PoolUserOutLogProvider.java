package com.yxt.talent.bk.svc.pool.component;

import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.talent.bk.svc.pool.bean.PoolUser4Update;
import com.yxt.talent.bk.svc.pool.bean.PoolUserOut4Log;
import com.yxt.talent.bk.svc.pool.enums.PoolUserOutEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * PoolUserOutLogProvider
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 1:51 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class PoolUserOutLogProvider implements AuditLogDataProvider<PoolUser4Update, PoolUserOut4Log> {
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final PoolRepository poolRepository;
    @Override
    public PoolUserOut4Log before(PoolUser4Update param, AuditLogBasicBean logBasic) {
        String orgId = logBasic.getOrgId();
        PoolUserOut4Log out4Log = new PoolUserOut4Log();
        String userNames = udpLiteUserRepository.userNames4Log(orgId,
                param.getPoolUser().getUserIdList());
        out4Log.setUserDesc(userNames);
        if (CollectionUtils.size(param.getPoolUser().getUserIdList()) == 1) {
            //单个人操作时
            out4Log.setUserFullName(userNames);
        }
        return out4Log;
    }

    @Override
    public PoolUserOut4Log after(PoolUser4Update param, AuditLogBasicBean logBasic) {
        PoolUserOut4Log out4Log = new PoolUserOut4Log();
        out4Log.setOutDesc(PoolUserOutEnum.getNameByCode(param.getEventPoolOut()));
        out4Log.setRemark(param.getRemark());
        return out4Log;
    }

    @Override
    public Pair<String, String> entityInfo(PoolUser4Update param, PoolUserOut4Log beforeObj, PoolUserOut4Log afterObj, AuditLogBasicBean logBasic) {
        String poolName = poolRepository.getNameById(param.getPoolUser().getPoolId());
        if (StringUtils.isNotEmpty(beforeObj.getUserFullName())) {
            return Pair.of(IArrayUtils.getFirst(param.getPoolUser().getUserIdList()),
                    String.format("人才池-%s-%s-出池", poolName, beforeObj.getUserFullName()));
        } else {
            return Pair.of(IArrayUtils.getFirst(param.getPoolUser().getUserIdList()),
                    String.format("人才池-%s-出池", poolName));
        }
    }
}
