package com.yxt.talent.bk.api.controller.pool;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.core.pool.bean.BindTraining4Get;
import com.yxt.talent.bk.core.pool.bean.PoolTraining4Search;
import com.yxt.talent.bk.core.pool.bean.Training4Bind;
import com.yxt.talent.bk.core.pool.bean.TrainingBindResult;
import com.yxt.talent.bk.svc.pool.PoolTrainingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static com.yxt.common.Constants.LOG_TYPE_CREATESINGLE;
import static com.yxt.common.Constants.LOG_TYPE_GETLIST;
import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.bk.common.constants.TalentBkAuthCodes.BK_AUTH_CODE_ALL;
import static com.yxt.talent.bk.common.constants.TalentBkConstants.LOG_OBJ_POOL;

@RestController
@AllArgsConstructor
@RequestMapping("/mgr/pool/training")
@Tag(name = "人才池绑定培训项目")
public class PoolTrainingController extends BaseController {
    private final PoolTrainingService poolTrainingService;

    @Operation(summary = "绑定培训项目")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_BIND_PROJECT)
    @PostMapping(value = "/{poolId}/bind", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = LOG_TYPE_CREATESINGLE, type = {TOKEN}, codes = {BK_AUTH_CODE_ALL})
    public TrainingBindResult bindTraining(@RequestBody Training4Bind training4Bind, @PathVariable String poolId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return poolTrainingService.bindTraining(poolId, training4Bind, userDetail);
    }

    @Operation(summary = "人才池绑定的培训项目列表")
    @PostMapping(value = "/{poolId}/list", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = LOG_TYPE_GETLIST, type = {TOKEN}, codes = {BK_AUTH_CODE_ALL})
    public PagingList<BindTraining4Get> listBindTraining(@PathVariable String poolId,
            @RequestBody PoolTraining4Search criteria) {
        UserCacheDetail userDetail = getUserCacheDetail();
        String orgId = userDetail.getOrgId();
        return poolTrainingService.listBindTraining(poolId, criteria, orgId);
    }

}
