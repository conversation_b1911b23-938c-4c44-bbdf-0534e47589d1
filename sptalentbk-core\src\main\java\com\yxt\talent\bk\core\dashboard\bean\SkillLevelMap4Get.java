package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * SkillLevelMap4Get
 *
 * <AUTHOR>
 * @since 2020-08-25 15:33:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SkillLevelMap4Get extends SkillLevelMap4Create{

	@Schema(description = "能力-等级关联id")
	private String id;

    @Schema(description = "能力id")
    private String skillId;
}
