package com.yxt.talent.bk.core.heir.bean.open;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlField;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * DwdHeirPosUserBean
 *
 * <AUTHOR> geyan
 * @Date 14/9/23 5:23 pm
 */
@SqlTable(DwdHeirPosUserEntity.TABLE)
@TableName(DwdHeirPosUserEntity.TABLE)
@Data
public class DwdHeirPosUserEntity {
    public static final String TABLE = "dwd_heir_pos_user";
    @Schema(description = "雪花id")
    private Long id;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "用户id")
    @SqlField("user_id")
    private String userId;

    @Schema(description = "dwd_heir_pos pkId")
    @SqlField("pos_id")
    private String posId;

    @Schema(description = "0:继任用户，1:标杆用户")
    private Integer userType;

    @Schema(description = "准备等级名称")
    private String levelName;

    @Schema(description = "准备等级颜色")
    private String levelColor;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "是否删除.0：未删除，1：已删除")
    private Integer deleted;
}
