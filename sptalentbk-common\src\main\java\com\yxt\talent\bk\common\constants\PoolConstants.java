package com.yxt.talent.bk.common.constants;

public class PoolConstants {
    private PoolConstants(){}

    /** 人才池不存在或已删除 **/
    public static final String VERIFY_DELETE_NOT_EXIST = "apis.talentbk.pool.delete.notExist";
    /** 请先移除人才池中的人员后再删除 **/
    public static final String VERIFY_DELETE_REAL_NUM_NOT_ZERO = "apis.talentbk.pool.delete.realNum.notZero";
    /** 人才池id不允许为空 **/
    public static final String VERIFY_UPDATE_POOLID_NOTBLANK = "apis.talentbk.pool.poolId.notBlank";
    /** 名称长度为2-50个字符 **/
    public static final String VERIFY_SAVE_POOLNAME_SIZE = "apis.talentbk.pool.poolName.size";

    /** 请输入1-999999范围内的数值 **/
    public static final String VERIFY_SAVE_EXPECTNUM_RANGE = "apis.talentbk.pool.expectNum.range";

    /** 备注长度为0-500个字符 **/
    public static final String VERIFY_SAVE_REMARK_SIZE = "apis.talentbk.pool.remark.size";
    /** 本分类下有人才池，不支持删除 **/
    public static final String VERIFY_CATALOG_DELETE_REFERENCE = "apis.talentbk.catalog.catalogId.reference";
    /** 准备度支持最长20个字符，最短2个字符 **/
    public static final String VERIFY_READINESS_SAVE_NAME_SIZE = "apis.talentbk.readiness.readinessName.size";
    /** 本准备度正在使用中，不支持删除 **/
    public static final String VERIFY_READINESS_DELETE_REFERENCE = "apis.talentbk.readiness.readinessId.reference";
    /** 准备度名称已存在 **/
    public static final String VERIFY_READINESS_SAVE_NAME_EXIST = "apis.talentbk.readiness.readinessName.exist";
    /** 本准备度最少1个、最多5个 **/
    public static final String VERIFY_READINESS_SAVE_ROW_OVERFLOW =  "apis.talentbk.readiness.rowCount.overflow";
    /** 支持最长20个字符，最短1个字符  **/
    public static final String VERIFY_POOL_CATALOG_NAME_SIZE = "apis.talentbk.pool.catalog.catalogName.size";


}
