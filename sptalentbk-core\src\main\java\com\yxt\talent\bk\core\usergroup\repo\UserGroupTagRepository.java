package com.yxt.talent.bk.core.usergroup.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupTag;
import com.yxt.talent.bk.core.usergroup.mapper.UserGroupTagMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/17
 */
@Slf4j
@Repository
public class UserGroupTagRepository extends ServiceImpl<UserGroupTagMapper, UserGroupTag> {

    private LambdaQueryWrapper<UserGroupTag> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public void batchInsert(List<UserGroupTag> userGroupTags) {
        getBaseMapper().batchInsert(userGroupTags);
    }

    public void deleteByGroupId(String orgId, Long id) {
        getBaseMapper().deleteByGroupId(orgId, id);
    }
}
