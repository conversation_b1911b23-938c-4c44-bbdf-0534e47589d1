package com.yxt.talent.bk.api.controller.pool;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.spevalfacade.bean.evaluation.Evaluation4List;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.pool.PoolProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yxt.common.Constants.LOG_TYPE_CREATESINGLE;
import static com.yxt.common.Constants.LOG_TYPE_GETLIST;
import static com.yxt.common.enums.AuthType.TOKEN;

/**
 * <p>
 * 人才池测评关联
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Tag(name = "人才池测评关联")
@RestController
@RequestMapping("/mgr/pooleval")
public class PoolEvalController extends BaseController {

    @Resource
    private PoolProjectService poolProjectService;

    @Operation(summary = "查询人才池测评list")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = LOG_TYPE_GETLIST, type = TOKEN)
    @GetMapping("/findbypoolid/{poolId}")
    public CommonList<Evaluation4List> list(@PathVariable() String poolId,
        @RequestParam(required = false) String keyword) {
        UserCacheBasic userCache = getUserCacheBasic();
        return poolProjectService.findByPoolId(userCache.getOrgId(), poolId, keyword);
    }

    @Operation(summary = "创建人才池测评/测训关系")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_BIND_EVAL,paramExp = "{'evalIds':#evalIds,'poolId':#poolId}")
    @Parameters({@Parameter(name = "targetType", description = "1:测评，2：测训项目",in = ParameterIn.QUERY),
            @Parameter(name = "poolId", description = "人才池id", in = ParameterIn.QUERY)})
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = LOG_TYPE_CREATESINGLE, type = TOKEN)
    @PostMapping("/save/{poolId}/{targetType}")
    public void save(@RequestBody List<String> evalIds, @PathVariable String poolId,
                     @PathVariable int targetType) {
        UserCacheBasic userCache = getUserCacheBasic();
        poolProjectService.save4PoolEval(userCache.getOrgId(), userCache.getUserId(), poolId, evalIds, targetType);
    }
}

