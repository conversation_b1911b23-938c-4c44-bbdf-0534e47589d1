package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description TODO
 *
 * <AUTHOR>
 * @Date 2023/8/19 11:05
 **/
@Data
public class SchemeRuleVO {

    private Long schemeId;

    @Schema(description = "筛选方案名称")
    private String schemeName;

    @Schema(description = "描述")
    private String schemeDesc;

    @Schema(description = "筛选规则id，没有传空")
    private Long ruleId;

    private Integer tagSearchType;

    private String tagSearch;
}
