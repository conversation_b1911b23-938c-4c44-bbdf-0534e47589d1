package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.SnowFlowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirPrepareRemindUserEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 6:21 pm
 */
@Data
@TableName("bk_heir_prepare_remind_user")
public class HeirPrepareRemindUserEntity extends SnowFlowIdEntity {
    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "岗位或部门id")
    private String posId;

    @Schema(description = "用户id")
    private String userId;
}
