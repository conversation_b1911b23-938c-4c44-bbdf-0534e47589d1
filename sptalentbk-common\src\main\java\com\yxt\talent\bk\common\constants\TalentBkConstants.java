package com.yxt.talent.bk.common.constants;

import cn.hutool.core.lang.Pair;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

@UtilityClass
public final class TalentBkConstants {
    public static final String MODULE_CODE = "sptalentbk";
    public static final String APP_CODE = "sptalent-jq";
    public static final String LOG_TALENT_BANK = "bank";
    public static final String LOG_TALENT_BANK_THEME = "personatheme";

    public static final String LOG_TALENT_SCHEME = "talentscheme";
    public static final String LOG_TALENT_BANK_USER_SEARCH_EXPORT = "userSearchExport";
    public static final String LOG_TALENT_BANK_USER_SEARCH_LIST = "userSearchList";
    public static final String LOG_TALENT_BANK_OVERVIEW = "useroverview";
    public static final String LOG_TALENT_BANK_TAG = "tag";
    public static final String SQL_ORDER_ASC = "ASC";
    public static final String DB_1_TRANSACTION_MANAGER = "db1TransactionManager";
    public static final String ES_INDEX = "talent_bk_v2";
    public static final String ES_TYPE = "_doc";
    public static final String BK_TRANSACTION_MANAGER = DB_1_TRANSACTION_MANAGER;
    public static final String MARK_1 = "*";
    public static final String MARK_2 = ".";
    public static final int MAX_LEASE_TIME = 60;
    /**
     * 代码操作时固定的操用户Id
     */
    public static final String CODE_OPERATOR_ID = "javaapi0-0000-0000-0000-************";

    public static final String FILE_TYPE_EXCEL = ".xlsx";

    public static final String FILE_ORID = "--Orig";

    public static final BigDecimal BIG_DECIMAL_100 = new BigDecimal(100);
    public static final String LOG_OBJ_POOL = "pool";

    public static final String STR_FMT_3PARAM = "%s%s%s";

    public static final String SQL_LIMIT_ONE = " limit 1";

    public static final String DEMO_COPY_ORG_INITIALIZE = "org_initialize";
    public static final Pair<Long, TimeUnit> DEMO_COPY_RUN_DATA_KEEP_TIME = Pair.of(10L, TimeUnit.DAYS);

    //资源转移：人才群组
    public static final String RES_TRANSFER_GROUP = "spbk_group";
    //资源转移：人才池
    public static final String RES_TRANSFER_POOL = "spbk_pool";
    //资源转移：人才继任(可见权限)
    public static final String RES_TRANSFER_SUCCESSION = "spbk_succession";
}
