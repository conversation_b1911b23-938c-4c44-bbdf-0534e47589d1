<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.ConfigPageImageMapper">

    <update id="deleteByOrgIdAndId">
        update bk_config_page_image set deleted=1, update_time = now(), update_user_id = #{currentUserId} where id=#{id} and org_id = #{orgId}
    </update>

    <select id="listByOrgId" resultType="com.yxt.talent.bk.core.heir.ext.ConfigPageImageExt">
        select id, image_url, link_url from bk_config_page_image where org_id = #{orgId} and deleted = 0 order by create_time asc
    </select>
</mapper>
