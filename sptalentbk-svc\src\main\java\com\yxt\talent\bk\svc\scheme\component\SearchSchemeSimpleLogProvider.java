package com.yxt.talent.bk.svc.scheme.component;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.bean.OneFieldAuditLog;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.usergroup.mapper.SearchSchemeMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * UserGroupSimpleLogProvider
 *
 * <AUTHOR> geyan
 * @Date 22/3/24 4:57 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class SearchSchemeSimpleLogProvider implements AuditLogDataProvider<Long, OneFieldAuditLog> {
    private final SearchSchemeMapper searchSchemeMapper;
    @Override
    public OneFieldAuditLog before(Long param, AuditLogBasicBean logBasic) {
        String schemeName = searchSchemeMapper.getNameById(param);
        return new OneFieldAuditLog("筛选器名称", schemeName);
    }

    @Override
    public OneFieldAuditLog after(Long param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public Pair<String, String> entityInfo(Long param, OneFieldAuditLog beforeObj, OneFieldAuditLog afterObj, AuditLogBasicBean logBasic) {
        String schemeName = CommonUtils.firstNotEmpty(beforeObj, afterObj, OneFieldAuditLog::getValue);
        if (StringUtils.isEmpty(schemeName)) {
            schemeName = searchSchemeMapper.getNameById(param);
        }
        return Pair.of(String.valueOf(param), String.format("人才列表-筛选器-%s", schemeName));
    }
}
