<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.usergroup.mapper.UserGroupMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.usergroup.entity.UserGroup">
        <!--@mbg.generated-->
        <!--@Table bk_user_group-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="group_type" jdbcType="INTEGER" property="groupType"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="enabled" jdbcType="INTEGER" property="enabled"/>
        <result column="group_desc" jdbcType="VARCHAR" property="groupDesc"/>
        <result column="search_rule_id" jdbcType="BIGINT" property="searchRuleId"/>
        <result column="caculate_time" jdbcType="TIMESTAMP" property="caculateTime"/>
        <result column="user_count" jdbcType="INTEGER" property="userCount"/>
        <result column="caculate_job" jdbcType="INTEGER" property="caculateJob"/>
        <result column="caculate_date" jdbcType="INTEGER" property="caculateDate"/>
        <result column="scheme_id" jdbcType="BIGINT" property="schemeId"/>
        <result column="base_search" jdbcType="LONGVARCHAR" property="baseSearch"/>
        <result column="team_group" jdbcType="TINYINT" property="teamGroup"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        org_id,
        deleted,
        create_time,
        update_user_id,
        update_time,
        create_user_id,
        group_type,
        group_name,
        enabled,
        group_desc,
        search_rule_id,
        caculate_time,
        user_count,
        caculate_job,
        caculate_date,
        scheme_id,
        base_search,
        team_group
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bk_user_group
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="create_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="group_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.groupType,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="group_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.groupName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.enabled,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="group_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.groupDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="search_rule_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.searchRuleId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="caculate_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT}
                    then #{item.caculateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="user_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.userCount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="caculate_job = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.caculateJob,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="caculate_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.caculateDate,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="scheme_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.schemeId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="base_search = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT}
                    then #{item.baseSearch,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="team_group = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.teamGroup,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into bk_user_group
        (id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, group_type,
        group_name, enabled, group_desc, search_rule_id, caculate_time, user_count, caculate_job,
        caculate_date, scheme_id, base_search, team_group)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=CHAR},
            #{item.deleted,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.createUserId,jdbcType=CHAR}, #{item.groupType,jdbcType=INTEGER},
            #{item.groupName,jdbcType=VARCHAR},
            #{item.enabled,jdbcType=INTEGER}, #{item.groupDesc,jdbcType=VARCHAR},
            #{item.searchRuleId,jdbcType=BIGINT},
            #{item.caculateTime,jdbcType=TIMESTAMP}, #{item.userCount,jdbcType=INTEGER},
            #{item.caculateJob,jdbcType=INTEGER},
            #{item.caculateDate,jdbcType=INTEGER}, #{item.schemeId,jdbcType=BIGINT},
            #{item.baseSearch,jdbcType=LONGVARCHAR},
            #{item.teamGroup,jdbcType=TINYINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.yxt.talent.bk.core.usergroup.entity.UserGroup">
        <!--@mbg.generated-->
        insert into bk_user_group
        (id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, group_type,
        group_name, enabled, group_desc, search_rule_id, caculate_time, user_count, caculate_job,
        caculate_date, scheme_id, base_search, team_group)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=CHAR}, #{deleted,jdbcType=TINYINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{createUserId,jdbcType=CHAR},
        #{groupType,jdbcType=INTEGER}, #{groupName,jdbcType=VARCHAR},
        #{enabled,jdbcType=INTEGER},
        #{groupDesc,jdbcType=VARCHAR}, #{searchRuleId,jdbcType=BIGINT},
        #{caculateTime,jdbcType=TIMESTAMP},
        #{userCount,jdbcType=INTEGER}, #{caculateJob,jdbcType=INTEGER},
        #{caculateDate,jdbcType=INTEGER},
        #{schemeId,jdbcType=BIGINT}, #{baseSearch,jdbcType=LONGVARCHAR},
        #{teamGroup,jdbcType=TINYINT})
        on duplicate key update id = #{id,jdbcType=BIGINT},
        org_id = #{orgId,jdbcType=CHAR},
        deleted = #{deleted,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user_id = #{updateUserId,jdbcType=CHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_user_id = #{createUserId,jdbcType=CHAR},
        group_type = #{groupType,jdbcType=INTEGER},
        group_name = #{groupName,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=INTEGER},
        group_desc = #{groupDesc,jdbcType=VARCHAR},
        search_rule_id = #{searchRuleId,jdbcType=BIGINT},
        caculate_time = #{caculateTime,jdbcType=TIMESTAMP},
        user_count = #{userCount,jdbcType=INTEGER},
        caculate_job = #{caculateJob,jdbcType=INTEGER},
        caculate_date = #{caculateDate,jdbcType=INTEGER},
        scheme_id = #{schemeId,jdbcType=BIGINT},
        base_search = #{baseSearch,jdbcType=LONGVARCHAR},
        team_group = #{teamGroup,jdbcType=TINYINT}
    </insert>
    <insert id="insertOrUpdateSelective"
            parameterType="com.yxt.talent.bk.core.usergroup.entity.UserGroup">
        <!--@mbg.generated-->
        insert into bk_user_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orgId != null and orgId != ''">
                org_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id,
            </if>
            <if test="groupType != null">
                group_type,
            </if>
            <if test="groupName != null and groupName != ''">
                group_name,
            </if>
            <if test="enabled != null">
                enabled,
            </if>
            <if test="groupDesc != null and groupDesc != ''">
                group_desc,
            </if>
            <if test="searchRuleId != null">
                search_rule_id,
            </if>
            <if test="caculateTime != null">
                caculate_time,
            </if>
            <if test="userCount != null">
                user_count,
            </if>
            <if test="caculateJob != null">
                caculate_job,
            </if>
            <if test="caculateDate != null">
                caculate_date,
            </if>
            <if test="schemeId != null">
                scheme_id,
            </if>
            <if test="baseSearch != null and baseSearch != ''">
                base_search,
            </if>
            <if test="teamGroup != null">
                team_group,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null and orgId != ''">
                #{orgId,jdbcType=CHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null and createUserId != ''">
                #{createUserId,jdbcType=CHAR},
            </if>
            <if test="groupType != null">
                #{groupType,jdbcType=INTEGER},
            </if>
            <if test="groupName != null and groupName != ''">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                #{enabled,jdbcType=INTEGER},
            </if>
            <if test="groupDesc != null and groupDesc != ''">
                #{groupDesc,jdbcType=VARCHAR},
            </if>
            <if test="searchRuleId != null">
                #{searchRuleId,jdbcType=BIGINT},
            </if>
            <if test="caculateTime != null">
                #{caculateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userCount != null">
                #{userCount,jdbcType=INTEGER},
            </if>
            <if test="caculateJob != null">
                #{caculateJob,jdbcType=INTEGER},
            </if>
            <if test="caculateDate != null">
                #{caculateDate,jdbcType=INTEGER},
            </if>
            <if test="schemeId != null">
                #{schemeId,jdbcType=BIGINT},
            </if>
            <if test="baseSearch != null and baseSearch != ''">
                #{baseSearch,jdbcType=LONGVARCHAR},
            </if>
            <if test="teamGroup != null">
                #{teamGroup,jdbcType=TINYINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null and orgId != ''">
                org_id = #{orgId,jdbcType=CHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId,jdbcType=CHAR},
            </if>
            <if test="groupType != null">
                group_type = #{groupType,jdbcType=INTEGER},
            </if>
            <if test="groupName != null and groupName != ''">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                enabled = #{enabled,jdbcType=INTEGER},
            </if>
            <if test="groupDesc != null and groupDesc != ''">
                group_desc = #{groupDesc,jdbcType=VARCHAR},
            </if>
            <if test="searchRuleId != null">
                search_rule_id = #{searchRuleId,jdbcType=BIGINT},
            </if>
            <if test="caculateTime != null">
                caculate_time = #{caculateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userCount != null">
                user_count = #{userCount,jdbcType=INTEGER},
            </if>
            <if test="caculateJob != null">
                caculate_job = #{caculateJob,jdbcType=INTEGER},
            </if>
            <if test="caculateDate != null">
                caculate_date = #{caculateDate,jdbcType=INTEGER},
            </if>
            <if test="schemeId != null">
                scheme_id = #{schemeId,jdbcType=BIGINT},
            </if>
            <if test="baseSearch != null and baseSearch != ''">
                base_search = #{baseSearch,jdbcType=LONGVARCHAR},
            </if>
            <if test="teamGroup != null">
                team_group = #{teamGroup,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <select id="find4Page" resultType="com.yxt.talent.bk.core.usergroup.bean.UserGroup4Get">
        select t.id,
        t.org_id,
        t.deleted,
        t.create_time,
        t.update_user_id,
        t.update_time,
        t.create_user_id,
        t.group_type,
        t.group_name,
        t.enabled,
        t.group_desc,
        t.search_rule_id,
        t.caculate_time,
        t.user_count,
        t.caculate_job,
        t.caculate_date,
        t.scheme_id
        from bk_user_group t
        inner join udp_lite_user_sp u
        on t.create_user_id = u.id and u.org_id = t.org_id
        where t.org_id = #{orgId}
        and t.deleted = 0
        and t.team_group = #{teamGroup}
        <if test="keyword != null and keyword != ''">
            AND t.group_name like CONCAT('%', #{keyword}, '%') ESCAPE '\\'
        </if>
        <if test="userIds != null and userIds.size() &gt; 0">
            AND (u.id in
            <foreach close=")" collection="userIds" item="userId" open="(" separator=",">
                #{userId}
            </foreach>
            <if test="deptIds != null and deptIds.size() &gt; 0">
                or u.dept_id in
                <foreach close=")" collection="deptIds" item="deptId" open="(" separator=",">
                    #{deptId}
                </foreach>
            </if>
            )
        </if>
        order by t.create_time desc
    </select>

    <update id="deleteLogic">
        update bk_user_group
        set update_time    = now(),
            deleted        = 1,
            update_user_id = #{userId}
        where id = #{id}
    </update>
    <select id="selectCaculateGroup" resultType="com.yxt.talent.bk.core.usergroup.entity.UserGroup">
        select
        <include refid="Base_Column_List">
        </include>
        from bk_user_group
        where deleted = 0
        and group_type = 2
        and enabled = 1
        and ((caculate_job = 1) or (caculate_job = 2 and caculate_date = #{weekdate}) or
        (caculate_job = 2 and caculate_date = #{day}))
        and exists(select 1 from udp_org o where o.id = org_id and o.deleted = 0
        and o.domain is not null and o.domain != ''
        and o.end_date > DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        <if test="(orgIds != null and orgIds.size() &gt; 0)">
            AND org_id in
            <foreach close=")" collection="orgIds" item="orgId" open="(" separator=",">
                #{orgId}
            </foreach>
        </if>
    </select>

    <select id="selectAllCaculateGroup"
            resultType="com.yxt.talent.bk.core.usergroup.entity.UserGroup">
        select
        <include refid="Base_Column_List">
        </include>
        from bk_user_group
        where deleted = 0
        and group_type = 2
        and enabled = 1
        and ((caculate_job = 1) or (caculate_job = 2 and caculate_date = #{weekdate}) or
        (caculate_job = 2 and caculate_date = #{day}))
        and exists(select 1 from udp_org o where o.id = org_id and o.deleted = 0
        and o.domain is not null and o.domain != ''
        and o.end_date > DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        <if test="(orgIds != null and orgIds.size() &gt; 0)">
            AND org_id in
            <foreach close=")" collection="orgIds" item="orgId" open="(" separator=",">
                #{orgId}
            </foreach>
        </if>
    </select>

    <select id="checkGroupNameExists" resultType="java.lang.Long">
        select a.id
        from bk_user_dept_ugroup a
                 join bk_user_group b on a.ugroup_id = b.id
        where a.deleted = 0
          and b.deleted = 0
          and a.org_id = #{orgId}
          and a.dept_id = #{deptId}
          and a.create_user_id = #{userId}
          and b.group_name = #{groupName}
    </select>
    <select id="findAuthData4PageModule"
            resultType="com.yxt.talent.bk.core.usergroup.bean.UserGroupModulePageVO">
        select id,group_name as groupName,group_type as groupType,user_count as userCount
        from bk_user_group bug
        where bug.org_id =#{orgId}
        AND bug.deleted = 0
        AND bug.enabled = 1
        AND bug.team_group = 0
        <if test="groupName != null and groupName != ''">
            AND bug.group_name like CONCAT('%', #{groupName}, '%') ESCAPE '\\'
        </if>
        <if test="rangeUserIds != null and rangeUserIds.size() > 0">
            AND (bug.create_user_id in
            <foreach collection="rangeUserIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
            OR
            bug.id in (select group_id from bk_user_group_manager where org_id =#{orgId} and deleted =0
            and user_id in
            <foreach collection="rangeUserIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
            )
            )
        </if>
        order by bug.create_time desc
    </select>
    <select id="getAllCount" resultType="java.lang.Long">
        select count(1) from bk_user_group
        where org_id =#{orgId}
        and deleted =0
        <if test="groupIds != null and groupIds.size() > 0">
            and id in
            <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </if>


    </select>
    <select id="findAllAuthData" resultType="com.yxt.talent.bk.core.usergroup.entity.UserGroup">
        select id,group_name as groupName,group_type as groupType
        from bk_user_group bug
        where bug.org_id =#{orgId}
        AND bug.deleted = 0
        AND bug.enabled = 1
        AND bug.team_group = 0
        <if test="rangeUserIds != null and rangeUserIds.size() > 0">
            AND (bug.create_user_id in
            <foreach collection="rangeUserIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
            OR
            bug.id in (select group_id from bk_user_group_manager where org_id =#{orgId} and deleted =0
            and user_id in
            <foreach collection="rangeUserIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
            )
            )
        </if>
        order by bug.create_time desc
    </select>
    <select id="findAuthData4PageByIds" resultType="com.yxt.talent.bk.core.usergroup.entity.UserGroup">
        select id ,group_name as groupName
        from bk_user_group bug
        where bug.org_id =#{orgId}
        AND bug.deleted = 0
        AND bug.enabled = 1
        AND bug.team_group = 0
        <if test="groupIds != null and groupIds.size() >0">
            AND bug.id in
            <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </if>
        order by bug.create_time desc
    </select>

    <select id="resTransferGroupIds" resultType="long">
        select g.id from bk_user_group g where g.org_id = #{orgId} and g.deleted = 0
        and g.team_group = 0
        and (g.create_user_id = #{userId} or
        exists(select 1 from bk_user_group_manager where org_id = #{orgId} and group_id = g.id and deleted = 0 and user_id = #{userId}))
    </select>

    <update id="execResTransfer">
        update bk_user_group set create_user_id = #{newCreateUserId},update_time = now(),update_user_id = #{optUserId}
        where org_id = #{orgId} and deleted = 0 and create_user_id = #{createUserId}
    </update>
</mapper>
