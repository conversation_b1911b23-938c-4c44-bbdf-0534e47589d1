package com.yxt.talent.bk.core.dashboard.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class DashBoardGroupDimVO {
    @Schema(description = "群组ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long groupId;
    @Schema(description = "群组名称")
    private String groupName;
    @Schema(description = "启用人数")
    private long enableUserCount;
    @Schema(description = "能力达标人数")
    private long skillReachCount;
    @Schema(description = "能力达标率")
    private String skillReachRate;
    @Schema(description = "任务达标人数")
    private long taskReachCount;
    @Schema(description = "任务达标率")
    private String taskReachRate;
}
