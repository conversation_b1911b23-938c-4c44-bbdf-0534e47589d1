package com.yxt.talent.bk.core.tag.bean;

import com.yxt.common.Constants;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@NoArgsConstructor
public class TagSimpleBean {
    private String id;
    /**
     * 标签类型(0-普通标签,1-分层标签)
     */
    private Integer tagType;
    /**
     * 0-单选，1-多选
     */
    private Integer valueChooseModel = 0;

    private String valueId;

    private String valueName;

    private String tagName;

    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;
}
