package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.entity.PackageDataEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * PackageDataMapper
 *
 * <AUTHOR> harleyge
 * @Date 24/7/24 10:07 am
 */
@Mapper
public interface PackageDataMapper extends BkBaseMapper<PackageDataEntity> {

    @Select("select biz_data from bk_package_data where id = #{id}")
    String getDataById(Long id);

    @Select("""
    select id from bk_package_data where org_id = #{orgId}
    and biz_type = #{bizType} and biz_data_md5 = #{bizDataMd5}
    """)
    Long getByBizDataMd5(@Param("orgId") String orgId,
                         @Param("bizType") String bizType,
                         @Param("bizDataMd5") String bizDataMd5);
}
