package com.yxt.talent.bk.svc.pool.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.spsdk.common.annotation.SpExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "人才池列表")
public class Pool4Export {
    @Schema(description = "主键")
    private String id;


    @Schema(description = "人才池名称")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.export.poolName", index = 0)
    private String poolName;

    @Schema(description = "分类名称")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.export.catalogName", index = 1)
    private String catalogName;

    @Schema(description = "创建日期")
    @JsonFormat(pattern = Constants.SDF_YEAR2DAY, timezone = Constants.STR_GMT8)
    @SpExcelProperty(nameKey = "apis.talentbk.pool.export.createTime", index = 2)
    @DateFormatField
    private String createTime;

    @Schema(description = "管理者,负责人")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.export.mgrNames", index = 3)
    private String mgrNames;

    @Schema(description = "现有人数")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.export.realNum", index = 4)
    private Integer realNum;


    @Schema(description = "预期人数, 期望人数")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.export.expectNum", index = 5)
    private String expectNum;


    @Schema(description = "饱和度")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.export.saturability", index = 6)
    private String saturability;


    @Schema(description = "备注")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.export.remark", index = 7)
    private String remark;

}
