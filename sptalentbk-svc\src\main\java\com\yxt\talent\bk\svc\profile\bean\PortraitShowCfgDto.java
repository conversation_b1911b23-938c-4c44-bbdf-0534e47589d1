package com.yxt.talent.bk.svc.profile.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PortraitShowCfgDto {
    @Schema(description = "基本信息")
    private Integer userBasic;
    @Schema(description = "综合能力")
    private Integer userAbility;
    @Schema(description = "绩效表现")
    private Integer userPerf;
    @Schema(description = "职业特征评估")
    private Integer jobFeatureEval;
    @Schema(description = "职责任务")
    private Integer userJob;

    @Schema(description = "内部任职履历")
    private Integer innerJob;
    @Schema(description = "外部工作经历")
    private Integer outerJob;
    @Schema(description = "奖惩信息")
    private Integer userAward;

    @Schema(description = "人才测评")
    private Integer spEval;
    @Schema(description = "人岗匹配")
    private Integer positionMatch;
    @Schema(description = "盘点九宫格")
    private Integer rvCell;
    @Schema(description = "继任图谱")
    private Integer heirTree;
    @Schema(description = "人才池")
    private Integer userPool;

    @Schema(description = "总览数据")
    private Integer studySummary;
    @Schema(description = "培训学习")
    private Integer o2oStudy;
    @Schema(description = "课程学习")
    private Integer kngStudy;
    @Schema(description = "参加考试")
    private Integer exam;
    @Schema(description = "个人发展计划")
    private Integer idp;
}
