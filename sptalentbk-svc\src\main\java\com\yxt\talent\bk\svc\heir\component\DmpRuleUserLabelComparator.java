package com.yxt.talent.bk.svc.heir.component;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.util.BeanHelper;
import com.yxt.spmodel.facade.bean.label.LabelVO;
import com.yxt.spmodel.facade.bean.label.LabelValueVO;
import com.yxt.spmodel.facade.bean.rule.LabelOperatorInfo;
import com.yxt.spmodel.facade.bean.rule.LabelRuleInfo;
import com.yxt.talent.bk.svc.heir.bean.DmpRuleValue;
import com.yxt.talent.bk.svc.heir.bean.DmpRuleValueType;
import com.yxt.talent.bk.svc.heir.enums.ComparisonOperator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class DmpRuleUserLabelComparator {
    /**
     * 查询学员的标签是否满足规则条件
     *
     * @param rule
     * @param userLabel
     * @return
     */
    public boolean checkLabelRule(LabelRuleInfo rule, LabelVO userLabel) {
        // 区间类型的，会有多条operator，此时需要两条都满足才算满足
        // 理论上，标签类型除了【包含其一，全部包含，全部不包含】三种情况外，其他比较符对应的规则值只有一条
        // 如果有多条，按照与的关系进行比较，即需要全部满足才算满足
        boolean isSatisfied = false;
        for (LabelOperatorInfo operator : rule.getOperators()) {
            isSatisfied = compareForLabel(operator, userLabel);
            // 如果其中一个操作符不满足条件，则返回false
            if (!isSatisfied) {
                return false;
            }
        }

        return isSatisfied;
    }

    /**
     * 规则标签和员工标签进行比较
     *
     * @param operator
     * @param userLabel
     * @return
     */
    boolean compareForLabel(LabelOperatorInfo operator, LabelVO userLabel) {
        Object value = operator.getValue();
        if (value == null) {
            log.warn("LOG60250:标签的值为空");
            return false;
        }

        ComparisonOperator comparisonOperator = ComparisonOperator.getByCode(operator.getOperateType());
        if (comparisonOperator == null) {
            log.warn("LOG60210:标签的运算符类型不存在");
            return false;
        }

        // 用户标签值为空，但是运算符是反向的，即【不等于、不包含、文本不包含】，则算作满足条件
        if (userLabel == null || CollectionUtils.isEmpty(userLabel.getLabelValueList())) {
            return comparisonOperator.isNegative();
        }

        return inferredAndCompareForLabel(value, comparisonOperator, userLabel.getLabelValueList());
    }

    /**
     * 推断返回的值类型，并进行条件比对
     * <p>
     * 理论上标签只有对象和数组两种情况，其中【包含其一、不包含、都包含】这三种操作符返回数组类型，其他情况返回对象类型
     * 【注意】如果返回其他类型，默认返回false
     *
     * @param value
     * @param userLabelValues
     * @return
     */
    private boolean inferredAndCompareForLabel(
            Object value,
            ComparisonOperator operator,
            List<LabelValueVO> userLabelValues) {

        if (value == null) {
            return false;
        }

        if (value instanceof Map) {
            DmpRuleValueType dmpRuleValueType = new DmpRuleValueType();
            dmpRuleValueType.setType(DmpRuleValueType.TYPE_OBJ);
            dmpRuleValueType.setDmpRuleValue(
                BeanHelper.json2Bean(BeanHelper.bean2Json(value, JsonInclude.Include.ALWAYS), DmpRuleValue.class));
            return doCompareForLabel(dmpRuleValueType, operator, userLabelValues);
        } else if (value instanceof List) {
            DmpRuleValueType dmpRuleValueType = new DmpRuleValueType();
            dmpRuleValueType.setType(DmpRuleValueType.TYPE_LIST);
            List<DmpRuleValue> dmpRuleValues =
                BeanHelper.json2Bean(BeanHelper.bean2Json(value, JsonInclude.Include.ALWAYS), List.class,
                    DmpRuleValue.class);
            dmpRuleValueType.setDmpRuleValues(dmpRuleValues);
            return doCompareForLabel(dmpRuleValueType, operator, userLabelValues);
        }
        log.error("LOG61160:标签的值类型不正确");
        return false;
    }

    private boolean doCompareForLabel(
            DmpRuleValueType dmpRuleValueType,
            ComparisonOperator operator,
            List<LabelValueVO> userLabelValues) {

        // 规则只有一个对象的时候
        DmpRuleValue dmpRuleValue = dmpRuleValueType.getDmpRuleValue();
        // 规则是区间的时候，这里返回多个规则对象
        List<DmpRuleValue> dmpRuleValues = dmpRuleValueType.getDmpRuleValues();
        boolean ruleValueEmpty = CollectionUtils.isEmpty(dmpRuleValues);
        if (dmpRuleValue == null && ruleValueEmpty) {
            log.warn("LOG61170:规则标签的值为空");
            return false;
        }

        // 运算类型 1:等于 2:不等于 3:大于 4:小于 5:大于等于 6:小于等于 7:包含 8:不包含 9:都包含 10:文本包含 11:文本不包含 12:为空 13:不为空
        boolean isSatisfied;
        switch (operator) {
            case EQUAL:
                isSatisfied = userLabelValues.stream()
                        .anyMatch(userLabel -> isEquals(dmpRuleValue, userLabel));
                break;
            case NOT_EQUAL:
                isSatisfied = userLabelValues.stream()
                        .anyMatch(userLabel -> !isEquals(dmpRuleValue, userLabel));
                break;
            case GREATER_THAN:
                isSatisfied = userLabelValues.stream()
                        .anyMatch(userLabel ->
                                isNotNull(dmpRuleValue, userLabel) &&
                                (userLabel.getOrderIndex() > dmpRuleValue.getOrderIndex()));
                break;
            case LESS_THAN:
                isSatisfied = userLabelValues.stream()
                        .anyMatch(userLabel ->
                                isNotNull(dmpRuleValue, userLabel) &&
                                (userLabel.getOrderIndex() < dmpRuleValue.getOrderIndex()));
                break;
            case GREATER_THAN_OR_EQUAL:
                isSatisfied = userLabelValues.stream()
                        .anyMatch(userLabel ->
                                isNotNull(dmpRuleValue, userLabel) &&
                                (userLabel.getOrderIndex() >= dmpRuleValue.getOrderIndex()));
                break;
            case LESS_THAN_OR_EQUAL:
                isSatisfied = userLabelValues.stream()
                        .anyMatch(userLabel ->
                                isNotNull(dmpRuleValue, userLabel) &&
                                (userLabel.getOrderIndex() <= dmpRuleValue.getOrderIndex()));
                break;
            case CONTAINS_ANY:
                isSatisfied = !ruleValueEmpty && dmpRuleValues.stream()
                        .anyMatch(ruleValue -> userLabelValues.stream()
                                .anyMatch(userLabelValue -> isEquals(ruleValue, userLabelValue)));
                break;
            case NOT_CONTAINS:
                isSatisfied = !ruleValueEmpty && dmpRuleValues.stream()
                        .allMatch(ruleValue -> userLabelValues.stream()
                                .noneMatch(userLabelValue -> isEquals(ruleValue, userLabelValue)));

                break;
            case ALL_CONTAINS:
                isSatisfied = !ruleValueEmpty && dmpRuleValues.stream()
                        .allMatch(ruleValue -> userLabelValues.stream()
                                .anyMatch(userLabelValue -> isEquals(ruleValue, userLabelValue)));
                break;
            default:
                // 如果没有匹配的运算符，返回false
                return false;
        }
        return isSatisfied;
    }

    private static boolean isNotNull(DmpRuleValue dmpRuleValue, LabelValueVO userLabel) {
        return dmpRuleValue != null && userLabel.getOrderIndex() != null && dmpRuleValue.getOrderIndex() != null;
    }

    private static boolean isEquals(DmpRuleValue dmpRuleValue, LabelValueVO userLabel) {
        return isNotNull(dmpRuleValue, userLabel) &&
               Objects.equals(dmpRuleValue.getId(), String.valueOf(userLabel.getLabelValueId()));
    }

}
