package com.yxt.talent.bk.svc.export;

import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.I18nComponent;
import com.yxt.spsdk.common.utils.ExcelRawWriter;
import com.yxt.spsdk.common.utils.IArrayUtils;
import com.yxt.spsdk.export.BaseOutputStrategy;
import com.yxt.spsdk.export.bean.DownTaskContext;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.core.heir.ext.HeirPosUserExt;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import com.yxt.talent.bk.core.udp.mapper.UdpDeptMapper;
import com.yxt.talent.bk.svc.heir.HeirOrgLevelConfigService;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.bean.HeirDeptExportBean;
import com.yxt.talent.bk.svc.heir.bean.HeirPositionExportBean;
import com.yxt.talent.bk.svc.heir.bean.UserBriefBean;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPosNodeBean;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareResp;
import com.yxt.talent.bk.svc.heir.enums.PosUserStatusEnum;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yxt.talent.bk.common.constants.ExportConstants.FILE_SUFFIX_XLSX_ORIG;
@Slf4j
public abstract class HeirBaseExportStrategy extends BaseOutputStrategy<List<HeirPosNodeBean>> {
    @Autowired
    private HeirPosService heirPosService;
    @Autowired
    private HeirPosUserMapper heirPosUserMapper;
    @Autowired
    private UdpDeptMapper udpDeptMapper;
    @Autowired
    private HeirOrgLevelConfigService heirOrgLevelConfigService;
    @Autowired
    private I18nComponent i18nComponent;
    @Override
    public void writeFile(DownTaskContext taskCtx, String filePath, List<HeirPosNodeBean> data) {
        int posType = posType();
        String orgId = taskCtx.getUserCache().getOrgId();
        boolean deptHeir = posType == HeirPosTypeEnum.DEPT.getType();
        String locale = taskCtx.getUserCache().getLocale();
        Function<Integer, String> userStatusFunc = state -> {
            if (Objects.equals(state, YesOrNo.YES.getValue())) {
                return ExportConstants.COMMON_ENABLE;
            } else if (Objects.equals(state, YesOrNo.NO.getValue())) {
                return ExportConstants.COMMON_DISABLE;
            }
            return ExportConstants.COMMON_DELETED;
        };
        Map<Long, HeirPrepareResp> prepareLevelMap = StreamUtil.list2map(heirOrgLevelConfigService.getHeirPrepareData(orgId),
                HeirPrepareResp::getId);
        String localeCn = Locale.SIMPLIFIED_CHINESE.toString();
        String localeZh = Locale.TRADITIONAL_CHINESE.toString();
        String localeEn = Locale.ENGLISH.toString();
        Function<Long, String> prepareNameFunc = levelId ->
            Optional.ofNullable(prepareLevelMap.get(levelId))
                    .map(prepareLevel -> {
                        if (localeCn.equals(locale)) {
                            return prepareLevel.getLevelName1();
                        } else if (localeZh.equals(locale)) {
                            return prepareLevel.getLevelName3();
                        } else if (localeEn.equals(locale)) {
                            return prepareLevel.getLevelName2();
                        } else{
                            return prepareLevel.getLevelName1();
                        }
                    }).orElse("--");
        if (deptHeir) {
            //部门继任
            try (ExcelRawWriter rawWriter = new ExcelRawWriter(filePath, locale, true)) {
                rawWriter.writeHead(HeirDeptExportBean.class, null);
                BatchOperationUtil.batchExecute(data, 50, heirList -> {
                    heirPosService.loadPostDetail(posType, orgId, heirList);
                    Map<String, String> deptFullNameMap = deptFullPathName(heirList);
                    writePosDetail(orgId, rawWriter, HeirDeptExportBean.class, heirList, (heirPos, posUser) -> {
                        HeirDeptExportBean exportBean = new HeirDeptExportBean();
                        exportBean.setName(deptFullNameMap.getOrDefault(heirPos.getId(), heirPos.getName()));
                        Optional.ofNullable(IArrayUtils.getFirst(heirPos.getBenchmarkUsers()))
                                .ifPresent(benchUser -> {
                                    exportBean.setManagerUsername(benchUser.getUsername());
                                    exportBean.setManagerFullname(benchUser.getFullname());
                                });
                        if (posUser != null) {
                            exportBean.setFullname(posUser.getFullName());
                            exportBean.setUsername(posUser.getUsername());
                            exportBean.setDeptNamePath(posUser.getDeptName());
                            exportBean.setPositionName(posUser.getPositionName());
                            exportBean.setStatusDesc(userStatusFunc.apply(posUser.getStatus()));
                            exportBean.setHeirStatusDesc(PosUserStatusEnum.getNameKeyByType(posUser.getHeirStatus()));
                            exportBean.setPrepareLevelName(prepareNameFunc.apply(posUser.getPrepareLevelId()));
                        }
                        return exportBean;
                    });
                });
            }
        } else {
            //岗位继任
            try (ExcelRawWriter rawWriter = new ExcelRawWriter(filePath, locale, true)) {
                rawWriter.writeHead(HeirPositionExportBean.class, null);
                BatchOperationUtil.batchExecute(data, 50, heirList -> {
                    heirPosService.loadPostDetail(posType, orgId, heirList);
                    writePosDetail(orgId, rawWriter, HeirPositionExportBean.class, heirList, (heirPos, posUser) -> {
                        HeirPositionExportBean exportBean = new HeirPositionExportBean();
                        exportBean.setName(heirPos.getName());
                        exportBean.setUserQty(heirPos.getPositionUserQty());
                        exportBean.setBenchmarkUsers(String.join(";",
                                BeanCopierUtil.convertList(heirPos.getBenchmarkUsers(), UserBriefBean::getFullname)));
                        if (posUser != null) {
                            exportBean.setFullname(posUser.getFullName());
                            exportBean.setUsername(posUser.getUsername());
                            exportBean.setDeptNamePath(posUser.getDeptName());
                            exportBean.setPositionName(posUser.getPositionName());
                            exportBean.setStatusDesc(userStatusFunc.apply(posUser.getStatus()));
                            exportBean.setHeirStatusDesc(PosUserStatusEnum.getNameKeyByType(posUser.getHeirStatus()));
                            exportBean.setPrepareLevelName(prepareNameFunc.apply(posUser.getPrepareLevelId()));
                        }
                        return exportBean;
                    });
                });
            }
        }
    }

    private <T> void writePosDetail(String orgId, ExcelRawWriter rawWriter,
                                    Class<T> dataType,
                                    List<HeirPosNodeBean> heirList,
                                    BiFunction<HeirPosNodeBean, HeirPosUserExt, T> convert) {
        List<T> writeList = new ArrayList<>();
        List<String> posIds = heirList.stream().filter(item -> item.getHasPerm() == YesOrNo.YES.getValue())
                .map(HeirPosNodeBean::getId).collect(Collectors.toList());
        Map<String, List<HeirPosUserExt>> posUsersMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(posIds)) {
            posUsersMap = IArrayUtils.list2Map(
                    heirPosUserMapper.listByPosIds(orgId, posIds),
                    HeirPosUserExt::getPosId);
        }
        for (HeirPosNodeBean heirPos : heirList) {
            List<HeirPosUserExt> posUsers = posUsersMap.get(heirPos.getId());
            if (CollectionUtils.isEmpty(posUsers)) {
                writeList.add(convert.apply(heirPos, null));
            } else {
                posUsers.forEach(posUser -> writeList.add(convert.apply(heirPos, posUser)));
            }
        }
        rawWriter.writeData(dataType, null, writeList);
    }

    @Override
    public void fillTaskInfo(DownTaskContext taskCtx) {
        taskCtx.setModuleCode(ModuleConstants.MODULE_CODE);
        String locale = taskCtx.getUserCache().getLocale();
        String posName = posType() == HeirPosTypeEnum.DEPT.getType() ?
                i18nComponent.getI18nValue("apis.talentbk.heir.export.dept.fileName", locale) :
                i18nComponent.getI18nValue("apis.talentbk.heir.export.postition.fileName", locale);
        String fileName = posName + "_" + System.currentTimeMillis();
        taskCtx.setName(fileName);
        taskCtx.setFileName(fileName + FILE_SUFFIX_XLSX_ORIG);
    }

    private Map<String, String> deptFullPathName(List<HeirPosNodeBean> list) {
        Map<String, String> deptFullNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, String> deptNameMap = new HashMap<>();
            for (HeirPosNodeBean deptNode : list) {
                if (StringUtils.isNotEmpty(deptNode.getIdFullPath())) {
                    for (String pathIdItem : deptNode.getIdFullPath().split(";")) {
                        deptNameMap.put(pathIdItem, StringPool.EMPTY);
                    }
                }
            }
            udpDeptMapper.queryNameByIds(deptNameMap.keySet())
                    .forEach(deptName -> deptNameMap.put(deptName.getId(), deptName.getName()));
            for (HeirPosNodeBean deptNode : list) {
                if (StringUtils.isNotEmpty(deptNode.getIdFullPath())) {
                    String[] idPathArr = deptNode.getIdFullPath().split(";");
                    for (int i = 0; i < idPathArr.length; i++) {
                        idPathArr[i] = deptNameMap.get(idPathArr[i]);
                    }
                    deptFullNameMap.put(deptNode.getId(), String.join("->", idPathArr));
                }
            }
        }
        return deptFullNameMap;
    }

    public abstract int posType();
}
