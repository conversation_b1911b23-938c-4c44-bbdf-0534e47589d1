package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.common.Constants;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlOrder;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Setter
@Getter
@SqlTable("dwd_career_history")
@Schema(name = "人才画像-任职履历表")
public class CareerHistory4Get {

    @Schema(description = "主键Id")
    private String id;

    @Schema(description = "绚星2.0平台用户id")
    private String userId;

    @JsonIgnore
    @Schema(description = "三方用户id")
    private String thirdUserId;

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "部门名称")
    private String thirdDeptName;

    @Schema(description = "岗位名称")
    private String thirdPositionName;

    @Schema(description = "职级名称")
    private String thirdJobgradeName;

    @Schema(description = "任职动作名称（入职、转正、晋升、转岗等）")
    private String actionName;

    @SqlOrder(order = 1, direction = SqlOrder.DESC)
    @Schema(description = "任职动作发生时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime occurrenceTime;

}
