package com.yxt.talent.bk.common.utils;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.yxt.talent.bk.common.enums.NumberEnum;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

public final class ExcelbkUtil {

    private ExcelbkUtil() {
        //NO SONAR
    }

    /**
     * 生成动态的excel下拉列表
     *
     * @param headers
     * @param data
     * @param filePath
     * @param templatePath
     * @param param
     * @throws IOException
     */
    public static void exportTemplate(Map<String, Object> headers, Object data, String filePath, String templatePath, String[] param) throws
            IOException {
        exportTemplate(headers, data, filePath, new ClassPathResource(templatePath).getInputStream(), param);
    }

    public static void exportTemplate(Map<String, Object> headers, Object data, String filePath, InputStream templateInputStream, String[] param)
            throws IOException {
        // 创建文件对象
        File file = new File(filePath);
        boolean fileExist = file.exists();
        // 如果文件不存在则新建文件
        if (!fileExist) {
            fileExist = file.createNewFile();
        }
        if (fileExist) {
            ExcelWriter excelWriter = EasyExcelFactory.write(new FileOutputStream(file))
                    .withTemplate(templateInputStream).build();
            HashMap<Integer, String[]> dropDownMap = new HashMap<>(1);
            dropDownMap.put(NumberEnum.TWO.getNumber(), param);
            WriteSheet writeSheet = EasyExcelFactory.writerSheet().registerWriteHandler(new ExcelOption(dropDownMap, 2)).build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(data, fillConfig, writeSheet);
            if (headers != null) {
                excelWriter.fill(headers, writeSheet);
            }
            excelWriter.finish();
        } else {
            throw new FileNotFoundException("file not exist!");
        }
    }

}
