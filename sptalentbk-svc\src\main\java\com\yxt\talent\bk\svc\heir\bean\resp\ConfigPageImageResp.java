package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ConfigPageImageResp {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "图片地址")
    private String imageUrl;

    @Schema(description = "链接地址")
    private String linkUrl;
}
