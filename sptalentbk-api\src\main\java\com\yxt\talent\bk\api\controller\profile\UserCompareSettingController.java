package com.yxt.talent.bk.api.controller.profile;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkAuthCodes;
import com.yxt.talent.bk.core.usergroup.bean.UserCompareBean;
import com.yxt.talent.bk.core.usergroup.bean.UserCompareDetail;
import com.yxt.talent.bk.core.usergroup.bean.UserCompareSettingBean;
import com.yxt.talent.bk.svc.profile.UserProfileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: sptalentbkapi
 * @description: 任务对比
 **/
@Tag(name = "人才画像，人才对比")
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mgr/profile")
public class UserCompareSettingController extends BaseController {
    private final UserProfileService userProfileService;

    @Operation(summary = "新增对比配置")
    @PostMapping(value = "/setting", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public void addSetting(@RequestBody @Validated UserCompareSettingBean userCompareSettingBean) {
        UserCacheBasic userCache = getUserCacheBasic();
        userProfileService.createUserSetting(userCache.getOrgId(), userCache.getUserId(),
            userCompareSettingBean.getSetting());
    }

    @Operation(summary = "查询对比配置")
    @GetMapping(value = "/setting")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public UserCompareSettingBean getSetting() {
        UserCacheBasic userCache = getUserCacheBasic();
        return userProfileService.getUserSetting(userCache.getOrgId());
    }

    @Operation(summary = "人才对比的数据")
    @PostMapping(value = "/info", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN, codes = {TalentBkAuthCodes.BK_FILE_SEARCH_COMPARE})
    public UserCompareDetail getCompareDetail(@RequestBody UserCompareBean userCompareBean) {
        UserCacheBasic userCache = getUserCacheBasic();
        return userProfileService.getCompareDetail(userCache.getOrgId(), userCompareBean);
    }
}
