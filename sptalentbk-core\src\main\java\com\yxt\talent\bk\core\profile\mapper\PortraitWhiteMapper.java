package com.yxt.talent.bk.core.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.profile.entity.PortraitWhite;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 个人画像白名单表(BkPortraitWhite)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-12 15:25:09
 */
public interface PortraitWhiteMapper extends BaseMapper<PortraitWhite> {

    List<PortraitWhite> queryAllByOrgId(@Param("orgId") String orgId);


    List<PortraitWhite> queryEnableByOrgId(@Param("orgId") String orgId, @Param("enable") Integer enable);

    @Select("""
    select user_id from bk_portrait_white where org_id = #{orgId} and white_enable = 1
    """)
    List<String> queryEnableUserIdByOrgId(@Param("orgId") String orgId);


    int findEnableByUserIds(@Param("orgId") String orgId, @Param("userId") String userId);

    /**
     * 新增数据
     *
     * @param portraitWhite 实例对象
     * @return 影响行数
     */
    int insert(PortraitWhite portraitWhite);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BkPortraitWhite> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PortraitWhite> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BkPortraitWhite> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PortraitWhite> entities);

    void updateEnableByUserIds(@Param("orgId") String orgId, @Param("userIds") List<String> userIds,
            @Param("enable") Integer enable);

    void updateAllDisEnable(@Param("orgId") String orgId);

}

