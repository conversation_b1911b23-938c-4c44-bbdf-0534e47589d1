package com.yxt.talent.bk.core.search.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 筛选项表
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Data
@Deprecated
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("bk_search_filter_item")
public class SearchFilterItem extends CreatorEntity {

    @TableId("id")
    private String id;

    @TableField("org_id")
    private String orgId;

    @TableField("item_key")
    private String itemKey;

    @TableField("item_name")
    private String itemName;

    @TableField("item_catalog_name")
    private String itemCatalogName;

    @TableField("order_index")
    private int orderIndex;
}
