package com.yxt.talent.bk.svc.common.enums;

import com.yxt.auditlog.consts.AuditConsts;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.base.AuditLogPointBase;
import com.yxt.spsdk.audit.enums.AuditLogicTypeEnum;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.LogConstants;
import com.yxt.talent.bk.svc.heir.component.HeirBmUserEditLogProvider;
import com.yxt.talent.bk.svc.heir.component.HeirDeptEditLogProvider;
import com.yxt.talent.bk.svc.heir.component.HeirEditLogProvider;
import com.yxt.talent.bk.svc.heir.component.HeirPosDelLogProvider;
import com.yxt.talent.bk.svc.heir.component.HeirPosEditLogProvider;
import com.yxt.talent.bk.svc.heir.component.HeirPrepareCfgEditLogProvider;
import com.yxt.talent.bk.svc.heir.component.HeirPrepareRuleEditLogProvider;
import com.yxt.talent.bk.svc.heir.component.HeirUserEditLogProvider;
import com.yxt.talent.bk.svc.pool.component.PoolCurdLogProvider;
import com.yxt.talent.bk.svc.pool.component.PoolEvalLogProvider;
import com.yxt.talent.bk.svc.pool.component.PoolRemovePrjLogProvider;
import com.yxt.talent.bk.svc.pool.component.PoolUserExportLogProvider;
import com.yxt.talent.bk.svc.pool.component.PoolUserOutLogProvider;
import com.yxt.talent.bk.svc.pool.component.PoolUserPrepareLogProvider;
import com.yxt.talent.bk.svc.profile.component.PortraitConfigLogProvider;
import com.yxt.talent.bk.svc.scheme.component.SearchSchemeCreateLogProvider;
import com.yxt.talent.bk.svc.scheme.component.SearchSchemeSimpleLogProvider;
import com.yxt.talent.bk.svc.usergroup.component.UserGroupCreateLogProvider;
import com.yxt.talent.bk.svc.usergroup.component.UserGroupSimpleLogProvider;

/**
 * AuditLogPointEnum
 *
 * <AUTHOR> geyan
 * @Date 14/3/24 3:16 pm
 */
public enum AuditLogPointEnum implements AuditLogPointBase {
    /**
     * 日志记录点
     */
    POOL_ADD(AuditLogConstants.POOL_ADD,LogConstants.BK_POOL,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            PoolCurdLogProvider.class,
            "人才池"),
    POOL_MOD(AuditLogConstants.POOL_MOD,LogConstants.BK_POOL,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            PoolCurdLogProvider.class,
            "人才池"),
    POOL_DEL(AuditLogConstants.POOL_DEL,LogConstants.BK_POOL,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE,
            PoolCurdLogProvider.class,
            "人才池"),
    POOL_EXPORT(AuditLogConstants.POOL_EXPORT,LogConstants.BK_POOL,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT,
            null, "人才池-导出"),

    POOL_USER_EXPORT(AuditLogConstants.POOL_USER_EXPORT,LogConstants.BK_POOL,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT,
            PoolUserExportLogProvider.class, "导出人才池明细-%s"),
    POOL_BIND_PROJECT(AuditLogConstants.POOL_BIND_PROJECT,LogConstants.BK_POOL,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            null, "人才池-关联培养项目"),
    POOL_REMOVE_PROJECT(AuditLogConstants.POOL_REMOVE_PROJECT,LogConstants.BK_POOL,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE,
            PoolRemovePrjLogProvider.class, "人才池-移除培养项目"),

    POOL_BIND_EVAL(AuditLogConstants.POOL_BIND_EVAL,LogConstants.BK_POOL,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            PoolEvalLogProvider.class, "人才池-关联测评"),
    POOL_REMOVE_EVAL(AuditLogConstants.POOL_REMOVE_EVAL,LogConstants.BK_POOL,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE,
            PoolEvalLogProvider.class, "人才池-移除测评"),
    POOL_USER_ADD(AuditLogConstants.POOL_USER_ADD,LogConstants.BK_POOL,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            null, "人才池-添加人员"),
    POOL_USER_OUT(AuditLogConstants.POOL_USER_OUT,LogConstants.BK_POOL,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            PoolUserOutLogProvider.class, "人才池-出池"),
    POOL_USER_PREPARE(AuditLogConstants.POOL_USER_PREPARE,LogConstants.BK_POOL,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            PoolUserPrepareLogProvider.class, "人才池-更新准备度"),

    HEIR_ADD_POS(AuditLogConstants.HEIR_ADD_POS,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            null, "继任地图-新增"),
    HEIR_MOD_POS(AuditLogConstants.HEIR_MOD_POS,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            HeirPosEditLogProvider.class,
            "继任地图-岗位设置"),
    HEIR_DEL_POS(AuditLogConstants.HEIR_DEL_POS,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.DELETE,
            HeirPosDelLogProvider.class, "继任地图-删除"),

    HEIR_MOD_DEPT(AuditLogConstants.HEIR_MOD_DEPT,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            HeirDeptEditLogProvider.class, "继任地图-岗位设置"),

    HEIR_PERM_ENABLE(AuditLogConstants.HEIR_PERM_ENABLE,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            HeirEditLogProvider.class, "继任地图-权限设置开关"),
    HEIR_PERM_USER(AuditLogConstants.HEIR_PERM_USER,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.UPDATE,
            HeirEditLogProvider.class, "继任地图-权限可见人员"),
    HEIR_ADD_USER(AuditLogConstants.HEIR_ADD_USER,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            HeirEditLogProvider.class, "继任地图-添加人员"),
    HEIR_REMOVE_USER(AuditLogConstants.HEIR_REMOVE_USER,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.UPDATE,
            HeirEditLogProvider.class, "继任地图-删除人员"),
    HEIR_USER_PREPARE(AuditLogConstants.HEIR_USER_PREPARE,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            HeirUserEditLogProvider.class, "继任地图-调整准备度"),
    HEIR_USER_EXIT(AuditLogConstants.HEIR_USER_EXIT,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.UPDATE,
            HeirUserEditLogProvider.class, "继任地图-退出"),
    HEIR_USER_REJOIN(AuditLogConstants.HEIR_USER_REJOIN,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.UPDATE,
            HeirUserEditLogProvider.class, "继任地图-重新加入"),
    HEIR_BENCHMARK_USER_ENABLE(AuditLogConstants.HEIR_BENCHMARK_USER_ENABLE, LogConstants.BK_HEIR,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            HeirBmUserEditLogProvider.class, "继任地图-标杆用户开启/关闭"),

    HEIR_PREPARE_RULE_EDIT(AuditLogConstants.HEIR_PREPARE_RULE_EDIT, LogConstants.BK_HEIR,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.UPDATE,
            HeirPrepareRuleEditLogProvider.class, "继任地图-%s-准备度规则-%s"),

    HEIR_PREPARE_CFG_EDIT(AuditLogConstants.HEIR_PREPARE_CFG_EDIT, LogConstants.BK_HEIR,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            HeirPrepareCfgEditLogProvider.class, "继任地图-%s-准备度规则"),
    HEIR_POS_EXPORT(AuditLogConstants.HEIR_POS_EXPORT,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT,
            null, "继任地图-岗位-导出pdf"),
    HEIR_DEPT_EXPORT(AuditLogConstants.HEIR_DEPT_EXPORT,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT,
            null, "继任地图-部门-导出pdf"),
    HEIR_POS_DETAIL_EXPORT(AuditLogConstants.HEIR_POS_DETAIL_EXPORT,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT,
            null, "继任地图-岗位-导出明细数据"),
    HEIR_DEPT_DETAIL_EXPORT(AuditLogConstants.HEIR_DEPT_DETAIL_EXPORT,LogConstants.BK_HEIR,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT,
            null, "继任地图-部门-导出明细数据"),

    USER_GROUP_CREATE(AuditLogConstants.USER_GROUP_CREATE,LogConstants.BK_GROUP,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            UserGroupCreateLogProvider.class, "人才群组-创建"),
    USER_GROUP_ENABLE(AuditLogConstants.USER_GROUP_ENABLE,LogConstants.BK_GROUP,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE,
            UserGroupSimpleLogProvider.class, "人才群组-%s-启用"),
    USER_GROUP_DISABLE(AuditLogConstants.USER_GROUP_DISABLE,LogConstants.BK_GROUP,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE,
            UserGroupSimpleLogProvider.class, "人才群组-%s-禁用"),
    USER_GROUP_CALC(AuditLogConstants.USER_GROUP_CALC,LogConstants.BK_GROUP,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE,
            UserGroupSimpleLogProvider.class, "人才群组-%s-重新计算"),
    USER_GROUP_EXPORT(AuditLogConstants.USER_GROUP_EXPORT,LogConstants.BK_GROUP,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT,
            UserGroupSimpleLogProvider.class, "人才群组-%s-下载"),
    USER_GROUP_DELETE(AuditLogConstants.USER_GROUP_DELETE,LogConstants.BK_GROUP,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE,
            UserGroupSimpleLogProvider.class, "人才群组-%s"),
    USER_LIST_EXPORT(AuditLogConstants.USER_LIST_EXPORT,LogConstants.BK_USER,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT,
            null, "人才列表-导出"),

    USER_SEARCH_SCHEME_CREATE(AuditLogConstants.USER_SEARCH_SCHEME_CREATE,LogConstants.BK_USER,
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            SearchSchemeCreateLogProvider.class, "人才列表-筛选器-创建"),
    USER_SEARCH_SCHEME_DEL(AuditLogConstants.USER_SEARCH_SCHEME_DEL,LogConstants.BK_USER,
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE,
            SearchSchemeSimpleLogProvider.class, "人才列表-筛选器-删除"),
    USER_PORTRAIT_CONFIG(AuditLogConstants.USER_PORTRAIT_CONFIG,LogConstants.BK_USER,
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE,
            PortraitConfigLogProvider.class, "个人画像设置"),
    ;
    private String pointCode;
    private String module;
    /**
     * ref AuditLogicTypeEnum
     */
    private int logicType;
    private String auditAction;
    private Class<? extends AuditLogDataProvider> dataProvider;
    private String pointName;

    AuditLogPointEnum(String pointCode,String module,
                      int logicType, String auditAction,
                      Class dataProvider, String pointName) {
        this.pointCode = pointCode;
        this.module = module;
        this.logicType = logicType;
        this.auditAction = auditAction;
        this.dataProvider = dataProvider;
        this.pointName = pointName;
    }

    @Override
    public String getPointCode() {
        return pointCode;
    }

    @Override
    public String getModule() {
        return module;
    }

    @Override
    public int getLogicType() {
        return logicType;
    }

    @Override
    public String getAuditAction() {
        return auditAction;
    }

    @Override
    public Class<? extends AuditLogDataProvider> getDataProvider() {
        return dataProvider;
    }

    @Override
    public String getPointName() {
        return pointName;
    }
}
