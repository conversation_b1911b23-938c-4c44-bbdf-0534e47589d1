package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.svc.heir.bean.HeirEditParam4Log;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
@Schema(name = "添加继任者")
public class PosUserAddReq {

    @Schema(description = "继任id")
    @NotBlank(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String posId;

    @Schema(description = "继任类型：0:岗位，1:部门")
    @NotNull(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private Integer posType;

    @Schema(description = "选中人员id list（最多1000个）")
    @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    @Size(message = BkApiErrorKeys.PARAM_SIZE_INVALID_MESSAGE, min = 1, max = 1000)
    private List<String> userIds;

    public HeirEditParam4Log editParam4Log() {
        HeirEditParam4Log ret = new HeirEditParam4Log();
        ret.setPosId(posId);
        ret.setUserIds(userIds);
        return ret;
    }
}
