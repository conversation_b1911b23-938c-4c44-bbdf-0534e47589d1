package com.yxt.talent.bk.svc.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.svc.common.DemoCopyService;
import com.yxt.talentbkfacade.bean.SpDemoInitStatus4Mq;
import com.yxt.talentbkfacade.constant.BkFacadeContants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * UdpDemoInitStatusConsumer
 *
 * <AUTHOR> harleyge
 * @Date 13/9/24 3:01 pm
 */
@Slf4j
@RequiredArgsConstructor
@Component
@RocketMQMessageListener(consumerGroup = TalentBkRocketMqConstant.GROUP_PREFIX + BkFacadeContants.TOPIC_SP_DEMO_INIT_STATUS,         topic = BkFacadeContants.TOPIC_SP_DEMO_INIT_STATUS, consumeThreadNumber = 2, consumeTimeout = 30)
public class SpDemoInitStatusConsumer implements RocketMQListener<SpDemoInitStatus4Mq> {
    private final DemoCopyService demoCopyService;
    @Override
    public void onMessage(SpDemoInitStatus4Mq message) {
        try {
            demoCopyService.demoCopyBizDone(message.getOrgId(),
                    SpDemoInitStatus4Mq.bizCode(message.getModule(), message.getStage()),
                    Optional.ofNullable(message.getStatus()).orElse(YesOrNo.NO.getValue()));
        } catch (Exception e) {
            log.error("SpDemoInitStatusConsumer fail msg {}", JSON.toJSONString(message), e);
        }
    }
}
