package com.yxt.talent.bk.core.search.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.search.bean.UserSearchAnalyseDimensionBean;
import com.yxt.talent.bk.core.search.entity.SearchAnalyseDimension;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Mapper
@Deprecated
public interface SearchAnalyseDimensionMapper extends BaseMapper<SearchAnalyseDimension> {
    /**
     * 根据orgId，返回该机构需要透视的维度
     */
    List<UserSearchAnalyseDimensionBean> findByOrgId(@Param("orgId") String orgId, @Param("userId") String userId);

}
