package com.yxt.talent.bk.core.heir.bean;

import com.yxt.spsdk.common.bean.AsyncExportBase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PosExportParam extends AsyncExportBase<PosExportParam> {
    private int posType;
    @Schema(description = "部分或者岗位ids")
    private List<String> posIds;
    @Schema(description = "继任者用户ids")
    private List<String> userIds;
    @Schema(description = "继任风险id")
    private Long riskLevelId;
    @Override
    public PosExportParam strategyData() {
        return this;
    }
}
