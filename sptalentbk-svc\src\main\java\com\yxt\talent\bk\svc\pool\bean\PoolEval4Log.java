package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PoolEval4Log
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 5:01 pm
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PoolEval4Log {
    @AuditLogField(name = "测评名称", orderIndex = 0, fullEqual = true)
    private String evalNames;
}
