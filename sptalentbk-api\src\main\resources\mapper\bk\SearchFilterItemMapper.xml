<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.search.mapper.SearchFilterItemMapper">

    <select id="findItemBean" resultType="com.yxt.talent.bk.core.tag.bean.TagItemFilterBean">
        select a.id ,
               a.org_id as orgId,
               a.item_key as itemKey,
               a.item_name as itemName,
               a.item_catalog_name as itemCatalogName,
               b.tag_name as tagRealName
        from
        bk_search_filter_item a
        left join bk_tag b
        on a.org_id = b.org_id
        and a.item_key = b.tag_key
        where a.org_id = #{orgId}
        order by a.order_index asc
    </select>

</mapper>
