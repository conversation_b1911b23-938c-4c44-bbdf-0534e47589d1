package com.yxt.talent.bk.svc.export;

import com.alibaba.fastjson.JSON;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.I18nComponent;
import com.yxt.idworker.YxtIdWorker;
import com.yxt.spsdfacade.bean.SkillLevelMap;
import com.yxt.spsdfacade.bean.spsd.ModelDto;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.spsdk.common.utils.ExcelRawWriter;
import com.yxt.spsdk.common.utils.IArrayUtils;
import com.yxt.spsdk.export.BaseOutputStrategy;
import com.yxt.spsdk.export.bean.DownTaskContext;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO;
import com.yxt.talent.bk.core.dashboard.bean.PersonalEvalModelSimpleVO;
import com.yxt.talent.bk.core.sd.bean.SdModelIndicator4ResultDto;
import com.yxt.talent.bk.core.sd.bean.SdModelInfoDto;
import com.yxt.talent.bk.core.spmodel.entity.DwdUserIndicatorResult;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserEvalSkillRtMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserJqTaskRvMapper;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.rpc.SdRpc;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.yxt.talent.bk.common.constants.ExportConstants.FILE_SUFFIX_ZIP_ORIG;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */
@Component
@AllArgsConstructor
@Slf4j
public class PersonalDetailExportStrategy extends BaseOutputStrategy<List<DwsUserSkillRtDTO>> {


    private final UdpLiteUserRepository udpLiteUserRepository;
    private final DwdUserEvalSkillRtMapper dwdUserEvalSkillRtMapper;
    private final DwdUserJqTaskRvMapper dwdUserJqTaskRvMapper;
    private final SdRpc sdRpc;
    private final I18nComponent i18nComponent;
    private static final int MODEL_SHEET_NAME_MAX = 15;

    @SneakyThrows
    @Override
    public void writeFile(DownTaskContext taskCtx, String filePath, List<DwsUserSkillRtDTO> handleDataList) {
        if (CollectionUtils.isEmpty(handleDataList)) {
            return;
        }
        String orgId = taskCtx.getUserCache().getOrgId();
        String tempPath = taskCtx.getBasePath() + File.separator + YxtIdWorker.getId();
        new File(tempPath).mkdirs();
        byte[] bs = new byte[32 * 1024];
        ExportCommonParam commonParam = new ExportCommonParam();
        commonParam.taskId = taskCtx.getTaskId();
        commonParam.userCache = taskCtx.getUserCache();
        log.debug("writeFile commonParam={}", JSON.toJSONString(commonParam));
        try (ZipOutputStream zipOutput = new ZipOutputStream(new FileOutputStream(filePath))) {
            List<String> udpUserIds = handleDataList.stream().map(DwsUserSkillRtDTO::getUserId)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(udpUserIds)) {
                return;
            }
            log.debug("writeFile step udpUserIds={}", JSON.toJSONString(udpUserIds));
            List<UdpLiteUser> udpLiteUsers = udpLiteUserRepository.getUdpUserInfo(orgId, udpUserIds);
            log.debug("writeFile step1 udpLiteUsers={}", JSON.toJSONString(udpLiteUsers));
            if (CollectionUtils.isEmpty(udpLiteUsers)) {
                return;
            }
            Map<String, DwsUserSkillRtDTO> userSkillRtDTOMap = StreamUtil.list2map(handleDataList,
                    DwsUserSkillRtDTO::getUserId);
            log.debug("writeFile step2 userSkillRtDTOMap={}", JSON.toJSONString(userSkillRtDTOMap));
            for (UdpLiteUser udpLiteUser : udpLiteUsers) {
                DwsUserSkillRtDTO dwsUserSkillRtDTO = userSkillRtDTOMap.getOrDefault(udpLiteUser.getId(),
                        null);
                log.debug("writeFile subItem udpLiteUser={}, dwsUserSkillRtDTO={}", JSON.toJSONString(udpLiteUser),
                        JSON.toJSONString(dwsUserSkillRtDTO));
                if (Objects.isNull(dwsUserSkillRtDTO)) {
                    continue;
                }
                long totalRtCnt = dwsUserSkillRtDTO.getTotalRtCnt();
                long totalSkillCnt = dwsUserSkillRtDTO.getTotalSkillCnt();
                File entryFile = writeUserModel(commonParam, orgId, tempPath, udpLiteUser, totalRtCnt, totalSkillCnt);
                if (entryFile != null && entryFile.exists()) {
                    zipOutput.putNextEntry(new ZipEntry(entryFile.getName()));
                    try (FileInputStream entryInput = new FileInputStream(entryFile)) {
                        int readLen;
                        while ((readLen = entryInput.read(bs)) >= 0) {
                            zipOutput.write(bs, 0, readLen);
                        }
                    }
                    zipOutput.closeEntry();
                    TalentbkUtil.deleteFile(entryFile);
                }
            }
        } finally {
            //临时文件删除
            commonParam.tmpFileList.forEach(TalentbkUtil::deleteFile);
            TalentbkUtil.deleteFile(new File(tempPath));
        }
    }

    private static class ExportCommonParam {
        private long taskId;
        private UserCacheDetail userCache;
        private List<File> tmpFileList = new ArrayList<>();
    }

    private File writeUserModel(ExportCommonParam commonParam, String orgId, String tempPath, UdpLiteUser udpLiteUser,
            long totalRtCnt, long totalSkillCnt) {
        String locate = commonParam.userCache.getLocale();
        String fileName = String.format("%s（%s）%s-", udpLiteUser.getFullname(), udpLiteUser.getUsername(),
                i18nComponent.getI18nValue(ExportConstants.DASHBOARD_PERSONAL_DETAIL_SUB_EXPORT_FILE_NAME, locate))
                + System.currentTimeMillis() + ExportConstants.FILE_SUFFIX_XLSX;
        String writeFilePath = tempPath + File.separator + fileName;
        log.debug("writeUserModel locate={}", locate);
        File write = new File(writeFilePath);
        commonParam.tmpFileList.add(write);
        try (ExcelRawWriter rawWriter = new ExcelRawWriter(writeFilePath, locate, false)) {
            if (totalRtCnt <= 0L && totalSkillCnt <= 0L) {
                return write;
            }
            //查询获取该人员的所有能力或任务模型信息
            List<PersonalEvalModelSimpleVO> models = getUserModels(orgId, udpLiteUser, totalRtCnt, totalSkillCnt);
            log.debug("writeUserModel udpLiteUser={},models={}", JSON.toJSONString(udpLiteUser),
                    JSON.toJSONString(models));
            if (CollectionUtils.isEmpty(models)) {
                return write;
            }

            Set<String> allModelIds = models.stream().map(PersonalEvalModelSimpleVO::getModelId)
                    .collect(Collectors.toSet());
            Map<String, ModelInfoContainer> modelInfoMap = new HashMap<>(allModelIds.size());
            allModelIds.forEach(modelId -> {
                ModelInfo modelInfo = sdRpc.getModelDetailData(orgId, modelId);
                if (modelInfo != null) {
                    String modelName = sdRpc.modelNameWithVersion(modelInfo);
                    modelInfoMap.put(modelId, new ModelInfoContainer(modelInfo,
                            org.apache.commons.lang3.StringUtils.truncate(CommonUtils.fixSheetName(modelName, 0),MODEL_SHEET_NAME_MAX)));
                }
            });
            //查询所有能力模型的能力得分
            Map<String, List<DwdUserIndicatorResult>> modelUserResultMap = getUserIndicatorResult(orgId, udpLiteUser,
                    allModelIds);
            //截取前255个，sheet最多255
            List<PersonalEvalModelSimpleVO> subModels = models;
            if (models.size() > 255) {
                subModels = models.subList(0, 255);
            }
            List<ModelInfoContainer> modelInfoContainers = subModels.stream().map(model -> modelInfoMap.get(model.getModelId()))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            String taskSheetName = i18nComponent.getI18nValue(ExportConstants.SDINDICATOR_TYPE_TASK, locate);
            String abilitySheetName = i18nComponent.getI18nValue(ExportConstants.SDINDICATOR_TYPE_ABILITY, locate);
            int indTypeNameLen = Math.max(taskSheetName.length(), abilitySheetName.length());
            fixSheetName(modelInfoContainers, MODEL_SHEET_NAME_MAX + indTypeNameLen);
            for (PersonalEvalModelSimpleVO model : subModels) {
                ModelInfoContainer modelInfo = modelInfoMap.get(model.getModelId());
                if (modelInfo == null) {
                    continue;
                }
                log.debug("writeUserModel udpLiteUser={} for model={}", JSON.toJSONString(udpLiteUser),
                        JSON.toJSONString(model));
                if (model.getModelType() == 2) {
                    model.setSheetName(modelInfo.sheetName + "-" + taskSheetName);
                    List<SdModelIndicator4ResultDto> indicators = Optional.ofNullable(sdRpc.convertAsResult(modelInfo.modelInfo, 4))
                            .map(SdModelInfoDto::getIndicators).orElse(Lists.emptyList());
                    log.debug("writeUserModel  udpLiteUser={} forRt model={},indicators={}",
                            JSON.toJSONString(udpLiteUser), JSON.toJSONString(model), indicators.size());
                    if (CollectionUtils.isEmpty(indicators)) {
                        continue;
                    }
                    writeRtModelDataSheet(udpLiteUser, rawWriter, modelUserResultMap, model, indicators);
                } else {
                    model.setSheetName(modelInfo.sheetName + "-" + abilitySheetName);
                    List<SdModelIndicator4ResultDto> indicators = Optional.ofNullable(sdRpc.convertAsResult(modelInfo.modelInfo, 1))
                            .map(SdModelInfoDto::getIndicators).orElse(Lists.emptyList());
                    log.debug("writeUserModel udpLiteUser={} forSkill model={},indicators={}",
                            JSON.toJSONString(udpLiteUser), JSON.toJSONString(model),
                            indicators.size());
                    if (CollectionUtils.isEmpty(indicators)) {
                        continue;
                    }
                    writeSkillDataSheet(udpLiteUser, rawWriter, modelUserResultMap, model, indicators);
                }
            }
        }
        return write;
    }

    private void fixSheetName(List<ModelInfoContainer> models, int baseLen) {
        Map<String, List<ModelInfoContainer>> nameDupMap = IArrayUtils.list2Map(models, ModelInfoContainer::getSheetName);
        AtomicBoolean needNext = new AtomicBoolean();
        nameDupMap.forEach((dupName, dupList) -> {
            if (dupList.size() > 1) {
                //sheet重名规则,模型名-seq
                int maxLen = baseLen - (String.valueOf(dupList.size()).length() + 1);
                dupList.forEach(item -> {
                    if (item.getSheetName().length() > maxLen) {
                        needNext.set(true);
                        item.setSheetName(org.apache.commons.lang3.StringUtils.truncate(item.getSheetName(), maxLen));
                    }
                });
            }
        });
        if (needNext.get()) {
            fixSheetName(models, baseLen);
        } else {
            nameDupMap.forEach((dupName, dupList) -> {
                if (dupList.size() > 1) {
                    for (int i = 0; i < dupList.size(); i++) {
                        ModelInfoContainer model = dupList.get(i);
                        model.setSheetName(model.getSheetName() + "-" + (i + 1));
                    }
                }
            });
        }
    }

    @NotNull
    private Map<String, List<DwdUserIndicatorResult>> getUserIndicatorResult(String orgId, UdpLiteUser udpLiteUser,
            Collection<String> modelIds) {
        if (CollectionUtils.isEmpty(modelIds) || Objects.isNull(udpLiteUser) || StringUtils.isBlank(orgId)) {
            return new HashMap<>();
        }
        List<DwdUserIndicatorResult> userEvalSkillResList = dwdUserEvalSkillRtMapper.queryAllByUserIdAndModelIds(orgId,
                udpLiteUser.getId(), modelIds);
        return userEvalSkillResList.stream().collect(Collectors.groupingBy(DwdUserIndicatorResult::getModelId));
    }

    private void writeSkillDataSheet(UdpLiteUser udpLiteUser, ExcelRawWriter rawWriter,
                                     Map<String, List<DwdUserIndicatorResult>> modelIdSkillItemListMap,
                                     PersonalEvalModelSimpleVO model,
                                     List<SdModelIndicator4ResultDto> indicators) {
        String sheetName = model.getSheetName();
        rawWriter.startSheet(String.format("%s(%s)", sheetName, model.getModelNum()));
        log.debug("writeUserModel udpLiteUser={} forSkill model={}", JSON.toJSONString(udpLiteUser), JSON.toJSONString(model));
        List<ExportSkillDetailDTO> skillDetailDTOList = new ArrayList<>();
        List<DwdUserIndicatorResult> dwdUserEvalSkillRts = modelIdSkillItemListMap.getOrDefault(model.getModelId(), null);
        Map<String, DwdUserIndicatorResult> skillResIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dwdUserEvalSkillRts)) {
            skillResIdMap = dwdUserEvalSkillRts.stream().collect(getScoreMapCollector());
        }
        Map<String, DwdUserIndicatorResult> finalSkillResIdMap = skillResIdMap;
        SdModelInfoDto.forEachIndicator(indicators, skillModelItem -> {
            ExportSkillDetailDTO exportSkillDetailDTO = getExportSkillDetailDTO(finalSkillResIdMap, skillModelItem);
            skillDetailDTOList.add(exportSkillDetailDTO);
        });
        log.debug("writeUserModel  udpLiteUser={} forSkill model={},skillDetailDTOList={}",
                JSON.toJSONString(udpLiteUser), JSON.toJSONString(model), JSON.toJSONString(skillDetailDTOList));
        writeSkillData(rawWriter, skillDetailDTOList);
        rawWriter.endSheet();
    }

    @NotNull
    private ExportSkillDetailDTO getExportSkillDetailDTO(Map<String, DwdUserIndicatorResult> finalSkillResIdMap,
                                                         SdModelIndicator4ResultDto skillModelItem) {
        ExportSkillDetailDTO exportSkillDetailDTO = new ExportSkillDetailDTO();
//        exportSkillDetailDTO.setCatalogName(skillModelItem.getSkillCatalogName());
//        exportSkillDetailDTO.setSecCatalogName(StringUtils.isBlank(skillModelItem.getSecondSkillCatalogName()) ?
//                "--" : skillModelItem.getSecondSkillCatalogName());
        exportSkillDetailDTO.setSkillName(skillModelItem.getName());
        exportSkillDetailDTO.setSkillLevelCount(String.valueOf(skillModelItem.getLevel()));
        exportSkillDetailDTO.setSkillStandard(Objects.isNull(skillModelItem.getStandardLevel()) ?
                "--" : skillModelItem.getStandardLevelStr());
        exportSkillDetailDTO.setSkillScore("--");
        exportSkillDetailDTO.setSkillResult("--");
        exportSkillDetailDTO.setSkillReached("--");
        DwdUserIndicatorResult dwdUserEvalSkillRt = finalSkillResIdMap.getOrDefault(skillModelItem.getItemId(), null);
        if (Objects.nonNull(dwdUserEvalSkillRt)) {
            exportSkillDetailDTO.setSkillScore(Objects.nonNull(dwdUserEvalSkillRt.getScoreTen()) ?
                    dwdUserEvalSkillRt.getScoreTen().stripTrailingZeros().toPlainString() :
                    "--");
            exportSkillDetailDTO.setSkillResult(StringUtils.isBlank(dwdUserEvalSkillRt.getLevelName()) ?
                    "--" :
                    dwdUserEvalSkillRt.getLevelName());
            exportSkillDetailDTO.setSkillReached(
                    Integer.valueOf(1).equals(dwdUserEvalSkillRt.getQualified()) ? "是" : "否");
        }
        return exportSkillDetailDTO;
    }

    /**
     * 获取标准登记名称
     *
     * @param standardLevel
     * @param skillLevelMapList
     * @return
     */
    private String getSkillStandardName(Integer standardLevel, List<SkillLevelMap> skillLevelMapList) {
        if (Objects.isNull(standardLevel) || CollectionUtils.isEmpty(skillLevelMapList)) {
            return "--";
        }
        Optional<SkillLevelMap> skillLevelMapOptional = skillLevelMapList.stream()
                .filter(el -> el.getLevel() == standardLevel).findFirst();
        if (skillLevelMapOptional.isPresent()) {
            return skillLevelMapOptional.get().getName();
        }
        return "--";
    }

    private void writeRtModelDataSheet(UdpLiteUser udpLiteUser, ExcelRawWriter rawWriter,
            Map<String, List<DwdUserIndicatorResult>> rtModelIdItemMap, PersonalEvalModelSimpleVO model,
            List<SdModelIndicator4ResultDto> rtBeans) {
        List<DwdUserIndicatorResult> dwdUserJqTaskRvs = rtModelIdItemMap.getOrDefault(model.getModelId(), null);
        Map<String, DwdUserIndicatorResult> rtResMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dwdUserJqTaskRvs)) {
            rtResMap = dwdUserJqTaskRvs.stream().collect(getTaskScoreMapCollector());
        }
        String sheetName = model.getSheetName();
        rawWriter.startSheet(String.format("%s(%s)", sheetName, model.getModelNum()));
        List<ExportRtDetailDTO> rtDetailDTOList = new ArrayList<>();
        Map<String, DwdUserIndicatorResult> finalRtResMap = rtResMap;
        SdModelInfoDto.forEachIndicator(rtBeans, rtBean -> {
            ExportRtDetailDTO exportRtDetailDTO = new ExportRtDetailDTO();
            exportRtDetailDTO.setTaskName(rtBean.getRtName());
            exportRtDetailDTO.setParentTaskName(
                    StringUtils.isBlank(rtBean.getParentName()) ? "--" : rtBean.getParentName());
            exportRtDetailDTO.setTaskDesc(
                    StringUtils.isBlank(rtBean.getDescription()) ? "--" : rtBean.getDescription());
            exportRtDetailDTO.setTaskResult("--");
            exportRtDetailDTO.setTaskReached("--");
            DwdUserIndicatorResult dwdUserJqTaskRv = finalRtResMap.getOrDefault(rtBean.getItemId(), null);
            if (Objects.nonNull(dwdUserJqTaskRv)) {
                exportRtDetailDTO.setTaskResult(Objects.nonNull(dwdUserJqTaskRv.getScore()) ?
                        dwdUserJqTaskRv.getScore().toPlainString() :
                        "--");
                exportRtDetailDTO.setTaskReached(
                        Integer.valueOf(1).equals(dwdUserJqTaskRv.getQualified()) ? "是" : "否");
            }
            rtDetailDTOList.add(exportRtDetailDTO);
        });
        log.debug("writeUserModel  udpLiteUser={} forRt model={},rtDetailDTOList={}", JSON.toJSONString(udpLiteUser),
                JSON.toJSONString(model), JSON.toJSONString(rtDetailDTOList));
        writeRtData(rawWriter, rtDetailDTOList);
        rawWriter.endSheet();
    }

    private Collector<DwdUserIndicatorResult, ?, Map<String, DwdUserIndicatorResult>> getTaskScoreMapCollector() {
        return Collectors.toMap(DwdUserIndicatorResult::getIndicatorId, Function.identity(), (o1, o2) -> {
            BigDecimal score1 = Optional.ofNullable(o1.getScore()).orElse(BigDecimal.ZERO);
            BigDecimal score2 = Optional.ofNullable(o2.getScore()).orElse(BigDecimal.ZERO);
            return score1.compareTo(score2) > 0 ? o1 : o2;
        });
    }

    private Collector<DwdUserIndicatorResult, ?, Map<String, DwdUserIndicatorResult>> getScoreMapCollector() {
        return Collectors.toMap(DwdUserIndicatorResult::getIndicatorId, Function.identity(), (o1, o2) -> {
            BigDecimal score1 = Optional.ofNullable(o1.getScoreTen()).orElse(BigDecimal.ZERO);
            BigDecimal score2 = Optional.ofNullable(o2.getScoreTen()).orElse(BigDecimal.ZERO);
            return score1.compareTo(score2) > 0 ? o1 : o2;
        });
    }

    private void writeSkillData(ExcelRawWriter rawWriter, List<ExportSkillDetailDTO> skillDetailDTOList) {
        if (CollectionUtils.isNotEmpty(skillDetailDTOList)) {
            rawWriter.writeHead(ExportSkillDetailDTO.class, null);
            rawWriter.writeData(ExportSkillDetailDTO.class, null, skillDetailDTOList);
        }
    }

    private void writeRtData(ExcelRawWriter rawWriter, List<ExportRtDetailDTO> rtDetailDTOList) {
        if (CollectionUtils.isNotEmpty(rtDetailDTOList)) {
            rawWriter.writeHead(ExportRtDetailDTO.class, null);
            rawWriter.writeData(ExportRtDetailDTO.class, null, rtDetailDTOList);
        }
    }

    private List<PersonalEvalModelSimpleVO> getUserModels(String orgId, UdpLiteUser udpLiteUser, long totalRtCnt,
            long totalSkillCnt) {
        List<PersonalEvalModelSimpleVO> models = new ArrayList<>();
        if (totalSkillCnt > 0L) {
            List<PersonalEvalModelSimpleVO> skillModelList = dwdUserEvalSkillRtMapper.getUsedSkillModelListByUserId(
                    orgId, udpLiteUser.getId());
            if (CollectionUtils.isNotEmpty(skillModelList)) {
                //根据模型ID查询基本信息
                List<String> modelIds = skillModelList.stream().map(PersonalEvalModelSimpleVO::getModelId)
                        .collect(Collectors.toList());
                List<ModelDto> modelBeanList = sdRpc.getModelDetailList(orgId, modelIds);
                Map<String, ModelDto> modelIdNameMap = StreamUtil.list2map(modelBeanList,
                        ModelDto::getId);
                skillModelList.forEach(el -> {
                    ModelDto skillModelBean = modelIdNameMap.getOrDefault(el.getModelId(), null);
                    if (Objects.nonNull(skillModelBean)) {
                        el.setModelName(skillModelBean.getTitle());
                        el.setModelNum(skillModelBean.getModelNum());
                    }
                    el.setModelType(1);
                });
                models.addAll(skillModelList);
            }
        }
        if (totalRtCnt > 0L) {
            List<PersonalEvalModelSimpleVO> rtModelList = dwdUserJqTaskRvMapper.getUsedRtModelListByUserId(orgId,
                    udpLiteUser.getId());
            if (CollectionUtils.isNotEmpty(rtModelList)) {
                //根据模型ID查询基本信息
                List<String> rtModelIds = rtModelList.stream().map(PersonalEvalModelSimpleVO::getModelId)
                        .collect(Collectors.toList());
                List<ModelDto> rtModelBeanList = sdRpc.getRtModelDetailList(orgId, rtModelIds);
                Map<String, ModelDto> rtModelIdNameMap = StreamUtil.list2map(rtModelBeanList, ModelDto::getId);
                rtModelList.forEach(el -> {
                    ModelDto rtModelName = rtModelIdNameMap.getOrDefault(el.getModelId(), null);
                    if (Objects.nonNull(rtModelName)) {
                        el.setModelName(rtModelName.getTitle());
                        el.setModelNum(rtModelName.getModelNum());
                    }
                    el.setModelType(2);
                });
                models.addAll(rtModelList);
            }
        }
        return models;
    }

    @Override
    public void fillTaskInfo(DownTaskContext taskContext) {
        taskContext.setModuleCode(ModuleConstants.MODULE_CODE);
        String zipFileName = i18nComponent.getI18nValue(ExportConstants.DASHBOARD_PERSONAL_DETAIL_EXPORT_FILE_NAME,
                taskContext.getUserCache().getLocale()) + "_" + System.currentTimeMillis();
        taskContext.setName(zipFileName);
        taskContext.setFileName(zipFileName + FILE_SUFFIX_ZIP_ORIG);
    }

    private static class ModelInfoContainer {
        private ModelInfo modelInfo;
        private String sheetName;

        public ModelInfoContainer(ModelInfo modelInfo, String sheetName) {
            this.modelInfo = modelInfo;
            this.sheetName = sheetName;
        }

        public String getSheetName() {
            return sheetName;
        }

        public void setSheetName(String sheetName) {
            this.sheetName = sheetName;
        }
    }
}
