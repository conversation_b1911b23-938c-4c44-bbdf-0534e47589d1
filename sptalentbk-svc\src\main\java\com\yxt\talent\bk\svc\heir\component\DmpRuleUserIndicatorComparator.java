package com.yxt.talent.bk.svc.heir.component;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.DateUtil;
import com.yxt.spmodel.facade.bean.indicators.MetaTableColumnEnumInfo;
import com.yxt.spmodel.facade.bean.indicators.UserIndicatorsValueListVO;
import com.yxt.spmodel.facade.bean.rule.LabelOperatorInfo;
import com.yxt.spmodel.facade.bean.rule.LabelRuleInfo;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.svc.heir.bean.DmpRuleValue;
import com.yxt.talent.bk.svc.heir.bean.DmpRuleValueType;
import com.yxt.talent.bk.svc.heir.bean.RuleComparisonBean;
import com.yxt.talent.bk.svc.heir.enums.ComparisonOperator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class DmpRuleUserIndicatorComparator {

    /**
     * 检查学员指标是否满足规则设定的条件值
     *
     * @param rule
     * @param userIndicator
     * @return
     */
    public boolean checkIndicatorRule(LabelRuleInfo rule, UserIndicatorsValueListVO userIndicator) {
        boolean isSatisfied = false;
        // 区间类型的，会有多条operator，此时需要两条都满足才算满足
        for (LabelOperatorInfo operator : rule.getOperators()) {
            isSatisfied = compareForIndicator(rule.getColumnType(), operator, userIndicator);
            // 如果其中一个操作符不满足条件，则返回false
            if (!isSatisfied) {
                return false;
            }
        }
        return isSatisfied;
    }

    /**
     * 规则指标和员工指标进行比较
     *
     * @param columnType          指标数据类型 0:枚举 1:字符串（文本） 2:整数  3:浮点数  4:日期  5:日期时间
     * @param operator
     * @param userIndicator
     * @return
     */
    private boolean compareForIndicator(Integer columnType, LabelOperatorInfo operator,
        UserIndicatorsValueListVO userIndicator) {

        Long indicatorsId = Optional.ofNullable(userIndicator)
                .map(UserIndicatorsValueListVO::getIndicatorsId).orElse(null);

        Object ruleValue = operator.getValue();
        if (ruleValue == null) {
            log.warn("LOG60300:规则指标[{}]的值为空", indicatorsId);
            return false;
        }

        ComparisonOperator comparisonOperator = ComparisonOperator.getByCode(operator.getOperateType());
        if (comparisonOperator == null) {
            log.warn("LOG60310:规则指标[{}]的运算符类型不存在", indicatorsId);
            return false;
        }

        boolean isSatisfied = false;
        if (columnType == 0) {
            // ruleValue ==> 0:枚举
            List<MetaTableColumnEnumInfo> colEnumInfos = Optional.ofNullable(userIndicator)
                    .map(UserIndicatorsValueListVO::getEnums).orElse(null);
            if (CollectionUtils.isEmpty(colEnumInfos)) {
                // 用户指标值为空，但是运算符是反向的，即【不等于、不包含、文本不包含】，则算作满足条件
                return comparisonOperator.isNegative();
            } else {
                // 用户同一个指标下可能会有多个指标值，这里取或处理，即只要有一个指标符合条件，即算满足
                for (MetaTableColumnEnumInfo userIndicatorEnum : colEnumInfos) {
                    isSatisfied = inferredAndCompareForIndicatorEnum(ruleValue, comparisonOperator, userIndicatorEnum);
                    if (isSatisfied) {
                        break;
                    }
                }
            }
        } else {
            // ruleValue ==> 1:字符串（文本） 2:整数  3:浮点数  4:日期  5:日期时间
            List<String> indicatorValues = Optional.ofNullable(userIndicator)
                    .map(UserIndicatorsValueListVO::getIndicatorValues).orElse(null);
            if (CollectionUtils.isEmpty(indicatorValues)) {
                // 用户指标值为空，但是运算符是反向的，即【不等于、不包含、文本不包含】，则算作满足条件
                return comparisonOperator.isNegative();
            } else {
                // 用户同一个指标下可能会有多个指标值，这里取或处理，即只要有一个指标符合条件，即算满足
                for (String userIndicatorValue : indicatorValues) {
                    isSatisfied = doCompareSimpleTypeForIndicator(ruleValue, comparisonOperator, userIndicatorValue);
                    if (isSatisfied) {
                        break;
                    }
                }
            }
        }
        return isSatisfied;
    }

    private boolean inferredAndCompareForIndicatorEnum(Object value, ComparisonOperator operator,
        MetaTableColumnEnumInfo userIndicatorEnum) {

        if (value == null) {
            return false;
        }

        // 枚举类型的rule value只有
        if (value instanceof Map) {
            DmpRuleValueType dmpRuleValueType = new DmpRuleValueType();
            dmpRuleValueType.setType(DmpRuleValueType.TYPE_OBJ);
            dmpRuleValueType.setDmpRuleValue(
                BeanHelper.json2Bean(BeanHelper.bean2Json(value, JsonInclude.Include.ALWAYS), DmpRuleValue.class));
            return doCompareForEnumIndicator(dmpRuleValueType, operator, userIndicatorEnum);
        } else if (value instanceof List) {
            DmpRuleValueType dmpRuleValueType = new DmpRuleValueType();
            dmpRuleValueType.setType(DmpRuleValueType.TYPE_LIST);
            List<DmpRuleValue> dmpRuleValues =
                BeanHelper.json2Bean(BeanHelper.bean2Json(value, JsonInclude.Include.ALWAYS), List.class,
                    DmpRuleValue.class);
            dmpRuleValueType.setDmpRuleValues(dmpRuleValues);
            return doCompareForEnumIndicator(dmpRuleValueType, operator, userIndicatorEnum);
        }
        log.error("LOG60320:指标[{}]的值类型不是对象或数组：value={}", userIndicatorEnum, value);
        return false;
    }

    private boolean doCompareForEnumIndicator(DmpRuleValueType dmpRuleValueType, ComparisonOperator operator,
        MetaTableColumnEnumInfo userIndicatorEnum) {
        if (userIndicatorEnum == null) {
            log.warn("LOG60360:");
            return false;
        }
        DmpRuleValue dmpRuleValue = dmpRuleValueType.getDmpRuleValue();
        List<DmpRuleValue> dmpRuleValues = dmpRuleValueType.getDmpRuleValues();
        boolean ruleValueEmpty = CollectionUtils.isEmpty(dmpRuleValues);
        if (dmpRuleValue == null && ruleValueEmpty) {
            log.warn("LOG60370");
            return false;
        }

        boolean isSatisfied;
        switch (operator) {
            case EQUAL:
                isSatisfied = isEquals(userIndicatorEnum, dmpRuleValue);
                break;
            case NOT_EQUAL:
                isSatisfied = !isEquals(userIndicatorEnum, dmpRuleValue);
                break;
            case GREATER_THAN:
                isSatisfied = isNotNull(dmpRuleValue, userIndicatorEnum) &&
                              userIndicatorEnum.getOrderIndex() > dmpRuleValue.getOrderIndex();
                break;
            case LESS_THAN:
                isSatisfied = isNotNull(dmpRuleValue, userIndicatorEnum) &&
                              userIndicatorEnum.getOrderIndex() < dmpRuleValue.getOrderIndex();
                break;
            case GREATER_THAN_OR_EQUAL:
                isSatisfied = isNotNull(dmpRuleValue, userIndicatorEnum) &&
                              userIndicatorEnum.getOrderIndex() >= dmpRuleValue.getOrderIndex();
                break;
            case LESS_THAN_OR_EQUAL:
                isSatisfied = isNotNull(dmpRuleValue, userIndicatorEnum) &&
                              userIndicatorEnum.getOrderIndex() <= dmpRuleValue.getOrderIndex();
                break;
            case CONTAINS_ANY:
                isSatisfied = !ruleValueEmpty && dmpRuleValues.stream().anyMatch(e -> isEquals(userIndicatorEnum, e));
                break;
            case NOT_CONTAINS:
                isSatisfied = !ruleValueEmpty && dmpRuleValues.stream().noneMatch(e -> isEquals(userIndicatorEnum, e));
                break;
            case ALL_CONTAINS:
                isSatisfied = !ruleValueEmpty && dmpRuleValues.stream().allMatch(e -> isEquals(userIndicatorEnum, e));
                break;
            // 其它操作符相应的判断可以在这里继续补充
            default:
                // 如果没有匹配的运算符，返回false
                return false;
        }

        return isSatisfied;
    }

    private static boolean isEquals(MetaTableColumnEnumInfo userIndicatorEnum, DmpRuleValue dmpRuleValue) {
        return isNotNull(dmpRuleValue, userIndicatorEnum) &&
               userIndicatorEnum.getOrderIndex().intValue() == dmpRuleValue.getOrderIndex().intValue();
    }

    private static boolean isNotNull(DmpRuleValue dmpRuleValue, MetaTableColumnEnumInfo userIndicatorEnum) {
        return dmpRuleValue != null && userIndicatorEnum.getOrderIndex() != null &&
               dmpRuleValue.getOrderIndex() != null;
    }

    private boolean doCompareSimpleTypeForIndicator(Object ruleValue, ComparisonOperator operator,
        String userIndicatorValue) {
        if (ruleValue == null) {
            log.warn("LOG60340:");
            return false;
        }
        if (userIndicatorValue == null) {
            log.warn("LOG60350:operator={}", operator.isNegative());
            return operator.isNegative();
        }
        RuleComparisonBean compareBean = new RuleComparisonBean();
        compareBean.setRuleValue(ruleValue);
        compareBean.setUserIndicatorValue(userIndicatorValue);
        // 运算类型 1:等于 2:不等于 3:大于 4:小于 5:大于等于 6:小于等于 7:包含 8:不包含 9:都包含 10:匹配 11:不匹配 12:为空 13:不为空
        return operator.getIndicatorCompare() == null ? false : operator.getIndicatorCompare().apply(compareBean);
    }

    private static boolean doEqual(Object ruleValue, String userIndicatorValue) {
        boolean isSatisfied;
        if (ruleValue instanceof Number) {
            // 不论整形还是浮点型，都转成浮点型进行比较，使用BigDecimal比较浮点
            BigDecimal ruleBigDecimal = new BigDecimal(ruleValue.toString());
            BigDecimal userIndicatorBigDecimal = new BigDecimal(userIndicatorValue);
            isSatisfied = userIndicatorBigDecimal.compareTo(ruleBigDecimal) == 0;
        } else if (ruleValue instanceof String) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) == 0;
        } else if (ruleValue instanceof Boolean) {
            isSatisfied = userIndicatorValue.compareTo(ruleValue.toString()) == 0;
        } else if (ruleValue instanceof Date) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtil.formatDate((Date) ruleValue)) == 0 ||
                          userIndicatorValue.compareTo(DateUtil.formatSimpleDate((Date) ruleValue)) == 0;
        } else if (ruleValue instanceof LocalDateTime) {
            // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
            isSatisfied = userIndicatorValue.compareTo(DateUtils.formatDate((LocalDateTime) ruleValue)) == 0 ||
                          userIndicatorValue.compareTo(DateUtils.formatSimpleDate((LocalDateTime) ruleValue)) == 0;
        } else {
            isSatisfied = false;
        }
        return isSatisfied;
    }

}
