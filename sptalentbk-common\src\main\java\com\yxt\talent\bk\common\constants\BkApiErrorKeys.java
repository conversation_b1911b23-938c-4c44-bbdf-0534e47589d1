package com.yxt.talent.bk.common.constants;

import lombok.experimental.UtilityClass;

@UtilityClass
public final class BkApiErrorKeys {
    public static final String UNKNOWN_ERROR = "apis.talentbk.error";

    public static final String PARAM_NULL_ERROR_MESSAGE = "apis.talentbk.null_or_empty";
    public static final String PARAM_SIZE_INVALID_MESSAGE = "apis.talentbk.size.exceed";

    /**
     * 不能将子级岗位设置为上级岗位或者自己设置为上级岗位
     */
    public static final String HEIR_SET_PARENT_POS_ID_CYCLE = "apis.talentbk.heir.set.parent.pos.id.cycle";

    /**
     * 您选择的人员均已退出
     */
    public static final String HEIR_USER_EXIT = "apis.talentbk.heir.user.exit";

    /**
     * 您选择的人员在当前选择岗位的在职人员中
     */
    public static final String HEIR_USER_POSITION_EXIST = "apis.talentbk.heir.user.position.exist";

    /**
     * 用户已删除
     */
    public static final String HEIR_USER_DELETE = "apis.talentbk.heir.user.delete";
    public static final String USER_DEPT_UGROUP_NOT_EXIST = "apis.talentbk.user_dept_ugroup.not.exist";
    public static final String USER_GROUP_SEARCH_RULE_NOT_EXIST = "apis.talentbk.user_group_search_rule.not.exist";
    public static final String USER_GROUP_NOT_EXIST = "apis.talentbk.user_group.not.exist";

    public static final String APIS_TALENTBK_USERSEARCH_TOO_MANY_CLAUSES = "apis.talentbk.usersearch.too_many_clauses";
    public static final String APIS_TALENTBK_TAG_NOT_EXIST = "apis.talentbk.tag.not.exist";
    public static final String APIS_TALENTBK_USERGROUP_RANGE_BLANK = "apis.talentbk.userGroup.range.blank";
    public static final String APIS_TALENTBK_HEIR_USER_PREPARE_HANDLED = "apis.talentbk.heir.user.prepare.handled";
    public static final String APIS_TALENTBK_USERSEARCHRULE_NULL = "apis.talentbk.usersearchrule.null";

    public static final String CONFIG_BANNER_COUNT_OVER_LIMIT = "apis.talentbk.config.banner.count.over.limit";

    public static final String APIS_TALENTBK_PARAM_ERROR = "apis.talentbk.param.error";

    public static final String USER_NOT_EXIST = "apis.talentbk.user.not.exist";

    public static final String APIS_TALENTBK_FILE_EXPORT_ING = "apis.talentbk.file.export.ing";

    public static final String EXPORT_NO_DATA = "apis.talentbk.export.no.data";
    public static final String ERROR_KEY_DIMENSION_NAME_EXIST = "apis.talentbk.dimension.name.exist";
    public static final String ERROR_KEY_DIMENSION_NAME_NOT_EMPTY = "apis.talentbk.dimension.name.not.empty";
    public static final String ERROR_KEY_DIMENSION_NOT_FOUND = "apis.talentbk.dimension.not.found";
    public static final String ERROR_KEY_DIMENSION_TAG_RELATION_NOT_FOUND = "apis.talentbk.dimension.tag.relation.not.found";
    public static final String ERROR_KEY_TAG_NOT_FOUND = "apis.talentbk.tag.not.found";
    public static final String ERROR_KEY_DIMENSION_BUILD_CANNOT_REMOVE = "apis.talentbk.dimension.cannot.remove";
    public static final String ERROR_KEY_DIMENSION_TAG_RELATION_CANNOT_REMOVE = "apis.talentbk.dimension.tag.relation.cannot.remove";
    public static final String ERROR_KEY_DIMENSION_BUILD_CANNOT_BIND_TAG = "apis.talentbk.dimension.cannot.bind.tag";
    /**
     * 筛选组不存在
     */
    public static final String ERROR_KEY_GROUP_NOT_FOUND = "apis.talentbk.group.not.exist";
    /**
     * 筛选组名称重复
     */
    public static final String ERROR_KEY_GROUP_NAME_REPEAT = "apis.talentbk.group.name.repeat";
    /**
     * 筛选组名称格式错误
     */
    public static final String ERROR_KEY_GROUP_NAME_FORM_ERROR = "apis.talentbk.group.name.form.error";
    /**
     * 默认筛选组不能删除
     */
    public static final String ERROR_KEY_GROUP_DELETE_ERROR = "apis.talentbk.group.delete.error";
    /**
     * 默认筛选组不能修改
     */
    public static final String ERROR_KEY_GROUP_NO_EDIT = "apis.talentbk.group.no.edit";
    public static final String ERROR_KEY_DIMENSION_SIZE_ERROR = "apis.talentbk.dimension.size.error";
    /**
     * 维度保存重复
     */
    public static final String ERROR_KEY_DIMENSION_REPEAT = "apis.talentbk.dimension.repeat";
    public static final String ERROR_KEY_CATALOG_NAME_EXIST = "apis.talentbk.catalog.name.exist";
    public static final String ERROR_KEY_CATALOG_BE_USED = "apis.talentbk.catalog.be.used";
    /**
     * 标签为空
     */
    public static final String ERROR_KEY_TAG_IS_NULL = "apis.talentbk.tag.is.null";
    public static final String TAG_EXIST_MUL_VALUE = "apis.talentbk.tag.exist.mul.value";
    /**
     * 导入读取文件流异常
     */
    public static final String EXTRA_IMPORT_INVALID = "apis.talentbk.extra.import.fileId.invalid";
    /**
     * sheet 名称错误
     */
    public static final String EXCEL_SHEET_NAME_ERROR = "apis.talentbk.import.excel.sheetName.error";
    /**
     * 准备度存在校验
     */
    public static final String POOL_READINESS_EXIST_CONFIRM = "apis.talentbk.pool.confirm.readiness.exist.error";

    /**
     * 准备度删除校验
     */
    public static final String POOL_READINESS_DELETE_CONFIRM = "apis.talentbk.heir.exist.used.prepare";

    public static final String POOL_PREPARE_DELETE_CONFIRM = "apis.talentbk.heir.prepare.cannot.delete";

    public static final String POOL_PREPARE_CANNOT_SAME_LEVEL_NAME = "apis.talentbk.heir.prepare.cannot.same.level.name";

    public static final String POOL_RISK_CANNOT_SAME_LEVEL_NAME = "apis.talentbk.heir.risk.cannot.same.level.name";

    public static final String POOL_RISK_CANNOT_SAME_VALUE = "apis.talentbk.heir.risk.cannot.same.value";
    public static final String TALENT_BK_PROJECT_BIND_TRAINING_USER_ID_EMPTY = "apis.talentbk.project.bind.training.user.id.empty";
    public static final String TALENT_BK_PROJECT_BIND_TRAINING_ID_EMPTY = "apis.talentbk.project.bind.training.id.empty";
    public static final String TALENT_BK_PROJECT_BIND_TRAINING_NOT_EXIST = "apis.talentbk.project.bind.training.not.exist";
}
