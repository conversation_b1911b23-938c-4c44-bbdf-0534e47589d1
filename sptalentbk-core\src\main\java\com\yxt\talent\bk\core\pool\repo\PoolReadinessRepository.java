package com.yxt.talent.bk.core.pool.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.pool.entity.PoolReadiness;
import com.yxt.talent.bk.core.pool.mapper.PoolReadinessMapper;
import jakarta.validation.constraints.NotEmpty;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class PoolReadinessRepository extends ServiceImpl<PoolReadinessMapper, PoolReadiness> {

    public PoolReadiness getById(String orgId, String id) {
        LambdaQueryWrapper<PoolReadiness> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolReadiness::getId, id);
        queryWrapper.eq(PoolReadiness::getOrgId, orgId);
        return getOne(queryWrapper);
    }

    /**
     * 获取一个key为准备度值，value为准备度名的map，根据准备度值集合
     * @param orgId, orderIndexs
     * @return java.util.Map<java.lang.Integer,java.lang.String>
     * <AUTHOR>
     * @since 2022/11/21
     */
    public Map<Integer, String> findMapByOrderIndex(String orgId,
        @Validated @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) Collection<Integer> orderIndexs) {
        LambdaQueryWrapper<PoolReadiness> queryWrapper = getQueryWrapper();
        queryWrapper.in(PoolReadiness::getOrderIndex, orderIndexs);
        queryWrapper.eq(PoolReadiness::getOrgId, orgId);
        List<PoolReadiness> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>(0);
        }
        return StreamUtil.list2map(list, PoolReadiness::getOrderIndex, PoolReadiness::getReadinessName);
    }

    public List<PoolReadiness> list(String orgId) {
        LambdaQueryWrapper<PoolReadiness> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolReadiness::getOrgId, orgId);
        queryWrapper.orderByAsc(PoolReadiness::getOrderIndex);
        return list(queryWrapper);
    }

    public boolean removeById(String orgId, String id) {
        LambdaQueryWrapper<PoolReadiness> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolReadiness::getId, id);
        queryWrapper.eq(PoolReadiness::getOrgId, orgId);
        return super.remove(queryWrapper);
    }

    public long countByOrgId(String orgId) {
        LambdaQueryWrapper<PoolReadiness> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PoolReadiness::getOrgId, orgId);
        return count(queryWrapper);
    }

    public List<PoolReadiness> findByReadinessName(String orgId, String readinessName) {
        LambdaQueryWrapper<PoolReadiness> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PoolReadiness::getOrgId, orgId);
        queryWrapper.eq(PoolReadiness::getReadinessName, readinessName);
        return list(queryWrapper);
    }

    public String getNameById(String id) {
        return baseMapper.getNameById(id);
    }

    private LambdaQueryWrapper<PoolReadiness> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }
}
