package com.yxt.talent.bk.core.tag.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("bk_tag")
@EqualsAndHashCode(callSuper = true)
public class TagEntity extends CreatorEntity {
    @TableId
    private String id;
    /**
     * 机构id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * '标签名称(性别|出生日期...)
     */
    @TableField("tag_name")
    private String tagName;
    /**
     * '标签key(sex|birthday...)
     */
    @TableField("tag_key")
    private String tagKey;
    /**
     * 标签描述/定义
     */
    @TableField("description")
    private String description;
    /**
     * 标签分类id/字段空是未分组
     */
    @TableField("catalog_id")
    private String catalogId;
    /**
     * 标签类型(0-普通标签,1-分层标签)
     */
    @TableField("tag_type")
    private Integer tagType;
    /**
     * '标签值选择类型(0-单选,1-多选)'
     */
    @TableField("value_choose_model")
    private Integer valueChooseModel;
    /**
     * '标签来源(2-固定,0-内置,1-自建)
     */
    @TableField("tag_source")
    private Integer source;
    /**
     * 启用状态(0-禁用,1-启用)
     */
    @TableField("tag_enable")
    private Integer enable;
    /**
     * 可见状态(0-不可见,1-可见)
     */
    @TableField("show_type")
    private Integer showType;
    /**
     * 创建方式(0-静态,1-规则,2-模型)
     */
    @TableField("create_type")
    private Integer createType;
    /**
     * 标签更新方式(0-手动,1-自动)
     */
    @TableField("update_type")
    private Integer updateType;
    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;
}
