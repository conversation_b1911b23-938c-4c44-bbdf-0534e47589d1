package com.yxt.talent.bk.core.usergroup.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 群组标签引用关系;
 */
@Getter
@Setter
@TableName(value = "bk_user_group_tag")
public class UserGroupTag {
	/**
	 * 唯一 ID
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	/**
	 * 企业id
	 */
	@TableField(value = "org_id")
	private String orgId;

	/**
	 * 是否删除(0-未删除,1-已删除)
	 */
	@TableField(value = "deleted")
	private Integer deleted;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time")
	private LocalDateTime createTime;

	/**
	 * 修改人id
	 */
	@TableField(value = "update_user_id")
	private String updateUserId;

	/**
	 * 修改时间
	 */
	@TableField(value = "update_time")
	private LocalDateTime updateTime;

	/**
	 * 创建人id
	 */
	@TableField(value = "create_user_id")
	private String createUserId;

	/**
	 * 群组 ID
	 */
	@TableField(value = "group_id")
	private Long groupId;

	/**
	 * 标签 ID
	 */
	@TableField(value = "tag_id")
	private Long tagId;

	/**
	 * 群组名称
	 */
	@TableField(value = "group_name")
	private String groupName;
}