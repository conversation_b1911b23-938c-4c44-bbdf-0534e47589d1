package com.yxt.talent.bk.svc.heir.component;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import com.yxt.spsdk.audit.bean.OneFieldAuditLog;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.heir.ext.HeirPosExt;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.udp.mapper.UdpDeptMapper;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.common.enums.AuditLogPointEnum;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.talent.bk.svc.heir.bean.HeirEditParam4Log;
import com.yxt.talent.bk.svc.udp.UdpQueryService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * HeirEditLogProvider
 *
 * <AUTHOR> geyan
 * @Date 19/3/24 2:34 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirEditLogProvider implements AuditLogDataProvider<HeirEditParam4Log, OneFieldAuditLog> {
    private final HeirPosRepository heirPosRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final UdpDeptMapper udpDeptMapper;
    private final UdpQueryService udpQueryService;
    private final HeirPosMapper heirPosMapper;
    private final HeirPosTypeSelector heirPosTypeSelector;
    @Override
    public HeirEditParam4Log convertParam(Object param, AuditLogBasicBean logBasic) {
        if (param instanceof HeirEditParam4Log) {
            return (HeirEditParam4Log)param;
        }
        return JSON.parseObject(JSON.toJSONString(param), HeirEditParam4Log.class);
    }

    @Override
    public OneFieldAuditLog before(HeirEditParam4Log param, AuditLogBasicBean logBasic) {
        if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_PERM_ENABLE)) {
            int permissionEnable = Optional.ofNullable(heirPosMapper.getById(logBasic.getOrgId(), param.getPosId()))
                    .map(HeirPosExt::getPermissionFlag).orElse(YesOrNo.NO.getValue());
            return new OneFieldAuditLog("权限", permissionEnableDesc(permissionEnable));
        }
        return null;
    }

    @Override
    public OneFieldAuditLog after(HeirEditParam4Log param, AuditLogBasicBean logBasic) {
        String fieldName;
        if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_PERM_ENABLE)) {
            return new OneFieldAuditLog("权限", permissionEnableDesc(param.getPermissionEnable()));
        } else if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_PERM_USER)) {
            fieldName = "岗位可见人员";
        } else {
            fieldName = "继任者列表";
        }
        return new OneFieldAuditLog(fieldName, udpLiteUserRepository.userNames4Log(logBasic.getOrgId(), param.getUserIds()));
    }

    @Override
    public Pair<String, String> entityInfo(HeirEditParam4Log param, OneFieldAuditLog beforeObj, OneFieldAuditLog afterObj, AuditLogBasicBean logBasic) {
        String posName = heirPosTypeSelector.getPosNameById(logBasic.getOrgId(), param.getPosId()).getValue();
        String entityName;
        if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_PERM_ENABLE)) {
            //继任地图-{关键岗位名称}-设置权限
            entityName = String.format("继任地图-%s-设置权限", posName);
        } else if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_PERM_USER)) {
            //继任地图-{关键岗位名称}-设置权限人员
            entityName = String.format("继任地图-%s-设置权限人员", posName);
        } else if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_ADD_USER)) {
            //继任地图-{关键岗位名称}-添加继任者
            entityName = String.format("继任地图-%s-添加继任者", posName);
        } else {
            //继任地图-{关键岗位名称}-移除继任者
            entityName = String.format("继任地图-%s-移除继任者", posName);
        }
        return Pair.of(param.getPosId(), entityName);
    }

    private String permissionEnableDesc(Integer permissionEnable) {
        return CommonUtils.isTrue(permissionEnable) ? "开启" : "关闭";
    }
}
