package com.yxt.talent.bk.core.heir.ext;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * HeirPosUserEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:54 am
 */
@Getter
@Setter
public class ConfigPageImageExt implements Serializable {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "图片地址")
    private String imageUrl;

    @Schema(description = "链接地址")
    private String linkUrl;

}
