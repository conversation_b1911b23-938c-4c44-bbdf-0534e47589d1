package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.bk.common.bean.searchrule.BaseSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @program: sptalentbkapi
 * @description: 群组预览 Bean
 **/
@Data
public class UserGroupPreviewBean {

    @Schema(description = "方案 ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long schemeId;

    @Schema(description = "搜索规则", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private SearchRuleBean searchRuleBean;

    /**
     * 基础信息搜索 json
     */
    @Schema(description = "基础信息搜索 json", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private BaseSearchBean baseSearch;

}
