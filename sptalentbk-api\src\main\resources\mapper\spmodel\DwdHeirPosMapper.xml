<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdHeirPosMapper">
    <select id="listTop" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdHeirPosEntity">
        SELECT h.pos_id,
               h.pos_name,
               h.heir_target_qty,
               h.heir_valid_qty,
               h.risk_level_name,
               h.risk_level_color
        FROM dwd_heir_pos h
        WHERE h.pos_type = ${posType}
        AND h.deleted = 0
        AND h.org_id = '${orgId}'
        <if test="deptId != null and deptId != ''">
            <choose>
                <when test="posType == 0">
                    and exists(select 1 from dwm_dept_position
                    where org_id = '${orgId}' and h.pos_id = position_id
                    and yearly = ${yearly} and dept_id = '${deptId}')
                </when>
                <otherwise>
                    and exists(select 1 from dim_dept_closure
                    where org_id = '${orgId}' and h.pos_id = dept_id and deleted = 0
                    and parent_id = '${deptId}')
                </otherwise>
            </choose>
        </if>
        order by h.heir_valid_qty desc, h.update_time desc limit 10
    </select>
</mapper>
