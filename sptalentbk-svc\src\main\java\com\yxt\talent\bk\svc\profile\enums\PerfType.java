package com.yxt.talent.bk.svc.profile.enums;

import lombok.Getter;

public enum PerfType {

    //0-月度绩效, 1-季度绩效, 2-半年度，3-年度

    /**
     * 标签来源(0-内置,1-自建,2-固定)
     */
    MONTH(0, "月度绩效",
            "apis.talentbk.perf.type0.name"),
    QUARTER(1, "季度绩效",
            "apis.talentbk.perf.type1.name"),
    HALF_YEAR(2, "半年度",
            "apis.talentbk.perf.type2.name"),
    YEAR(3, "年度",
            "apis.talentbk.perf.type3.name");

    @Getter
    private Integer type;
    @Getter
    private String name;
    @Getter
    private String nameKey;

    PerfType(int type, String name, String nameKey) {
        this.type = type;
        this.name = name;
        this.nameKey = nameKey;
    }


    public static String getNameByType(Integer type) {
        for (PerfType aiChatGptTypeEnum : PerfType.values()) {
            if (aiChatGptTypeEnum.getType().equals(type)) {
                return aiChatGptTypeEnum.getName();
            }
        }
        return "";
    }

    public static String getNameKeyByType(Integer type) {
        for (PerfType aiChatGptTypeEnum : PerfType.values()) {
            if (aiChatGptTypeEnum.getType().equals(type)) {
                return aiChatGptTypeEnum.getNameKey();
            }
        }
        return "";
    }

}
