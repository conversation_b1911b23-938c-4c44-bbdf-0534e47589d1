package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "dwd_user_jq_task_rv")
public class DwdUserIndicatorResult {
    private String id;
    private String orgId;
    private String thirdUserId;
    private String userId;
    private String indicatorId;
    private String indicatorName;
    /**
     * 指标类别：0-基础、1-能力、2-技能、3-知识、4-任务
     */
    private Integer indicatorCategory;
    private String levelName;
    private Integer levelOrder;
    private BigDecimal score;
    private BigDecimal scoreTen;
    private Integer qualified;
    private String modelId;
    private Date createTime;
    private Date updateTime;
}
