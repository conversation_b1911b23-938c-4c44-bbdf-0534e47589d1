package com.yxt.talent.bk.common.utils;

import cn.hutool.core.lang.Validator;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.handle.L10NUdpTranslateHandle;
import com.yxt.common.Constants;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.*;
import com.yxt.export.I18nComponent;
import com.yxt.idworker.YxtIdWorker;
import com.yxt.spmodel.facade.bean.rule.LabelConditionInfo;
import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import com.yxt.talent.bk.common.bean.PageParam;
import com.yxt.talent.bk.common.bean.udp.UdpLangFullBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangKeywordBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.enums.NumberEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import jakarta.servlet.http.HttpServletRequest;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.io.File;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public final class TalentbkUtil {
    private static final String CREATE_USER_ID = "createUserId";
    private static final String UPDATE_USER_ID = "updateUserId";
    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_TIME = "updateTime";
	private static final Pattern PATTERN = Pattern
			.compile("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？+-]"); // NOSONAR

	public static final String DATETIME_FORMAT_CHINESE = "yyyy年MM月dd日 HH时mm分";

	public static final String FIELD_CREATE_USER_ID = "createUserId";
	public static final String FIELD_UPDATE_USER_ID = "updateUserId";
	public static final int SPILT_NUM=50;
    public static Consumer<List<UdpLangFullBean>> deptPathSwap;

	public static BigDecimal divide(int a, int b) {
		return divide(a, b, 2, BigDecimal.ROUND_HALF_UP);
	}

	private TalentbkUtil() {
	}

	public static String getMapKey(Map<String, String> map, String key) {
		if (StringUtils.isEmpty(key) || StringUtils.isEmpty(map.get(key))) {
			return key;
		} else {
			return map.get(key);
		}
	}

	public static String getMapKey4Object(Map<Object, Object> map, String key) {
	    if (StringUtils.isEmpty(key)) {
	        return key;
        }
		Object value = map.get(key);
		if (StringUtils.isEmpty(key) || value == null) {
			return key;
		} else {
			return value.toString();
		}
	}

	public static BigDecimal divide(int a, int b, int scale, int round) {
		if (a == 0 || b == 0) {
			return BigDecimal.ZERO;
		}
		return BigDecimal.valueOf((float) a / b).setScale(scale, round);
	}

	public static boolean isNumeric(String str) {
		if (StringUtils.isNotBlank(str)) {
			return str.matches("^(([1-9]{1}\\d{0,10})|(0{1}))(\\.\\d{1,2})?$");
		} else {
			return false;
		}
	}

	public static int str2Int(String str, int errorReturn) {
		try {
			return Integer.parseInt(str);
		} catch (NumberFormatException e) {
			return errorReturn;
		}
	}

	public static double str2Double(String str, double errorReturn) {
		try {
			return Double.parseDouble(str);
		} catch (NumberFormatException e) {
			return errorReturn;
		}
	}

	/**
	 * 截取positionList中的岗位id 岗位ID名称，id@@name,id2@@name2
	 * @param arrs
	 * @param arr
	 * @return
	 */
	public static List<String> splitByStrIndex(String[] arrs, String arr) {
	    if(arrs == null){
	        return Lists.newArrayList();
        }
		List<String> result = new ArrayList<>();
		for (String temp : arrs) {
			int index = temp.indexOf(arr);
            String target = temp;
            if(index!=-1){
                target = temp.substring(NumberEnum.ZERO.getNumber(), index);
            }
			result.add(target);
		}
		return result;
	}

	public static List<String> getNameByStrIndex(String[] arrs, String arr) {
		List<String> result = new ArrayList<>();
		for (String temp : arrs) {
			int index = temp.indexOf(arr);
			String target = temp.substring(index + 2);
			result.add(target);
		}
		return result;
	}

	public static boolean checkSpecialCharacter(String str) {
		return PATTERN.matcher(str).find();
	}

	/**
	 * 处理查询中SQL 通配符的问题
	 * @param str
	 * @return
	 */
	public static String escapeStr(String str) {
		if (StringUtils.isBlank(str)) {
			return StringUtils.EMPTY;
		}

		StringBuilder temp = new StringBuilder();
		for (int i = 0; i < str.length(); i++) {
			if (str.charAt(i) == '%' || str.charAt(i) == '_') {
				temp.append("\\").append(str.charAt(i));
			} else {
				temp.append(str.charAt(i));
			}
		}
		return temp.toString();
	}

	public static PageParam getPage(HttpServletRequest request) {

		PageParam page = new PageParam();
		int limit = StringUtil.str2Int(request.getParameter(Constants.PARAM_NAME_LIMIT), Constants.DEFAULT_LIMIT);
		if (limit > Constants.MAX_LIMIT) {
			limit = Constants.MAX_LIMIT;
		} else if (limit <= 0) {
			limit = Constants.DEFAULT_LIMIT;
		}
		page.setLimit(limit);

		int offset = StringUtil.str2Int(request.getParameter(Constants.PARAM_NAME_OFFSET), 0);
		page.setOffset(offset);

		return page;
	}

	public static String formatDate(Date date, String pattern) {
		if (date == null) {
			return StringUtils.EMPTY;
		}
		if (pattern == null) {
			return FastDateFormat.getInstance(DATETIME_FORMAT_CHINESE).format(date);
		}
		return FastDateFormat.getInstance(pattern).format(date);
	}

    /**
	 *
	 */
	public static OrderItem generateOrderItem(PageRequest pageRequest) {
		OrderItem orderItem = new OrderItem();
		orderItem.setColumn(StringUtil.camelToUnderline(pageRequest.getOrderBy()));
		if (StringUtils.isNotBlank(pageRequest.getDirection())) {
			orderItem.setAsc(StringUtils.equalsIgnoreCase(pageRequest.getDirection(), TalentBkConstants.SQL_ORDER_ASC));
		} else {
			orderItem.setAsc(Boolean.FALSE);
		}
		return orderItem;
	}

	public static OrderItem generateOrderItem(PageRequest pageRequest, String suffix) {
		OrderItem orderItem = new OrderItem();
		orderItem.setColumn(suffix + StringUtil.camelToUnderline(pageRequest.getOrderBy()));
		if (StringUtils.isNotBlank(pageRequest.getDirection())) {
			orderItem.setAsc(StringUtils.equalsIgnoreCase(pageRequest.getDirection(), TalentBkConstants.SQL_ORDER_ASC));
		} else {
			orderItem.setAsc(Boolean.FALSE);
		}
		return orderItem;
	}

	public static List<String> getPositionIds(String positionList) {
		String[] arr = org.apache.commons.lang.StringUtils.split(positionList, Constants.SEPARATOR_CHAR_COMMA);
		return splitByStrIndex(arr, Constants.SEPARATOR_CHAR_ATAT);
	}

	/**
	 * 拼接主岗与兼岗，设置到positionList属性
	 * @param positionId 主岗id
	 * @param positionName 主岗名称
	 * @param parttimePosition 兼岗id@@兼岗名称
	 * @return
	 */
	public static String joinPositionList(String positionId, String positionName, String parttimePosition) {
		StringBuilder positionList = new StringBuilder();
		if (StringUtils.isNotBlank(positionId)) {
			positionList.append(positionId);
			positionList.append(Constants.SEPARATOR_CHAR_ATAT);
			positionList.append(positionName);
		}
		if (StringUtils.isNotBlank(parttimePosition)) {
			positionList.append(Constants.SEPARATOR_CHAR_COMMA);
			positionList.append(parttimePosition);
		}
		return new String(positionList);
	}

	public static <T> void setBaseFields(T entity, String targetObjectId, String targetOrgId, Date currentTime,
			Map<Object, Object> userIdMap) {
		String[] fields = new String[]{"id", "orgId", FIELD_CREATE_USER_ID, FIELD_UPDATE_USER_ID, CREATE_TIME, UPDATE_TIME};
		String sourceCreateUserId = null;
		String sourceUpdateUserId = null;
		if (ReflectionUtil.hasField(entity, FIELD_CREATE_USER_ID)) {
			sourceCreateUserId = (String) ReflectionUtil.invokeGetter(entity, FIELD_CREATE_USER_ID);
		}
		if (ReflectionUtil.hasField(entity, FIELD_UPDATE_USER_ID)) {
			sourceUpdateUserId = (String) ReflectionUtil.invokeGetter(entity, FIELD_UPDATE_USER_ID);
		}

		Object tempCreateId = userIdMap.get(sourceCreateUserId);
		Object tempUpdateId = userIdMap.get(sourceUpdateUserId);
		String tempCreateIdStr = tempCreateId == null ? null : tempCreateId.toString();
		String tempUpdateIdStr = tempUpdateId == null ? null : tempUpdateId.toString();
		String targetCreateUserId = StringUtils.isBlank(tempCreateIdStr) ? sourceCreateUserId : tempCreateIdStr;
		String targetUpdateUserId = StringUtils.isBlank(tempUpdateIdStr) ? sourceUpdateUserId : tempUpdateIdStr;

		Object[] value = new Object[]{targetObjectId, targetOrgId, targetCreateUserId, targetUpdateUserId, currentTime,
				currentTime};

		setDefaultValues(entity, fields, value);
	}

	private static <T> void setDefaultValues(T entity, String[] fields, Object[] value) {
		for (int i = 0; i < fields.length; ++i) {
			String field = fields[i];
			if (ReflectionUtil.hasField(entity, field)) {
				ReflectionUtil.invokeSetter(entity, field, value[i]);
			}
		}
	}

    /**
     * 评估分数转成字符串输出(去除末位0)
     * @param level 评估分数
     * @param scale 有效小数位
     * @param roundMode roundMode
     * @return level字符串
     */
	public static String convertDouble2Str(Double level, int scale, int roundMode) {
        if (level == null) {
            return StringUtils.EMPTY;
        }
        return BigDecimal.valueOf(level).setScale(scale, roundMode).toPlainString();
    }

    /**
     * 统一设置结果的小数保留位数
     */
    public static BigDecimal setScale4BigDecimal(BigDecimal value, int scale, int roundMode) {
        if (value == null) {
            value = BigDecimal.ZERO;
        }
        return value.setScale(scale, roundMode);
    }

    public static BigDecimal setScale4BigDecimal(BigDecimal value) {
        return setScale4BigDecimal(value, 2, BigDecimal.ROUND_DOWN);
    }

    /**
     * 分割列表
     * @param list 待分割列表
     * @param len 分割长度
     */
    public static <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.isEmpty() || len < 1) {
            return new ArrayList<>();
        }

        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }

        return result;
    }

    public static <T> List<List<T>> splitList(List<T> list) {
        return splitList(list, 1000);
    }

    /**
     * 计算属于userIds1，不属于userIds2的用户Id列表
     *
     * @param collection1 用户id列表
     * @param collection2 用户id列表
     * @return 属于userIds1，不属于userIds2的用户Id列表
     */
    public static <T> List<T> calculateDifferenceUserIds(
            Collection<T> collection1, Collection<T> collection2) {
        if (CollectionUtils.isEmpty(collection1)) {
            return Lists.newArrayListWithCapacity(0);
        }
        if (CollectionUtils.isEmpty(collection2)) {
            return new ArrayList<>(collection1);
        }
        Set<T> set1 = new HashSet<>(collection1);
        Set<T> set2 = new HashSet<>(collection2);
        Sets.SetView<T> differenceSet = Sets.difference(set1, set2);
		// differenceSet集合中不能有null元素，因为下面的immutableCopy()不支持，更换为构造函数传入的方式
		return new ArrayList<>(differenceSet);
    }

    /**
     * 计算属于userIds1，同时属于userIds2的用户Id列表
     *
     * @param collection1 用户id列表
     * @param collection2 用户id列表
     * @return 属于userIds1，同时属于userIds2的用户Id列表
     */
    public static <T> List<T> calculateCommonUserIds(Collection<T> collection1, Collection<T> collection2) {
        if (CollectionUtils.isEmpty(collection1) || CollectionUtils.isEmpty(collection2)) {
            return Lists.newArrayListWithCapacity(0);
        }
        Set<T> set1 = new HashSet<>(collection1);
        Set<T> set2 = new HashSet<>(collection2);
        Sets.SetView<T> differenceSet = Sets.intersection(set1, set2);
		// differenceSet集合中不能有null元素，因为下面的immutableCopy()不支持，更换为构造函数传入的方式
		return new ArrayList<>(differenceSet);
    }

    /**
     * 比较两个List集合是否相等
     * <p>1. 如果一个List的引用为<code>null</code>，或者其包含的元素个数为0，那么该List在本逻辑处理中都算作空；
     * <p>2. 泛型参数E涉及到对象，所以需要确保正确实现了对应对象的<code>equal()</code>方法。
     * <p>3. 两个list不能同时为空
     * @param list1 list1
     * @param list2 list2
     * @return true-相等 false-不相等
     */
    public static <T> boolean isListEqual(List<T> list1, List<T> list2) {
        // 两个list引用相同
        if (list1 == list2) {
            return true;
        }
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isNotEmpty(list2)) {
            return false;
        }
        if (CollectionUtils.isEmpty(list2) && CollectionUtils.isNotEmpty(list1)) {
            return false;
        }
        // 两个list元素个数不相同
        if (list1.size() != list2.size()) {
            return false;
        }
        // 两个list元素个数已经相同，再比较两者内容
        // 采用这种可以忽略list中的元素的顺序
        // 涉及到对象的比较是否相同时，确保实现了equals()方法
        return list1.containsAll(list2);
    }

    /**
     * 获取处理后的statusSource 可参考StudyConfigStatusSourceEnum
     * 原因：因为 0-岗位能力方案创建，1-普通配课发布 同属于岗位学习，在某些策略的处理上应当做同一类型处理
     * @return 0-岗位学习，2-培训地图，3-测训
     */
    public static Integer handleStatusSource(Integer statusSource) {
        if(statusSource==1){
            return 0;
        }
        return statusSource;
    }

    public static String buildEsQueryString(String query) {
        StringBuilder builder = new StringBuilder();
        builder.append("{\"query\":").append(query).append("}");
        return builder.toString();
    }

    /**
     * 生成雪花id
     *
     * @param
     * @return long
     * <AUTHOR>
     * @date 2021/12/14 13:53
     **/
    public static long snowFLowId() {
        return YxtIdWorker.getId();
    }

    public static <T> void setCreateInfo(String createUserId, T entity) {
        // 默认属性
        String[] fields = {CREATE_USER_ID, UPDATE_USER_ID, CREATE_TIME, UPDATE_TIME};
        // 设置主键属性
        Field field = ReflectionUtil.getAccessibleField(entity, "id");
        if (field != null && field.getType().equals(String.class)) {
            Object pkValue = ReflectionUtil.getFieldValue(entity, "id");
            if (pkValue == null || "".equals(pkValue)) {
                setDefaultValues(entity, new String[]{"id"}, new Object[]{ApiUtil.getUuid()});
            }
        }

        // 默认值
        Object[] value = new Object[4];
        value[0] = createUserId;
        value[1] = createUserId;

        field = ReflectionUtil.getAccessibleField(entity, CREATE_TIME);
        value[2] = initDateValue(field);
        field = ReflectionUtil.getAccessibleField(entity, UPDATE_TIME);
        value[3] = initDateValue(field);

        // 填充默认属性值
        setDefaultValues(entity, fields, value);

        // 设置version 默认值
        Field fieldVersion = ReflectionUtil.getAccessibleField(entity, "version");
        if (fieldVersion != null && fieldVersion.getType().equals(Integer.class)) {
            setDefaultValues(entity, new String[]{"version"}, new Object[]{1});
        }
        // 设置deleted 默认值
        Field deletedVersion = ReflectionUtil.getAccessibleField(entity, "deleted");
        if (deletedVersion != null && deletedVersion.getType().equals(Integer.class)) {
            setDefaultValues(entity, new String[]{"deleted"}, new Object[]{0});
        }
    }

    public static <T> void setUpdatedInfo(String updateUserId, T entity) {
        // 默认属性
        String[] fields = {UPDATE_USER_ID, UPDATE_TIME};
        Object[] value = new Object[2];
        value[0] = updateUserId;
        Field field = ReflectionUtil.getAccessibleField(entity, UPDATE_TIME);
        if (field != null && field.getType().equals(Date.class)) {
            Date now = DateUtil.currentTime();
            value[1] = now;
        } else if (field != null && field.getType().equals(String.class)) {
            String now = DateUtil.now(Constants.SDF_YEAR2MILLSECOND);
            value[1] = now;
        }

        // 填充默认属性值
        setDefaultValues(entity, fields, value);
    }

    private static Object initDateValue(Field field) {
        Object value = null;
        if (field != null && field.getType().equals(Date.class)) {
            value = DateUtil.currentTime();
        } else if (field != null && field.getType().equals(String.class)) {
            value = DateUtil.now(Constants.SDF_YEAR2MILLSECOND);
        } else if (field != null && field.getType().equals(LocalDateTime.class)) {
            value = LocalDateTime.now();
        }
        return value;
    }

    public static List<Long> str2Long(List<String> traningIds) {
        if (CollectionUtils.isEmpty(traningIds)) {
            return new ArrayList<>();
        }
        return traningIds.stream()
            .filter(Objects::nonNull)
            .filter(StringUtils::isNotBlank)
            .map(Long::parseLong)
            .collect(Collectors.toList());
    }

    public static void execAfterCommitIfHas(Runnable runnable) {
        if (runnable == null) {
            return;
        }
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {

                @Override
                public void afterCommit() {
                    log.debug("LOG10740:");
                    runnable.run();
                }
            });
        } else {
            runnable.run();
        }
    }

    public static <T extends UdpLangSupport> List<T> bkUdpTranslate(String orgId, Supplier<List<T>> listGetter) {
        return bkUdpTranslate(Pair.of(null, orgId), false, listGetter.get());
    }

    public static <T extends UdpLangSupport> List<T> bkUdpTranslate(String orgId, List<T> list) {
        return bkUdpTranslate(Pair.of(null, orgId), false, list);
    }

    public static <T extends UdpLangSupport> List<T> bkUdpTranslate(String orgId, boolean deptFullPath, List<T> list) {
        return bkUdpTranslate(Pair.of(null, orgId), deptFullPath, list);
    }

    public static <T extends UdpLangSupport> List<T> bkUdpTranslate(Pair<String, String> langAndOrgId,
                                                                 boolean deptFullPath,
                                                                 List<T> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            String lang = langAndOrgId.getKey();
            String orgId = langAndOrgId.getValue();
            List<UdpLangFullBean> deptLangList = Lists.newArrayList();
            if (StringUtils.isEmpty(lang)) {
                if (ApiUtil.getRequestByContext() == null) {
                    //没有指定lang,并且不是http请求线程时不处理(job,mq,async)
                    return list;
                }
                try {
                    lang = SpringContextHolder.getBean(AuthService.class).getLocale().toString();
                } catch (Exception e) {
                    return list;
                }
            }
            boolean useDeptFullPath = deptFullPath && CollectionUtils.isNotEmpty(
                    SpringContextHolder.getBean(L10NUdpTranslateHandle.class)
                            .provider().i18NFilterEnableOrgId(Lists.newArrayList(orgId), Lists.newArrayList(lang)));
            List<UdpLangFullBean> transList = list.stream()
                    .map(item -> convertToLangFull(item, useDeptFullPath, deptLangList))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (deptPathSwap != null) {
                deptPathSwap.accept(deptLangList);
            }
            udpTranslate(Pair.of(lang, orgId), transList);
        }
        return list;
    }

    private static UdpLangFullBean convertToLangFull(UdpLangSupport item,
                                                     boolean useDeptFullPath,
                                                     List<UdpLangFullBean> deptLangList) {
        if (item == null) {
            return null;
        }
        int translateQty = 0;
        UdpLangFullBean fullBean = new UdpLangFullBean();
        {
            UdpLangUnitBean unitBean = item.userLangProperty();
            if (UdpLangUnitBean.validUnit(unitBean)) {
                translateQty++;
                fullBean.setId(unitBean.getId());
                fullBean.setFullname(unitBean.getName());
                fullBean.setFullnameSetter(unitBean.getNameSetter());
            }
        }
        {
            UdpLangUnitBean unitBean = item.deptLangProperty();
            if (UdpLangUnitBean.validUnit(unitBean)) {
                translateQty++;
                fullBean.setDeptId(unitBean.getId());
                fullBean.setDeptName(unitBean.getName());
                fullBean.setDeptNameSetter(unitBean.getNameSetter());
                if (useDeptFullPath) {
                    deptLangList.add(fullBean);
                }
            }
        }
        {
            UdpLangUnitBean unitBean = item.positionLangProperty();
            if (UdpLangUnitBean.validUnit(unitBean)) {
                translateQty++;
                fullBean.setPositionId(unitBean.getId());
                fullBean.setPositionName(unitBean.getName());
                fullBean.setPositionNameSetter(unitBean.getNameSetter());
            }
        }
        return translateQty > 0 ? fullBean : null;
    }

    public static <T extends L10NContent> void udpTranslate(String orgId, List<T> list) {
        udpTranslate(Pair.of(null, orgId), list);
    }

    public static <T extends L10NContent> void udpTranslate(Pair<String, String> langAndOrgId, List<T> list) {
        udpCorpTranslate(Pair.of(langAndOrgId.getKey(), Lists.newArrayList(langAndOrgId.getValue())), list);
    }

    public static <T extends L10NContent> void udpCorpTranslate(Pair<String, List<String>> langAndCorpOrgIds, List<T> list) {
        List<String> corpOrgIds = langAndCorpOrgIds.getValue();
        String lang = langAndCorpOrgIds.getKey();
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(corpOrgIds)) {
            return;
        }
        L10NUdpTranslateHandle translateHandle = SpringContextHolder.getBean(L10NUdpTranslateHandle.class);
        if (StringUtils.isEmpty(lang)) {
            if (ApiUtil.getRequestByContext() == null) {
                //没有指定lang,并且不是http请求线程时不处理(job,mq,async)
                return;
            }
            try {
                lang = SpringContextHolder.getBean(AuthService.class).getLocale().toString();
            } catch (Exception e) {
                return;
            }
        }
        Class<T> itemType = (Class<T>) list.get(0).getClass();
        try {
            translateHandle.translate(corpOrgIds, lang, itemType, list);
        } catch (Exception e) {
            log.error("udpTranslate fail clazz {}", itemType.getSimpleName());
        }
    }

    public static UdpLangKeywordBean langKeywordQuery(String orgId, String keyword, boolean searchUser, boolean searchPosition) {
        UdpLangKeywordBean ret = new UdpLangKeywordBean();
        List<String> corpOrgIds = Lists.newArrayList(orgId);
        L10NUdpTranslateHandle translateHandle = SpringContextHolder.getBean(L10NUdpTranslateHandle.class);
        if (StringUtils.isNotBlank(keyword) && CollectionUtils.isNotEmpty(translateHandle.provider()
                .i18NFilterEnableOrgId(corpOrgIds, null))) {
            ret.setEnabled(true);
            if (searchUser) {
                ret.setUserIds(BeanCopierUtil.convertList(
                        translateHandle.provider().searchContentByKey(corpOrgIds, ResourceTypeEnum.USER, keyword).getDatas(),
                        IdName::getId));
            }
            if (searchPosition) {
                ret.setPositionIds(BeanCopierUtil.convertList(
                        translateHandle.provider().searchContentByKey(corpOrgIds, ResourceTypeEnum.POSITION, keyword).getDatas(),
                        IdName::getId));
            }
        }
        return ret;
    }

    public static PagingList emptyPage(int limit) {
        return new PagingList<>(Collections.emptyList(), new Paging(limit, 0, 0, 0));
    }

    public static <T> Page<T> toPage(PageRequest pageReq) {
        return new Page<>(pageReq.getCurrent(), pageReq.getSize());
    }

    public static <T> Page<T> allPage() {
        Page page = new Page<>(1, Integer.MAX_VALUE);
        page.setSearchCount(false);
        return page;
    }

    public static <T> Page<T> toPage(int offset, int limit) {
        if (limit > Constants.MAX_LIMIT) {
            limit = Constants.MAX_LIMIT;
        } else if (limit <= 0) {
            limit = Constants.DEFAULT_LIMIT;
        }
        int current = (offset + limit) / limit;
        if (current <= 0) {
            current = Constants.DEFAULT_CURRENT_PAGE;
        }
        return new Page<>(current, limit);
    }

    public static String labelRuleGroupMd5(LabelConditionJsonBean origRuleGroup) {
        if (origRuleGroup == null || CollectionUtils.isEmpty(origRuleGroup.getConditions())) {
            return StringPool.EMPTY;
        }
        //复制一份
        LabelConditionJsonBean ruleGroup = JSON.parseObject(JSON.toJSONString(origRuleGroup), LabelConditionJsonBean.class);
        List<LabelConditionInfo> conditions = ruleGroup.getConditions();
        for (int i = 0; i < conditions.size(); i++) {
            LabelConditionInfo conditionInfo = conditions.get(i);
            if (CollectionUtils.isEmpty(conditionInfo.getRules())) {
                conditions.set(i, null);
            } else {
                conditionInfo.getRules().forEach(rule -> {
                    rule.setUuid(null);
                    rule.setDescription(null);
                });
            }
        }
        return MD5.create().digestHex(JSON.toJSONString(ruleGroup));
    }

    public static void deleteFile(File file) {
        if (file != null && file.exists()) {
            if (!file.delete()) {
                log.warn("file {} deleted false", file.getAbsolutePath());
            }
        }
    }

    public static String getMessage(String key) {
        return getMessage(key, null);
    }
    public static String getMessage(String key, String localeStr) {
        return SpringContextHolder.getBean(I18nComponent.class).getI18nValue(key, localeStr);
    }

    public static Map<String, String> getAnyMessage(String localeStr, String... keys) {
        Map<String, String> retMap = new HashMap<>();
        for (String key : keys) {
            String mapKey = key;
            int lastDot = mapKey.lastIndexOf(".");
            if (lastDot > -1) {
                mapKey = mapKey.substring(lastDot + 1);
            }
            retMap.put(mapKey, getMessage(key, localeStr));
        }
        return retMap;
    }

    public static String toJson(Object obj) {
        return obj == null ? null : JSON.toJSONString(obj);
    }


    /**
     * 验证uuid
     */
    public static void isUuid(String str, String errKey) {
        if (!StringUtils.isNotBlank(str)) {
            return;
        }
        Validate.isTrue(Validator.isUUID(str), errKey);
    }

    public static void isUuids(List<String> strs, String errKey) {
        for (String str : strs) {
            isUuid(str, errKey);
        }
    }
}
