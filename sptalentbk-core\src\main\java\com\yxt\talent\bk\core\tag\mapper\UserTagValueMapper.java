package com.yxt.talent.bk.core.tag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.bk.core.tag.bean.TagValueBean;
import com.yxt.talent.bk.core.tag.bean.UserBaseInfoBean;
import com.yxt.talent.bk.core.tag.bean.UserTagBaseBean;
import com.yxt.talent.bk.core.tag.bean.UserTagValueBaseBean;
import com.yxt.talent.bk.core.tag.entity.UserTagValueEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/15
 */
@Mapper
public interface UserTagValueMapper extends BaseMapper<UserTagValueEntity> {

    List<UserTagValueBaseBean> listUserTagValue(@Param("orgId") String orgId,
            @Param("tagValueIds") Collection<String> tagValueIds, @Param("userIds") Collection<String> userIds);

    IPage<UserBaseInfoBean> listPageTagValueUser(IPage<UserBaseInfoBean> page, @Param("orgId") String orgId,
            @Param("keyword") String keyword, @Param("tagId") String tagId, @Param("tagValueId") String tagValueId);

    List<UserBaseInfoBean> listPageTagValueUser(@Param("orgId") String orgId, @Param("keyword") String keyword,
            @Param("tagId") String tagId, @Param("tagValueId") String tagValueId);

    List<UserTagBaseBean> listUserTagValueName(@Param("orgId") String orgId, @Param("tagId") String tagId,
            @Param("userIds") Collection<String> userIds);

    /**
     * 删除标签值关联的用户
     *
     * @param orgId       机构id
     * @param tagIds      标签id列表
     * @param userIds     用户id列表
     * @param operatorId  操作人id
     * @param updateTime  更新时间
     * @param tagValueIds 标签值id列表
     */
    void delTagValueUser(@Param("orgId") String orgId, @Param("tagIds") Collection<String> tagIds,
            @Param("userIds") Collection<String> userIds, @Param("operatorId") String operatorId,
            @Param("updateTime") Date updateTime, @Param("tagValueIds") Collection<String> tagValueIds);

    /**
     * 删除标签值关联的用户
     *
     * @param orgId       机构id
     * @param tagIds      标签id列表
     * @param userIds     用户id列表
     * @param operatorId  操作人id
     * @param updateTime  更新时间
     */
    void deleteByTagIdsAndUserIds(@Param("orgId") String orgId, @Param("tagIds") Collection<String> tagIds,
            @Param("userIds") Collection<String> userIds, @Param("operatorId") String operatorId,
            @Param("updateTime") Date updateTime);

    void delTagOtherValueUser(@Param("orgId") String orgId, @Param("tagIds") Collection<String> tagIds,
            @Param("userIds") Collection<String> userIds, @Param("operatorId") String operatorId,
            @Param("updateTime") Date updateTime, @Param("tagValueIds") Collection<String> tagValueIds);

    /**
     * 获取用户标签信息
     *
     * @param orgId   机构id
     * @param tagIds  标签id
     * @param userIds 用户id列表
     * @return 用户标签信息
     */
    List<UserTagBaseBean> listUserTagValueByTagId(@Param("orgId") String orgId,
            @Param("tagIds") Collection<String> tagIds, @Param("userIds") Collection<String> userIds);

    /**
     * 获取标签值关联的所有用户id列表
     *
     * @param orgId       机构id
     * @param tagId       标签id
     * @param tagValueIds 标签值id列表
     * @return 用户id列表
     */
    List<String> listTagValueAllUserId(@Param("orgId") String orgId, @Param("tagId") String tagId,
            @Param("tagValueIds") Collection<String> tagValueIds);

    List<TagValueBean> findValueNameTagIdLike(@Param("orgId") String orgId, @Param("keyword") String keyword);

}
