package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "继任者列表")
public class PosUserListReq {

    @Schema(description = "继任id")
    @NotNull(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String posId;

    @Schema(description = "准备度规则等级id")
    private Long prepareLevelId;

    @Schema(description = "继任状态：0:进行中，1:已退出")
    private Integer heirStatus;
}
