package com.yxt.talent.bk.api.controller.pool;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkAuthCodes;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.pool.PoolService;
import com.yxt.talent.bk.svc.pool.bean.Pool4BasicInfo;
import com.yxt.talent.bk.svc.pool.bean.Pool4List;
import com.yxt.talent.bk.svc.pool.bean.Pool4Save;
import com.yxt.talent.bk.svc.pool.bean.Pool4Search;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.yxt.talent.bk.common.constants.TalentBkAuthCodes.BK_AUTH_CODE_POOL_DEL;

@RestController
@RequestMapping(value = "/mgr/pool")
@Slf4j
@AllArgsConstructor
@Tag(name = "人才池")
public class PoolController extends BaseController {
    private final PoolService poolService;

    @Operation(summary = "人才池-列表")
    @Parameters({             @Parameter(name = "limit", description = "每页记录数. 默认值为20.", in = ParameterIn.QUERY),             @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "                     + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY),             @Parameter(name = "orderby", description = "排序字段【createTime、realNum、saturability】", in = ParameterIn.QUERY),             @Parameter(name = "direction", description = "排序方式【asc、desc】", in = ParameterIn.QUERY) })
    @PostMapping(value = "/list", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<Pool4List> findPage(@RequestBody Pool4Search search) {
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        return poolService.findPage(userBasic, ApiUtil.getPageRequest(getRequest()), search);
    }

    @Operation(summary = "人才池-创建")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_ADD, paramExp = "#return")
    @PostMapping(value = "", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(
        value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN,
        codes = {TalentBkAuthCodes.BK_AUTH_CODE_POOL_CREATE}
    )
    public String create(@Valid  @RequestBody Pool4Save bean) {
        UserCacheBasic userCache = getUserCacheBasic();
        return poolService.create(userCache.getOrgId() ,userCache.getUserId(), bean);
    }

    @Operation(summary = "人才池-编辑")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_MOD, paramExp = "#bean.id")
    @PutMapping(value = "", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(
        value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN,
        codes = {TalentBkAuthCodes.BK_AUTH_CODE_POOL_CREATE}
    )
    public void update(@Valid @RequestBody Pool4Save bean) {
        UserCacheBasic userCache = getUserCacheBasic();
        poolService.update(userCache.getOrgId() ,userCache.getUserId(), bean);
    }

    @Operation(summary = "人才池-基本信息")
    @GetMapping(value = "/basic/{id}", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public Pool4BasicInfo basic(@PathVariable String id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return poolService.getBasicInfo(userCache.getOrgId(),id);
    }

    @Operation(summary = "人才池-删除")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_DEL, paramExp = "#id")
    @DeleteMapping(value = "/{id}", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(
        value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN,
        codes = BK_AUTH_CODE_POOL_DEL
    )
    public void delete(@PathVariable String id) {
        UserCacheBasic userCache = getUserCacheBasic();
        poolService.delete(userCache, id);
    }

    @Operation(summary = "人才池-导出")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_EXPORT)
    @PostMapping(value = "/export")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_USER_SEARCH_EXPORT, action = Constants.LOG_TYPE_EXPORT,
          type = AuthType.TOKEN)
    public Map<String, String> exportPoolInfo(@RequestBody Pool4Search search) {
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        UserCacheDetail userDetail = getUserCacheDetail();
        return poolService.exportResult(userBasic, search, userDetail);
    }


}
