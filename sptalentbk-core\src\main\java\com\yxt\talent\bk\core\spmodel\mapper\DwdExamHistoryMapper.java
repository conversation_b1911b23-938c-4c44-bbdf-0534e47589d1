package com.yxt.talent.bk.core.spmodel.mapper;

import com.yxt.talent.bk.core.spmodel.entity.DwdExamHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 考试历史表(DwdExamHistory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 11:00:16
 */
public interface DwdExamHistoryMapper {

   int countByUserId(@Param("orgId") String orgId, @Param("userId") String userId);

    List<DwdExamHistory> listByUserId(@Param("orgId") String orgId, @Param("userId") String userId, @Param("offset") long offset, @Param("limit") long limit);
}

