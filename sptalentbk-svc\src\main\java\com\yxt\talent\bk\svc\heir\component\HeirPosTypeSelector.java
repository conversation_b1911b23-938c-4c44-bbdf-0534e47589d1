package com.yxt.talent.bk.svc.heir.component;

import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.udp.repo.UdpDeptRepository;
import com.yxt.spsdk.audit.base.AuditLogSelector;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * HeirPosTypeSelector
 *
 * <AUTHOR> geyan
 * @Date 20/3/24 2:53 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirPosTypeSelector implements AuditLogSelector<String, Integer> {
    private final HeirPosRepository heirPosRepository;
    private final UdpDeptRepository udpDeptRepository;
    @Override
    public Integer selectResult(String posId, AuditLogBasicBean logBasic) {
        return heirPosRepository.getPosType(posId);
    }

    public Pair<Integer, String> getPosNameById(String orgId, String id) {
        int posType = heirPosRepository.getPosType(id);
        String posName = getPosNameById(orgId, id, posType);
        return Pair.of(posType, posName);
    }

    public String getPosNameById(String orgId, String id, int posType) {
        if (posType == HeirPosTypeEnum.DEPT.getType()) {
            return udpDeptRepository.getNameById(orgId, id);
        } else {
            return udpDeptRepository.getPositionNameById(orgId, id);
        }
    }
}
