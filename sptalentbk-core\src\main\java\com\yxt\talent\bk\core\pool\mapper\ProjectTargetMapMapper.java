package com.yxt.talent.bk.core.pool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.pool.entity.ProjectTargetMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProjectTargetMapMapper extends BaseMapper<ProjectTargetMap> {
    int updateBatch(List<ProjectTargetMap> list);

    int batchInsert(@Param("list") List<ProjectTargetMap> list);

    int insertOrUpdate(ProjectTargetMap record);

    int insertOrUpdateSelective(ProjectTargetMap record);

    int countByOrgIdAndTrainingId(@Param("orgId") String orgId, @Param("trainingId") String trainingId);

    List<ProjectTargetMap> selectByOrgIdAndPoolId(@Param("orgId") String orgId, @Param("poolId") String poolId);

    List<String> selectTargetIdByProjectIdAndTargetType(@Param("orgId") String orgId,
        @Param("projectId") String projectId, @Param("targetType") int targetType);

    List<ProjectTargetMap> selectByOrgIdAndProjectIdAndTargetIdIn(@Param("orgId") String orgId,
        @Param("projectId") String projectId, @Param("targetIds") List<String> targetIds);

    void removeByProjectIdAndTargetIdIn(@Param("orgId") String orgId, @Param("projectId") String projectId,
        @Param("targetIds") List<String> targetIds);

    List<String> selectTargetIdByOrgIdAndProjectId(@Param("orgId") String orgId, @Param("projectId") String projectId);
}
