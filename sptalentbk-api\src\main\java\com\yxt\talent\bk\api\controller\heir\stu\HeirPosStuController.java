package com.yxt.talent.bk.api.controller.heir.stu;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.core.heir.bean.HeirDeptInfoDTO;
import com.yxt.talent.bk.core.heir.bean.HeirUserPosBriefBean;
import com.yxt.talent.bk.core.heir.bean.PosUserBriefBean;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.HeirPosUserService;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirUserPosBriefResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * HeirPosStuController
 *
 * <AUTHOR> geyan
 * @Date 1/9/23 9:22 am
 */
@RestController
@RequestMapping(value = "/mgr/heir/stu")
@Slf4j
@AllArgsConstructor
@Tag(name = "用户数据")
public class HeirPosStuController extends BaseController {

    private final HeirPosService heirPosService;
    private final HeirPosUserService heirPosUserService;

    @Operation(summary = "用户继任详情")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @GetMapping("/heir_brief")
    public HeirUserPosBriefResp userPosBrief() {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("StuUserHeirBrief userId : {} ", user.getUserId());
        List<HeirUserPosBriefBean> list = heirPosService.userPosList(user.getOrgId(), user.getUserId());
        HeirUserPosBriefResp ret = new HeirUserPosBriefResp();
        ret.setDeptList(list.stream().filter(item -> item.getPosType() != HeirPosTypeEnum.POSITION.getType())
            .collect(Collectors.toList()));
        ret.setPositionList(list.stream().filter(item -> item.getPosType() == HeirPosTypeEnum.POSITION.getType())
            .collect(Collectors.toList()));
        return ret;
    }

    @Operation(summary = "继任部门列表")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @PostMapping("/pos_dept_list")
    public PagingList<HeirDeptInfoDTO> posDeptList(@RequestParam String deptId) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheDetail());
        log.info("posDeptList userId : {} deptId : {}", user.getUserId(), deptId);
        return heirPosService.deptHeirList(user, getPage(), deptId);
    }

    @Operation(summary = "继任者列表")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/list_user_brief")
    public List<PosUserBriefBean> listUserBrief(@RequestParam String posId) {
        UserCacheBasic currentUser = getUserCacheBasic();
        return heirPosUserService.listUserBrief(currentUser, posId);
    }
}
