package com.yxt.talent.bk.core.tag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.spsdk.common.bean.SpIdNameBean;
import com.yxt.talent.bk.core.TagCatalog;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Mapper
public interface CatalogMapper extends BaseMapper<TagCatalog> {

    List<SpIdNameBean> getNameByIds(Collection<String> list);
}
