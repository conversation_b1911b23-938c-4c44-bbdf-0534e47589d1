package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(name = "人才画像-课程学习记录")
public class UserCourse4Get {

    @Schema(description = "课程名称")
    private String title;

    @Schema(description = "学习时长")
    private BigDecimal learningTime;

    @Schema(description = "最近学习时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private LocalDateTime lastStudyTime;

    @Schema(description = "学习进度")
    private BigDecimal studySchedule;
}
