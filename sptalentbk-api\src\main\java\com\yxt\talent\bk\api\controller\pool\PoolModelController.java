package com.yxt.talent.bk.api.controller.pool;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.GenericCommonData;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdk.common.bean.FillAmDrawerPair;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.spsdk.common.utils.IArrayUtils;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.CatalogConstants;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.core.common.repo.CatalogRepository;
import com.yxt.talent.bk.core.pool.entity.Pool;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.svc.pool.PoolService;
import com.yxt.talent.bk.svc.pool.bean.Pool4Get;
import com.yxt.talent.bk.svc.pool.bean.Pool4Save;
import com.yxt.talent.bk.svc.pool.bean.PoolCategory4Get;
import com.yxt.talent.bk.svc.pool.bean.TalentPool4Create;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping(value = "/mgr/model/pool/")
@Slf4j
@AllArgsConstructor
@Tag(name = "人才池胶水层")
public class PoolModelController extends BaseController {
    private final CatalogRepository catalogRepository;
    private final PoolRepository poolRepository;
    private final PoolService poolService;


    @Operation(summary = "人才池创建")
    @PostMapping(value = "add", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    public GenericCommonData<String> add(@Validated @RequestBody TalentPool4Create bean) {
        UserCacheBasic userCache = getUserCacheBasic();
        Pool4Save addPool = new Pool4Save();
        addPool.setPoolName(bean.getName());
        addPool.setCatalogId(Optional.ofNullable(IArrayUtils.getFirst(bean.getCatagory())).map(AmSlDrawer4ReqDTO::getId).orElse(null));
        addPool.setExpectNum(Optional.ofNullable(bean.getExpectedPerson()).map(Long::intValue).orElse(null));
        addPool.setRemark(bean.getRemark());
        addPool.setMgrIdList(BeanCopierUtil.convertList(bean.getManager(), AmSlDrawer4ReqDTO::getId));
        return new GenericCommonData(poolService.create(userCache.getOrgId() ,userCache.getUserId(), addPool));
    }

    @Operation(summary = "人才池分类列表")
    @PostMapping(value = "/category/list")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<PoolCategory4Get> pageCategoryList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        String orgId = getUserCacheBasic().getOrgId();
        String queryName = search.getSearch().getValue();
        Page<TagCatalog> page = catalogRepository.listPage(orgId, CatalogConstants.CatalogSource.POOL.getValue(),
                ApiUtil.toPage(request), queryName);
        return BeanCopierUtil.toPagingList(page, item -> {
            PoolCategory4Get category = new PoolCategory4Get();
            category.setId(item.getId());
            category.setOrgId(item.getOrgId());
            category.setName(item.getCatalogName());
            category.setCreateTime(item.getCreateTime());
            category.setUpdateTime(item.getUpdateTime());
            return category;
        });
    }

    @Operation(summary = "人才池列表")
    @PostMapping(value = "/list")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<Pool4Get> pagePoolList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        String orgId = getUserCacheBasic().getOrgId();
        String queryName = search.getSearch().getValue();
        String categoryId = search.getEqVal("catagory");
        IPage<Pool> poolPage = poolRepository.getBaseMapper().findPageBy(ApiUtil.toPage(request), categoryId, orgId,
                queryName, null,null,null);
        return CommonUtils.convertToModel(poolPage, pool -> {
            Pool4Get pool4Get = new Pool4Get();
            pool4Get.setId(pool.getId());
            pool4Get.setName(pool.getPoolName());
            pool4Get.setCreateTime(pool.getCreateTime());
            pool4Get.setUpdateTime(pool.getUpdateTime());
            pool4Get.setActualPerson(pool.getRealNum());
            pool4Get.setExpectedPerson(pool.getExpectNum());
            return pool4Get;
        }, new FillAmDrawerPair<>(catalogRepository.getBaseMapper()::getNameByIds, Pair.of(Pool::getCatalogId, Pool4Get::setCatagory)));
    }
}
