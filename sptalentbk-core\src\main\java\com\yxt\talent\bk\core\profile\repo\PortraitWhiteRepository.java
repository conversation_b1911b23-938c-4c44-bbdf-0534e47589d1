package com.yxt.talent.bk.core.profile.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.core.profile.entity.PortraitWhite;
import com.yxt.talent.bk.core.profile.mapper.PortraitWhiteMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * PortraitWhiteRepository
 *
 * <AUTHOR> harleyge
 * @Date 17/10/24 2:55 pm
 */
@Slf4j
@Repository
@AllArgsConstructor
public class PortraitWhiteRepository extends ServiceImpl<PortraitWhiteMapper, PortraitWhite> {

    public List<PortraitWhite> queryEnableByOrgId(String orgId) {
        LambdaQueryWrapper<PortraitWhite> wrapper = getQueryWrapper();
        wrapper.eq(PortraitWhite::getOrgId, orgId);
        wrapper.eq(PortraitWhite::getWhiteEnable, YesOrNo.YES.getValue());
        return list(wrapper);
    }

    private LambdaQueryWrapper<PortraitWhite> getQueryWrapper() {
        LambdaQueryWrapper<PortraitWhite> wrapper = new LambdaQueryWrapper<>();
        return wrapper;
    }
}
