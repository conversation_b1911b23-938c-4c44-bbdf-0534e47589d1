package com.yxt.talent.bk.core.persona.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("bk_persona_theme")
@EqualsAndHashCode(callSuper = true)
public class PersonaThemeEntity extends CreatorEntity {
    /**
     * 画像主题主键
     */
    @TableId
    private String id;
    /**
     * 机构id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * 主题名称（默认主题）
     */
    @TableField("theme_name")
    private String themeName;
    /**
     * 主题来源(0-内置,1-自建,-1-全部)
     */
    @TableField("theme_source")
    private Integer themeSource;
}
