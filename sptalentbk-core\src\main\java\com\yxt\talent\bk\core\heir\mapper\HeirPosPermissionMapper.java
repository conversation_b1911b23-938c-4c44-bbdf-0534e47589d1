package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.bean.HeirPosPermUserBean;
import com.yxt.talent.bk.core.heir.entity.HeirPosPermissionEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosPermissionExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * HeirPosPermissionMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 2:31 pm
 */
@Mapper
public interface HeirPosPermissionMapper extends BkBaseMapper<HeirPosPermissionEntity> {
    int resTransferCount(@Param("orgId") String orgId, @Param("userIds") List<String> userIds);
    List<HeirPosPermUserBean> resTransferList(@Param("orgId") String orgId, @Param("userIds") List<String> userIds);
    void execResTransfer(@Param("orgId") String orgId, @Param("list") List<HeirPosPermUserBean> list);
    List<HeirPosPermissionExt> list(@Param("orgId") String orgId, @Param("posId") String posId);

    Set<String> listUserId(@Param("orgId") String orgId, @Param("posId") String posId);

    List<String> listExistUserId(@Param("orgId") String orgId, @Param("posId") String posId, @Param("userIds") List<String> userIds);

    List<String> listUserPosIds(@Param("userId") String userId, @Param("posIds") List<String> posIds);

    void deleteByUserIds(@Param("orgId") String orgId, @Param("currentUserId") String currentUserId,
        @Param("currentTime") Date currentTime, @Param("posId") String posId, @Param("userIds") Collection<String> userIds);
}
