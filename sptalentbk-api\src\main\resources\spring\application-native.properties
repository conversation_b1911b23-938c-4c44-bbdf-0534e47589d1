spring.application.name=sptalentbkapi
sptalentapi.swagger.enabled=true

app.version= @project.version@
app.build.time=@build.time@
management.health.elasticsearch.enabled=false
management.health.rabbit.enabled=false

#base
base.jackson.config.enabled=true
waf.webkit.enabled=true
waf.webkit.smooth-down.enabled=true
base.lock.key-prefix=sptalent:lock:
base.zone.date.serializer=false
base.db.hint.enabled=true
base.db.hint.master.keyword=force_master
yxt.id.worker.auto.enable=false
logging.level.com.yxt.common.util.RedisUtil=ERROR

base.jwt.secret=2/TRdKkiyHZuy-imWA*44iJM(1$z_viF
base.jwt.expire=1209600
base.jwt.client.expire=1209600
base.auth.redis.cluster.nodes=10.100.2.96:6379
base.auth.redis.port=6379
base.auth.redis.password=XKH4lCe3Tr9Tpiil$
base.auth.redis.database=0
base.auth.redis.timeout=2000
base.auth.redis.jedis.pool.min-idle=4
base.auth.redis.jedis.pool.max-idle=20
base.auth.redis.jedis.pool.max-active=100
base.auth.redis.jedis.pool.max-wait=10000
base.auth.redis.lettuce.pool.min-idle=4
base.auth.redis.lettuce.pool.max-idle=20
base.auth.redis.lettuce.pool.max-active=100
base.auth.redis.lettuce.pool.max-wait=10000

#downloadcenter
downloadcenter.path=/data/downloadcenter/
downloadcenter.uperr.path=/data/downloadcenter/uperr/
downloadcenter.uperr.domain=https://uperr-phx-di-tc.yunxuetang.com.cn/

#recommend
data.recommend.kng.url=https://sg2.yunxuetang.com/std/recommendapi/vxx2/gwnl/admin/kngs
data.recommend.course.url=https://sg2.yunxuetang.com/std/recommendapi/vxx2/gwnl/user/kngs
big.data.url.ak=ebee5e7493c0527863042334604c92303c2342eb51726295839e52f390fc7110
big.data.url.sk=40f03034e02ee225e9936ff2b217a53029d3ee54733572a17c0136e85d61b586

#audit
base.audit-log.enabled=true
base.audit-log.service-id=sptalentbkapi
base.audit-log.url=https://api-phx-di-tc.yunxuetang.com.cn/sls/auditlog

#feign && ribbon
feign.hystrix.enabled=false
feign.client.config.default.logger-level=full
hystrix.command.default.execution.timeout.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=20000
hystrix.threadpool.default.coreSize=50
hystrix.threadpool.default.maxQueueSize=500
hystrix.threadpool.default.queueSizeRejectionThreshold=500
ribbon.ReadTimeout=10000
ribbon.ConnectTimeout=10000

# aksk
base.skmap.polestarapi=K1l6R8tDF3bfEZ9Pm3ryDr5LLzzL4dxk
base.skmap.sptalentapi=06034afef9e211ed8a9e1c34da4f7358
base.skmap.sptalentrvapi=8p6omwgp6erebkecs67q6aycdgtnwkpm
base.skmap.sptalentbkapi=d8qmy9u79sd0inu7njudyopba6ngjv2c
base.skmap.spevalapi=1edcc95af9e211ed-8a9e1c34da4f7358
base.skmap.spmodelapi=ob1xenc6ezqwtytbv74a0u5mwozmagdu
base.skmap.o2oapi=2fa409e8841a4033972cce5b2f074553

#facade url
spevalapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/speval/
sptalent.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/sptalent/
sptalentrvapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/sptalentrv/
o2oapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/o2o/
cerapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/cer/
kng.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/kng/
evaleng.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/evaleng/
evalapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/eval/
flipapp.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/flipapp/
sspapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/ssp/
oteapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/ote/
polestarapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/polestar/
talent.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/talent/
udp.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/udp/
coreapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/core/
msgapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/msg/
utility.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/utility/
survey.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/survey/
orginit.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/orginit/
udpes.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/udpes/
qida.center.api.url=http://api-qidacenter.yunxuetang.com.cn/v1
udp.api.url=http://devinner.yunxuetang.com.cn/v1/udp/
hweng.facade.url=http://eng-hw-phx.yunxuetang.com.cn/v2
feign.csmapi.url=https://api-info-di.yunxuetang.com.cn/csm/
file.facade.url=https://api-phx-di.yunxuetang.com.cn/file/
miscapi.facade.url=https://api-phx-di.yunxuetang.com.cn/misc/
down.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/down/
meeting.url=https://meetdev.yxt.com
feign.spmodelapi.url=https://api-phx-di-tc.yunxuetang.com.cn/spmodel/
sptalentsd.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/spds/
sptalentbkapi.facade.url=https://api-phx-di-tc.yunxuetang.com.cn/sptalentbk/

#datasource
spring.datasource.druid.bk.url=***********************************************************************************************************************************************************************************
spring.datasource.druid.bk.username=yxt
spring.datasource.druid.bk.password=afg)gppOs22k
spring.datasource.druid.bk.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.bk.initial-size=1
spring.datasource.druid.bk.max-active=30
spring.datasource.druid.bk.min-idle=1
spring.datasource.druid.bk.max-wait=60000
spring.datasource.druid.bk.pool-prepared-statements=false
spring.datasource.druid.bk.validation-query=SELECT 'x' from dual
spring.datasource.druid.bk.validation-query-timeout=60
spring.datasource.druid.bk.test-on-borrow=false
spring.datasource.druid.bk.test-on-return=false
spring.datasource.druid.bk.test-while-idle=true
spring.datasource.druid.bk.time-between-eviction-runs-millis=60000
spring.datasource.druid.bk.min-evictable-idle-time-millis=300000
spring.datasource.druid.bk.max-evictable-idle-time-millis=600000

#redis
sptalentapi.redis.cluster.nodes=10.100.2.96:6379
sptalentapi.redis.port=6379
sptalentapi.redis.password=XKH4lCe3Tr9Tpiil$
sptalentapi.redis.database=3
sptalentapi.redis.timeout=2000
sptalentapi.redis.lettuce.pool.min-idle=4
sptalentapi.redis.lettuce.pool.max-idle=20
sptalentapi.redis.lettuce.pool.max-active=40
sptalentapi.redis.lettuce.pool.max-wait=10000

#rocketmq
rocketmq.enabled=true
rocketmq.consumer-enabled=false
rocketmq.name-server=10.100.17.183:9876
rocketmq.producer.group=sptalentbk-producer-group
rocketmq.producer.maxMessageSize=4194304
rocketmq.producer.compress-message-body-threshold=4096
rocketmq.producer.sendMessageTimeout=3000
rocketmq.producer.retryTimesWhenSendFailed=2
rocketmq.producer.retryTimesWhenSendAsyncFailed=0
rocketmq.consumer.consumeThreadMin=2
rocketmq.consumer.consumeThreadMax=3
rocketmq.consumer.consumeMessageBatchMaxSize=1

#es
spring.data.elasticsearch.repositories.enabled=true
spring.elasticsearch.rest.uris=http://10.100.2.123:9200
spring.elasticsearch.rest.username=elastic
spring.elasticsearch.rest.password=m5yC6Av3VS9Y

#job
xxl.job.enabled=false
xxl.job.executor-appname=sptalentbk-executor

#index
elasticsearch.indexName=talent_bk_v2

ucache.enabled=true
ucache.remote.dft.type=redis#waf-redisProperties
ucache.local.enabled=false
ucache.local.type=caffeine
ucache.local.maximumSize=5000
ucache.local.expireAfterWrite=3000
ucache.bigKey.enableSizeLimit=true
ucache.bigKey.warnSize=10k
ucache.bigKey.forbiddenSize=1m
ucache.bigKey.forbiddenException=false
ucache.pierceDefend.cacheNullValue=true
