package com.yxt.talent.bk.core.pool.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Pool4AuditLog
 *
 * <AUTHOR> geyan
 * @Date 15/3/24 2:03 pm
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PoolExport4AuditLog {
    @AuditLogField(name = "人才池", orderIndex = 0)
    private String poolNames;
}
