package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "职责任务映射")
public class RtModelMap4Get {

    @Schema(description = "主键id")
    private String id;
    @Schema(description = "职责任务Id")
    private String rtId;
    @Schema(description = "任务名称")
    private String name;
    @Schema(description = "任务类型(1-父级任务，2-末级任务)")
    private int type;
    @Schema(description = "任务排序")
    private int orderIndex;
    @Schema(description = "任务描述")
    private String taskDesc;

    @Schema(description = "子任务集合")
    private List<RtModelMap4Get> subTasks;

    @Schema(description = "评估结果")
    private String evaluateResult;
    @Schema(description = "是否达标 0-不达标 1-达标")
    private Integer reached;
}
