<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.search.mapper.SearchAnalyseDimensionMapper">
    <select id="findByOrgId" resultType="com.yxt.talent.bk.core.search.bean.UserSearchAnalyseDimensionBean">
      SELECT
      T1.item_key as itemKey,
      ifnull(t2.tag_name, T1.item_value) as itemName,
      T1.order_index AS orderIndex
      FROM  bk_search_analyse_dimension T1
      LEFT JOIN  bk_tag T2 ON T1.org_id = T2.org_id AND T1.item_key = T2.tag_key
      WHERE T1.org_Id = #{orgId}
      AND T1.user_id = #{userId}
      AND (T2.tag_enable = 1 OR T1.item_key IN ('departmentId','departmentName','gradeId','gradeName','positionName','positionId','ordinaryTag'))
      order by T1.order_index
    </select>

</mapper>
