package com.yxt.talent.bk.svc.udp;

import com.yxt.talent.bk.core.udp.entity.UdpDept;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门层级优化工具类
 * 用于从众多部门中梳理出最顶层的部门，避免查询时包含过多的子部门
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
public class DeptHierarchyUtils {

    /**
     * 基于闭包表思想，从部门列表中过滤出最顶层的部门
     * 如果一个部门的父部门也在列表中，则该部门会被过滤掉
     *
     * @param deptList 部门列表
     * @return 过滤后的最顶层部门列表
     */
    public static List<UdpDept> filterTopLevelDepts(List<UdpDept> deptList) {
        if (CollectionUtils.isEmpty(deptList)) {
            return new ArrayList<>();
        }

        // 构建部门ID到部门对象的映射
        Map<String, UdpDept> deptMap = deptList.stream()
                .collect(Collectors.toMap(UdpDept::getId, dept -> dept, (existing, replacement) -> existing));

        // 存储最终的顶层部门
        List<UdpDept> topLevelDepts = new ArrayList<>();

        for (UdpDept dept : deptList) {
            if (isTopLevelDept(dept, deptMap)) {
                topLevelDepts.add(dept);
            }
        }

        log.info("部门层级过滤: 原始部门数量={}, 过滤后顶层部门数量={}", deptList.size(), topLevelDepts.size());
        return topLevelDepts;
    }

    /**
     * 基于部门ID列表过滤出最顶层的部门ID
     *
     * @param deptIds 部门ID列表
     * @param allDepts 所有部门信息（用于查找父子关系）
     * @return 过滤后的最顶层部门ID列表
     */
    public static List<String> filterTopLevelDeptIds(List<String> deptIds, List<UdpDept> allDepts) {
        if (CollectionUtils.isEmpty(deptIds) || CollectionUtils.isEmpty(allDepts)) {
            return new ArrayList<>(deptIds);
        }

        // 构建部门ID到部门对象的映射
        Map<String, UdpDept> deptMap = allDepts.stream()
                .collect(Collectors.toMap(UdpDept::getId, dept -> dept, (existing, replacement) -> existing));

        // 只保留在deptIds中的部门
        List<UdpDept> targetDepts = deptIds.stream()
                .filter(deptMap::containsKey)
                .map(deptMap::get)
                .collect(Collectors.toList());

        return filterTopLevelDepts(targetDepts).stream()
                .map(UdpDept::getId)
                .collect(Collectors.toList());
    }

    /**
     * 基于idFullPath路径过滤最顶层部门
     * idFullPath格式类似: "root.dept1.subdept1"
     *
     * @param deptIds 部门ID列表
     * @param deptPathMap 部门ID到完整路径的映射
     * @return 过滤后的最顶层部门ID列表
     */
    public static List<String> filterTopLevelDeptIdsByPath(List<String> deptIds, Map<String, String> deptPathMap) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return new ArrayList<>();
        }

        Set<String> topLevelDeptIds = new HashSet<>();

        for (String deptId : deptIds) {
            String currentPath = deptPathMap.get(deptId);
            if (StringUtils.isEmpty(currentPath)) {
                // 如果没有路径信息，默认认为是顶层部门
                topLevelDeptIds.add(deptId);
                continue;
            }

            boolean isTopLevel = true;

            // 检查是否有其他部门是当前部门的父部门
            for (String otherDeptId : deptIds) {
                if (deptId.equals(otherDeptId)) {
                    continue;
                }

                String otherPath = deptPathMap.get(otherDeptId);
                if (StringUtils.isEmpty(otherPath)) {
                    continue;
                }

                // 如果当前部门的路径包含其他部门的路径，说明其他部门是父部门
                if (currentPath.startsWith(otherPath + ".")) {
                    isTopLevel = false;
                    break;
                }
            }

            if (isTopLevel) {
                topLevelDeptIds.add(deptId);
            }
        }

        List<String> result = new ArrayList<>(topLevelDeptIds);
        log.info("基于路径的部门层级过滤: 原始部门数量={}, 过滤后顶层部门数量={}", deptIds.size(), result.size());
        return result;
    }

    /**
     * 判断部门是否为顶层部门
     * 顶层部门的定义：在给定的部门集合中，没有父部门存在
     *
     * @param dept 待判断的部门
     * @param deptMap 部门映射表
     * @return true表示是顶层部门
     */
    private static boolean isTopLevelDept(UdpDept dept, Map<String, UdpDept> deptMap) {
        String parentId = dept.getParentId();

        // 如果没有父部门ID，认为是顶层部门
        if (StringUtils.isEmpty(parentId)) {
            return true;
        }

        // 如果父部门不在当前的部门集合中，认为是顶层部门
        return !deptMap.containsKey(parentId);
    }

    /**
     * 基于routingPath路径过滤最顶层部门
     * routingPath格式类似: "000001.000002.000003"
     *
     * @param deptIds 部门ID列表
     * @param routingPathMap 部门ID到路由路径的映射
     * @return 过滤后的最顶层部门ID列表
     */
    public static List<String> filterTopLevelDeptIdsByRoutingPath(List<String> deptIds, Map<String, String> routingPathMap) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return new ArrayList<>();
        }

        Set<String> topLevelDeptIds = new HashSet<>();

        for (String deptId : deptIds) {
            String currentPath = routingPathMap.get(deptId);
            if (StringUtils.isEmpty(currentPath)) {
                // 如果没有路径信息，默认认为是顶层部门
                topLevelDeptIds.add(deptId);
                continue;
            }

            boolean isTopLevel = true;

            // 检查是否有其他部门是当前部门的父部门
            for (String otherDeptId : deptIds) {
                if (deptId.equals(otherDeptId)) {
                    continue;
                }

                String otherPath = routingPathMap.get(otherDeptId);
                if (StringUtils.isEmpty(otherPath)) {
                    continue;
                }

                // 如果当前部门的路径以其他部门的路径开头，且长度更长，说明其他部门是父部门
                if (currentPath.startsWith(otherPath + ".") && currentPath.length() > otherPath.length()) {
                    isTopLevel = false;
                    break;
                }
            }

            if (isTopLevel) {
                topLevelDeptIds.add(deptId);
            }
        }

        List<String> result = new ArrayList<>(topLevelDeptIds);
        log.info("基于路由路径的部门层级过滤: 原始部门数量={}, 过滤后顶层部门数量={}", deptIds.size(), result.size());
        return result;
    }

    public static void main(String[] args) {
        // 创建测试用的多级部门数据
        List<UdpDept> allDepts = new ArrayList<>();

        // 创建根部门
        UdpDept rootDept = new UdpDept();
        rootDept.setId("root");
        rootDept.setParentId("");
        rootDept.setName("根部门");
        allDepts.add(rootDept);

        // 创建一级部门
        UdpDept dept1 = new UdpDept();
        dept1.setId("dept1");
        dept1.setParentId("root");
        dept1.setName("一级部门1");
        allDepts.add(dept1);

        UdpDept dept2 = new UdpDept();
        dept2.setId("dept2");
        dept2.setParentId("root");
        dept2.setName("一级部门2");
        allDepts.add(dept2);

        // 创建二级部门
        UdpDept subDept1 = new UdpDept();
        subDept1.setId("sub1");
        subDept1.setParentId("dept1");
        subDept1.setName("二级部门1");
        allDepts.add(subDept1);

        UdpDept subDept2 = new UdpDept();
        subDept2.setId("sub2");
        subDept2.setParentId("dept2");
        subDept2.setName("二级部门2");
        allDepts.add(subDept2);

        // 创建三级部门
        UdpDept subSubDept1 = new UdpDept();
        subSubDept1.setId("subsub1");
        subSubDept1.setParentId("sub1");
        subSubDept1.setName("三级部门1");
        allDepts.add(subSubDept1);

        // 测试场景1: 只包含根部门
        System.out.println("=== 测试场景1: 只包含根部门 ===");
        List<String> testDeptIds1 = Arrays.asList("root");
        List<String> result1 = filterTopLevelDeptIds(testDeptIds1, allDepts);
        System.out.println("输入部门: " + testDeptIds1);
        System.out.println("顶层部门: " + result1);
        System.out.println();

        // 测试场景2: 包含一级部门
        System.out.println("=== 测试场景2: 包含一级部门 ===");
        List<String> testDeptIds2 = Arrays.asList("root", "dept1", "dept2");
        List<String> result2 = filterTopLevelDeptIds(testDeptIds2, allDepts);
        System.out.println("输入部门: " + testDeptIds2);
        System.out.println("顶层部门: " + result2);
        System.out.println();

        // 测试场景3: 包含多级部门
        System.out.println("=== 测试场景3: 包含多级部门 ===");
        List<String> testDeptIds3 = Arrays.asList("root", "dept1", "dept2", "sub1", "sub2", "subsub1");
        List<String> result3 = filterTopLevelDeptIds(testDeptIds3, allDepts);
        System.out.println("输入部门: " + testDeptIds3);
        System.out.println("顶层部门: " + result3);
        System.out.println();

        // 测试场景4: 不包含根部门，只包含子部门
        System.out.println("=== 测试场景4: 不包含根部门，只包含子部门 ===");
        List<String> testDeptIds4 = Arrays.asList("dept1", "sub1", "sub2");
        List<String> result4 = filterTopLevelDeptIds(testDeptIds4, allDepts);
        System.out.println("输入部门: " + testDeptIds4);
        System.out.println("顶层部门: " + result4);
        System.out.println();

        // 测试场景5: 只包含同级子部门
        System.out.println("=== 测试场景5: 只包含同级子部门 ===");
        List<String> testDeptIds5 = Arrays.asList("sub1", "sub2");
        List<String> result5 = filterTopLevelDeptIds(testDeptIds5, allDepts);
        System.out.println("输入部门: " + testDeptIds5);
        System.out.println("顶层部门: " + result5);
        System.out.println();

        // 测试场景6: 跨级
        System.out.println("=== 测试场景6: 跨级 ===");
        List<String> testDeptIds6 = Arrays.asList("dept1", "subsub1", "sub1","sub2");
        List<String> result6 = filterTopLevelDeptIds(testDeptIds6, allDepts);
        System.out.println("输入部门: " + testDeptIds6);
        System.out.println("顶层部门: " + result6);
        System.out.println();

        System.out.println("测试完成!");
    }
}
