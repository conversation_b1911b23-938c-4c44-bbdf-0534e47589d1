package com.yxt.talent.bk.core.udp.repo;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.bk.common.bean.udp.DeptConditionBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangUserBean;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.common.utils.StringConcatBuilder;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.udp.bean.QueryUserBean;
import com.yxt.talent.bk.core.udp.bean.UdpPositionBean;
import com.yxt.talent.bk.core.udp.bean.UdpQueryUserBean;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.udp.mapper.UdpLiteUserMapper;
import com.yxt.talent.bk.core.usergroup.bean.Scheme4Search;
import com.yxt.talent.bk.core.usergroup.bean.SchemeBean;
import com.yxt.talent.bk.core.usergroup.bean.SchemeUser4Export;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * UdpLiteUserRepository
 *
 * <AUTHOR> geyan
 * @Date 18/8/23 10:27 am
 */
@Repository
@AllArgsConstructor
public class UdpLiteUserRepository {
    private final UdpLiteUserMapper udpLiteUserMapper;


    public IPage<SchemeBean> findUserPage(IPage<SchemeBean> page, String orgId, Scheme4Search param,
            List<String> deptIds) {
        param.setKeywordLangMatch(TalentbkUtil.langKeywordQuery(orgId, param.getKeyword(), true, false));
        param.setKeyword(SqlUtils.escapeLike(param.getKeyword()));
        IPage<SchemeBean> ret = udpLiteUserMapper.findUserPage(page, orgId, param, deptIds);
        TalentbkUtil.bkUdpTranslate(orgId, true, ret.getRecords());
        return ret;
    }

    public List<String> findNamesById(String orgId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        List<UdpLangUserBean> ret = udpLiteUserMapper.findNamesById(orgId, userIds);
        TalentbkUtil.udpTranslate(orgId, ret);
        return BeanCopierUtil.convertList(ret, UdpLangUserBean::getFullname);
    }

    public List<String> existUserIds(String orgId, Collection<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        return udpLiteUserMapper.existUserIds(orgId, userIds);
    }

    public List<UdpPositionBean> queryPositionUserQty(String orgId, Collection<String> positionIds) {
        if (CollectionUtils.isEmpty(positionIds)) {
            return Lists.newArrayList();
        }
        return udpLiteUserMapper.queryPositionUserQty(orgId, positionIds);
    }

    public List<SchemeUser4Export> findUser4Export(String orgId, Scheme4Search param, DeptConditionBean deptIdCon) {
        param.setKeywordLangMatch(TalentbkUtil.langKeywordQuery(orgId, param.getKeyword(), true, false));
        param.setKeyword(SqlUtils.escapeLike(param.getKeyword()));
        List<SchemeUser4Export> ret = udpLiteUserMapper.findUser4Export(orgId, param, deptIdCon);
        BatchOperationUtil.batchExecute(ret, 500, subList -> {
            TalentbkUtil.bkUdpTranslate(orgId, true, subList);
        });
        return ret;
    }

    public UdpLiteUser findByOrgIdAndUserId(String orgId, String userId) {
        UdpLiteUser user = udpLiteUserMapper.findByOrgIdAndUserId(orgId, userId);
        if (user != null) {
            TalentbkUtil.bkUdpTranslate(orgId, true, Lists.newArrayList(user));
        }
        return user;
    }

    public List<String> listIdByPosition(String orgId, String posId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        return udpLiteUserMapper.listIdByPosition(orgId, posId, userIds);
    }

    public IPage<UdpUserBriefBean> briefPage(Page page, String orgId, QueryUserBean queryUser) {
        queryUser.setKeywordLangMatch(TalentbkUtil.langKeywordQuery(orgId, queryUser.getFullname(), true, false)
                .rebuildIdsIfEnabled(true, false));
        queryUser.setFullname(SqlUtils.escapeLike(queryUser.getFullname()));
        IPage<UdpUserBriefBean> retPage = udpLiteUserMapper.briefPage(page, orgId, queryUser);
        TalentbkUtil.bkUdpTranslate(orgId, true, retPage.getRecords());
        return retPage;
    }


    public IPage<SchemeBean> findUserSchemePage(IPage<SchemeBean> page, String orgId, Scheme4Search param,
            DeptConditionBean deptIdCon) {
        param.setKeywordLangMatch(TalentbkUtil.langKeywordQuery(orgId, param.getKeyword(), true, false));
        param.setKeyword(SqlUtils.escapeLike(param.getKeyword()));
        IPage<SchemeBean> retPage = udpLiteUserMapper.findUserSchemePage(page, orgId, param, deptIdCon);
        TalentbkUtil.bkUdpTranslate(orgId, true, retPage.getRecords());
        return retPage;
    }

    public List<UdpLiteUser> getUdpUserInfo(String orgId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<UdpLiteUser> query = new LambdaQueryWrapper<>();
        query.in(UdpLiteUser::getId, userIds);
        query.eq(UdpLiteUser::getOrgId, orgId);
        query.eq(UdpLiteUser::getDeleted, YesOrNo.NO.getValue());
        query.select(UdpLiteUser::getId, UdpLiteUser::getStatus, UdpLiteUser::getGradeName, UdpLiteUser::getPositionId,
                UdpLiteUser::getPositionName, UdpLiteUser::getDeptId, UdpLiteUser::getDeptName,
                UdpLiteUser::getUsername, UdpLiteUser::getFullname, UdpLiteUser::getThirdUserId);
        List<UdpLiteUser> ret = udpLiteUserMapper.selectList(query);
        TalentbkUtil.bkUdpTranslate(orgId, true, ret);
        return ret;
    }

    public Set<String> listUserIds4Group(String orgId, Integer status, List<Pair<String, Boolean>> deptIds,
            List<String> positionIds, List<String> gradeIds, List<String> userIds) {
        UdpQueryUserBean param = query4GroupParam(status, deptIds, positionIds, gradeIds, userIds);
        return udpLiteUserMapper.listUserIds4Group(orgId, param);
    }

    public IPage<UdpUserBriefBean> listUser4Group(Page page, String orgId, Integer status,
            List<Pair<String, Boolean>> deptIds, List<String> positionIds, List<String> gradeIds,
            List<String> userIds) {
        UdpQueryUserBean param = query4GroupParam(status, deptIds, positionIds, gradeIds, userIds);
        IPage<UdpUserBriefBean> ret = udpLiteUserMapper.listUser4Group(page, orgId, param);
        TalentbkUtil.bkUdpTranslate(orgId, true, ret.getRecords());
        return ret;
    }

    private UdpQueryUserBean query4GroupParam(Integer status, List<Pair<String, Boolean>> deptIds,
            List<String> positionIds, List<String> gradeIds, List<String> userIds) {
        deptIds = Optional.ofNullable(deptIds).orElse(Lists.newArrayList());
        List<String> allDeptIds = BeanCopierUtil.convertList(deptIds, Pair::getKey);
        List<String> parentDeptIds = deptIds.stream().filter(Pair::getValue).map(Pair::getKey)
                .collect(Collectors.toList());
        UdpQueryUserBean param = new UdpQueryUserBean();
        param.setStatus(status);
        param.setDeptIds(allDeptIds);
        param.setParentDeptIds(parentDeptIds);
        param.setPositionIds(positionIds);
        param.setGradeIds(gradeIds);
        param.setUserIds(userIds);
        return param;
    }

    public String userNames4Log(String orgId, List<String> userIds) {
        StringConcatBuilder userNames = new StringConcatBuilder(StringPool.COMMA);
        fillUserInfo(orgId, userIds, item -> item, (userId, userInfo) -> {
            userNames.append(userInfo.getFullname());
            userNames.append("(").append(userInfo.getUsername()).append(")").appendConcat();
        }, Lists.newArrayList("username", "fullname"));
        return userNames.output();
    }

    /**
     * 填充用户信息
     *
     * @param orgId
     * @param list
     * @param userIdGetter
     * @param consumer
     * @param queryFields
     * @param <T>
     */
    public <T> void fillUserInfo(String orgId, List<T> list, Function<T, String> userIdGetter,
            BiConsumer<T, UdpUserBriefBean> consumer, List<String> queryFields) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> userIds = list.stream().map(userIdGetter).filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        if (userIds.isEmpty()) {
            return;
        }
        if (CollectionUtils.isNotEmpty(queryFields)) {
            for (int i = 0; i < queryFields.size(); i++) {
                queryFields.set(i, CommonUtils.field2Column(queryFields.get(i)));
            }
        }
        List<UdpUserBriefBean> userList = udpLiteUserMapper.queryByUserIds(orgId, userIds, queryFields);
        TalentbkUtil.bkUdpTranslate(orgId, true, userList);
        Map<String, UdpUserBriefBean> userMap = StreamUtil.list2map(userList, UdpUserBriefBean::getId);
        list.forEach(item -> {
            UdpUserBriefBean userBrief = userMap.get(userIdGetter.apply(item));
            if (userBrief == null) {
                return;
            }
            consumer.accept(item, userBrief);
        });
    }

    /**
     * 填充奇点用户信息
     *
     * @param orgId
     * @param list
     * @param userIdGetter
     * @param consumer
     * @param queryFields
     * @param <T>
     */
    public <T> void fillSpUserInfo(String orgId, List<T> list, Function<T, String> userIdGetter,
            BiConsumer<T, UdpUserBriefBean> consumer, List<String> queryFields) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> userIds = list.stream().map(userIdGetter).filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        if (userIds.isEmpty()) {
            return;
        }
        if (CollectionUtils.isNotEmpty(queryFields)) {
            for (int i = 0; i < queryFields.size(); i++) {
                queryFields.set(i, CommonUtils.field2Column(queryFields.get(i)));
            }
        }
        List<UdpUserBriefBean> userList = udpLiteUserMapper.queryByUserIds(orgId, userIds, queryFields);
        TalentbkUtil.bkUdpTranslate(orgId, true, userList);
        Map<String, UdpUserBriefBean> userMap = StreamUtil.list2map(userList, UdpUserBriefBean::getId);
        userMap.forEach((userId, userBrief) -> userBrief.setSpUser(true));
        list.forEach(item -> {
            UdpUserBriefBean userBrief = userMap.get(userIdGetter.apply(item));
            if (userBrief == null) {
                return;
            }
            consumer.accept(item, userBrief);
        });
    }

    public UdpLiteUser getUdpUserInfoById(String orgId, String userId) {
        LambdaQueryWrapper<UdpLiteUser> query = new LambdaQueryWrapper<>();
        query.eq(UdpLiteUser::getId, userId);
        query.eq(UdpLiteUser::getOrgId, orgId);
        query.eq(UdpLiteUser::getDeleted, YesOrNo.NO.getValue());
        query.eq(UdpLiteUser::getStatus, YesOrNo.YES.getValue());
        UdpLiteUser ret = udpLiteUserMapper.selectOne(query);
        TalentbkUtil.bkUdpTranslate(orgId, true, Lists.newArrayList(ret));
        return ret;
    }

}
