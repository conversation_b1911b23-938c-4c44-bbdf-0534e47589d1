package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
/**
 * 学员证书颁发表(DwdCerIssueHistory)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 11:24:57
 */
@Data
@TableName(value = "dwd_cer_issue_history")
public class DwdCerIssueHistory {
    /**
     * 主键id
     */
    @TableField(value = "id")
    private String id;
    /**
     * 三方用户 id
     */
    @TableField(value = "third_user_id")
    private String thirdUserId;

    @TableField(value = "user_id")
    private String userId;

    /**
     * 机构 id
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 证书id
     */
    @TableField(value = "cer_id")
    private String cerId;
    /**
     * 证书名称
     */
    @TableField(value = "cer_name")
    private String cerName;
    /**
     * 证书编号
     */
    @TableField(value = "cer_no")
    private String cerNo;
    /**
     * 证书状态，0：正常、1：吊销、2：已被更新
     */
    @TableField(value = "cer_status")
    private Integer cerStatus;
    /**
     * 颁发时间
     */
    @TableField(value = "issue_time")
    private LocalDateTime issueTime;
    /**
     * 过期时间
     */
    @TableField(value = "expired_time")
    private LocalDateTime expiredTime;
    /**
     * 是否删除(0-否,1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

}
