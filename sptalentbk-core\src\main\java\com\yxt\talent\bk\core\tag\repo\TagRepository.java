package com.yxt.talent.bk.core.tag.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.tag.bean.TagNameBean;
import com.yxt.talent.bk.core.tag.bean.TagSimpleBean;
import com.yxt.talent.bk.core.tag.bean.TagValueSimpleBean;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.mapper.TagMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class TagRepository extends ServiceImpl<TagMapper, TagEntity> {

    /**
     * 洗数据使用4.1迭代删除
     * @param tagKey
     * @return
     */
    public List<TagEntity> queryTagByKey(String tagKey) {
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagEntity::getTagKey, tagKey);
        return list(wrapper);
    }

    public List<TagEntity> queryTagByTagKey(String orgId, String tagKey) {
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagEntity::getTagKey, tagKey);
        wrapper.eq(TagEntity::getOrgId, orgId);
        return list(wrapper);
    }

    public TagEntity queryTagById(String orgId, String tagId) {
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagEntity::getId, tagId);
        wrapper.eq(TagEntity::getOrgId, orgId);
        return getOne(wrapper);
    }

    public List<TagEntity> queryTagByIds(String orgId, Collection<String> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            log.warn("LOG10240:org_id={}", orgId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        wrapper.in(TagEntity::getId, tagIds);
        wrapper.eq(TagEntity::getOrgId, orgId);
        List<TagEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }

    public long countByCatalogId(String orgId, String catalogId) {
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagEntity::getCatalogId, catalogId);
        wrapper.eq(TagEntity::getOrgId, orgId);
        return count(wrapper);
    }

    public List<TagEntity> list(String orgId) {
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagEntity::getOrgId, orgId);
        return list(wrapper);
    }

    public boolean removeById(String orgId, String id) {
        LambdaQueryWrapper<TagEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(TagEntity::getId, id);
        queryWrapper.eq(TagEntity::getOrgId, orgId);
        return super.remove(queryWrapper);
    }

    public boolean removeByIds(String orgId, Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("LOG10200:org_id={}", orgId);
            return false;
        }
        LambdaQueryWrapper<TagEntity> queryWrapper = getQueryWrapper();
        queryWrapper.in(TagEntity::getId, ids);
        queryWrapper.eq(TagEntity::getOrgId, orgId);
        return super.remove(queryWrapper);
    }

    /**
     * 获取分层标签
     *
     * @param orgId
     * @return
     */
    public List<TagEntity> findTab4Select(String orgId) {
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagEntity::getOrgId, orgId);
        wrapper.eq(TagEntity::getSource, 1);
        wrapper.eq(TagEntity::getEnable, 1);
        wrapper.eq(TagEntity::getTagType, 1);
        wrapper.orderByAsc(TagEntity::getCreateTime);
        return list(wrapper);
    }

    public List<TagSimpleBean> fingTagMsg(String orgId, List<String> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return new ArrayList<>();
        }
        return baseMapper.findTagMsg(orgId, tagIds);
    }

    /**
     * 获取自建标签
     *
     * @param orgId 机构
     * @param tagIds
     * @return
     */
    public List<TagSimpleBean> fingSelfTag(String orgId, List<String> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return new ArrayList<>();
        }
        return baseMapper.fingSelfTag(orgId, tagIds);
    }

    public List<TagSimpleBean> findTag4Search(String orgId, String catalogId, List<String> tagIds, Collection<String> nameLikes, Collection<String> nameValueLikes) {
        return baseMapper.findTag4Search(orgId, catalogId, tagIds, nameLikes, nameValueLikes);
    }

    public List<TagEntity> findTagBy(String orgId, Integer tagType,Integer enable, Integer showType,Integer tagSource,Integer createType) {
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        if(tagType != null){
            wrapper.eq(TagEntity::getTagType,tagType);
        }
        if(enable != null){
            wrapper.eq(TagEntity::getEnable,enable);
        }
        if(showType != null){
            wrapper.eq(TagEntity::getShowType,showType);
        }
        if(tagSource != null){
            wrapper.eq(TagEntity::getSource,tagSource);
        }
        if(createType != null){
            wrapper.eq(TagEntity::getCreateType,createType);
        }
        wrapper.eq(TagEntity::getOrgId, orgId);

        return list(wrapper);
    }

    public List<TagEntity> findTagByTagKeys(String orgId,Collection<String> tagKeys){
        if (CollectionUtils.isEmpty(tagKeys)) {
            log.warn("LOG10220:org_id={}", orgId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<TagEntity> wrapper = getQueryWrapper();
        wrapper.eq(TagEntity::getOrgId, orgId);
        wrapper.eq(TagEntity::getEnable, 1);
        wrapper.in(TagEntity::getTagKey,tagKeys);
        return list(wrapper);
    }


    public List<TagValueSimpleBean> findTagValueList(String orgId, String tagKey) {
        return baseMapper.findfindTagValueList(orgId, tagKey);
    }

    /**
     * 获取 普通标签
     *
     * @param orgId
     * @return
     */
    public List<TagEntity> findOrdinaryTag(String orgId) {
        LambdaQueryWrapper<TagEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(TagEntity::getOrgId, orgId);
        queryWrapper.eq(TagEntity::getTagType, 0);
        queryWrapper.eq(TagEntity::getEnable, 1);
        queryWrapper.orderByDesc(TagEntity::getCreateTime);
        return list(queryWrapper);
    }


    private LambdaQueryWrapper<TagEntity> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public List<String> findTagNameLike(String orgId, String keyword) {
        return baseMapper.findTagNameLike(orgId, keyword);
    }

    public List<String> findTagNameByIds(String orgId, Collection<String> ids) {
        return baseMapper.findTagNameByIds(orgId, ids);
    }

    /**
     * 获取内置,固定标签中，禁用的标签
     *
     * @return
     */
    public List<TagEntity> findForbiddenTag(String orgId) {
        LambdaQueryWrapper<TagEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(TagEntity::getOrgId, orgId);
        queryWrapper.eq(TagEntity::getEnable, 0);
        queryWrapper.in(TagEntity::getSource, 0, 2);
        return list(queryWrapper);
    }

    /**
     *
     * 获取内置标签
     */
    public List<TagNameBean> findSelfTagByOrgId(String orgId){
        return baseMapper.findSelfTagByOrgId(orgId);
    }
}
