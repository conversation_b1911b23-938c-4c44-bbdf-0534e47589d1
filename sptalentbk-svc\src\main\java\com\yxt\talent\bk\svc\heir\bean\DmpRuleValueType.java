package com.yxt.talent.bk.svc.heir.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class DmpRuleValueType {

    public static final int TYPE_STRING = 1;
    public static final int TYPE_OBJ = 2;
    public static final int TYPE_LIST = 3;
    public static final int TYPE_NUMBER = 4;

    /**
     * type: 1-字符串（布尔、日期会全部算作字符串，并以字符串方式比较） 2-对象  3-集合  4-数字(整形、浮点型)
     */
    private int type;

    private String str;

    private Double num;

    private DmpRuleValue dmpRuleValue;

    private List<DmpRuleValue> dmpRuleValues;

}
