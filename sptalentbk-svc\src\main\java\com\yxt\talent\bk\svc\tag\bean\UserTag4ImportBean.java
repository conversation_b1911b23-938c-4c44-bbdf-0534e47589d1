package com.yxt.talent.bk.svc.tag.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;

/**
 * 用户贴标签导入bean
 *
 * <AUTHOR>
 * @since 2022/8/19
 */
@Data
@NoArgsConstructor
public class UserTag4ImportBean {

    @Schema(description = "用户id")
    @NotEmpty(message = "")
    private String userId;

    @Schema(description = "用户账号")
    @NotEmpty(message = "")
    private String username;

    @Schema(description = "标签bean")
    @NotEmpty(message = "")
    private TagBean tagBean;
}
