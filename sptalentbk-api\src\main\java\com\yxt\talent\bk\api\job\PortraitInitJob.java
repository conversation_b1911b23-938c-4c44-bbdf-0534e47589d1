package com.yxt.talent.bk.api.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.talent.bk.svc.profile.UserProfileConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@Component
@AllArgsConstructor
public class PortraitInitJob extends IJobHandler {

    private final UserProfileConfigService userProfileConfigService;

    @Override
    @XxlJob(value = "portraitInitJob")
    public ReturnT<String> execute(String param) {
        try {
            if(StringUtils.isNotBlank(param)){
                String[] split = param.split(",");
                if(split.length > 0){
                    Set<String> orgIds = Set.of(split);
                    log.info("portraitInitJob start");
                    userProfileConfigService.init(orgIds);
                    log.info("portraitInitJob end");
                }
            }
        }catch (Exception e){
            log.error("portraitInitJob error", e);
        }
        return ReturnT.SUCCESS;
    }
}
