package com.yxt.talent.bk.core.tag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.bk.core.tag.bean.UserBaseInfoBean;
import com.yxt.talent.bk.core.tag.bean.UserTagBaseBean;
import com.yxt.talent.bk.core.tag.bean.UserTagCountBean;
import com.yxt.talent.bk.core.tag.entity.UserTagEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/15
 */
@Mapper
public interface UserTagMapper extends BaseMapper<UserTagEntity> {

    /**
     * @param orgId   机构id
     * @param tagIds  标签id列表 不能为空
     * @param userIds 用户标签列表 不能为空
     * @return 用户标签信息
     */
    List<UserTagBaseBean> listUserTag(@Param("orgId") String orgId, @Param("tagIds") Collection<String> tagIds,
            @Param("userIds") Collection<String> userIds);

    IPage<UserBaseInfoBean> listPageTagUser(@Param("page") IPage<UserBaseInfoBean> page, @Param("orgId") String orgId,
        @Param("keyword") String keyword, @Param("tagId") String tagId, @Param("tagValueId") String tagValueId);

    List<UserBaseInfoBean> listTagUser(@Param("orgId") String orgId, @Param("keyword") String keyword,
            @Param("tagId") String tagId);

    List<UserTagCountBean> findByOrgIdAndTagIds(@Param("orgId") String orgId, @Param("tagIds") List<String> tagIds);

    /**
     * 删除指定用户标签
     *
     * @param orgId      机构id
     * @param tagIds     标签id列表
     * @param userIds    用户id列表
     * @param operatorId 操作人id
     * @param now        当前时间
     */
    void delUserTag(@Param("orgId") String orgId, @Param("tagIds") Collection<String> tagIds,
            @Param("userIds") Collection<String> userIds, @Param("operatorId") String operatorId,
            @Param("now") Date now);
}
