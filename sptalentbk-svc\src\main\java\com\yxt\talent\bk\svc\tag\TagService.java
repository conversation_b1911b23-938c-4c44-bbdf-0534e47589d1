package com.yxt.talent.bk.svc.tag;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.yxt.common.Constants;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.*;
import com.yxt.talent.bk.common.bean.UserSearchDateRangeBean;
import com.yxt.talent.bk.common.constants.CatalogConstants;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.UserSearchConstants;
import com.yxt.talent.bk.common.es.ESQueryConveter;
import com.yxt.talent.bk.common.utils.UserSearchUtils;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.core.common.repo.CatalogRepository;
import com.yxt.talent.bk.core.tag.bean.*;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.TagValueRepository;
import com.yxt.talent.bk.svc.base.CoreFactorService;
import com.yxt.talent.bk.svc.search.bean.TagBean4Search;
import com.yxt.talent.bk.svc.search.bean.UserSearchListBean;
import com.yxt.talent.bk.svc.tag.bean.Tag4Create;
import com.yxt.talent.bk.svc.tag.bean.TagValue4Create;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.metrics.valuecount.ValueCountAggregationBuilder;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@AllArgsConstructor
@Service
@Slf4j
public class TagService {

    private final TagRepository tagRepository;
    private final TagValueRepository tagValueRepository;
    private final CatalogRepository catalogRepository;
    private final CoreFactorService coreFactorService;
    private final ElasticsearchRestTemplate esTemplate;
    private final ESQueryConveter esQueryConveter;

    /**
     * 标签列表分页查询
     *
     * @param pageRequest
     * @param orgId
     * @param tagBean4Search
     * @return
     */
    public PagingList<Tag4Page> findTagPage(PageRequest pageRequest, String orgId, TagBean4Search tagBean4Search) {
        IPage<Tag4Page> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        if (null != tagBean4Search && StringUtils.isNotEmpty(tagBean4Search.getKeyWord())) {
            ApiUtil.getFiltedLikeString(tagBean4Search.getKeyWord());
        }
        TagSearchBean tagSearchBean = new TagSearchBean();
        BeanCopierUtil.copy(tagBean4Search, tagSearchBean);
        //是否开通人才盘点
        boolean rvOrNot =  coreFactorService.isOpenRv(orgId);
        tagSearchBean.setRvOrNot(rvOrNot);
        IPage<Tag4Page> pageList = tagRepository.getBaseMapper().findTag4Page(page, orgId, tagSearchBean);
        // 改为从数据库查询人数
        if (tagBean4Search != null && tagBean4Search.getTagBindUser() == 0 && null != pageList && CollectionUtils
                .isNotEmpty(pageList.getRecords())) {
            getTagUserCountByEs(orgId, pageList.getRecords());
        }
        return BeanCopierUtil.toPagingList(pageList);
    }

    private void getTagUserCountByEs(String orgId, List<Tag4Page> list){

        if(CollectionUtils.isEmpty(list)){
            return;
        }

        // 查询ES 并设置结果
        Set<String> tagKeys = list.stream().map(Tag4Page::getTagKey).collect(Collectors.toSet());
        NativeSearchQuery nativeSearchQuery = generateAnalyseSearchQuery(orgId, tagKeys);
        log.debug("LOG11170:{}{}", System.lineSeparator(), esQueryConveter.extractJson(nativeSearchQuery));
        AggregatedPage<UserSearchListBean> aggregatedPage = esTemplate.queryForPage(nativeSearchQuery, UserSearchListBean.class);
        list.forEach(tag -> {
            ParsedFilter parsedFilter = aggregatedPage.getAggregations().get(tag.getTagKey());
            tag.setTagUserCount(Integer.valueOf((int)parsedFilter.getDocCount()));
        });
    }

    private NativeSearchQuery generateAnalyseSearchQuery(String orgId, Set<String> tagKeys){

        // 1. 查询条件
        // 机构隔离
        BoolQueryBuilder orgIdQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(UserSearchConstants.EsSpecialLabel.ORG_ID.getValue(), orgId));
        // 只查未删除的用户
        BoolQueryBuilder unDeletedQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(UserSearchConstants.EsSpecialLabel.USER_DELETED.getValue(), 0));
        // 将条件加入must查询组 相当于mysql用 and 链接起来
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        mainQueryBuilder.must().add(orgIdQueryBuilder);
        mainQueryBuilder.must().add(unDeletedQueryBuilder);

        // 2.聚合条件
        NativeSearchQueryBuilder nsqb = new NativeSearchQueryBuilder();
        nsqb.withQuery(mainQueryBuilder);
        //设置查询条件 聚合不需要返回具体数据，设置size 为 1
        nsqb.withPageable(org.springframework.data.domain.PageRequest.of(0, 1));
        //count 聚合
        tagKeys.forEach(esKey -> {

            // ageGroup、workDateGroup等这些特殊key 需要转换为birthday、workDate
            String realEsKey = UserSearchUtils.generateEsPropertiesKey(esKey);
            BoolQueryBuilder boolFilter = QueryBuilders.boolQuery();
            if(!UserSearchConstants.getXxGroupTagEnumMap().containsKey(realEsKey)){
                boolFilter.mustNot(QueryBuilders.termQuery(realEsKey, ""));
            }else{
                realEsKey = UserSearchConstants.getXxGroupTagEnumMap().get(realEsKey);
            }
            boolFilter.must(QueryBuilders.existsQuery(realEsKey));

            ValueCountAggregationBuilder subAggregation = AggregationBuilders.count(esKey).field(realEsKey);
            nsqb.addAggregation(
                    AggregationBuilders.filter(esKey, boolFilter).subAggregation(subAggregation));
        });

        return nsqb.build();
    }

    /**
     * 创建标签
     * @param orgId
     * @param userId
     * @param tag4Create
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public Tag4Create createTag(String orgId, String userId, String fullName, Tag4Create tag4Create) {
        //保存基本信息
        TagEntity tagEntity = new TagEntity();
        String id = ApiUtil.getUuid();
        BeanCopierUtil.copy(tag4Create, tagEntity);
        tagEntity.setId(id);
        EntityUtil.setCreateInfo(userId, tagEntity);
        tagEntity.setOrgId(orgId);
        tagEntity.setTagKey(id);
        //设置默认值
        //标签来源(0-内置,1-自建,2-固定)
        tagEntity.setSource(1);
        tagEntity.setEnable(1);
        tagEntity.setShowType(1);
        //创建方式(0-静态,1-规则,2-模型)
        tagEntity.setCreateType(0);
        //标签更新方式(0-手动,1-自动)
        tagEntity.setUpdateType(0);
        tagEntity.setCreateUserName(fullName);
        //处理下分配，做一下判断
        if (StringUtils.isEmpty(tagEntity.getCatalogId())) {
            //绑定到默认分类下
            TagCatalog defaultCatalog = catalogRepository.getDefaultCatalog(orgId, CatalogConstants.CatalogSource.TAG.getValue());
            if (null != defaultCatalog) {
                tagEntity.setCatalogId(defaultCatalog.getId());
            } else {
                log.info("默认分类不存在，分类初始化数据存在错误！");
            }
        }
        tagRepository.saveOrUpdate(tagEntity);
        tag4Create.setId(id);
        //如果是分层标签，处理下分层标签值
        if (1 == tag4Create.getTagType()) {
            //读取分层标签列表
            if (CollectionUtils.isNotEmpty(tag4Create.getTagValueList())) {
                validateTagValueNames(tag4Create.getTagValueList());
                List<TagValueEntity> tagValueEntityList = new ArrayList<>();
                tag4Create.getTagValueList().forEach(e -> {
                    TagValueEntity tagValueEntity = new TagValueEntity();
                    String valueId = ApiUtil.getUuid();
                    EntityUtil.setCreateInfo(userId, tagValueEntity);
                    BeanCopierUtil.copy(e, tagValueEntity);
                    tagValueEntity.setId(valueId);
                    e.setId(valueId);
                    tagValueEntity.setTagId(id);
                    tagValueEntity.setOrgId(orgId);
                    tagValueEntityList.add(tagValueEntity);
                });
                tagValueRepository.saveOrUpdateBatch(tagValueEntityList);
            } else {
                //至少一个分层标签
                throw new ApiException("apis.talentbk.tagvlue.list.contains.atleast.one.value");
            }
        } else {
            //普通标签创建一个默认值
            TagValueEntity defaultTagValue = new TagValueEntity();
            EntityUtil.setCreateInfo(userId, defaultTagValue);
            defaultTagValue.setId(ApiUtil.getUuid());
            defaultTagValue.setTagId(id);
            defaultTagValue.setValueName(tag4Create.getTagName());
            defaultTagValue.setOrgId(orgId);
            tagValueRepository.saveOrUpdate(defaultTagValue);
            //创建之后返回给前端
            List<TagValue4Create> tagValueList = new ArrayList<>(1);
            TagValue4Create tagValue4Create = new TagValue4Create();
            tagValue4Create.setId(defaultTagValue.getId());
            tagValue4Create.setOrderIndex(0);
            tagValue4Create.setValueName(defaultTagValue.getValueName());
            tagValueList.add(tagValue4Create);
            tag4Create.setTagValueList(tagValueList);
        }
        return tag4Create;
    }


    /**
     * 变更标签状态
     *
     * @param orgId  机构id
     * @param userId 操作人
     * @param opType 操作类型：0-禁用；1-启用
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void changeStatus(String orgId, String tagId, String userId, int opType) {
        TagEntity tagEntity = tagRepository.queryTagById(orgId, tagId);
        Validate.isNotNull(tagEntity, BkApiErrorKeys.APIS_TALENTBK_TAG_NOT_EXIST);
        tagEntity.setEnable(opType);
        EntityUtil.setUpdatedInfo(userId, tagEntity);
        tagRepository.saveOrUpdate(tagEntity);

    }

    /**
     * 标签基础信息
     *
     * @param orgId
     * @param id
     * @return
     */
    public Tag4Get getTagInfo(String orgId, String id) {
        Tag4Get tag4Get = new Tag4Get();
        TagEntity tagEntity = tagRepository.queryTagById(orgId, id);
        Validate.isNotNull(tagEntity, BkApiErrorKeys.APIS_TALENTBK_TAG_NOT_EXIST);
        BeanCopierUtil.copy(tagEntity, tag4Get);
        //处理下分类信息
        TagCatalog catalog = catalogRepository.getOne(orgId, tagEntity.getCatalogId());
        tag4Get.setCatalogName(null != catalog ? catalog.getCatalogName() : null);
        tag4Get.setCreateUserName(
                "init".equalsIgnoreCase(tag4Get.getCreateUserName()) ? null : tag4Get.getCreateUserName());
        return tag4Get;
    }

    /**
     * 获取标签详情
     *
     * @param orgId
     * @param id
     * @return
     */
    public Tag4Create getTagDetail(String orgId, String id) {
        Tag4Create tag4Create = new Tag4Create();
        TagEntity tagEntity = tagRepository.queryTagById(orgId, id);
        Validate.isNotNull(tagEntity, BkApiErrorKeys.APIS_TALENTBK_TAG_NOT_EXIST);
        BeanCopierUtil.copy(tagEntity, tag4Create);
        //查询tagvalue信息
        List<TagValueEntity> list = tagValueRepository.getTagValuesByTagId(orgId, id);
        if (CollectionUtils.isNotEmpty(list)) {
            List<TagValue4Create> tagValueList = BeanCopierUtil
                    .convertList(list, TagValueEntity.class, TagValue4Create.class);
            tag4Create.setTagValueList(tagValueList);
        }
        return tag4Create;
    }

    /**
     * 校验分层标签值是否有重名
     *
     * @param tagValueList
     */
    private void validateTagValueNames(List<TagValue4Create> tagValueList) {
        Set<String> set = tagValueList.stream().map(TagValue4Create::getValueName).collect(Collectors.toSet());
        Validate.isTrue(set.size() == tagValueList.size(), "apis.talentbk.tagvlue.name.conflict");
    }

    public Map<String, List<TagInfoBean>> getTagMap(String orgId, Collection<String> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return Maps.newHashMap();
        }
        List<TagInfoBean> tagInfoBeans = tagRepository.getBaseMapper().listTagValue(orgId, tagIds);
        if (CollectionUtils.isNotEmpty(tagInfoBeans)) {
            return tagInfoBeans.stream().collect(Collectors.groupingBy(TagInfoBean::getId));
        }
        return Maps.newHashMap();
    }

    /**
     * 从ES中查询标签用户数据<br>
     * 注：非所有标签都适用
     * @return
     */
    public org.springframework.data.domain.Page<UserTagListBean4Es> listPageTagUserForEs(String orgId, String keyword,
                                                String tagId, String tagValueId,PageRequest pageRequest){
        NativeSearchQuery nativeSearchQuery = generateListSearchQuery(orgId,keyword,tagId,tagValueId,pageRequest);
        log.debug("LOG11180:{}{}", System.lineSeparator(), esQueryConveter.extractJson(nativeSearchQuery));
        return esTemplate.queryForPage(nativeSearchQuery, UserTagListBean4Es.class);
    }

    /**
     * 从ES中统计标签用户数量<br>
     * 注：非所有标签都适用
     * @return
     */
    public long countTagUserForEs(String orgId, String keyword,String tagId, String tagValueId,PageRequest pageRequest){
        NativeSearchQuery nativeSearchQuery = generateListSearchQuery(orgId,keyword,tagId,tagValueId,pageRequest);
        log.debug("LOG11190:{}{}", System.lineSeparator(), esQueryConveter.extractJson(nativeSearchQuery));
        return esTemplate.count(nativeSearchQuery, UserTagListBean4Es.class);
    }

    private NativeSearchQuery generateListSearchQuery(String orgId, String keyword, String tagId, String tagValueId,
                                                      PageRequest pageRequest){
        // 1. 查询条件
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        // 机构隔离
        BoolQueryBuilder orgIdQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(UserSearchConstants.EsSpecialLabel.ORG_ID.getValue(), orgId));
        mainQueryBuilder.must().add(orgIdQueryBuilder);
        // 只查未删除的用户
        BoolQueryBuilder unDeletedQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(UserSearchConstants.EsSpecialLabel.USER_DELETED.getValue(), 0));
        mainQueryBuilder.must().add(unDeletedQueryBuilder);

        // 关键字查询
        if (StringUtils.isNotBlank(keyword)) {
            BoolQueryBuilder keywordQueryBuilder = QueryBuilders.boolQuery();
            keywordQueryBuilder.should(QueryBuilders.wildcardQuery("cnname", "*" + keyword + "*"));
            keywordQueryBuilder.should(QueryBuilders.wildcardQuery("username", "*" + keyword + "*"));
            mainQueryBuilder.must().add(keywordQueryBuilder);
        }

        if(StringUtils.isNotBlank(tagId)){
            TagEntity tagEntity = tagRepository.queryTagById(orgId, tagId);
            String esKey = UserSearchUtils.generateEsPropertiesKey(tagEntity.getTagKey());
            // 年龄段 实际上需要查birthday
            Map<String, String> xxGroupTagEnumMap = UserSearchConstants.getXxGroupTagEnumMap();
            if(xxGroupTagEnumMap.containsKey(tagEntity.getTagKey()) ){
                esKey = xxGroupTagEnumMap.get(tagEntity.getTagKey());
            }
            // 打了某标签、且标签不为空
            BoolQueryBuilder tagKeyQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.existsQuery(esKey));
            if(!xxGroupTagEnumMap.containsKey(tagEntity.getTagKey())){
                tagKeyQuery.mustNot(QueryBuilders.termQuery(esKey,""));
            }
            mainQueryBuilder.must().add(tagKeyQuery);

            if(StringUtils.isNotBlank(tagValueId)){
                if(esKey.contains(UserSearchConstants.EsLabelLevelName.DIYLABELS.getValue())){
                    // 自定义标签处理
                    BoolQueryBuilder tagValueIdQuery = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery(esKey,tagValueId));
                    mainQueryBuilder.must().add(tagValueIdQuery);
                }else{
                    // 固定标签处理
                    // 年龄段、 连续学习N周 等需要特殊处理
                    specialQuery(orgId,tagValueId,tagEntity,mainQueryBuilder);
                }
            }

        }

        //设置查询条件
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(mainQueryBuilder);
        if (pageRequest != null) {
            org.springframework.data.domain.PageRequest pr = org.springframework.data.domain.PageRequest
                    .of((int) pageRequest.getCurrent() - 1, (int) pageRequest.getSize());
            nativeSearchQueryBuilder.withPageable(pr);
        }

        return nativeSearchQueryBuilder.build();
    }

    private void specialQuery(String orgId, String tagValueId, TagEntity tagEntity,BoolQueryBuilder mainQueryBuilder){

        String esKey = UserSearchUtils.generateEsPropertiesKey(tagEntity.getTagKey());

        TagValueEntity tagValueEntity = tagValueRepository.queryTagById(orgId, tagValueId);
        // 年龄段
        Map<String, String> xxGroupTagEnumMap = UserSearchConstants.getXxGroupTagEnumMap();
        if(xxGroupTagEnumMap.containsKey(tagEntity.getTagKey())){
            esKey = xxGroupTagEnumMap.get(tagEntity.getTagKey());
            Map<String, String> ageRangeVKEnumMap = UserSearchConstants.getAgeRangeVKEnumMap();
            String value = ageRangeVKEnumMap.get(tagValueEntity.getValueName());

            UserSearchDateRangeBean dateRangeBean = UserSearchUtils.rangeToDate(value, true);
            if (dateRangeBean == null) {
                return ;
            }
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(esKey);
            //日期格式化
            rangeQueryBuilder.format(Constants.SDF_YEAR2DAY);
            if (dateRangeBean.getStartDate() != null) {
                // 大于等于 开始日期
                rangeQueryBuilder.gt(dateRangeBean.getStartDate());
            }
            if (dateRangeBean.getEndDate() != null) {
                // 小于等于 结束日期
                rangeQueryBuilder.lte(dateRangeBean.getEndDate());
            }

            mainQueryBuilder.must().add(QueryBuilders.boolQuery().must(rangeQueryBuilder));
        }else if(UserSearchConstants.ES_UNSPACED_LRN.equals(esKey)){
            // 连续学习N周 特殊处理
            String n = tagValueEntity.getValueName().replace("连续学习", "").replace("周", "");
            if(StringUtils.isNotBlank(n)){
                try {
                    int intN = Integer.parseInt(n);
                    BoolQueryBuilder tempQuery = QueryBuilders.boolQuery().must(QueryBuilders.termQuery(esKey,intN));
                    mainQueryBuilder.must().add(tempQuery);
                }catch (NumberFormatException e){
                    log.error("连续学习N周特殊处理",e);
                }
            }
        }else if(UserSearchConstants.ES_LRNDURATION_RATIO.equals(esKey)
                || UserSearchConstants.ES_SCORE_RATIO.equals(esKey)){
            String n = tagValueEntity.getValueName().replace("%", "");
            if(StringUtils.isNotBlank(n)){
                try {
                    int intN = Integer.parseInt(n);
                    BoolQueryBuilder tempQuery = QueryBuilders.boolQuery().must(QueryBuilders.termQuery(esKey,intN));
                    mainQueryBuilder.must().add(tempQuery);
                }catch (NumberFormatException e){
                    log.error("ES_LRNDURATION_RATIO | ES_SCORE_RATIO 特殊处理 ",e);
                }
            }
        } else{
            BoolQueryBuilder tempQuery = QueryBuilders.boolQuery().should(QueryBuilders.termQuery(esKey,tagValueEntity.getValueName()));
            mainQueryBuilder.must().add(tempQuery);
        }
    }


    public void addTagValue() {
        List<TagEntity> unspacedLrn = tagRepository.queryTagByKey("unspacedLrn");
        if (CollectionUtils.isEmpty(unspacedLrn)) {
            return;
        }
        Map<String, TagEntity> orgTagMap = StreamUtil.list2map(unspacedLrn, TagEntity::getOrgId);
        List<String> tagIds = StreamUtil.mapList(unspacedLrn, TagEntity::getId);
        if (orgTagMap.containsKey(StringUtils.EMPTY)) {
            TagEntity tagEntity = orgTagMap.get(StringUtils.EMPTY);
            String tagId = tagEntity.getId();
            Set<String> keySet = orgTagMap.keySet();
            List<TagValueEntity> sourceTagValueList = tagValueRepository.getTagValuesByTagId(StringUtils.EMPTY, tagId);
            List<TagValueEntity> tagValueExList = tagValueRepository.findTagValueOrgAndTagIds(keySet, tagIds);
            Map<String, List<TagValueEntity>> tagIdValueMap = tagValueExList.stream()
                    .collect(Collectors.groupingBy(TagValueEntity::getTagId));
            List<TagValueEntity> batchSaveTagValue = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(sourceTagValueList)) {
                for (String orgId : keySet) {
                    if (StringUtils.isNotBlank(orgId)) {
                        TagEntity sourceTag = orgTagMap.get(orgId);
                        if (!tagIdValueMap.containsKey(sourceTag.getId())) {
                            sourceTagValueList.forEach(tagValue -> {
                                TagValueEntity tagValueEntity = new TagValueEntity();
                                BeanCopierUtil.copy(tagValue, tagValueEntity);
                                tagValueEntity.setId(ApiUtil.getUuid());
                                tagValueEntity.setTagId(sourceTag.getId());
                                tagValueEntity.setOrgId(sourceTag.getOrgId());
                                batchSaveTagValue.add(tagValueEntity);
                            });
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(batchSaveTagValue)) {
                tagValueRepository.saveBatch(batchSaveTagValue);
            }
        }
    }
}
