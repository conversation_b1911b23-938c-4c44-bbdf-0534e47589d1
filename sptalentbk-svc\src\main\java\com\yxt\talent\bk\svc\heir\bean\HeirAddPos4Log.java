package com.yxt.talent.bk.svc.heir.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import com.yxt.spsdk.audit.base.AuditLogEntitySupport;
import lombok.Data;

/**
 * HeirAddPos4Log
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 6:43 pm
 */
@Data
public class HeirAddPos4Log implements AuditLogEntitySupport {
    private String positionId;
    @AuditLogField(name = "岗位", orderIndex = 0)
    private String positionName;
    @Override
    public String entityId() {
        return positionId;
    }

    @Override
    public String entityName() {
        return String.format("继任地图-%s", positionName);
    }
}
