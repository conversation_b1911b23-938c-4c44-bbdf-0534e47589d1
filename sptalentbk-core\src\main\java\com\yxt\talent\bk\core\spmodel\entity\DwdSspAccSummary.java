package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * 学时学分积分累计表(DwdSspAccSummary)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 10:38:36
 */
@Data
@TableName(value = "dwd_ssp_acc_summary")
public class DwdSspAccSummary {
    /**
     * 主键id
     */
    @TableField(value = "id")
    private String id;
    /**
     * 三方用户 id
     */
    @TableField(value = "third_user_id")
    private String thirdUserId;

    @TableField(value = "user_id")
    private String userId;
    /**
     * 机构 id
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 累计获得学分
     */
    @TableField(value = "acq_score")
    private BigDecimal acqScore;
    /**
     * 累计扣除学分
     */
    @TableField(value = "ded_score")
    private BigDecimal dedScore;
    /**
     * 实际获得学分
     */
    @TableField(value = "real_acq_score")
    private BigDecimal realAcqScore;
    /**
     * 累计获得积分
     */
    @TableField(value = "user_point")
    private Integer userPoint;
    /**
     * 累计扣除积分
     */
    @TableField(value = "deduct_point")
    private Long deductPoint;
    /**
     * 实际获得积分
     */
    @TableField(value = "actual_point")
    private Long actualPoint;
    /**
     * 累计学习时长
     */
    @TableField(value = "study_hour")
    private Long studyHour;
    /**
     * 在线课堂学习时长
     */
    @TableField(value = "kng_source_study_hour")
    private Long kngSourceStudyHour;
    /**
     * 培训学习时长
     */
    @TableField(value = "o2o_source_study_hour")
    private Long o2oSourceStudyHour;
    /**
     * 考试学习时长
     */
    @TableField(value = "exam_source_study_hour")
    private Long examSourceStudyHour;
    /**
     * 练习学习时长
     */
    @TableField(value = "prac_source_study_hour")
    private Long pracSourceStudyHour;
    /**
     * 直播学习时长
     */
    @TableField(value = "live_source_study_hour")
    private Long liveSourceStudyHour;
    /**
     * 人才发展学习时长
     */
    @TableField(value = "gwnl_source_study_hour")
    private Long gwnlSourceStudyHour;
    /**
     * 智能教练学习时长
     */
    @TableField(value = "sparring_source_study_hour")
    private Long sparringSourceStudyHour;
    /**
     * 连续学习周数
     */
    @TableField(value = "consecutive_study_weeks")
    private Integer consecutiveStudyWeeks;
    /**
     * 主动学习时长占比
     */
    @TableField(value = "active_study_duration_ratio")
    private Double activeStudyDurationRatio;
    /**
     * 近一年学习时长
     */
    @TableField(value = "study_duration_last_year")
    private Long studyDurationLastYear;
    /**
     * 近一年学习时长排名百分位
     */
    @TableField(value = "study_duration_rank_percentile_last_year")
    private Double studyDurationRankPercentileLastYear;
    /**
     * 近一年实际获得学分
     */
    @TableField(value = "credits_earned_last_year")
    private Long creditsEarnedLastYear;
    /**
     * 近一年实际获得学分排名百分位
     */
    @TableField(value = "credits_earned_rank_percentile_last_year")
    private Double creditsEarnedRankPercentileLastYear;
    /**
     * 是否删除(0-否,1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

}
