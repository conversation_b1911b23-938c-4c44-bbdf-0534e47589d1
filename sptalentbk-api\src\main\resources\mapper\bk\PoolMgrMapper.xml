<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.pool.mapper.PoolMgrMapper">
    <sql id="Base_Columns">
        t1.id AS  id,
        t1.org_id AS  orgId,
        t1.pool_Id AS  poolName,
        t1.mgr_id AS  mgrId,
        t1.create_user_id AS  createUserId,
        t1.create_time AS  createTime,
        t1.update_user_id AS  updateUserId,
        t1.update_time AS  updateTime
    </sql>

    <select id="findPoolMgrByPoolId" resultType="com.yxt.talent.bk.core.pool.bean.PoolMgr4List">
        SELECT
            t1.mgr_id AS  mgrId,
            t1.pool_id AS poolId,
            t2.fullname as mgrName
        FROM bk_pool_mgr t1
        JOIN
        udp_lite_user_sp t2
        ON t1.mgr_id = t2.id AND t1.org_id = t2.org_id
        WHERE t1.org_id = #{orgId}
        AND t1.pool_Id = #{poolId}
    </select>

    <select id="findPoolMgrByPoolIds" resultType="com.yxt.talent.bk.core.pool.bean.PoolMgr4List">
        SELECT
            t1.mgr_id AS  mgrId,
            t1.pool_id AS poolId,
            t2.fullname as mgrName
        FROM bk_pool_mgr t1
        JOIN
        udp_lite_user_sp t2
        ON t1.mgr_id = t2.id AND t1.org_id = t2.org_id
        WHERE t1.org_id = #{orgId}
        AND t1.pool_Id in
        <foreach collection="poolIds" item="poolId" open="(" separator="," close=")">
            #{poolId}
        </foreach>
    </select>

    <select id="findAuthPoolIdsByMgrIds" resultType="java.lang.String">
        SELECT
             DISTINCT t1.pool_id AS poolId
        FROM bk_pool_mgr t1
        WHERE t1.org_id = #{orgId}
        AND t1.mgr_id IN
        <foreach collection="mgrIds" item="mgrId" open="(" separator="," close=")">
            #{mgrId}
        </foreach>
    </select>

    <select id="resTransferList" resultType="com.yxt.talent.bk.core.pool.bean.PoolMgtBean">
        select id,pool_id,mgr_id,0 as deleted from bk_pool_mgr where org_id = #{orgId}
        and mgr_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        and pool_id in
        <foreach collection="poolIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="execResTransfer">
        <foreach collection="list" item="item" separator=";">
            <choose>
                <when test="item.deleted eq 1">
                    delete from bk_pool_mgr where org_id = #{orgId} and id = #{item.id}
                </when>
                <otherwise>
                    update bk_pool_mgr set mgr_id = #{item.mgrId} where org_id = #{orgId} and id = #{item.id}
                </otherwise>
            </choose>
        </foreach>
    </update>
</mapper>
