package com.yxt.talent.bk.common.constants;

import lombok.Getter;

public class CatalogConstants {

    public static final String DEFAULT_POOL_CATALOG_NAME = "默认分类";

    /**
     * 分类来源
     */
    @Getter
    public enum CatalogSource {
        /**
         * 0：标签，1：人才池
         */
        TAG("标签", 0),
        POOL("人才池", 1);
        private String name;
        private int value;

        CatalogSource(String name, int value) {
            this.name = name;
            this.value = value;
        }
    }

    /**
     * 分类类型
     */
    @Getter
    public enum CatalogType {
        /**
         * 0：默认，1：内置，2：自定义
         */
        DEFAULT("默认", 0),
        INTERNAL("内置", 1),
        CUSTOM("自定义", 2);
        private String name;
        private int value;

        CatalogType(String name, int value) {
            this.name = name;
            this.value = value;
        }
    }
}
