package com.yxt.talent.bk.svc.heir.component;

import com.yxt.common.pojo.IdName;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.common.utils.StringConcatBuilder;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserBriefBean;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.udp.bean.UdpPositionBean;
import com.yxt.talent.bk.core.udp.repo.UdpDeptRepository;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.common.enums.AuditLogPointEnum;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareBean;
import com.yxt.talent.bk.svc.heir.bean.HeirUserEdit4Log;
import com.yxt.talent.bk.svc.heir.bean.req.HeirUserEditParam4Log;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserUpdateReq;
import com.yxt.talent.bk.svc.udp.UdpQueryService;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.Locale;
import java.util.Map;
import java.util.Optional;

/**
 * HeirEditLogProvider
 *
 * <AUTHOR> geyan
 * @Date 19/3/24 2:34 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirUserEditLogProvider implements AuditLogDataProvider<HeirUserEditParam4Log, HeirUserEdit4Log> {
    private final HeirPosRepository heirPosRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final UdpDeptRepository udpDeptRepository;
    private final UdpQueryService udpQueryService;
    private final HeirPosUserMapper heirPosUserMapper;
    private final AuthService authService;
    private final HeirPosService heirPosService;

    @Override
    public HeirUserEditParam4Log convertParam(Object param, AuditLogBasicBean logBasic) {
        if (param instanceof PosUserUpdateReq) {
            HeirUserEditParam4Log ret = new HeirUserEditParam4Log();
            BeanCopierUtil.copy(param, ret);
            if (CollectionUtils.isNotEmpty(ret.getIds())) {
                ret.setPosUsers(heirPosUserMapper.listBriefByIds(ret.getIds()));
            }
            return ret;
        }
        return AuditLogDataProvider.super.convertParam(param, logBasic);
    }

    @Override
    public HeirUserEdit4Log before(HeirUserEditParam4Log param, AuditLogBasicBean logBasic) {
        HeirUserEdit4Log edit4Log = new HeirUserEdit4Log((AuditLogPointEnum) logBasic.getLogPoint().getPointEnum());
        if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_USER_PREPARE)) {
            Locale locale = authService.getLocale();
            Map<Long, HeirPrepareBean> prepareMap = heirPosService.queryPrepareMap(logBasic.getOrgId(), locale);
            param.setPrepareLevelName(IArrayUtils.mapGet(prepareMap, param.getPrepareLevelId(), HeirPrepareBean::getLevelName));
            StringConcatBuilder userInfoStr = new StringConcatBuilder(StringPool.COMMA);
            udpLiteUserRepository.fillUserInfo(logBasic.getOrgId(), param.getPosUsers(), HeirPosUserBriefBean::getUserId, (posUser, userInfo) -> {
                userInfoStr.append(userInfo.getFullname());
                userInfoStr.append("(").append(userInfo.getUsername()).append(")");
                userInfoStr.append(":").append(IArrayUtils.mapGet(prepareMap, posUser.getPrepareLevelId(), HeirPrepareBean::getLevelName));
                userInfoStr.appendConcat();
            }, Lists.newArrayList("username","fullname"));
            edit4Log.setUserInfoList(userInfoStr.output());
        }
        return edit4Log;
    }

    @Override
    public HeirUserEdit4Log after(HeirUserEditParam4Log param, AuditLogBasicBean logBasic) {
        HeirUserEdit4Log edit4Log = new HeirUserEdit4Log((AuditLogPointEnum) logBasic.getLogPoint().getPointEnum());
        if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_USER_PREPARE)) {
            StringConcatBuilder userInfoStr = new StringConcatBuilder(StringPool.COMMA);
            udpLiteUserRepository.fillUserInfo(logBasic.getOrgId(), param.getPosUsers(), HeirPosUserBriefBean::getUserId, (posUser, userInfo) -> {
                userInfoStr.append(userInfo.getFullname());
                userInfoStr.append("(").append(userInfo.getUsername()).append(")");
                userInfoStr.append(":").append(param.getPrepareLevelName());
                userInfoStr.appendConcat();
            }, Lists.newArrayList("username","fullname"));
            edit4Log.setUserInfoList(userInfoStr.output());
        } else {
            edit4Log.setUserInfoList(udpLiteUserRepository.userNames4Log(logBasic.getOrgId(),
                    BeanCopierUtil.convertList(param.getPosUsers(), HeirPosUserBriefBean::getUserId)));
            edit4Log.setExitReason(param.getQuitReason());
        }
        return edit4Log;
    }

    @Override
    public Pair<String, String> entityInfo(HeirUserEditParam4Log param, HeirUserEdit4Log beforeObj, HeirUserEdit4Log afterObj, AuditLogBasicBean logBasic) {
        int posType = heirPosRepository.getPosType(param.getPosId());
        String posName;
        if (posType == HeirPosTypeEnum.DEPT.getType()) {
            posName = udpDeptRepository.getNameById(logBasic.getOrgId(), param.getPosId());
        } else {
            posName = Optional.ofNullable(IArrayUtils.getFirst(
                    udpQueryService.listPositionByIds(logBasic.getOrgId(), Lists.newArrayList(param.getPosId()))))
                    .map(IdName::getName).orElse(StringPool.EMPTY);
        }
        String entityName;
        if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_USER_PREPARE)) {
            //继任地图-{关键岗位名称}-调整准备度
            entityName = String.format("继任地图-%s-调整准备度", posName);
        } else if (logBasic.equalsToPointEnum(AuditLogPointEnum.HEIR_USER_EXIT)) {
            //继任地图-{关键岗位名称}-退出
            entityName = String.format("继任地图-%s-退出", posName);
        } else {
            entityName = String.format("继任地图-%s-重新加入", posName);
        }
        return Pair.of(param.getPosId(), entityName);
    }
}
