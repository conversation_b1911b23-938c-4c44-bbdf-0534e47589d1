<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>sptalentbk-parent</artifactId>
        <groupId>com.yxt</groupId>
        <version>6.5.1-jdk17</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>sptalentbk-api</artifactId>
    <packaging>jar</packaging>

    <name>sptalentbk-api</name>
    <description>talent bank for yxt</description>

    <dependencies>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentbk-svc</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>sptalentbkapi</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
