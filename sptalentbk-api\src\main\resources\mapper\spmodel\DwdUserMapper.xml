<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdUserMapper">
    <select id="userUnderDept" resultType="string">
        select user_id from dws_user u
        where org_id = '${orgId}' and user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            '${item}'
        </foreach>
        and exists(
            select 1 from dim_dept_closure where dept_id = u.dept_id and parent_id = '${deptId }' and deleted = 0
        )
    </select>
    <select id="getDeptUserCount"
            resultType="com.yxt.talent.bk.core.dashboard.bean.DeptUserCountDTO">
        select a.parent_id as deptId, count(1) as userCount
        from (
            select distinct du.user_id, ddc.parent_id
            from dws_user         du
            join dim_dept_closure ddc on du.dept_id = ddc.dept_id and ddc.deleted = 0
                                      and ddc.org_id = '${orgId}' and ddc.parent_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                '${deptId}'
            </foreach>
            <if test="groupIds != null and groupIds.size() > 0">
                <!--@ignoreSql-->
                join dwd_user_group_member dugm on dugm.user_id = du.user_id and dugm.org_id ='${orgId}'
                and dugm.group_id in
                <foreach collection="groupIds" item="groupId" separator="," open="(" close=")">
                    ${groupId}
                </foreach>
            </if>
            where du.org_id = '${orgId}'
              and du.enabled = 1
              and du.deleted = 0
        ) a
        group by a.parent_id
    </select>
    <select id="getGroupUserCount" resultType="com.yxt.talent.bk.core.dashboard.bean.GroupUserCountDTO">
        select a.group_id as groupId,
        count(1) as userCount
        from (select distinct du.user_id,
        dugm.group_id
        from dws_user du
        join dwd_user_group_member dugm on dugm.user_id = du.user_id and dugm.org_id ='${orgId}'
        and dugm.group_id in
        <!--@ignoreSql-->
        <foreach collection="groupIds" item="groupId" separator="," open="(" close=")">
            ${groupId}
        </foreach>
        <if test="deptIds != null and deptIds.size() > 0">
            join dim_dept_closure ddc on du.dept_id = ddc.dept_id
            and ddc.org_id = '${orgId}'
            and ddc.deleted = 0
            and ddc.parent_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                '${deptId}'
            </foreach>
        </if>
        where du.org_id = '${orgId}'
        and du.enabled = 1
        and du.deleted = 0) a
        group by a.group_id
    </select>

    <select id="getUserInfoByUserIds" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdUser">
        select du.user_id as userId,
        du.user_id as userId,
        du.full_name as fullName,
        du.user_name as userName,
        du.dept_id as deptId,
        du.third_dept_name as thirdDeptName,
        du.position_id as positionId,
        du.third_position_name as thirdPositionName
        from dws_user du
        where du.org_id = '${orgId}'
        AND du.user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            '${userId}'
        </foreach>
        AND du.enabled = 1
        AND du.deleted = 0
    </select>
    <select id="getDeptAndGroupByUserMix" resultType="java.lang.String">
        select distinct a.dept_id
        from (
            select du.org_id, du.user_id, du.dept_id
            from dws_user du
            where du.org_id = '${orgId}'
              and du.deleted = 0
              and du.enabled = 1
              and du.dept_id in (
                select dept_id from dim_dept_closure
                where org_id = '${orgId}' and deleted = 0
                <if test="deptIds != null and deptIds.size() > 0">
                    and parent_id in
                    <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                        '${deptId}'
                    </foreach>
                </if>
             )
        ) a
        join (
            select org_id, user_id
            from dwd_user_group_member
            where org_id = '${orgId}' and deleted = 0
            <if test="groupIds != null and groupIds.size() > 0">
                and group_id in
                <!--@ignoreSql-->
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    ${groupId}
                </foreach>
            </if>
        ) b on a.org_id = b.org_id and a.user_id = b.user_id
    </select>
    <select id="getUserAuthGroupListByDeptAndGroup" resultType="java.lang.Long">
        select distinct b.group_id
        from (
            select du.org_id, du.user_id, du.dept_id
            from dws_user du
            where du.org_id = '${orgId}'
              and du.deleted = 0
              and du.enabled = 1
              and du.dept_id in (
                select dept_id from dim_dept_closure
                where org_id = '${orgId}' and deleted = 0
                <if test="deptIds != null and deptIds.size() > 0">
                    and parent_id in
                    <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                        '${deptId}'
                    </foreach>
                </if>
            )
        ) a
        join (
            select org_id, user_id, group_id
            from dwd_user_group_member
            where org_id = '${orgId}' and deleted = 0
            <if test="groupIds != null and groupIds.size() > 0">
                and group_id in
                <!--@ignoreSql-->
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    ${groupId}
                </foreach>
            </if>
        ) b on a.org_id = b.org_id and a.user_id = b.user_id
    </select>
</mapper>
