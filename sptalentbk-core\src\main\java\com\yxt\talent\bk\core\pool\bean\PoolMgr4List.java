package com.yxt.talent.bk.core.pool.bean;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PoolMgr4List implements UdpLangSupport {
    private String mgrId;
    private String poolId;
    private String mgrName;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(mgrId, mgrName, this::setMgrName);
    }
}
