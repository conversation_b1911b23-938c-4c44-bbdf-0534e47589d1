package com.yxt.talent.bk.svc.common;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spmodel.facade.bean.demo.OrgDemoIdMappingVO;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.spsdk.democopy.PreSetIdMapRepository;
import com.yxt.spsdk.logsave.LogRecorder;
import com.yxt.talent.bk.common.bean.searchrule.BaseSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SPTagSearchBean;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.common.constants.CatalogConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.common.enums.TargetTypeEnum;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.core.common.repo.CatalogRepository;
import com.yxt.talent.bk.core.component.RedisComponent;
import com.yxt.talent.bk.core.heir.entity.HeirLangI18nEntity;
import com.yxt.talent.bk.core.heir.entity.HeirOrgConfigEntity;
import com.yxt.talent.bk.core.heir.entity.HeirOrgLevelCfgEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosBenchmarkEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosPermissionEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosUserEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirLangI18nMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.repo.HeirOrgConfigRepository;
import com.yxt.talent.bk.core.heir.repo.HeirOrgLevelCfgRepository;
import com.yxt.talent.bk.core.heir.repo.HeirPosBenchmarkRepository;
import com.yxt.talent.bk.core.heir.repo.HeirPosPermissionRepository;
import com.yxt.talent.bk.core.heir.repo.HeirPosPrepareCfgRepository;
import com.yxt.talent.bk.core.heir.repo.HeirPosUserRepository;
import com.yxt.talent.bk.core.mq.RocketMqProducerRepository;
import com.yxt.talent.bk.core.pool.entity.Pool;
import com.yxt.talent.bk.core.pool.entity.PoolMgr;
import com.yxt.talent.bk.core.pool.entity.PoolReadiness;
import com.yxt.talent.bk.core.pool.entity.PoolUser;
import com.yxt.talent.bk.core.pool.entity.ProjectTargetMap;
import com.yxt.talent.bk.core.pool.repo.PoolMgrRepository;
import com.yxt.talent.bk.core.pool.repo.PoolReadinessRepository;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.core.pool.repo.PoolUserRepository;
import com.yxt.talent.bk.core.pool.repo.ProjectTargetMapRepository;
import com.yxt.talent.bk.core.profile.entity.PortraitOrgConfig;
import com.yxt.talent.bk.core.profile.entity.PortraitWhite;
import com.yxt.talent.bk.core.profile.mapper.PortraitOrgConfigMapper;
import com.yxt.talent.bk.core.profile.mapper.PortraitWhiteMapper;
import com.yxt.talent.bk.core.profile.repo.PortraitWhiteRepository;
import com.yxt.talent.bk.core.usergroup.entity.SearchRule;
import com.yxt.talent.bk.core.usergroup.entity.SearchScheme;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupManager;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupMember;
import com.yxt.talent.bk.core.usergroup.repo.SearchRuleRepository;
import com.yxt.talent.bk.core.usergroup.repo.SearchSchemeRepository;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupManagerRepository;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupMemberRepository;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupRepository;
import com.yxt.talent.bk.svc.common.bean.DemoCopyResult;
import com.yxt.talent.bk.svc.heir.bean.PrepareRuleCfgDTO;
import com.yxt.talent.bk.svc.rpc.EvalRpc;
import com.yxt.talent.bk.svc.rpc.O2oRpc;
import com.yxt.talent.bk.svc.rpc.SpmodelRpc;
import com.yxt.talentbkfacade.bean.SpDemoInitCmd4Mq;
import com.yxt.talentbkfacade.constant.BkFacadeContants;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * DemoCopyService
 *
 * <AUTHOR> harleyge
 * @Date 9/9/24 2:18 pm
 */
@Service
@AllArgsConstructor
@Slf4j
public class DemoCopyService {
    private final PoolReadinessRepository poolReadinessRepository;
    private final PoolRepository poolRepository;
    private final PoolMgrRepository poolMgrRepository;
    private final PoolUserRepository poolUserRepository;
    private final CatalogRepository catalogRepository;
    private final ProjectTargetMapRepository projectTargetMapRepository;
    private final PortraitOrgConfigMapper portraitOrgConfigMapper;
    private final PortraitWhiteMapper portraitWhiteMapper;
    private final PortraitWhiteRepository portraitWhiteRepository;
    private final HeirPosMapper heirPosMapper;
    private final HeirLangI18nMapper heirLangI18nMapper;
    private final UserGroupRepository userGroupRepository;
    private final UserGroupMemberRepository userGroupMemberRepository;
    private final SearchRuleRepository searchRuleRepository;
    private final SearchSchemeRepository searchSchemeRepository;
    private final UserGroupManagerRepository userGroupManagerRepository;
    private final RedisComponent redisComponent;
    private final RocketMqProducerRepository rocketMqProducerRepository;
    private final HeirPosBenchmarkRepository heirPosBenchmarkRepository;
    private final HeirPosPermissionRepository heirPosPermissionRepository;
    private final HeirPosUserRepository heirPosUserRepository;
    private final HeirOrgConfigRepository heirOrgConfigRepository;
    private final HeirOrgLevelCfgRepository heirOrgLevelCfgRepository;
    private final HeirPosPrepareCfgRepository heirPosPrepareCfgRepository;
    private final O2oRpc o2oRpc;
    private final SpmodelRpc spmodelRpc;
    private final EvalRpc evalRpc;
    @Value("${demo.copy.start.related:org_initialize,spmodel_copy,o2o_copy,cer_copy}")
    private Set<String> demoCopyStartRelated;
    @Value("${demo.copy.init.related:sptalentbk_init,sprv_init,spsd_init,sptalent_init,speval_init}")
    private Set<String> demoCopyInitRelated;
    @Value("${demo.copy.end.related:sptalentbk_copy,sprv_copy,spsd_copy,sptalent_copy,speval_copy}")
    private Set<String> demoCopyEndRelated;
    @Value("${demo.copy.disabled:0}")
    private String demoCopyDisabled;

    public List<String> preGenIdMap(OrgInit4Mq orgInit) {
        DemoCopyRunner runner = buildRunner(orgInit);
        runner.preSetIdMapGen();
        return runner.deleteEntitySQL(orgInit.getTargetOrgId());
    }

    public List<String> deleteEntitySQL(OrgInit4Mq orgInit) {
        DemoCopyRunner runner = buildRunner(orgInit);
        return runner.deleteEntitySQL(orgInit.getTargetOrgId());
    }

    public void demoCopy(OrgInit4Mq orgInit) {
        DemoCopyRunner runner = buildRunner(orgInit);
        runner.addPreSetIdMap(BkDemoConstants.O2O_PROJECT_ID,
                o2oRpc.demoCopyPrjOld2NewIdMap(orgInit.getTargetOrgId()));
        runner.addPreSetIdMap(BkDemoConstants.EVAL_EVALUATION_ID,
                evalRpc.getDemoIdInfo(orgInit.getSourceOrgId(), orgInit.getTargetOrgId()));

        OrgDemoIdMappingVO spmodelIdMap = spmodelRpc.demoCopyIdMap(orgInit.getTargetOrgId());
        runner.addPreSetIdMap(BkDemoConstants.SPM_LABEL_ID, spmodelIdMap.getLabelIdMap());
        runner.addPreSetIdMap(BkDemoConstants.SPM_LABEL_VALUE_ID, spmodelIdMap.getLabelValueIdMap());
        runner.addPreSetIdMap(BkDemoConstants.SPM_RULE_ID, spmodelIdMap.getRuleIdMap());
        //执行复制
        runner.copyRun();
        //runner.deleteEntitySQL(orgInit.getTargetOrgId()).forEach(System.out::println);
    }

    /**
     * 对外提供facade查询 idMap
     * @param idMapKey
     * @return
     */
    public Map queryPreSetIdMap(OrgInit4Mq orgInit, String idMapKey) {
        DemoCopyRunner runner = buildRunner(orgInit);
        return runner.queryPreSetIdMap(idMapKey);
    }

    public OrgInit4Mq orgInitData(String targetOrgId) {
        String saveKey = String.format(TalentBkRedisKeys.CACHE_COPY_ORG_DATA, targetOrgId);
        return JSON.parseObject(redisComponent.getValue(saveKey), OrgInit4Mq.class);
    }

    public void demoCopyBizDone(String targetOrgId, String bizCode, int status) {
        if (StringUtils.isAnyEmpty(targetOrgId, bizCode)) {
            log.warn("demoCopyBizDone invalid param targetOrgId {} bizCode {}", targetOrgId, bizCode);
            return;
        }
        if (CollectionUtils.isEmpty(demoCopyStartRelated)) {
            log.warn("demoCopyBizDone demoCopyStartRelated none targetOrgId {} bizCode {}", targetOrgId, bizCode);
            return;
        }
        log.info("demoCopyBizDone demoCopyDisabled {} targetOrgId {} bizCode {} status {}", demoCopyDisabled, targetOrgId, bizCode, status);
        if ("1".equals(demoCopyDisabled)) {
            return;
        }

        String lockKey = String.format(TalentBkRedisKeys.CACHE_LOCK_ORG_COPY_RESULT, targetOrgId);
        String resultKey = String.format(TalentBkRedisKeys.CACHE_ORG_COPY_RESULT, targetOrgId);
        CommonUtils.lockRun(lockKey,5, TimeUnit.MINUTES, () -> {
            DemoCopyResult copyResult = JSON.parseObject(redisComponent.getValue(resultKey), DemoCopyResult.class);
            if (copyResult == null) {
                copyResult = new DemoCopyResult();
                copyResult.setTargetOrgId(targetOrgId);
                copyResult.setCreateTime(System.currentTimeMillis());
            }
            DemoCopyResult.CopyBizResult bizResult = IArrayUtils.getFirstMatch(copyResult.getCopyResult(),
                    copyBiz -> bizCode.equals(copyBiz.getBizCode()));
            if (bizResult == null) {
                bizResult = new DemoCopyResult.CopyBizResult();
                copyResult.getCopyResult().add(bizResult);
            }
            if (bizResult.getStatus() == YesOrNo.YES.getValue()) {
                log.info("demoCopyBizDone duplicate targetOrgId {} bizCode {} status {}", targetOrgId, bizCode, status);
                return;
            }
            bizResult.setBizCode(bizCode);
            bizResult.setReceiveTime(System.currentTimeMillis());
            bizResult.setStatus(status);
            Set<String> doneBizCode = copyResult.getCopyResult().stream().filter(item -> item.getStatus() == YesOrNo.YES.getValue())
                    .map(DemoCopyResult.CopyBizResult::getBizCode).collect(Collectors.toSet());
            String orgInitDataKey = String.format(TalentBkRedisKeys.CACHE_COPY_ORG_DATA, targetOrgId);
            SpDemoInitCmd4Mq copyBean = new SpDemoInitCmd4Mq();
            copyBean.setOrgId(targetOrgId);
            copyBean.setOrgInitData(redisComponent.getValue(orgInitDataKey));
            if (!copyResult.isStart()) {
                if (doneBizCode.containsAll(demoCopyStartRelated)) {
                    copyResult.setStart(true);
                    copyResult.setStartTime(System.currentTimeMillis());
                    rocketMqProducerRepository.send(BkFacadeContants.TOPIC_SP_DEMO_INIT_START, JSON.toJSONString(copyBean));
                }
            } else if (!copyResult.isCopy()) {
                if (doneBizCode.containsAll(demoCopyInitRelated)) {
                    copyResult.setCopy(true);
                    copyResult.setCopyTime(System.currentTimeMillis());
                    rocketMqProducerRepository.send(BkFacadeContants.TOPIC_SP_DEMO_INIT_COPY, JSON.toJSONString(copyBean));
                }
            } else if (!copyResult.isEnd()) {
                if (doneBizCode.containsAll(demoCopyEndRelated)) {
                    copyResult.setEnd(true);
                    copyResult.setEndTime(System.currentTimeMillis());
                    redisComponent.expire(orgInitDataKey, TalentBkConstants.DEMO_COPY_RUN_DATA_KEEP_TIME);
                    redisComponent.delMemberOfSet(TalentBkRedisKeys.CACHE_DEMO_COPY_ORG_SET, targetOrgId);
                }
            }
            redisComponent.setValue(resultKey, JSON.toJSONString(copyResult), TalentBkConstants.DEMO_COPY_RUN_DATA_KEEP_TIME);
            if (!copyResult.isEnd() && !copyResult.isTimeoutNotify()) {
                redisComponent.addToSet(TalentBkRedisKeys.CACHE_DEMO_COPY_ORG_SET, targetOrgId, TalentBkConstants.DEMO_COPY_RUN_DATA_KEEP_TIME);
            }
        });
    }

    public void checkDemoCopyTimeout() {
        for (String targetOrgId : redisComponent.memberOfSet(TalentBkRedisKeys.CACHE_DEMO_COPY_ORG_SET)) {
            String lockKey = String.format(TalentBkRedisKeys.CACHE_LOCK_ORG_COPY_RESULT, targetOrgId);
            String resultKey = String.format(TalentBkRedisKeys.CACHE_ORG_COPY_RESULT, targetOrgId);
            {
                DemoCopyResult copyResult = JSON.parseObject(redisComponent.getValue(resultKey), DemoCopyResult.class);
                if (copyResult == null || copyResult.isEnd() || copyResult.isTimeoutNotify()) {
                    redisComponent.delMemberOfSet(TalentBkRedisKeys.CACHE_DEMO_COPY_ORG_SET, targetOrgId);
                    continue;
                }
                //未超时等待下一轮check
                if (copyResult.getStartTime() == null
                        || System.currentTimeMillis() - copyResult.getStartTime() < 60 * 60 * 1000) {
                    continue;
                }
            }
            CommonUtils.lockRun(lockKey,5, TimeUnit.MINUTES, () -> {
                DemoCopyResult copyResult = JSON.parseObject(redisComponent.getValue(resultKey), DemoCopyResult.class);
                if (copyResult == null || copyResult.isEnd() || copyResult.isTimeoutNotify()) {
                    redisComponent.delMemberOfSet(TalentBkRedisKeys.CACHE_DEMO_COPY_ORG_SET, targetOrgId);
                    return;
                }
                LogRecorder.error(targetOrgId, log, "demoOrg copy timeout " + targetOrgId, new TimeoutException("demoCopyTimeout"));
                copyResult.setTimeoutNotify(true);
                redisComponent.setValue(resultKey, JSON.toJSONString(copyResult), TalentBkConstants.DEMO_COPY_RUN_DATA_KEEP_TIME);
            });
        }
    }

    private void addPoolCopy(DemoCopyRunner runner) {
        runner.addDynamicIdMap(BkDemoConstants.DYN_MAP_POOL_TARGET, (prjTarget, copyCtx) -> {
            ProjectTargetMap targetMap = (ProjectTargetMap) prjTarget;
            if (TargetTypeEnum.EVALUATION.getType().equals(targetMap.getTargetType())) {
                return BkDemoConstants.EVAL_EVALUATION_ID;
            } else {
                //0: o2o项目
                return BkDemoConstants.O2O_PROJECT_ID;
            }
        });
        //需要复制的实现，查询方法，插入方法，查询依赖id(可选项)
        runner.addCopyEntity(PortraitWhite.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return portraitWhiteRepository.queryEnableByOrgId(sourceOrgId);
        }, portraitWhiteMapper::insertBatch);
        runner.addCopyEntity(PortraitOrgConfig.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            PortraitOrgConfig orgConfig = null;
            if (portraitOrgConfigMapper.queryByOrgId(runCtx.getCopyCtx().getTargetOrgId()) == null) {
                //一个机构一条，目标机构数据不存在再复制
                orgConfig = portraitOrgConfigMapper.queryByOrgId(sourceOrgId);
            }
            return orgConfig != null ? Lists.newArrayList(orgConfig) : Lists.newArrayList();
        }, portraitOrgConfigMapper::insertBatch);
        runner.addCopyEntity(PoolReadiness.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return poolReadinessRepository.list(sourceOrgId);
        }, poolReadinessRepository::saveBatch);
        runner.addCopyEntity(Pool.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return poolRepository.list(sourceOrgId);
        }, poolRepository::saveBatch);
        runner.addCopyEntity(TagCatalog.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return catalogRepository.findBySourceAndType(sourceOrgId, CatalogConstants.CatalogSource.POOL.getValue(), null);
        }, catalogRepository::saveBatch);
        runner.addCopyEntity(PoolMgr.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return poolMgrRepository.findByPoolIds(sourceOrgId, runCtx.getOneQueryIds());
        }, poolMgrRepository::saveBatch, BkDemoConstants.BK_POOL_ID);
        runner.addCopyEntity(PoolUser.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return poolUserRepository.findPoolIds(sourceOrgId, runCtx.getOneQueryIds());
        }, poolUserRepository::saveBatch, BkDemoConstants.BK_POOL_ID);
        runner.addCopyEntity(ProjectTargetMap.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return projectTargetMapRepository.findByPoolIds(sourceOrgId, runCtx.getOneQueryIds());
        }, projectTargetMapRepository::saveBatch, BkDemoConstants.BK_POOL_ID);
    }

    private void addHeirCopy(DemoCopyRunner runner) {
        runner.addDynamicIdMap(BkDemoConstants.DYN_MAP_POS_ID, (entity, copyCtx) -> {
            HeirPosEntity posEntity = (HeirPosEntity) entity;
            if (HeirPosTypeEnum.POSITION.getType() == posEntity.getPosType()) {
                return DemoCopyConstants.UDP_POSITION_ID;
            } else {
                return DemoCopyConstants.UDP_DEPT_ID;
            }
        });
        runner.addCopyEntity(HeirPosEntity.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return heirPosMapper.listByOrgId(sourceOrgId);
        }, heirPosMapper::insertBatchSomeColumn);
        runner.addCopyEntity(HeirPosBenchmarkEntity.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return heirPosBenchmarkRepository.findByPosIds(sourceOrgId, runCtx.getOneQueryIds());
        }, heirPosBenchmarkRepository::saveBatch, BkDemoConstants.BK_POS_ID);
        runner.addCopyEntity(HeirPosPermissionEntity.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return heirPosPermissionRepository.findByPosIds(sourceOrgId, runCtx.getOneQueryIds());
        }, heirPosPermissionRepository::saveBatch, BkDemoConstants.BK_POS_ID);
        runner.addCopyEntity(HeirPosUserEntity.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return heirPosUserRepository.findByPosIds(sourceOrgId, runCtx.getOneQueryIds());
        }, heirPosUserRepository::saveBatch, BkDemoConstants.BK_POS_ID);
        runner.addCopyEntity(HeirPosPrepareCfgEntity.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return heirPosPrepareCfgRepository.findByPosIds(sourceOrgId, runCtx.getOneQueryIds());
        }, cfgList -> {
            for (HeirPosPrepareCfgEntity prepareCfg : cfgList) {
                PrepareRuleCfgDTO ruleCfgDTO = JSON.parseObject(prepareCfg.getRuleCfgData(), PrepareRuleCfgDTO.class);
                if (ruleCfgDTO != null) {
                    ruleCfgDTO.setId(prepareCfg.getRuleCfgId());
                    prepareCfg.setRuleCfgData(JSON.toJSONString(ruleCfgDTO));
                }
            }
            heirPosPrepareCfgRepository.saveBatch(cfgList);
        }, BkDemoConstants.BK_POS_ID);
        runner.addCopyEntity(HeirOrgConfigEntity.class, runCtx -> {
            //一个机构一条数据
            HeirOrgConfigEntity targetEntity = heirOrgConfigRepository.getByOrgId(runCtx.getCopyCtx().getTargetOrgId());
            if (targetEntity != null) {
                return null;
            }
            HeirOrgConfigEntity sourceEntity = heirOrgConfigRepository.getByOrgId(runCtx.getCopyCtx().getSourceOrgId());
            if (sourceEntity == null) {
                return null;
            }
            return Lists.newArrayList(sourceEntity);
        }, list -> list.forEach(heirOrgConfigRepository::insert));
        runner.addCopyEntity(HeirOrgLevelCfgEntity.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return heirOrgLevelCfgRepository.listByOrgId(sourceOrgId);
        }, heirOrgLevelCfgRepository::saveBatch);
        runner.addCopyEntity(HeirLangI18nEntity.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            if (CollectionUtils.isEmpty(runCtx.getOneQueryIds())) {
                return Lists.newArrayList();
            }
            return heirLangI18nMapper.selectByGroupIds(sourceOrgId, runCtx.getOneQueryIds());
        }, heirLangI18nMapper::batchInsert, BkDemoConstants.BK_ORG_LEVEL_CFG_ID);
    }

    private void addUserGroup(DemoCopyRunner runner) {
        runner.addCopyEntity(UserGroup.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return userGroupRepository.listByOrgId(sourceOrgId);
        }, groupList -> {
            for (UserGroup userGroup : groupList) {
                //BaseSearchBean baseSearch;
                //udp idMap 转换
                BaseSearchBean baseSearch = JSON.parseObject(userGroup.getBaseSearch(), BaseSearchBean.class);
                if (baseSearch != null) {
                    baseSearch.setUserIds(BeanCopierUtil.convertList(baseSearch.getUserIds(),
                            userId -> runner.getIdMapValue(DemoCopyConstants.UDP_USER_ID, userId)));
                    baseSearch.setPositionIds(BeanCopierUtil.convertList(baseSearch.getPositionIds(),
                            posId -> runner.getIdMapValue(DemoCopyConstants.UDP_POSITION_ID, posId)));
                    baseSearch.setGradeIds(BeanCopierUtil.convertList(baseSearch.getGradeIds(),
                            gradeId -> runner.getIdMapValue(DemoCopyConstants.UDP_GRADE_ID, gradeId)));
                    runner.swapIdMapValue(baseSearch.getDepts(), DemoCopyConstants.UDP_DEPT_ID,
                            BaseSearchBean.Dept::getDeptId, BaseSearchBean.Dept::setDeptId);
                    userGroup.setBaseSearch(JSON.toJSONString(baseSearch));
                }
            }
            userGroupRepository.saveBatch(groupList);
        });
        runner.addCopyEntity(UserGroupMember.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return userGroupMemberRepository.listByGroupIds(sourceOrgId, runCtx.getOneQueryIds());
        }, userGroupMemberRepository::saveBatch, BkDemoConstants.BK_GROUP_ID);
        runner.addCopyEntity(UserGroupManager.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return userGroupManagerRepository.listByGroupIds(sourceOrgId, runCtx.getOneQueryIds());
        }, userGroupManagerRepository::saveBatch, BkDemoConstants.BK_GROUP_ID);

        runner.addCopyEntity(SearchRule.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return searchRuleRepository.listByOrgId(sourceOrgId);
        }, srRules -> {
            for (SearchRule srRule : srRules) {
                //String tagSearch  SPTagSearchBean[]
                //spmodel idMap 转换
                List<SPTagSearchBean> tagSrList = JSON.parseArray(srRule.getTagSearch(), SPTagSearchBean.class);
                if (CollectionUtils.isNotEmpty(tagSrList)) {
                    for (SPTagSearchBean spTagSearchBean : tagSrList) {
                        spTagSearchBean.setTagId(runner.getIdMapValue(BkDemoConstants.SPM_LABEL_ID, spTagSearchBean.getTagId()));
                        spTagSearchBean.setTagValues(BeanCopierUtil.convertList(spTagSearchBean.getTagValues(), tagValueId -> {
                            try {
                                return String.valueOf(runner.getIdMapValue(BkDemoConstants.SPM_LABEL_VALUE_ID, Long.parseLong(tagValueId)));
                            } catch (Exception e) {}
                            return tagValueId;
                        }));
                    }
                }
            }
            searchRuleRepository.saveBatch(srRules);
        });
        runner.addCopyEntity(SearchScheme.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return searchSchemeRepository.listByOrgId(sourceOrgId);
        }, searchSchemeRepository::saveBatch);
    }

    private DemoCopyRunner buildRunner(OrgInit4Mq orgInit) {
        DemoCopyRunner runner = new DemoCopyRunner();
        runner.initCopyCtx(orgInit);
        //配置预生成的idMap(如果有),设置需要对外提供的主表
        runner.setPreSetIdMapKey(new PreSetIdMapRepository() {
            @Override
            public void saveValue(String idMapKey, String subKey, String value) {
                String saveKey = String.format(TalentBkRedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                redisComponent.setHmValue(saveKey, subKey, value, TalentBkConstants.DEMO_COPY_RUN_DATA_KEEP_TIME);
            }

            @Override
            public String queryValue(String idMapKey, String subKey) {
                String saveKey = String.format(TalentBkRedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                return redisComponent.getHmValue(saveKey, subKey);
            }

            @Override
            public void delByIdMapKey(String idMapKey) {
                String saveKey = String.format(TalentBkRedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                redisComponent.removeKey(saveKey);
            }
        }, BkDemoConstants.BK_POOL_ID);
        //人才池
        addPoolCopy(runner);
        //人才继任
        addHeirCopy(runner);
        //人才群组
        addUserGroup(runner);
        return runner;
    }

    public Set<String> getDemoCopyStartRelated() {
        return demoCopyStartRelated;
    }
}
