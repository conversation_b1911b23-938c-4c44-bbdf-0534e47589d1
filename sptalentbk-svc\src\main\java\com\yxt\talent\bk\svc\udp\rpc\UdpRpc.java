package com.yxt.talent.bk.svc.udp.rpc;

import com.alibaba.fastjson.JSON;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.coreapi.client.bean.dataauth.CoreExchangeResult;
import com.yxt.coreapi.client.bean.shorturl.ShortUrl4Exchange;
import com.yxt.coreapi.client.bean.shorturl.ShortUrl4ExchangeBase;
import com.yxt.coreapi.client.bean.shorturl.UrlPair;
import com.yxt.coreapi.client.service.CoreApiFacade;
import com.yxt.udpfacade.bean.dept.DeptUserMapBean;
import com.yxt.udpfacade.bean.dept.ManagedDeptBean;
import com.yxt.udpfacade.bean.mq.org.OrgSyncRequest4MqBean;
import com.yxt.udpfacade.bean.mq.org.OrgSyncResponse4MqBean;
import com.yxt.udpfacade.bean.org.OrgBean;
import com.yxt.udpfacade.bean.org.groupcorp.OrgDeptPosIdParamBean;
import com.yxt.udpfacade.bean.position.PositionBean;
import com.yxt.udpfacade.bean.position.SelectPositionBean;
import com.yxt.udpfacade.service.UdpFacade;
import com.yxt.udpfacade.service.UdpGroupCorpFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class UdpRpc {
    private final UdpFacade udpFacade;
    private final UdpGroupCorpFacade udpGroupCorpFacade;
    private final CoreApiFacade coreApiFacade;

    public OrgBean getOrgInfo(String orgId) {
        return udpFacade.getOrgInfo(orgId);
    }

    public boolean checkIsDeptManager(String orgId, String userId) {
        return udpFacade.checkIsDeptManager(orgId, userId);
    }

    public boolean checkIsDirectManager(String orgId, String userId) {
        return udpFacade.checkIsDirectManager(orgId, userId);
    }

    public List<ManagedDeptBean> findManagedDeptUsers(String orgId, String userId) {
        return udpFacade.findManagedDeptUsers(orgId, userId);
    }

    public OrgSyncResponse4MqBean syncOrgDatasRequest4Mq(OrgSyncRequest4MqBean bean) {

        return this.udpFacade.syncOrgDatasRequest4Mq(bean);
    }

    /**
     * 一岗多职级，查询岗位关联职级，单次请求岗位id个数最多100
     */
    public CommonList<PositionBean> listPositionWithGrade(SelectPositionBean bean) {
        return udpFacade.listPositionWithGrade(bean);
    }

    public List<IdName> queryPositionNameByIds(String orgId, List<String> posIds) {
        if (CollectionUtils.isNotEmpty(posIds)) {
            OrgDeptPosIdParamBean param = new OrgDeptPosIdParamBean();
            param.setOrgId(orgId);
            param.setPositionIds(posIds.stream().collect(Collectors.toSet()));
            List<IdName> positions = udpGroupCorpFacade.listOrgDeptPosIdNames(param).getPositions();
            if (positions != null) {
                return positions;
            }
        }
        return Lists.newArrayList();
    }

    public List<IdName> queryDeptNameByIds(String orgId, List<String> deptIds) {
        if (CollectionUtils.isNotEmpty(deptIds)) {
            OrgDeptPosIdParamBean param = new OrgDeptPosIdParamBean();
            param.setOrgId(orgId);
            param.setDeptIds(deptIds.stream().collect(Collectors.toSet()));
            List<IdName> depts = udpGroupCorpFacade.listOrgDeptPosIdNames(param).getDepts();
            if (depts != null) {
                return depts;
            }
        }
        return Lists.newArrayList();
    }

    public List<DeptUserMapBean> getDeptManagerId(String orgId, List<String> deptIds) {
        List<DeptUserMapBean> ret = null;
        if (CollectionUtils.isNotEmpty(deptIds)) {
            ret = udpFacade.getDepartmentManagers(orgId, deptIds, new String[]{"deptId","manager","managerId","managerName"});
        }
        return ret != null ? ret : Lists.newArrayList();
    }

    /**
     * 根据PC端URL及H5端URL生成短地址
     *
     * @param urlPc 要跳转的pc端URL,不带https://，如/#reort/data?type=1;  如果带https:// 或 http://，将不拼接ISV免登中间件，按原pcUrl生成短地址
     * @param urlH5 要跳转的h5Url端URL,不带https://，如/m/#reort/data?type=1;  如果带https:// 或 http://，将不拼接ISV免登中间件，按原h5Url生成短地址
     * @param orgId 机构id
     * @return 短地址
     * <AUTHOR>
     * @date 20220325
     */
    public String buildExchangeResult(String urlPc, String urlH5, String orgId) {
        ShortUrl4Exchange shortUrl4Exchange = new ShortUrl4Exchange();
        shortUrl4Exchange.setOrgId(orgId);
        shortUrl4Exchange.setMode(new ShortUrl4ExchangeBase.Mode(1, 0));
        UrlPair urlPair = new UrlPair();
        urlPair.setUrlH5(urlH5);
        urlPair.setUrlPc(urlPc);
        shortUrl4Exchange.setUrlPair(urlPair);

        CoreExchangeResult coreExchangeResult = coreApiFacade.exchange(shortUrl4Exchange);
        log.info("buildExchangeResult req:{}, ret:{}", JSON.toJSONString(coreExchangeResult),
            JSON.toJSONString(coreExchangeResult));
        String retShortUrl = StringUtils.EMPTY;
        if (ObjectUtils.isNotEmpty(coreExchangeResult) && StringUtils.isNotEmpty(coreExchangeResult.getUrl())) {
            retShortUrl = coreExchangeResult.getUrl();
        }
        return retShortUrl;
    }

    public String heirPrepareJump(String orgId, String posId, String userId) {
        String urlH5 = String.format("/m/#/spgwnl/succession/adjust?posId=%s&userId=%s",
                posId, userId);
        String urlPc = String.format("/sptalentbank/#/adjust?posId=%s&userId=%s",
                posId, userId);
        //当前没有h5的地址,就直接去个首页了
        return buildExchangeResult(urlPc, urlH5, orgId);
    }
}
