package com.yxt.talent.bk.core.tag.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/***
 * <AUTHOR>
 * @since 2022/8/11 9:35
 */
@Data
@Schema(name = "标签信息对象")
public class Tag4Get {

    @Schema(description = "id")
    private String id;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "分类名称")
    private String catalogName;

    @Schema(description = "创建方式：0-静态,1-规则,2-模型")
    private Integer createType;

    @Schema(description = "标签来源(2-固定,0-内置,1-自建)")
    private Integer source;

    @Schema(description = "更新方式")
    private Integer updateType;

    @Schema(description = "标签类型")
    private Integer tagType;

    @Schema(description = "创建人")
    private String createUserName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date createTime;
}
