package com.yxt.talent.bk.core.usergroup.mapper;

import com.yxt.talent.bk.core.usergroup.bean.UserDeptUgroup4Get;
import com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserDeptUgroupMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserDeptUgroup record);

    int insertOrUpdate(UserDeptUgroup record);

    int insertOrUpdateSelective(UserDeptUgroup record);

    int insertSelective(UserDeptUgroup record);

    UserDeptUgroup selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserDeptUgroup record);

    int updateByPrimaryKey(UserDeptUgroup record);

    int updateBatch(List<UserDeptUgroup> list);

    int batchInsert(@Param("list") List<UserDeptUgroup> list);

    UserDeptUgroup selectByOrgIdAndId(@Param("orgId") String orgId, @Param("id") Long id);

    int countByUserIdAndDeptId(
            @Param("orgId") String orgId, @Param("userId") String userId, @Param("deptId") String deptId);

    /**
     * 返回人才池统计
     * @param orgId
     * @param userId
     * @param deptId
     * @return
     */
    List<UserDeptUgroup4Get> selectStatisticByUserIdAndDeptId(
            @Param("orgId") String orgId, @Param("userId") String userId, @Param("deptId") String deptId);

    List<UserDeptUgroup> selectByUserIdAndDeptId(
            @Param("orgId") String orgId, @Param("userId") String userId, @Param("deptId") String deptId);
}
