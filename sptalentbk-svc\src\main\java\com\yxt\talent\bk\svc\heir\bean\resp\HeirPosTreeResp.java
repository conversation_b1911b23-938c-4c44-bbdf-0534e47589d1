package com.yxt.talent.bk.svc.heir.bean.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * HeirPosTreeResp
 *
 * <AUTHOR> geyan
 * @Date 17/8/23 2:10 pm
 */
@Data
public class HeirPosTreeResp {
    @Schema(description = "继任部门或者岗位总数")
    private int posQty;
    @Schema(description = "继任人数")
    private int heirUserQty;
    private List<HeirPosNodeBean> nodeTree;
}
