package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(name = "人才搜索-主页-列表-搜索入参-透视维度标签")
@Data
public class UserSearchBean4AnalyseTag {
    @Schema(description = "透视维度key",example = "sex")
    private String dimensionKey;
    @Schema(description = "透视维度项key",example = "1")
    private String dimensionItemKey;
}
