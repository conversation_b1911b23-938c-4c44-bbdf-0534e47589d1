package com.yxt.talent.bk.core.pool.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * UserPoolInfoBean
 *
 * <AUTHOR> geyan
 * @Date 13/3/24 3:47 pm
 */
@Data
public class UserPoolInfoBean {
    private String poolId;
    private String poolName;
    private String readinessName;
    @Schema(description = "出池事件枚举(0：在池;1:合格出池且任用;2:合格出池;3:不合格出池;4:未完成中途退出;5:离职)")
    private Integer eventPoolOut;
    private Date createTime;
    private Date outTime;
}
