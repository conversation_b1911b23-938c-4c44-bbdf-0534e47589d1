package com.yxt.talent.bk.svc.pool.component;

import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.core.pool.repo.PoolReadinessRepository;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.talent.bk.svc.pool.bean.PoolUser4Update;
import com.yxt.talent.bk.svc.pool.bean.PoolUserPrepare4Log;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * PoolUserPrepareLogProvider
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 1:51 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class PoolUserPrepareLogProvider implements AuditLogDataProvider<PoolUser4Update, PoolUserPrepare4Log> {

    private final UdpLiteUserRepository udpLiteUserRepository;
    private final PoolReadinessRepository poolReadinessRepository;
    private final PoolRepository poolRepository;
    @Override
    public PoolUserPrepare4Log before(PoolUser4Update param, AuditLogBasicBean logBasic) {
        String orgId = logBasic.getOrgId();
        PoolUserPrepare4Log auditLog = new PoolUserPrepare4Log();
        String userNames = udpLiteUserRepository.userNames4Log(orgId,
                param.getPoolUser().getUserIdList());
        auditLog.setUserDesc(userNames);
        if (CollectionUtils.size(param.getPoolUser().getUserIdList()) == 1) {
            //单个人操作时
            auditLog.setUserFullName(userNames);
        }
        return auditLog;
    }

    @Override
    public PoolUserPrepare4Log after(PoolUser4Update param, AuditLogBasicBean logBasic) {
        PoolUserPrepare4Log log = new PoolUserPrepare4Log();
        log.setPrepareDesc(poolReadinessRepository.getNameById(param.getReadinessId()));
        return log;
    }

    @Override
    public Pair<String, String> entityInfo(PoolUser4Update param, PoolUserPrepare4Log beforeObj, PoolUserPrepare4Log afterObj, AuditLogBasicBean logBasic) {
        String poolName = poolRepository.getNameById(param.getPoolUser().getPoolId());
        if (StringUtils.isNotEmpty(beforeObj.getUserFullName())) {
            return Pair.of(IArrayUtils.getFirst(param.getPoolUser().getUserIdList()),
                    String.format("人才池-%s-%s-更新准备度", poolName, beforeObj.getUserFullName()));
        } else {
            return Pair.of(IArrayUtils.getFirst(param.getPoolUser().getUserIdList()),
                    String.format("人才池-%s-更新准备度", poolName));
        }
    }
}
