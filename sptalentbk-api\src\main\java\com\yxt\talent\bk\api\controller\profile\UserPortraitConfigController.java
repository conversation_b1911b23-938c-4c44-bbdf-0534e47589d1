package com.yxt.talent.bk.api.controller.profile;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.svc.profile.UserProfileConfigService;
import com.yxt.talent.bk.svc.profile.bean.PortraitConfig4Get;
import com.yxt.talent.bk.svc.profile.bean.PortraitConfig4Update;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/mgr/portrait/config")
@Tag(name = "人才画像设置")
public class UserPortraitConfigController extends BaseController {
    private final UserProfileConfigService userProfileConfigService;

    @Operation(summary = "查询配置信息")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/basic", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public PortraitConfig4Get getConfigBasic() {
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return userProfileConfigService.getPortraitConfig(orgId, userCache.getUserId());
    }

    @Operation(summary = "编辑配置信息")
    @ResponseStatus(HttpStatus.OK)
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.USER_PORTRAIT_CONFIG, paramExp = "#update")
    @PostMapping(value = "/update", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public void updateConfigBasic(@RequestBody PortraitConfig4Update update) {
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        update.setOrgId(orgId);
        userProfileConfigService.updatePortraitConfig(update, userCache.getUserId());
    }


    @Operation(summary = "人才画像权限初始化")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/init", produces = Constants.MEDIATYPE)
    @Auth(type = AuthType.CUSTOM)
    public void init(@RequestBody Set<String> orgIds) {
        userProfileConfigService.init(orgIds);
    }

}
