package com.yxt.talent.bk.core.udp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.bk.common.bean.udp.DeptConditionBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangUserBean;
import com.yxt.talent.bk.core.udp.bean.QueryUserBean;
import com.yxt.talent.bk.core.udp.bean.UdpPositionBean;
import com.yxt.talent.bk.core.udp.bean.UdpQueryUserBean;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.core.udp.bean.UdpUserLocaleBean;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.usergroup.bean.Scheme4Search;
import com.yxt.talent.bk.core.usergroup.bean.SchemeBean;
import com.yxt.talent.bk.core.usergroup.bean.SchemeUser4Export;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
public interface UdpLiteUserMapper extends BaseMapper<UdpLiteUser> {

    IPage<SchemeBean> findUserPage(IPage<SchemeBean> page, @Param("orgId") String orgId,@Param("param") Scheme4Search param, @Param("deptIds") List<String> deptIds);

    List<UdpLangUserBean> findNamesById(@Param("orgId") String orgId, @Param("userIds") List<String> userIds);

    List<String> existUserIds(@Param("orgId") String orgId,
                              @Param("userIds") Collection<String> userIds);

    List<UdpUserBriefBean> queryByUserIds(@Param("orgId") String orgId,
                                          @Param("userIds") Collection<String> userIds,
                                          @Param("queryFields") List<String> queryFields);

    List<UdpPositionBean> queryPositionUserQty(@Param("orgId") String orgId,
                                               @Param("positionIds") Collection<String> positionIds);

    List<UdpUserBriefBean> listByIds(@Param("orgId") String orgId, @Param("userIds") Collection<String> userIds);

    List<UdpUserLocaleBean> queryLocaleByIds(@Param("orgId") String orgId, @Param("userIds") Collection<String> userIds);

    List<SchemeUser4Export> findUser4Export(@Param("orgId") String orgId,
                                            @Param("param") Scheme4Search param,
                                            @Param("deptIdCon") DeptConditionBean deptIdCon);

    UdpLiteUser findByOrgIdAndUserId(@Param("orgId") String orgId, @Param("userId") String userId);

    List<String> listIdByPosition(@Param("orgId") String orgId, @Param("positionId") String posId, @Param("userIds") List<String> userIds);

    IPage<UdpUserBriefBean> briefPage(Page page,
                                      @Param("orgId") String orgId,
                                      @Param("q")QueryUserBean queryUser);


    IPage<SchemeBean> findUserSchemePage(IPage<SchemeBean> page, @Param("orgId") String orgId,@Param("param") Scheme4Search param,
            @Param("deptIdCon") DeptConditionBean deptIdCon);

    Set<String> listUserIds4Group(@Param("orgId")String orgId, @Param("param") UdpQueryUserBean param);

    IPage<UdpUserBriefBean> listUser4Group(Page page, @Param("orgId")String orgId, @Param("param") UdpQueryUserBean param);
}
