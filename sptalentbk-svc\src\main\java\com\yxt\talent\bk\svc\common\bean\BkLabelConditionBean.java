package com.yxt.talent.bk.svc.common.bean;

import com.yxt.spmodel.facade.bean.rule.LabelConditionInfo;
import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * BkLabelConditionBean
 *
 * <AUTHOR> harleyge
 * @Date 25/7/24 11:08 am
 */
@Data
public class BkLabelConditionBean {
    @Schema(name = "条件列表 不超过10条 复合标签专属字段", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<BkLabelConditionInfo> conditions;

    @Schema(name = "条件间逻辑 1：与 2：或 复合标签专属字段", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer logic;

    @Schema(name = "状态 0：正常 1：异常")
    private Integer state = 0;

    public void copyOf(LabelConditionJsonBean labelCondition) {
        this.logic = labelCondition.getLogic();
        this.state = labelCondition.getState();
        if (labelCondition.getConditions() != null) {
            conditions = new ArrayList<>(labelCondition.getConditions().size());
            for (LabelConditionInfo condition : labelCondition.getConditions()) {
                BkLabelConditionInfo bkCondition = new BkLabelConditionInfo();
                bkCondition.copyOf(condition);
                conditions.add(bkCondition);
            }
        }
    }

    public static BkLabelConditionBean createBy(LabelConditionJsonBean labelCondition) {
        if (labelCondition == null) {
            return null;
        }
        BkLabelConditionBean ret = new BkLabelConditionBean();
        ret.copyOf(labelCondition);
        return ret;
    }
}
