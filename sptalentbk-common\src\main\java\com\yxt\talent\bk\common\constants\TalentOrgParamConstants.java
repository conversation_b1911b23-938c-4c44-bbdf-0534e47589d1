package com.yxt.talent.bk.common.constants;

/**
 * 此类用于定义人才发展机构参
 * 目前人才发展为了支持1.0的导航和权限相关功能，使用1.0的机构参的形式来判断权限
 * 2.0的相关功能使用去版本要素的形式，参考factorService
 *
 * <AUTHOR>
 */
public class TalentOrgParamConstants {
    /**
     * 机构参：机构开通人才发展，有效期
     */
    public static final String ORG_PARAM_KEY_TALENT = "TalentModel";

    /**
     * 1.0机构参数-测训参数，废弃，不再使用
     * 请使用TalentTraining
     */
    public static final String ONLY_TALENT_TRAINING = "OnlyTalentTraining";

    /**
     * 机构参：机构开通测训项目，有效期
     */
    public static final String ORG_PARAM_KEY_TRAINING = "TalentTraining";

    /**
     * 机构参：盘点模块是否开通，有效期
     */
    public static final String ORG_PARAM_KEY_TALENT_RV = "gwnl_talentrv";


    /**
     * Hide Constructor.
     */
    private TalentOrgParamConstants() {
    }
}
