package com.yxt.talent.bk.core;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <p>
 * 分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@NoArgsConstructor
@TableName("bk_catalog")
@EqualsAndHashCode(callSuper = true)
public class TagCatalog extends CreatorEntity {

    @TableField("id")
    @Schema(description = "分类表主键")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = BkDemoConstants.BK_CATALOG_ID)
    private String id;

    @TableField("org_id")
    @Schema(description = "机构id")
    private String orgId;

    @TableField("parent_id")
    @Schema(description = "父分类id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_CATALOG_ID)
    private String parentId;

    @TableField("bk_catalog_name")
    @Schema(description = "分类名称")
    private String catalogName;

    /**
     * 内置是给内置标签用的，不做展示
     */
    @TableField("catalog_type")
    @Schema(description = "类型(0-默认，1-内置，2-自建)")
    private Integer catalogType;

    @TableField("catalog_source")
    @Schema(description = "类型(0-标签，1-人才池)")
    private Integer catalogSource;

    @TableField("order_index")
    @Schema(description = "排序")
    private Integer orderIndex;

    @TableField("show_type")
    @Schema(description = "分类是否可见（0：不可见，1：可见）")
    private Integer showType;

    @TableField("create_user_id")
    @Schema(description = "创建人id")
    private String createUserId;

    @TableField("create_time")
    @Schema(description = "创建时间")
    private Date createTime;

    @TableField("update_user_id")
    @Schema(description = "修改人id")
    private String updateUserId;

    @TableField("update_time")
    @Schema(description = "修改时间")
    private Date updateTime;

}
