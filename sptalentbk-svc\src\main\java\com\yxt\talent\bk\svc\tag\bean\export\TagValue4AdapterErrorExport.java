package com.yxt.talent.bk.svc.tag.bean.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * : 标签值导入人员 数据结构
 * <AUTHOR>
 * @since 2022/8/10 16:27
 */
@Data
@NoArgsConstructor
public class TagValue4AdapterErrorExport {

    @Schema(description = "0:普通标签，1：分层标签", example = "0")
    private Integer tagType;

    @Schema(description = "表格数据")
    private List<TagValue4ErrorExport4Mul> data;

}
