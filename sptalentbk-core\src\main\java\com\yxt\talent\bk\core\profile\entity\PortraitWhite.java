package com.yxt.talent.bk.core.profile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 个人画像白名单表(BkPortraitWhite)实体类
 *
 * <AUTHOR>
 * @since 2024-06-12 15:25:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "bk_portrait_white")
@EqualsAndHashCode(callSuper = true)
public class PortraitWhite extends CreatorEntity {
    /**
     * 主键
     */
    @TableField(value = "id")
    private String id;
    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String userId;
    /**
     * 白名单开启状态（0-开启，1-关闭）
     */
    @TableField(value = "white_enable")
    private Integer whiteEnable;

}
