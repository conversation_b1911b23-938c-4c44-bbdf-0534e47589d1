package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Schema(name = "继任者列表响应")
public class PosUserListResp implements Serializable {

    private static final long serialVersionUID = -4267904673382906323L;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "id")
    private Long id;

    @Schema(description = "账号")
    private String username;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "主岗位名称")
    private String positionName;

    @Schema(description = "主部门全路径名称，例：研发中心->园区 ")
    private String deptName;

    @Schema(description = "用户状态：用于标识当前用户的状态(0-禁用,1-启用)")
    private Integer status;

    @Schema(description = "用户是否已删除：(0-未删除,1-已删除)")
    private Integer userDeleted;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "继任id")
    private String posId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "准备度规则等级id")
    private Long prepareLevelId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "计算的准备度id")
    private Long calcLevelId;

    @Schema(description = "0:进行中，1:已退出")
    private Integer heirStatus;

    @Schema(description = "退出原因")
    private String quitReason;

    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    @Schema(description = "退出时间")
    private Date exitTime;
}
