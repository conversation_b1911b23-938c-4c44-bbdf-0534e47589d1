package com.yxt.talent.bk.svc.search.bean;

import com.yxt.talent.bk.common.constants.TalentBkConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Document;

import java.io.Serializable;

@Data
@Schema(name = "人才搜索-主页-列表")
@Document(indexName = "#{@bkConfigService.indexName}", type = TalentBkConstants.ES_TYPE)
public class UserSearchListBean implements Serializable {
    static final long serialVersionUID = 42L;
    @Schema(description="用户id")
    private String userId;
    @Schema(description="账号")
    private String username;
    @Schema(description="中文姓名")
    private String cnname;
    @Schema(description="工号")
    private String userNo;
    @Schema(description = "用户状态（1-启用，0-禁用）")
    private Integer userStatus;
    @Schema(description="部门全路径名称")
    private String departmentName;
    @Schema(description="岗位名称")
    private String positionName;
    @Schema(description="职级名称")
    private String gradeName;
    @Schema(description="司龄")
    private String entryDate;
    @Schema(description="绩效等级（高、中、低）")
    private String performanceLevel;
    @Schema(description="能力等级（高、中、低）")
    private String abilityLevel;
    @Schema(description="潜力等级（高、中、低）")
    private String potentialLevel;
    @Schema(description = "是否存在测评 0:不存在 1:存在")
    private int existEval = 0;
}
