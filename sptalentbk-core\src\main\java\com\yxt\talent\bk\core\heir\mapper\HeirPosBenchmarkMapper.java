package com.yxt.talent.bk.core.heir.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserBean;
import com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosUserBean;
import com.yxt.talent.bk.core.heir.entity.HeirPosBenchmarkEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * HeirPosBenchmarkMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 2:30 pm
 */
@Mapper
public interface HeirPosBenchmarkMapper extends BkBaseMapper<HeirPosBenchmarkEntity> {

    @Select("select user_id from bk_heir_pos_benchmark where pos_id = #{posId} and deleted = 0")
    List<String> posUserIds(@Param("posId")String posId);

    List<HeirPosUserBean> posUser(@Param("posId")String posId,
                                  @Param("userIds")List<String> userIds,
                                  @Param("deleted")Integer deleted);

    int updateDeleted(@Param("list")List<HeirPosUserBean> list);

    int removeByIds(@Param("ids")List<Long> ids);
    int removeByUserIds(@Param("posId")String posId,
                        @Param("userIds")List<String> userIds);

    List<HeirPosUserBean> posTopUsers(@Param("posIds")List<String> posIds);

    IPage<DwdHeirPosUserBean> listPage4Open(Page page,
                                            @Param("orgId") String orgId);
}
