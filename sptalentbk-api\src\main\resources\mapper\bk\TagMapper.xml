<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.tag.mapper.TagMapper">

    <select id="findTagMsg" resultType="com.yxt.talent.bk.core.tag.bean.TagSimpleBean">
        select
        a.tag_key as id,
        a.value_choose_model as valueChooseModel,
        b.id as valueId,
        b.value_name as valueName,
        a.tag_name as tagName,
        a.tag_type as tagType
        from bk_tag a
        left join bk_tag_value b
        on a.id = b.tag_id
        and a.org_id = b.org_id
        where a.org_id = #{orgId}
        and a.tag_key in
        <foreach collection="tagIds" item="tagId" open="(" close=")" separator=",">
            #{tagId}
        </foreach>
        and a.tag_enable = 1
        order by a.id, b.order_index
    </select>

    <select id="fingSelfTag" resultType="com.yxt.talent.bk.core.tag.bean.TagSimpleBean">
        select
        a.tag_key as id,
        a.value_choose_model as valueChooseModel,
        b.id as valueId,
        b.value_name as valueName,
        a.tag_name as tagName,
        a.tag_type as tagType
        from bk_tag a
        left join bk_tag_value b
        on a.id = b.tag_id
        and a.org_id = b.org_id
        where a.org_id = #{orgId}
        and a.tag_key in
        <foreach collection="tagIds" item="tagId" open="(" close=")" separator=",">
            #{tagId}
        </foreach>
        and a.tag_enable = 1
        and a.tag_source = 1
        order by a.id, b.order_index
    </select>

    <select id="findTag4Search" resultType="com.yxt.talent.bk.core.tag.bean.TagSimpleBean">
        select
        t.id as id,
        t.value_choose_model as valueChooseModel,
        b.id as valueId,
        b.value_name as valueName,
        t.tag_name as tagName,
        t.tag_type as tagType,
        t.create_time as createTime
        from bk_tag t
        left join bk_tag_value b
        on t.id = b.tag_id
        and t.org_id = b.org_id
        where t.org_id = #{orgId}
        and t.create_type &lt;&gt; 1
        <if test="catalogId != null and catalogId != ''">
            AND t.catalog_id = #{catalogId}
        </if>
        <if test="tagIds != null and tagIds.size() > 0">
            AND t.id IN
            <foreach collection="tagIds" item="tagId" open="(" close=")" separator=",">#{tagId}</foreach>
        </if>
        <choose>
            <when test="tagNames != null and tagNames.size() > 0">
                AND t.tag_name in
                <foreach collection="tagNames" item="tagName" open="(" close=")" separator=",">#{tagName}</foreach>
            </when>
            <otherwise >
                <if test="tagValueNames != null and tagValueNames.size() > 0">
                    OR b.value_name in
                    <foreach collection="tagValueNames" item="tagValueName" open="(" close=")" separator=",">#{tagValueName}</foreach>
                </if>
            </otherwise>
        </choose>
        <include refid="Find_Tag_4_Search_Common"/>
        <include refid="Tag_List_Common"/>
        order by t.create_time desc, b.order_index
    </select>
    <sql id="Find_Tag_4_Search_Common">
        and t.tag_enable = 1
    </sql>

    <select id="listTag" resultType="com.yxt.talent.bk.core.tag.bean.TagInfoBean">
        select id, tag_key tagKey, tag_type tagType, value_choose_model valueChooseModel, tag_source tagSource
        from
        bk_tag
        where
        org_id = #{orgId}
        and id in
        <choose>
            <when test="tagIds != null and tagIds.size() != 0">
                <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </when>
            <otherwise>
                ('')
            </otherwise>
        </choose>
    </select>

    <select id="listTagValue" resultType="com.yxt.talent.bk.core.tag.bean.TagInfoBean">
        select b.id, b.tag_key tagKey, b.tag_type tagType, b.value_choose_model valueChooseModel,
        b.tag_source tagSource, a.id tagValueId
        from
        bk_tag_value a
        left join
        bk_tag b
        on a.org_id = b.org_id and a.tag_id = b.id
        where a.org_id = #{orgId}
        and a.tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>

    <select id="findTag4Page" resultType="com.yxt.talent.bk.core.tag.bean.Tag4Page">
        select
        t.id,
        t.tag_key as tagKey,
        t.tag_name as tagName,
        t.description,
        t.tag_type as tagType,
        t.create_type as createType,
        t.tag_enable as tagEnable,
        t.show_type as showType,
        t.tag_source as tagSource,
        t.catalog_id as catalogId,
        cat.bk_catalog_name as catalogName,
        (select count(*) as cnt from bk_user_tag b where b.tag_id = t.id and b.deleted = 0) as tagUserCount
        from bk_tag t
        left join bk_catalog cat
        on t.org_id = cat.org_id and t.catalog_id = cat.id
        where t.org_id = #{orgId}
        <if test="tagSearchBean.keyWord != null and tagSearchBean.keyWord != ''">
            AND t.tag_name like CONCAT('%', #{tagSearchBean.keyWord} ,'%') ESCAPE '\\'
        </if>
        <if test="tagSearchBean.catalogId != null and tagSearchBean.catalogId != ''">
            AND t.catalog_id = #{tagSearchBean.catalogId}
        </if>
        <if test="tagSearchBean.tagEnable != -1">
            and t.tag_enable = #{tagSearchBean.tagEnable}
        </if>
        <if test="tagSearchBean.sourceType != null and tagSearchBean.sourceType.size() !=0 ">
            and t.tag_source in
            <foreach collection="tagSearchBean.sourceType" item="sourceType" open="(" separator="," close=")">
                #{sourceType}
            </foreach>
        </if>
        <if test="!tagSearchBean.rvOrNot">
            and cat.bk_catalog_name &lt;&gt; '人才盘点'
        </if>
        <include refid="Tag_List_Common"/>
        order by t.create_time desc
    </select>

    <sql id="Tag_List_Common">
        AND t.show_type = 1
    </sql>

    <select id="tagNameCheck" resultType="int">
        SELECT
        IFNULL(count(1),0)
        FROM
        bk_tag
        where org_id = #{orgId} and tag_name = #{tagName}
        <if test="id !=null and id !=''">
            and id !=#{id}
        </if>
    </select>

    <select id="findfindTagValueList" resultType="com.yxt.talent.bk.core.tag.bean.TagValueSimpleBean">
        select
        bt.tag_key as tagKey,
        bt.tag_name as tagName,
        btv.id as id,
        btv.value_name as valueName,
        btv.order_index as orderIndex
        from bk_tag bt
        left join bk_tag_value btv
        on bt.org_id = btv.org_id
        and bt.id = btv.tag_id
        where bt.org_id = #{orgId}
        and bt.tag_key = #{tagKey}
        and bt.tag_enable = 1
        order by btv.order_index
    </select>

    <select id="findCatalogIdByShowTypeInCatalogIds" resultType="java.lang.String">
        SELECT
            catalog_id
        FROM
            bk_tag
        WHERE
            org_id = #{orgId}
            AND show_type = 1
            <if test="catalogIds != null and catalogIds.size() > 0">
                AND catalog_id IN
                <foreach collection="catalogIds" item="catalogId" open="(" separator="," close=")">
                    #{catalogId}
                </foreach>
            </if>
    </select>

    <select id="findTagNameLike" resultType="java.lang.String">
        SELECT
            t.tag_name
        FROM
            bk_tag t
        WHERE
            t.org_id = #{orgId}
        <include refid="Find_Tag_4_Search_Common"/>
        <include refid="Tag_List_Common"/>
        <if test="keyword != null and keyword != ''">
            AND t.tag_name like CONCAT('%', #{keyword} ,'%') ESCAPE '\\'
        </if>

    </select>

    <select id="findTagNameByIds" resultType="java.lang.String">
        SELECT
            t.tag_name
        FROM
            bk_tag t
        WHERE
            t.org_id = #{orgId}
        <include refid="Find_Tag_4_Search_Common"/>
        <include refid="Tag_List_Common"/>
        <if test="ids != null and ids.size() > 0">
            AND t.id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

    </select>

    <select id="findSelfTagByOrgId" resultType="com.yxt.talent.bk.core.tag.bean.TagNameBean">
        select
            tag_key as tagKey,
            tag_name as tagName
        from
            bk_tag bt
        where
            org_id = #{orgId}
            and tag_source in (0, 2)
            and tag_enable = 1
    </select>


</mapper>
