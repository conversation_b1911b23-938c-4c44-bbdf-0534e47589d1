package com.yxt.talent.bk.svc.catalog;

import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.talent.bk.common.constants.CatalogConstants;
import com.yxt.talent.bk.common.constants.PoolConstants;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.core.common.repo.CatalogRepository;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.svc.catalog.bean.Catalog4List;
import com.yxt.talent.bk.svc.catalog.bean.Catalog4Save;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;


@Service
@RequiredArgsConstructor
@Slf4j
public class PoolCatalogService {
    private final AuthService authService;
    private final ILock lockService;
    private final CatalogRepository catalogRepository;
    private final PoolRepository poolRepository;

    private static final int MAX_LEASE_TIME = 100;

    /**
     * 查人才池分类
     * @param orgId
     * @return
     */
    public List<Catalog4List> findByOrgId(String orgId,Integer type) {
        List<TagCatalog> list = catalogRepository.findBySourceAndType(orgId, CatalogConstants.CatalogSource.POOL.getValue(),type);
        if(CollectionUtils.isNotEmpty(list)){
            return BeanCopierUtil.convertList(list,TagCatalog.class, Catalog4List.class);
        }

        // 如果为空， 需要初始化默认分类
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_CATALOG_POOL_DEFAULT_SAVE, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)){
            try {
                UserCacheBasic userCacheBasic = authService.getUserCacheBasic(ApiUtil.getRequestByContext());
                TagCatalog bean = new TagCatalog();
                bean.setOrgId(orgId);
                bean.setCatalogName(CatalogConstants.DEFAULT_POOL_CATALOG_NAME);
                bean.setCatalogType(CatalogConstants.CatalogType.DEFAULT.getValue());
                bean.setCatalogSource(CatalogConstants.CatalogSource.POOL.getValue());
                EntityUtil.setCreateInfo(userCacheBasic.getUserId(), bean);
                catalogRepository.save(bean);
                list.add(bean);
            } catch (Exception e) {
                log.error("Exception", e);
                throw e;
            }  finally {
                lockService.unLock(lockKey);
            }
        }else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }

        return BeanCopierUtil.convertList(list,TagCatalog.class, Catalog4List.class);
    }

    /**
     * 创建
     * @param orgId
     * @param userId
     * @param catalog
     */
    public Catalog4Save createPoolCatalog(String orgId,String userId,Catalog4Save catalog){

        Catalog4Save create = new Catalog4Save();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_CATALOG_POOL_DEFAULT_SAVE, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)){
            try {

                List<TagCatalog> listByName = catalogRepository.listByName(orgId, catalog.getCatalogName(), CatalogConstants.CatalogSource.POOL.getValue());
                if (CollectionUtils.isNotEmpty(listByName)) {
                    throw new ApiException(BkApiErrorKeys.ERROR_KEY_CATALOG_NAME_EXIST);
                }

                TagCatalog bean = new TagCatalog();
                bean.setOrgId(orgId);
                bean.setCatalogName(catalog.getCatalogName());
                bean.setCatalogType(CatalogConstants.CatalogType.CUSTOM.getValue());
                bean.setCatalogSource(CatalogConstants.CatalogSource.POOL.getValue());
                EntityUtil.setCreateInfo(userId, bean);
                catalogRepository.save(bean);
                BeanCopierUtil.copy(bean,create);

            } catch (Exception e) {
                log.error("Exception", e);
                throw e;
            }  finally {
                lockService.unLock(lockKey);
            }
        }else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }

        return create;
    }

    /**
     * 编辑
     * @param orgId
     * @param userId
     * @param catalog
     */
    public void updatePoolCatalog(String orgId,String userId,Catalog4Save catalog){
        if(catalog == null || StringUtils.isBlank(catalog.getId())){
            throw new ApiException("apis.talentbk.catalog.catalogId.notBlank");
        }
        TagCatalog bean = catalogRepository.getOne(orgId, catalog.getId());
        if(bean == null){
            throw new ApiException("apis.talentbk.catalog.catalogId.notExist");
        }
        if(bean.getCatalogType() == CatalogConstants.CatalogType.DEFAULT.getValue()){
            throw new ApiException("apis.talentbk.catalog.cannot.edit");
        }
        //重名校验
        List<TagCatalog> listByName = catalogRepository.listByName(orgId, catalog.getCatalogName(), CatalogConstants.CatalogSource.POOL.getValue());
        if (CollectionUtils.isNotEmpty(listByName)
                && !listByName.get(0).getId().equals(catalog.getId())) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_CATALOG_NAME_EXIST);
        }

        bean.setCatalogName(catalog.getCatalogName());
        EntityUtil.setUpdatedInfo(userId, bean);
        catalogRepository.saveOrUpdate(bean);
    }


    /**
     * 删除
     * @param orgId
     * @param id
     */
    public void deletePoolCatalog(String orgId,String id){
        TagCatalog bean = catalogRepository.getOne(orgId, id);
        if(bean == null){
            throw new ApiException("apis.talentbk.catalog.catalogId.notExist");
        }
        if(!orgId.equalsIgnoreCase(bean.getOrgId())){
            throw new ApiException("apis.talentbk.catalog.orgId.notExist");
        }
        if(bean.getCatalogType() == CatalogConstants.CatalogType.DEFAULT.getValue()){
            throw new ApiException("apis.talentbk.catalog.cannot.edit");
        }

        // 判断是否被人才池引用
        long count = poolRepository.countByCatalogId(orgId, id);
        if(count > 0){
            throw new ApiException(PoolConstants.VERIFY_CATALOG_DELETE_REFERENCE);
        }

        catalogRepository.removeById(orgId,id);
    }
}
