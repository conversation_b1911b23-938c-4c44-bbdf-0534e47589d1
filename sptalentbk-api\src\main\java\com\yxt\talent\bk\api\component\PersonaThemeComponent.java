package com.yxt.talent.bk.api.component;

import com.alibaba.fastjson.JSON;
import com.yxt.common.service.ILock;
import com.yxt.common.util.*;
import com.yxt.coreapi.client.bean.sale.OrgVerifyFactorBean;
import com.yxt.coreapi.client.service.CoreApiFacade;
import com.yxt.talent.bk.common.constants.*;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.core.common.repo.CatalogRepository;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeDimensionEntity;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeDimensionTagMapEntity;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeEntity;
import com.yxt.talent.bk.core.persona.repo.PersonaThemeDimensionRepository;
import com.yxt.talent.bk.core.persona.repo.PersonaThemeDimensionTagMapRepository;
import com.yxt.talent.bk.core.persona.repo.PersonaThemeRepository;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.TagValueRepository;
import com.yxt.talent.bk.svc.catalog.CatalogService;
import com.yxt.talent.bk.svc.persona.bean.*;
import com.yxt.talent.bk.svc.tag.TagValueService;
import com.yxt.talent.bk.svc.tag.UserTagService;
import com.yxt.talent.bk.svc.tag.bean.UserAllTagInfoBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class PersonaThemeComponent {
    private final PersonaThemeRepository personaThemeRepository;
    private final PersonaThemeDimensionRepository dimensionRepository;
    private final PersonaThemeDimensionTagMapRepository dimensionTagMapRepository;
    private final TagRepository tagRepository;
    private final CatalogService catalogService;
    private final CatalogRepository catalogRepository;
    private final TagValueService tagValueService;
    private final TagValueRepository tagValueRepository;
    private final ILock lockService;
    private final UserTagService userTagService;
    private final CoreApiFacade coreApiFacade;

    public List<Theme4List> getThemeList(String orgId, int sourceType) {
        List<Theme4List> theme4Lists = new ArrayList<>();
        //查询当前机构所有的主题
        List<PersonaThemeEntity> themeList = personaThemeRepository.queryTheme(orgId, -1);
        if (CollectionUtils.isEmpty(themeList)) {
            return theme4Lists;
        }
        List<String> themeIds = StreamUtil.mapList(themeList, PersonaThemeEntity::getId);
        //主题下关联的维度数据
        List<PersonaThemeDimensionEntity> dimensionList = dimensionRepository
                .queryThemeDimensionByThemeIds(orgId, themeIds, sourceType);
        //TODO 过滤教育经历udp无法提供这期隐藏
        Map<String, List<PersonaThemeDimensionEntity>> themeDimensionMap = dimensionList.stream()
                .filter(dimension -> !"教育经历".equals(dimension.getDimensionName()))
                .collect(Collectors.groupingBy(PersonaThemeDimensionEntity::getThemeId));
        //主题下维度关联标签数据
        List<PersonaThemeDimensionTagMapEntity> dimensionTagList = dimensionTagMapRepository
                .queryThemesDimensionTag(orgId, themeIds);
        List<String> tagIds = StreamUtil.mapList(dimensionTagList, PersonaThemeDimensionTagMapEntity::getTagId);
        //所有标签数据
        List<TagEntity> tagList = tagRepository.queryTagByIds(orgId, tagIds);
        tagList = tagList.stream()
                .filter(tag -> !UserSearchConstants.ES_WORK_DATE.equals(tag.getTagKey())).collect(Collectors.toList());
        Map<String, TagEntity> tagMap = StreamUtil.list2map(tagList, TagEntity::getId);
        Map<String, List<PersonaThemeDimensionTagMapEntity>> dimensionTagMap = dimensionTagList.stream()
                .collect(Collectors.groupingBy(PersonaThemeDimensionTagMapEntity::getDimensionId));
        themeList.forEach(theme -> {
            Theme4List theme4List = new Theme4List();
            BeanCopierUtil.copy(theme, theme4List);
            theme4List.setThemeId(theme.getId());
            List<PersonaThemeDimensionEntity> dimensions = themeDimensionMap.get(theme.getId());
            List<Dimension4List> dimension4ListList = buildDimensionInfo(dimensions, tagMap, dimensionTagMap);
            theme4List.setDimension(dimension4ListList);
            theme4Lists.add(theme4List);
        });
        return theme4Lists;
    }


    /**
     * 构建维度信息
     * @param dimensions 维度列表数据
     * @param tagMap 标签<id 详情> map信息
     * @param dimensionTagMap 维度标签关系信息
     * @return
     */
    private List<Dimension4List> buildDimensionInfo(List<PersonaThemeDimensionEntity> dimensions,
            Map<String, TagEntity> tagMap, Map<String, List<PersonaThemeDimensionTagMapEntity>> dimensionTagMap) {
        List<Dimension4List> dimension4ListList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dimensions)) {
            dimensions.forEach(dimension -> {
                Dimension4List dimension4List = new Dimension4List();
                BeanCopierUtil.copy(dimension, dimension4List);
                dimension4List.setDimensionId(dimension.getId());
                List<PersonaThemeDimensionTagMapEntity> dimensionTags = dimensionTagMap.get(dimension.getId());
                List<DimensionTagMap4List> tagMap4Lists = buildTagInfo(dimensionTags, tagMap, dimension);
                dimension4List.setTagMap(tagMap4Lists);
                dimension4ListList.add(dimension4List);
            });
        }
        return dimension4ListList;
    }

    /**
     * 构建标签信息
     * @param dimensionTags  维度标签关系信息
     * @param tagMap 标签<id 详情> map信息
     * @param dimension 维度数据
     * @return
     */
    private List<DimensionTagMap4List> buildTagInfo(List<PersonaThemeDimensionTagMapEntity> dimensionTags,
            Map<String, TagEntity> tagMap, PersonaThemeDimensionEntity dimension) {
        List<DimensionTagMap4List> dimensionTagMap4ListList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dimensionTags)) {
            dimensionTags.forEach(tag -> {
                TagEntity tagInfo = tagMap.get(tag.getTagId());
                if (tagInfo != null) {
                    DimensionTagMap4List tagMap4List = new DimensionTagMap4List();
                    tagMap4List.setDimensionTagMapId(tag.getId());
                    tagMap4List.setThemeId(dimension.getThemeId());
                    tagMap4List.setDimensionId(dimension.getId());
                    tagMap4List.setTagId(tagInfo.getId());
                    tagMap4List.setTagName(tagInfo.getTagName());
                    tagMap4List.setTagSource(tagInfo.getSource());
                    tagMap4List.setDescription(tagInfo.getDescription());
                    dimensionTagMap4ListList.add(tagMap4List);
                }
            });
        }
        return dimensionTagMap4ListList;
    }

    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void initThemeDimensionTag(String orgId, String userId) {
        String redisLockKey = String.format(TalentBkRedisKeys.TALENTBK_THEME_DIMENSION_TAG_INIT_LOCK_KEY, orgId);
        if (lockService.tryLock(redisLockKey, TalentBkRedisKeys.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                log.info("初始化主题，维度，维度标签 orgId={}", orgId);
                List<PersonaThemeEntity> orgThemeList = personaThemeRepository.queryTheme(orgId, -1);
                if (CollectionUtils.isNotEmpty(orgThemeList)) {
                    return;
                }
                //查询模板的主题
                List<PersonaThemeEntity> themeList = personaThemeRepository.queryTheme("", -1);
                if (CollectionUtils.isEmpty(themeList)) {
                    return;
                }
                //查询 机构下的职级
                Map<String, String> tagIdMap = initTag(orgId, userId);
                Map<String, String> themeRelationMap = new HashMap<>();
                List<PersonaThemeEntity> copyThemeList = new ArrayList<>();
                themeList.forEach(theme -> {
                    String newThemeId = ApiUtil.getUuid();
                    themeRelationMap.put(theme.getId(), newThemeId);
                    PersonaThemeEntity personaTheme = new PersonaThemeEntity();
                    BeanCopierUtil.copy(theme, personaTheme);
                    personaTheme.setId(newThemeId);
                    personaTheme.setOrgId(orgId);
                    EntityUtil.setCreateInfo(userId, personaTheme);
                    copyThemeList.add(personaTheme);
                });
                List<String> themeIds = StreamUtil.mapList(themeList, PersonaThemeEntity::getId);

                List<PersonaThemeDimensionEntity> copyDimensionList = new ArrayList<>();
                Map<String, String> dimensionRelationMap = new HashMap<>();
                List<PersonaThemeDimensionEntity> dimensionList = dimensionRepository
                        .queryThemeDimensionByThemeIds("", themeIds, -1);
                if (CollectionUtils.isNotEmpty(dimensionList)) {
                    dimensionList.forEach(dimension -> {
                        String newDimensionId = ApiUtil.getUuid();
                        dimensionRelationMap.put(dimension.getId(), newDimensionId);
                        PersonaThemeDimensionEntity personaThemeDimension = new PersonaThemeDimensionEntity();
                        BeanCopierUtil.copy(dimension, personaThemeDimension);
                        personaThemeDimension.setId(newDimensionId);
                        personaThemeDimension.setOrgId(orgId);
                        personaThemeDimension.setThemeId(themeRelationMap.get(dimension.getThemeId()));
                        EntityUtil.setCreateInfo(userId, personaThemeDimension);
                        personaThemeDimension.setCreateTime(dimension.getCreateTime());
                        copyDimensionList.add(personaThemeDimension);
                    });
                }
                //初始化标签

                List<PersonaThemeDimensionTagMapEntity> copyThemeDimensionTag = new ArrayList<>();
                List<PersonaThemeDimensionTagMapEntity> dimensionTagMapList = dimensionTagMapRepository
                        .queryThemesDimensionTag("", themeIds);
                if (CollectionUtils.isNotEmpty(dimensionTagMapList)) {
                    dimensionTagMapList.forEach(tag -> {
                        PersonaThemeDimensionTagMapEntity dimensionTagMap = new PersonaThemeDimensionTagMapEntity();
                        BeanCopierUtil.copy(tag, dimensionTagMap);
                        dimensionTagMap.setId(ApiUtil.getUuid());
                        dimensionTagMap.setOrgId(orgId);
                        String tagId = tagIdMap.get(tag.getTagId());
                        dimensionTagMap.setTagId(tagId);
                        dimensionTagMap.setThemeId(themeRelationMap.get(tag.getThemeId()));
                        dimensionTagMap.setDimensionId(dimensionRelationMap.get(tag.getDimensionId()));
                        EntityUtil.setCreateInfo(userId, dimensionTagMap);
                        dimensionTagMap.setCreateTime(tag.getCreateTime());
                        copyThemeDimensionTag.add(dimensionTagMap);
                    });
                }
                if (CollectionUtils.isNotEmpty(copyThemeList)) {
                    personaThemeRepository.saveBatch(copyThemeList);
                }
                if (CollectionUtils.isNotEmpty(copyDimensionList)) {
                    dimensionRepository.saveBatch(copyDimensionList);
                }
                if (CollectionUtils.isNotEmpty(copyThemeDimensionTag)) {
                    dimensionTagMapRepository.saveBatch(copyThemeDimensionTag);
                }
            } finally {
                lockService.unLock(redisLockKey);
            }
        }
    }
    private Map<String, String> initTag(String orgId, String userId) {
        List<TagCatalog> originalCatalogList = catalogRepository.listByOrgId("", CatalogConstants.CatalogSource.TAG.getValue());
        if (CollectionUtils.isEmpty(originalCatalogList)) {
            return new HashMap<>();
        }
        Map<String, String> catalogIdMap = new HashMap<>();
        List<TagCatalog> saveCatalogList = new ArrayList<>();
        originalCatalogList.forEach(catalog -> {
            String newCatalogKey = ApiUtil.getUuid();
            catalogIdMap.put(catalog.getId(), newCatalogKey);
            TagCatalog bkCatalog = new TagCatalog();
            BeanCopierUtil.copy(catalog, bkCatalog);
            bkCatalog.setOrgId(orgId);
            bkCatalog.setId(newCatalogKey);
            EntityUtil.setCreateInfo(userId, bkCatalog);
            bkCatalog.setCreateTime(catalog.getCreateTime());
            saveCatalogList.add(bkCatalog);
        });
        List<TagEntity> originalTagList = tagRepository.list("");
        Map<String, String> tagIdMap = new HashMap<>();
        List<TagEntity> saveTagList = new ArrayList<>();
        List<TagValueEntity> saveTagValueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(originalTagList)) {
            originalTagList.forEach(tag -> {
                if (catalogIdMap.containsKey(tag.getCatalogId())) {
                    String newTagKey = ApiUtil.getUuid();
                    tagIdMap.put(tag.getId(), newTagKey);
                    TagEntity tagEntity = new TagEntity();
                    BeanCopierUtil.copy(tag, tagEntity);
                    tagEntity.setId(newTagKey);
                    tagEntity.setOrgId(orgId);
                    tagEntity.setCatalogId(catalogIdMap.get(tag.getCatalogId()));
                    EntityUtil.setCreateInfo(userId, tagEntity);
                    tagEntity.setCreateTime(tag.getCreateTime());
                    saveTagList.add(tagEntity);
                }
            });
        }
        List<TagValueEntity> originalTagValueList = tagValueService.list("");
        originalTagValueList.forEach(tagValue -> {
            if (tagIdMap.containsKey(tagValue.getTagId())) {
                TagValueEntity tagValueEntity = new TagValueEntity();
                BeanCopierUtil.copy(tagValue, tagValueEntity);
                tagValueEntity.setId(ApiUtil.getUuid());
                tagValueEntity.setOrgId(orgId);
                tagValueEntity.setTagId(tagIdMap.get(tagValue.getTagId()));
                EntityUtil.setCreateInfo(userId, tagValueEntity);
                tagValueEntity.setCreateTime(tagValue.getCreateTime());
                saveTagValueList.add(tagValueEntity);
            }
        });
        if (CollectionUtils.isNotEmpty(saveCatalogList)) {
            catalogService.saveBatch(saveCatalogList);
        }
        if (CollectionUtils.isNotEmpty(saveTagList)) {
            tagRepository.saveBatch(saveTagList);
        }
        if (CollectionUtils.isNotEmpty(saveTagValueList)) {
            tagValueService.saveBatch(saveTagValueList);
        }
        return tagIdMap;
    }


    public List<Portrait4List> personaPortrait(String orgId, String userId) {
        List<Portrait4List> portraitResult = new ArrayList<>();
        //查询主题
        List<PersonaThemeEntity> themeList = personaThemeRepository.queryTheme(orgId, -1);
        if (CollectionUtils.isEmpty(themeList)) {
            return portraitResult;
        }
        //各个维度数据
        List<String> themeIds = StreamUtil.mapList(themeList, PersonaThemeEntity::getId);
        List<PersonaThemeDimensionEntity> dimensionList = dimensionRepository
                .queryThemeDimensionByThemeIds(orgId, themeIds, -1);
        if (CollectionUtils.isEmpty(dimensionList)) {
            return portraitResult;
        }
        //TODO 过滤教育经历udp无法提供这期隐藏
        dimensionList = dimensionList.stream().filter(dimension -> !"教育经历".equals(dimension.getDimensionName()))
                .collect(Collectors.toList());
        UserAllTagInfoBean esUserTagInfo = userTagService.getEsUserTagInfo(userId, orgId);
        //主题对应的维度信息
        Map<String, List<PersonaThemeDimensionEntity>> themeIdDimensionMap = dimensionList.stream()
                .collect(Collectors.groupingBy(PersonaThemeDimensionEntity::getThemeId));
        //维度关联标签信息
        List<PersonaThemeDimensionTagMapEntity> themeTagList = dimensionTagMapRepository
                .queryThemesDimensionTag(orgId, themeIds);
        if (CollectionUtils.isEmpty(themeTagList)) {
            return portraitResult;
        }
        Map<String, List<String>> themIdDimensionId4TagMap = themeTagList.stream().collect(Collectors
                .groupingBy(tag -> tag.getThemeId() + "_" + tag.getDimensionId(),
                        Collectors.mapping(PersonaThemeDimensionTagMapEntity::getTagId, Collectors.toList())));
        Set<String> tagIdSet = StreamUtil.map2set(themeTagList, PersonaThemeDimensionTagMapEntity::getTagId);
        //
        List<PortraitDimensionTag4List> portraitDimensionTag4Lists = tagPortrait(orgId, tagIdSet, esUserTagInfo);
        Map<String, PortraitDimensionTag4List> tagIdPortraitMap = StreamUtil
                .list2map(portraitDimensionTag4Lists, PortraitDimensionTag4List::getTagId);
        //获取是否开通盘点
        List<OrgVerifyFactorBean> orgParamList = coreApiFacade
                .verifyOrgFactors(orgId, Arrays.asList(TalentOrgParamConstants.ORG_PARAM_KEY_TALENT_RV));
        Integer talentRvStatus = 0;
        if (CollectionUtils.isNotEmpty(orgParamList)) {
            OrgVerifyFactorBean orgVerifyFactorBean = orgParamList.get(0);
            talentRvStatus = orgVerifyFactorBean.getFactorState();
        }
        Integer finalTalentRvStatus = talentRvStatus;
        themeList.forEach(theme -> {
            Portrait4List portrait4List = new Portrait4List();
            portrait4List.setThemeId(theme.getId());
            portrait4List.setThemeName(theme.getThemeName());
            List<PersonaThemeDimensionEntity> themeDimensionList = themeIdDimensionMap.get(theme.getId());
            AtomicInteger existDimension = new AtomicInteger();
            List<PortraitDimension4List> portraitDimension4Lists = dimensionPortrait(tagIdPortraitMap,
                    themIdDimensionId4TagMap, theme, themeDimensionList, existDimension, finalTalentRvStatus);
            portrait4List.setDimension4Lists(portraitDimension4Lists);
            //覆盖率
            if (CollectionUtils.isNotEmpty(portraitDimension4Lists)) {
                BigDecimal coverage = new BigDecimal(existDimension.get())
                        .divide(new BigDecimal(portraitDimension4Lists.size()), 2, BigDecimal.ROUND_HALF_UP);
                portrait4List.setDimensionCoverage(coverage);
            }
            portraitResult.add(portrait4List);
        });
        return portraitResult;
    }

    /**
     * 人员画像 组装标签数据
     * @param orgId
     * @param tagIdSet
     * @param esUserTagValueMap
     * @return
     */
    private List<PortraitDimensionTag4List> tagPortrait(String orgId, Set<String> tagIdSet,
            Map<String, Object> esUserTagValueMap) {
        List<PortraitDimensionTag4List> tag4Lists = new ArrayList<>();
        List<TagEntity> tagList = tagRepository.queryTagByIds(orgId, tagIdSet);
        if (CollectionUtils.isEmpty(tagList)) {
            return tag4Lists;
        }
        //筛选自建标签查询对应的标签值
        Map<String, Object> diyLabelsMap = (Map<String, Object>) esUserTagValueMap
                .get(UserSearchConstants.ES_DIY_LABELS);
        if (diyLabelsMap == null) {
            diyLabelsMap = new HashMap<>();
        }
        Collection<Object> values = diyLabelsMap.values();
        Collection<String> tagValueIdList = new ArrayList<>();
        values.forEach(value -> {
            if (value instanceof String) {
                tagValueIdList.add((String) value);
            }
            if (value instanceof List) {
                tagValueIdList.addAll((List)value);
            }
        });
        List<String> trimNullIdList = tagValueIdList.stream().filter(e -> !e.isEmpty()).collect(Collectors.toList());
        List<TagValueEntity> tagValueList = tagValueRepository.queryTagByIds(orgId, trimNullIdList);
        Map<String, String> tagValueIdNameMap = StreamUtil
                .list2map(tagValueList, TagValueEntity::getId, TagValueEntity::getValueName);
        Map<String, Object> finalDiyLabelsMap = diyLabelsMap;
        tagList.forEach(tag -> {
            Object tagValue = null;
            //自建标签在diylabels标签下
            if (tag.getSource() == 1) {
                tagValue = finalDiyLabelsMap.get(tag.getId());
            }
            //非自建标签直接查询标签key即可
            if (tag.getSource() != 1 && esUserTagValueMap.containsKey(tag.getTagKey())) {
                String tagKey = tag.getTagKey();
                tagValue = esUserTagValueMap.get(tagKey);
            }
            try {
                buildTagDetail(tag, tagValue, tag4Lists, tagValueIdNameMap);
            } catch (Exception e) {
                log.warn("标签值处理异常 tag={}, tagvalue={}", JSON.toJSONString(tag), tagValue.toString(), e);
            }
        });
        return tag4Lists;
    }


    /**
     *
     * @param tag 标签信息
     * @param tagValue 人维度es中此标签数据
     * @param tag4Lists 标签值的数据信息
     */
    private void buildTagDetail(TagEntity tag, Object tagValue, List<PortraitDimensionTag4List> tag4Lists,
            Map<String, String> tagValueIdNameMap) {
        String tagKey = tag.getTagKey();
        PortraitDimensionTag4List portraitDimensionTag = new PortraitDimensionTag4List();
        portraitDimensionTag.setTagId(tag.getId());
        portraitDimensionTag.setTagName(tag.getTagName());
        portraitDimensionTag.setDescription(tag.getDescription());
        portraitDimensionTag.setTagKey(tagKey);
        portraitDimensionTag.setValueType(-1);
        if (tagValue == null || StringUtils.isEmpty(tagValue.toString())) {
            portraitDimensionTag.setTagValue(new ArrayList<>());
            tag4Lists.add(portraitDimensionTag);
            return;
        }
        List<String> nameList = transferTagValueList(tag, tagValue, tagValueIdNameMap);
        portraitDimensionTag.setTagValue(nameList);
        //通用专业能力 优劣势需要处理下，标识方便前端展示对应的能力颜色
        if (UserSearchConstants.ES_ADV_PRO_ABILITY.equals(tagKey) || UserSearchConstants.ES_ADV_COM_ABILITY
                .equals(tagKey)) {
            portraitDimensionTag.setValueType(0);
        }
        if (UserSearchConstants.ES_INF_COM_ABILITY.equals(tagKey) || UserSearchConstants.ES_INF_PRO_ABILITY
                .equals(tagKey)) {
            portraitDimensionTag.setValueType(1);
        }
        tag4Lists.add(portraitDimensionTag);
    }

    private List<String> transferTagValueList(TagEntity tag, Object tagValue, Map<String, String> tagValueIdNameMap) {
        List<String> nameList = new ArrayList<>();
        //es数据集合类型需要转换
        List<String> tagValueList;
        if (tag.getValueChooseModel() == 1 || tagValue instanceof List) {
            tagValueList = (ArrayList) tagValue;
        } else {
            tagValueList = Arrays.asList(tagValue.toString());
        }

        tagValueList.forEach(tagValueId -> {
            String tagValueName = "";
            if (tag.getSource() != 1) {
                tagValueName = tagValueId;
            }
            //自建并且标签值map包含当前贴的标签id, id转换成名称展示
            if (tag.getSource() == 1 && tagValueIdNameMap.containsKey(tagValueId)) {
                tagValueName = tagValueIdNameMap.get(tagValueId);
            }
            //标签类型为绩效、能力、潜力时需要拼接字符串，标签名+es数据值
            if (UserSearchConstants.ES_PERFORMANCE_LEVEL.equals(tag.getTagKey()) || UserSearchConstants.ES_ABILITY_LEVEL
                    .equals(tag.getTagKey()) || UserSearchConstants.ES_POTENTIAL_LEVEL.equals(tag.getTagKey())) {
                tagValueName = String.format("%s%s", tag.getTagName(), tagValueId);
            }
            //如果标签是unspacedLrn 连续学习n周
            if (UserSearchConstants.ES_UNSPACED_LRN.equals(tag.getTagKey())) {
                tagValueName = tag.getTagName().replace("N", tagValueId);
            }
            //学分，时长TOP占比转换
            if (UserSearchConstants.ES_LRNDURATION_RATIO.equals(tag.getTagKey()) || UserSearchConstants.ES_SCORE_RATIO
                    .equals(tag.getTagKey())) {
                String replace = tag.getTagName().replace("占比", " ");
                tagValueName = String.format(TalentBkConstants.STR_FMT_3PARAM, replace, tagValueId, "%");
            }
            //工，司龄转换
            if (UserSearchConstants.ES_ENTRY_DATE.equals(tag.getTagKey()) || UserSearchConstants.ES_WORK_DATE
                    .equals(tag.getTagKey())) {
                Date date = DateUtil.formatDate(String.valueOf(tagValueId));
                String entryYear = DateUtils.getEntryYear(date);
                String tagName = tag.getTagName().replace("段", "");
                tagValueName = String.format(TalentBkConstants.STR_FMT_3PARAM, tagName, entryYear, "年");
            }
            if (StringUtils.isNotBlank(tagValueName)) {
                nameList.add(tagValueName);
            }
        });
        return nameList;
    }

    /**
     * 人员画像 组装各个维度及关联标签的数据
     * @param tagIdPortraitMap
     * @param themIdDimensionId4TagMap
     * @param theme
     * @param themeDimensionList
     * @param talentRvStatus 人才盘点是否开通： 0-未开通 1-已开通
     * @return
     */
    private List<PortraitDimension4List> dimensionPortrait(Map<String, PortraitDimensionTag4List> tagIdPortraitMap,
            Map<String, List<String>> themIdDimensionId4TagMap, PersonaThemeEntity theme,
            List<PersonaThemeDimensionEntity> themeDimensionList, AtomicInteger existDimension, Integer talentRvStatus) {
        List<PortraitDimension4List> dimension4Lists = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(themeDimensionList)) {
            themeDimensionList.forEach(dimension -> {
                boolean talentRvEnable = UserSearchConstants.ES_TALENT_RV_NAME.equals(dimension.getDimensionName())
                        && talentRvStatus == 1;
                boolean nonTalentRv = !UserSearchConstants.ES_TALENT_RV_NAME.equals(dimension.getDimensionName());
                if (talentRvEnable || nonTalentRv) {
                    PortraitDimension4List dimension4List = new PortraitDimension4List();
                    dimension4List.setDimensionId(dimension.getId());
                    dimension4List.setDimensionName(dimension.getDimensionName());
                    String key = String.format(TalentBkConstants.STR_FMT_3PARAM, theme.getId(), "_", dimension.getId());
                    List<PortraitDimensionTag4List> tag4Lists = new ArrayList<>();
                    if (themIdDimensionId4TagMap.containsKey(key)) {
                        List<String> tagIds = themIdDimensionId4TagMap.get(key);
                        tagIds.forEach(tagId -> {
                            if (tagIdPortraitMap.containsKey(tagId)) {
                                tag4Lists.add(tagIdPortraitMap.get(tagId));
                            }
                        });
                    }
                    boolean hasTagValue = tag4Lists.stream()
                            .anyMatch(tagValueList -> CollectionUtils.isNotEmpty(tagValueList.getTagValue()));
                    if (hasTagValue) {
                        existDimension.getAndAdd(1);
                        dimension4List.setTagValueFinished(true);
                    }
                    dimension4List.setTag4Lists(tag4Lists);
                    dimension4Lists.add(dimension4List);
                }
            });
        }
        return dimension4Lists;
    }
}
