package com.yxt.talent.bk.common.enums;

public enum TargetTypeEnum {
    /**
     * 来源
     */
    O2O_PROJECT("o2o项目", 0),
    EVALUATION("测评", 1);

    private String name;

    private Integer type;

    TargetTypeEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }
}
