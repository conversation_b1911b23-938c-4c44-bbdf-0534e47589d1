package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.bk.common.bean.searchrule.SPTagSearchBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangKeywordBean;
import com.yxt.talent.bk.core.pool.bean.SearchDeptInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * @Description TODO
 *
 * <AUTHOR>
 * @Date 2023/8/16 14:48
 **/
@Data
public class Scheme4Search {

    @Schema(description = "搜索，姓名账号")
    private String keyword;
    @JsonIgnore
    private UdpLangKeywordBean keywordLangMatch;

    @Schema(description = "部门id")
    List<SearchDeptInfo> depts;

    @Schema(description = "岗位id")
    private List<String> positionIds;

    @Schema(description = "职级id")
    private List<String> gradeIds;

    @Schema(description = "账号状态，0-禁用，1-启用,全部就不传值")
    private Integer status;

    @Schema(description = "常用筛选方案id，不传时给空值")
    private Long schemeId;

    @Schema(description = "标签搜索类型，1：交集，2：并集", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @Range(min = 1, max = 2, message = "apis.talentbk.userGroup.args.error")
    private Integer tagSearchType;

    /**
     * 标签搜索 json
     */
    @Schema(description = "标签搜索 json", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<SPTagSearchBean> tagSearch;

    @Schema(description = "人员id")
    private List<String> userIds;
}
