package com.yxt.talent.bk.core.pool.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Schema(name = "绑定培训项目返回结果")
public class TrainingBindResult {

    @Schema(description = "已经删除的项目")
    private List<String> deletedProjectIds = new ArrayList<>();

    @Schema(description = "已经归档的项目")
    private List<String> filedProjectIds = new ArrayList<>();

    @Schema(description = "已经结束的项目")
    private List<String> endProjectIds = new ArrayList<>();

}
