package com.yxt.talent.bk.core.pool.repo;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.core.pool.entity.ProjectTargetMap;
import com.yxt.talent.bk.core.pool.mapper.ProjectTargetMapMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * ProjectTargetMapRepository
 *
 * <AUTHOR> harleyge
 * @Date 9/9/24 2:50 pm
 */
@Slf4j
@Repository
@AllArgsConstructor
public class ProjectTargetMapRepository extends ServiceImpl<ProjectTargetMapMapper, ProjectTargetMap> {

    public List<ProjectTargetMap> findByPoolIds(String orgId, Collection<String> poolIds) {
        if (CollectionUtils.isEmpty(poolIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ProjectTargetMap> queryWrapper = getQueryWrapper();
        queryWrapper.eq(ProjectTargetMap::getOrgId, orgId);
        queryWrapper.in(ProjectTargetMap::getProjectId, poolIds);
        return list(queryWrapper);
    }

    public List<ProjectTargetMap> findByOrgId(String orgId) {
        LambdaQueryWrapper<ProjectTargetMap> queryWrapper = getQueryWrapper();
        queryWrapper.eq(ProjectTargetMap::getOrgId, orgId);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<ProjectTargetMap> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }
}
