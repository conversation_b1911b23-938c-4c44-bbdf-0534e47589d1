package com.yxt.talent.bk.svc.heir;

import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.svc.heir.bean.req.ConfigPageImageAddReq;
import com.yxt.talent.bk.svc.heir.bean.req.ConfigPageImageUpdateReq;
import com.yxt.talent.bk.svc.heir.bean.resp.ConfigPageImageResp;

public interface ConfigPageImageService {

    void create(UserCacheBasic currentUser, ConfigPageImageAddReq req);

    void update(UserCacheBasic currentUser, ConfigPageImageUpdateReq req);

    CommonList<ConfigPageImageResp> list(UserCacheBasic currentUser);

    void delete(UserCacheBasic currentUser, String id);

}
