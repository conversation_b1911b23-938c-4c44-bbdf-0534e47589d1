package com.yxt.talent.bk.svc.heir.component;

import com.alibaba.fastjson.JSON;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.talent.bk.svc.heir.bean.HeirBmUserEdit4Log;
import com.yxt.talent.bk.svc.heir.bean.HeirBmUserEditParam4Log;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

/**
 * HeirBmUserEditLogProvider
 *
 * <AUTHOR> geyan
 * @Date 21/3/24 10:09 am
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirBmUserEditLogProvider implements AuditLogDataProvider<HeirBmUserEditParam4Log, HeirBmUserEdit4Log> {
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final HeirPosTypeSelector heirPosTypeSelector;

    @Override
    public HeirBmUserEditParam4Log convertParam(Object param, AuditLogBasicBean logBasic) {
        if (param instanceof HeirBmUserEditParam4Log) {
            return AuditLogDataProvider.super.convertParam(param, logBasic);
        }
        return JSON.parseObject(JSON.toJSONString(param), HeirBmUserEditParam4Log.class);
    }

    @Override
    public HeirBmUserEdit4Log before(HeirBmUserEditParam4Log param, AuditLogBasicBean logBasic) {
        HeirBmUserEdit4Log edit4Log = new HeirBmUserEdit4Log();
        edit4Log.setUserName(udpLiteUserRepository.userNames4Log(logBasic.getOrgId(), Lists.newArrayList(param.getUserId())));
        edit4Log.setEnabledDesc(enabledDesc(!param.isEnabled()));
        return edit4Log;
    }

    @Override
    public HeirBmUserEdit4Log after(HeirBmUserEditParam4Log param, AuditLogBasicBean logBasic) {
        HeirBmUserEdit4Log edit4Log = new HeirBmUserEdit4Log();
        edit4Log.setEnabledDesc(enabledDesc(param.isEnabled()));
        return edit4Log;
    }

    @Override
    public Pair<String, String> entityInfo(HeirBmUserEditParam4Log param, HeirBmUserEdit4Log beforeObj, HeirBmUserEdit4Log afterObj, AuditLogBasicBean logBasic) {
        return Pair.of(param.getPosId(), String.format("继任地图-%s-设置标杆用户",
                heirPosTypeSelector.getPosNameById(logBasic.getOrgId(), param.getPosId()).getValue()));
    }

    private String enabledDesc(boolean enabled) {
        return enabled ? "启用" : "不启用";
    }
}
