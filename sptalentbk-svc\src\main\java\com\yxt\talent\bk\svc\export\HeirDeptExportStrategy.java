package com.yxt.talent.bk.svc.export;

import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class HeirDeptExportStrategy extends HeirBaseExportStrategy {

    @Override
    public int posType() {
        return HeirPosTypeEnum.DEPT.getType();
    }
}
