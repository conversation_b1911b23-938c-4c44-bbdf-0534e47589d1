package com.yxt.talent.bk.svc.rpc;

import com.google.common.collect.Maps;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.o2ofacade.bean.project.DemoCopyIDReq;
import com.yxt.o2ofacade.bean.project.DemoCopyIDResp;
import com.yxt.o2ofacade.client.ProjectClient;
import com.yxt.o2ofacade.service.ProjectFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * O2oRpc
 *
 * <AUTHOR> harleyge
 * @Date 23/9/24 10:43 am
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class O2oRpc {
    private final ProjectFacade projectFacade;
    private final ProjectClient projectClient;

    public Map<Long, Long> demoCopyPrjOld2NewIdMap(String orgId) {
        Map<Long, Long> idMap = new HashMap<>();
        DemoCopyIDReq req = new DemoCopyIDReq();
        req.setOrgId(orgId);
        req.setQueryType(0);
        List<DemoCopyIDResp> copyIdList = projectClient.getDemoCopyNewId(req);
        copyIdList.forEach(item -> idMap.put(item.getOldId(), item.getNewId()));
        return idMap;
    }
}
