package com.yxt.talent.bk.api.controller.tag;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.bk.api.component.PersonaThemeComponent;
import com.yxt.talent.bk.api.component.TagComponent;
import com.yxt.talent.bk.api.component.UserTagComponent;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkAuthCodes;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.core.tag.bean.Tag4Get;
import com.yxt.talent.bk.core.tag.bean.Tag4Page;
import com.yxt.talent.bk.core.tag.bean.UserBaseInfoBean;
import com.yxt.talent.bk.core.tag.bean.UserTagBaseBean;
import com.yxt.talent.bk.svc.search.bean.TagBean4Search;
import com.yxt.talent.bk.svc.search.bean.TagValueBean4Search;
import com.yxt.talent.bk.svc.tag.TagService;
import com.yxt.talent.bk.svc.tag.UserTagValueService;
import com.yxt.talent.bk.svc.tag.bean.DelUserTagBean;
import com.yxt.talent.bk.svc.tag.bean.Tag4Create;
import com.yxt.talent.bk.svc.tag.bean.UserTagBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 *  标签控制层
 * <AUTHOR>
 * @since 2022/8/3 13:57
 * @version 1.0
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Deprecated
@Tag(name = "标签控制层")
@AllArgsConstructor
@RestController
@RequestMapping(value = "/mgr/tag")
public class TagController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(TagController.class);
    private final TagService tagService;
    private final UserTagComponent userTagComponent;
    private final TagComponent tagComponent;
    private final ILock lockService;
    private final PersonaThemeComponent personaThemeComponent;
    private final UserTagValueService userTagValueService;

    @Deprecated
    @Operation(summary = "查询标签表列表")
    @Parameters({             @Parameter(name = "limit", description = "每页记录数. 默认值为20.", in = ParameterIn.QUERY),             @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "                     + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY)})
    @PostMapping(value = "/list", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN, codes = TalentBkAuthCodes.BK_AUTH_CODE_ALL)
    public PagingList<Tag4Page> list(@RequestBody TagBean4Search tagBean4Search) {
        UserCacheBasic userCache = getUserCacheBasic();
        personaThemeComponent.initThemeDimensionTag(userCache.getOrgId(), userCache.getUserId());
        return tagService.findTagPage(ApiUtil.getPageRequest(getRequest()), userCache.getOrgId(), tagBean4Search);
    }

    @Deprecated
    @Operation(summary = "创建标签")
    @PostMapping(value = "", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.CREATED)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN, codes = TalentBkAuthCodes.BK_AUTH_CODE_TAG_ADD)
    public Tag4Create createTag(@Valid @RequestBody Tag4Create tag4Create) {
        UserCacheDetail userCacheDetail = getUserCacheDetail();
        return tagService
                .createTag(userCacheDetail.getOrgId(), userCacheDetail.getUserId(), userCacheDetail.getFullname(),
                        tag4Create);
    }

    @Deprecated
    @Operation(summary = "编辑标签")
    @PutMapping(value = "", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN, codes = TalentBkAuthCodes.BK_AUTH_CODE_TAG_EDIT)
    public Tag4Create editTag(@Valid @RequestBody Tag4Create tag4Create) {
        UserCacheBasic userCache = getUserCacheBasic();
        return tagComponent.editTag(userCache.getOrgId(), userCache.getUserId(), tag4Create);
    }

    @Deprecated
    @Operation(summary = "获取标签信息")
    @GetMapping(value = "/detail/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN, codes = TalentBkAuthCodes.BK_AUTH_CODE_TAG_VIEW)
    public Tag4Create getTagDetail(@PathVariable String id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return tagService.getTagDetail(userCache.getOrgId(), id);
    }

    @Deprecated
    @Operation(summary = "获取标签基础信息")
    @GetMapping(value = "{id}")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN, codes = TalentBkAuthCodes.BK_AUTH_CODE_TAG_VIEW)
    public Tag4Get getTag(@PathVariable String id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return tagService.getTagInfo(userCache.getOrgId(), id);
    }

    @Deprecated
    @Operation(summary = "启用/禁用标签")
    @Parameters({@Parameter(name = "id", description = "标签id", in = ParameterIn.PATH),             @Parameter(name = "type", description = "操作类型：0-禁用；1-启用", in = ParameterIn.PATH)})
    @PutMapping(value = "/{id}/{type}", produces = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN, codes = TalentBkAuthCodes.BK_AUTH_CODE_TAG_STATUS)
    public void change(@PathVariable String id, @PathVariable int type) {
        UserCacheBasic userCache = getUserCacheBasic();
        tagService.changeStatus(userCache.getOrgId(), id, userCache.getUserId(), type);
    }

    @Deprecated
    @Operation(summary = "删除标签")
    @Parameter(name = "id", description = "标签id", in = ParameterIn.PATH)
    @DeleteMapping(value = "/{id}", produces = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN, codes = TalentBkAuthCodes.BK_AUTH_CODE_TAG_DELETE)
    public void deleteTag(@PathVariable String id) {
        UserCacheBasic userCache = getUserCacheBasic();
        tagComponent.removeTag(userCache.getOrgId(), id, userCache.getUserId());
    }

    @Deprecated
    @Operation(summary = "根据标签id查询自建标签用户分页列表")
    @PostMapping(value = "/listpagetaguser")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<UserBaseInfoBean> listPageTagUser(@RequestBody @Validated TagValueBean4Search search) {
        PageRequest pageRequest = ApiUtil.getPageRequest(getRequest());
        UserCacheBasic userCache = getUserCacheBasic();
        return userTagComponent.listPageTagUserForEs(userCache.getOrgId(), ApiUtil.getFiltedLikeString(search.getKeyword()),
                search.getTagId(), search.getTagValueId(), pageRequest);
    }

    @Deprecated
    @Operation(summary = "根据标签id查询自建标签用户分页列表v2, 通过数据库查询")
    @PostMapping(value = "/tagusers")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<UserBaseInfoBean> listTagusers(@RequestBody @Validated TagValueBean4Search search) {
        PageRequest pageRequest = ApiUtil.getPageRequest(getRequest());
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        String keyword = ApiUtil.getFiltedLikeString(search.getKeyword());
        String tagId = search.getTagId();
        String tagValueId = search.getTagValueId();
        PagingList<UserBaseInfoBean> result =
            userTagComponent.listPageTagUse(orgId, keyword, tagId, tagValueId, pageRequest);
        fillTagBaseBeanList(orgId, search.getTagId(), result);
        return result;
    }

    private void fillTagBaseBeanList(String orgId, String tagId, PagingList<UserBaseInfoBean> result) {
        List<String> userIds = StreamUtil.mapList(result.getDatas(), UserBaseInfoBean::getUserId);
        Map<String, List<UserTagBaseBean>>
            userTagBaseBeans = userTagValueService.getUserTagValueNameMap(orgId, tagId, userIds);
        result.getDatas().forEach(data -> {
            data.setTagBaseBeanList(userTagBaseBeans.get(data.getUserId()));
        });
    }

    @Deprecated
    @Operation(summary = "根据标签id查询自建标签用户导出")
    @PostMapping(value = "/taguserexport")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public Map<String, String> tagUserExport(@RequestBody @Validated TagValueBean4Search search) {
        UserCacheBasic userCache = getUserCacheBasic();
        return userTagComponent.exportUserTag(userCache.getOrgId(), userCache.getUserId()
                ,search.getKeyword(),search.getTagId(), search.getTagValueId());
    }

    @Deprecated
    @Operation(summary = "删除标签用户")
    @PutMapping(value = "/delTagUser")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_UPDATEMULTIPLE, type = AuthType.TOKEN)
    public void delTagUser(@RequestBody @Valid DelUserTagBean delUserTagBean) {
        UserCacheBasic userCache = getUserCacheBasic();
        String cacheKey = String.format(TalentBkRedisKeys.CACHE_KEY_ORG_DEL_USER_TAG, userCache.getOrgId());
        if (lockService.tryLock(cacheKey, TalentBkRedisKeys.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                userTagComponent.delUserTag(userCache.getOrgId(), delUserTagBean, userCache.getUserId());
            } finally {
                lockService.unLock(cacheKey);
            }
        }
    }

    @Deprecated
    @Operation(summary = "新增标签用户")
    @PostMapping(value = "/add")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE,
          type = AuthType.TOKEN,
          codes = {TalentBkAuthCodes.BK_AUTH_CODE_TAG_ADD, TalentBkAuthCodes.BK_AUTH_CODE_TAG_USER})
    public void addTagUser(@RequestBody @Valid UserTagBean userTagBean) {
        UserCacheBasic userCache = getUserCacheBasic();
        String cacheKey = String.format(TalentBkRedisKeys.CACHE_KEY_ORG_ADD_USER_TAG, userCache.getOrgId());
        if (lockService.tryLock(cacheKey, TalentBkRedisKeys.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                userTagComponent.tagUser(userCache.getOrgId(), userTagBean, userCache.getUserId());
            } finally {
                lockService.unLock(cacheKey);
            }
        } else {
            Set<String> tagValueIds = userTagBean.listTagValueId();
            Set<String> delTagIds = userTagBean.listDelTagId();
            log.warn("LOG10330:cacheKey={}, tagValueIds={}, delTagIds={}", cacheKey, tagValueIds, delTagIds);
        }
    }

    @Deprecated
    @Operation(summary = "机构增加学习周期数据")
    @GetMapping(value = "/addvalue")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE,
          type = AuthType.TOKEN)
    public void flashTagValue() {
        tagService.addTagValue();
    }

}
