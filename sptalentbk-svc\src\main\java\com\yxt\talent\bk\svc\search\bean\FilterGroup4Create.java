package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "筛选组")
public class FilterGroup4Create {

    @Schema(description = "筛选组id")
    private String id;

    @Schema(description = "筛选组名称")
    private String groupName;

    @Schema(description = "筛选组内容集合")
    private String text;
}
