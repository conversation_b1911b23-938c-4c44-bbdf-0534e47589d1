package com.yxt.talent.bk.svc.strategy;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.utils.ExcelbkUtil;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.pool.bean.PoolSimpleBean;
import com.yxt.talent.bk.core.pool.entity.PoolReadiness;
import com.yxt.talent.bk.core.pool.repo.PoolReadinessRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人才池准备度模板
 */

@Component
@AllArgsConstructor
public class PoolReadinessTempStrategry implements OutputStrategy {
    private final I18nComponent i18nComponent;
    private final PoolReadinessRepository poolReadinessRepository;
    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        List<PoolSimpleBean> resList = (List<PoolSimpleBean>) data;
        String orgId = resList.get(0).getOrgId();
        List<PoolReadiness> list = poolReadinessRepository.list(orgId);
        String[] param = new String[list.size()];
        for (int i = 0; i < list.size() ; i++) {
            PoolReadiness poolReadiness = list.get(i);
            param[i] = poolReadiness.getReadinessName();
        }
        Map<String, Object> headerMap = baseHeaderMap();
        // 获取
        ExcelbkUtil.exportTemplate(headerMap,
                data, filePath, ExportConstants.POOL_READONESS_IMPORT_TEMP_PATH, param);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE).appCode(ModuleConstants.APP_CODE)
                .moduleCode(ModuleConstants.MODULE_CODE).fileName(fileName)
                .name(i18nComponent.getI18nValue(ExportConstants.POOL_READONESS_IMPORT_TEMP_NAME)).build();
    }

    public static Map<String, Object> baseHeaderMap() {
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("readinessDesc",
                TalentbkUtil.getMessage("apis.talentbk.pool.readiness.import.desc"));
        headerMap.put("readinessRule1",
                TalentbkUtil.getMessage("apis.talentbk.pool.readiness.import.rule1"));
        headerMap.put("readinessRule2",
                TalentbkUtil.getMessage("apis.talentbk.pool.readiness.import.rule2"));

        headerMap.put("fullName",
                TalentbkUtil.getMessage("apis.talentbk.pool.user.export.header.fullName"));
        headerMap.put("userName",
                TalentbkUtil.getMessage("apis.talentbk.pool.user.export.header.userName"));
        headerMap.put("readinessName",
                TalentbkUtil.getMessage("apis.talentbk.pool.user.export.header.readinessName"));
        return headerMap;
    }
}
