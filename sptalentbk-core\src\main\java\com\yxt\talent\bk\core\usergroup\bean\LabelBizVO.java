package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/8/18
 */
@Data
@Schema
public class LabelBizVO {

    @Schema(description = "标签ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long labelId;
    @Schema(description = "标签值ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long labelValueId;
    @Schema(description = "标签值")
    private String labelValue;
}
