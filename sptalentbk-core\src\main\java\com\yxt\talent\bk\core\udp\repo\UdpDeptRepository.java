package com.yxt.talent.bk.core.udp.repo;

import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.bk.common.bean.udp.UdpLangDeptBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangFullBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangPositionBean;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.udp.bean.DeptFullPathBean;
import com.yxt.talent.bk.core.udp.mapper.UdpDeptMapper;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * UdpDeptRepository
 *
 * <AUTHOR> harleyge
 * @Date 6/5/24 2:28 pm
 */
@Repository
@AllArgsConstructor
public class UdpDeptRepository implements InitializingBean {
    private final UdpDeptMapper udpDeptMapper;

    @Override
    public void afterPropertiesSet() throws Exception {
        beanInitEnd(this);
    }

    private static void beanInitEnd(UdpDeptRepository udpDeptRepository) {
        TalentbkUtil.deptPathSwap = udpDeptRepository::deptLangAsFullPath;
    }

    public String getNameById(String orgId, String id) {
        UdpLangDeptBean ret = udpDeptMapper.getNameById(orgId, id);
        if (ret != null) {
            TalentbkUtil.udpTranslate(orgId, Lists.newArrayList(ret));
        }
        return Optional.ofNullable(ret).map(UdpLangDeptBean::getName).orElse(null);
    }

    public String getPositionNameById(String orgId, String id) {
        UdpLangPositionBean ret = udpDeptMapper.getPositionNameById(orgId, id);
        if (ret != null) {
            TalentbkUtil.udpTranslate(orgId, Lists.newArrayList(ret));
        }
        return Optional.ofNullable(ret).map(UdpLangPositionBean::getName).orElse(null);
    }

    public void deptLangAsFullPath(List<UdpLangFullBean> deptLangList) {
        if (CollectionUtils.isNotEmpty(deptLangList)) {
            List<String> deptIds = BeanCopierUtil.convertList(deptLangList, UdpLangFullBean::getDeptId);
            Map<String, DeptFullPathBean> fullPathMap = StreamUtil.list2map(udpDeptMapper.queryIdFullPathByIds(deptIds),
                    DeptFullPathBean::getId);
            deptLangList.forEach(deptLang -> {
                DeptFullPathBean fullPathBean = fullPathMap.get(deptLang.getDeptId());
                if (fullPathBean != null) {
                    deptLang.setDeptId(fullPathBean.getIdFullPath());
                }
            });
        }
    }
}
