package com.yxt.talent.bk.core.profile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 个人画像配置表(BkPortraitOrgConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-06-12 13:49:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "bk_portrait_org_config")
@EqualsAndHashCode(callSuper = true)
public class PortraitOrgConfig extends CreatorEntity {
    /**
     * 主键
     */
    @TableField(value = "id")
    private String id;
    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 员工是否可见（0-否，1-是）
     */
    @TableField(value = "client_range")
    private Integer clientRange;
    /**
     * 是否开启白名单（0-否，1-是）
     */
    @TableField(value = "white_range")
    private Integer whiteRange;
    /**
     * 管理者是否可见（0-否，1-是）
     */
    @TableField(value = "manager_range")
    private Integer managerRange;

    /**
     * 客户端可见模块设置
     */
    private String clientShowCfg;

    /**
     * 管理端可见模块设置
     */
    private String managerShowCfg;
}
