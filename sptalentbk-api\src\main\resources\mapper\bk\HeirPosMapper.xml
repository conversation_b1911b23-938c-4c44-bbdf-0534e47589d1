<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirPosMapper">

    <select id="existIds" resultType="com.yxt.talent.bk.common.bean.EntityDeletedBean">
        select id,deleted from bk_heir_pos where org_id = #{orgId} and id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectPIdList" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosPIdBean">
        select id,parent_pos_id from bk_heir_pos where org_id = #{orgId} and pos_type = #{posType} and deleted = 0
    </select>

    <update id="replacePId">
        update bk_heir_pos set parent_pos_id = #{toParentId}
        where org_id = #{orgId} and pos_type = #{posType}
        and parent_pos_id = #{fromParentId} and deleted = 0
    </update>

    <update id="removePosRelated">
        update bk_heir_pos_prepare_cfg set deleted = 1,update_time = now() where org_id = #{orgId} and pos_id = #{posId} and deleted = 0;
        update bk_heir_pos_permission set deleted = 1,update_time = now() where pos_id = #{posId} and deleted = 0;
        update bk_heir_pos_user set deleted = 1,update_time = now() where org_id = #{orgId} and pos_id = #{posId} and deleted = 0;
        update bk_heir_pos_benchmark set deleted = 1,update_time = now() where pos_id = #{posId} and deleted = 0;
    </update>

    <update id="update">
        update bk_heir_pos
        <set>
            <if test="permissionFlag != null">
                permission_flag = #{permissionFlag},
            </if>
            <if test="calcCycle != null">
                calc_cycle = #{calcCycle},
            </if>
            <if test="nextCalcTime != null">
                next_calc_time = #{nextCalcTime},
            </if>
            <if test="calcCycle != null">
                next_calc_version = next_calc_version + 1,
            </if>
            <if test="remindCycle != null">
                remind_cycle = #{remindCycle},
            </if>
            <if test="nextRemindTime != null">
                next_remind_time = #{nextRemindTime},
            </if>
            <if test="remindCycle != null">
                next_remind_version = next_remind_version + 1,
            </if>
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        </set>
        where org_id = #{orgId} and id = #{id}
    </update>

    <select id="allPos" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosRawBean">
        <choose>
            <when test="posType eq 0">
                select id,pos_name as name,source_deleted,parent_pos_id,heir_target_qty,heir_valid_qty,
                risk_rule_type,risk_level_id,permission_flag,create_user_id,0 as pos_type from bk_heir_pos
                where org_id = #{orgId} and pos_type = #{posType} and deleted = 0
            </when>
            <otherwise>
                select d.id,d.name,d.id_full_path,d.parent_id as parent_pos_id,p.heir_target_qty,p.heir_valid_qty,
                p.risk_rule_type,p.risk_level_id,p.permission_flag,1 as pos_type from udp_dept d
                left join bk_heir_pos p on p.id = d.id
                where d.org_id = #{orgId}
                and d.deleted = 0 order by d.order_index
            </otherwise>
        </choose>
    </select>

    <select id="selectDeptHeir" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosRawBean">
        select d.id,d.name,d.parent_id as parent_pos_id,
        p.heir_target_qty,p.heir_valid_qty,p.risk_rule_type,p.risk_level_id,p.permission_flag,
        1 as pos_type
        from udp_dept d
        join bk_heir_pos p on p.id = d.id and p.heir_target_qty > 0
        where d.org_id = #{orgId}
          and (d.id  = #{deptId} or d.routing_path like concat(#{deptRoutingPath},'.%'))
          and d.deleted = 0
        order by d.routing_path
    </select>

    <select id="getById" resultType="com.yxt.talent.bk.core.heir.ext.HeirPosExt">
        select id, pos_name, permission_flag from bk_heir_pos where id = #{id} and org_id = #{orgId} and deleted = 0
    </select>

    <select id="getIdByParentIds" resultType="string">
        select id from bk_heir_pos where org_id = #{orgId} and deleted = 0 and pos_type = #{posType}
        and parent_pos_id in
        <foreach collection="pIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="removeByIds" resultType="string">
        update bk_heir_pos set deleted = 1,update_time = now() where org_id = #{orgId} and deleted = 0
        and id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectNeedReadinessCompute" resultType="com.yxt.talent.bk.core.heir.entity.HeirPosEntity">
        select p.* from bk_heir_pos p
        left join udp_dept d on d.id = p.id and d.deleted = 0 and p.pos_type = 1
        where p.deleted = 0 and date_format(p.next_calc_time,'%Y-%m-%d') &lt;= date_format(now(),'%Y-%m-%d')
        and (p.pos_type = 0 or d.id is not null)
        and exists(select 1 from udp_org o where o.id = p.org_id and o.deleted = 0
        and o.domain is not null and o.domain != ''
        and o.end_date > DATE_SUB(CURDATE(), INTERVAL 1 DAY))
    </select>

    <select id="selectNeedReadinessRemind" resultType="com.yxt.talent.bk.core.heir.entity.HeirPosEntity">
        select p.* from bk_heir_pos p
        left join udp_dept d on d.id = p.id and d.deleted = 0 and p.pos_type = 1
        where p.deleted = 0 and date_format(p.next_remind_time,'%Y-%m-%d') &lt;= date_format(now(),'%Y-%m-%d')
        and (p.pos_type = 0 or d.id is not null)
        and exists(select 1 from udp_org o where o.id = p.org_id and o.deleted = 0
        and o.domain is not null and o.domain != ''
        and o.end_date > DATE_SUB(CURDATE(), INTERVAL 1 DAY))
    </select>

    <select id="deptPosCount" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosCountBean">
        select count(d.id) as totalQty,count(p.id) as heirQty from udp_dept d
        left join bk_heir_pos p on p.id = d.id and p.heir_valid_qty > 0
        where d.org_id = #{orgId} and d.deleted = 0
    </select>

    <select id="userPosList" resultType="com.yxt.talent.bk.core.heir.bean.HeirUserPosBriefBean">
        select u.prepare_level_id,u.pos_id,case when p.pos_type = 0 then p.pos_name else d.name end as name,p.pos_type,d.id_full_path
        from bk_heir_pos_user u
        join bk_heir_pos p on p.id = u.pos_id and p.deleted = 0
        left join udp_dept d on d.id = u.pos_id and p.pos_type = 1 and d.deleted = 0
        where u.org_id = #{orgId} and u.user_id = #{userId} and u.heir_status = 0
        and u.deleted = 0 and (p.pos_type = 0 or d.id is not null)
    </select>

    <select id="listPage4Open" resultType="com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosBean">
        select h.id,h.org_id,h.pos_type,case when h.pos_type = 1 then d.name else p.name end as pos_name,
               case when h.pos_type = 1 then d.id else p.id end as deptOrPosid,
               h.risk_rule_type,h.risk_level_id,h.heir_target_qty,h.heir_valid_qty,h.create_time,h.update_time,h.deleted
        from bk_heir_pos h
        left join udp_dept d on d.id = h.id and h.pos_type = 1 and d.deleted = 0
        left join udp_position p on p.id = h.id and h.pos_type = 0
        where h.org_id = #{orgId}
        and h.deleted = 0 and (h.pos_type = 0 or (d.id is not null and h.heir_valid_qty > 0))
        and ((h.pos_type = 0 and p.id is not null and p.id &lt;&gt; '')
        or (h.pos_type = 1 and d.id is not null and d.id &lt;&gt; ''))
        order by h.id
    </select>

    <select id="queryDeptOrPosIdByIds" resultType="com.yxt.talent.bk.core.udp.bean.IdAndPosIdBean">
        select h.id,case when h.pos_type = 1 then d.id else p.id end as deptOrPosId
        from bk_heir_pos h
        left join udp_dept d on d.id = h.id and h.pos_type = 1
        left join udp_position p on p.id = h.id and h.pos_type = 0
        where h.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listByOrgId" resultType="com.yxt.talent.bk.core.heir.entity.HeirPosEntity">
        select p.* from bk_heir_pos p
        left join udp_dept d on d.id = p.id and d.deleted = 0 and p.pos_type = 1
        where p.deleted = 0 and (p.pos_type = 0 or d.id is not null)
    </select>
</mapper>
