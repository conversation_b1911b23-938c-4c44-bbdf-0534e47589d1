package com.yxt.talent.bk.svc.heir.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * RiskLevelValueBean
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 8:57 pm
 */
@Data
public class RiskLevelValueDTO {
    private Long id;
    @Schema(description = "百分比")
    private BigDecimal value;

    @Schema(description = "自定义名称（简体）")
    @JsonIgnore
    private String levelName;
    @Schema(description = "颜色设置")
    @JsonIgnore
    private String colorCode;
}
