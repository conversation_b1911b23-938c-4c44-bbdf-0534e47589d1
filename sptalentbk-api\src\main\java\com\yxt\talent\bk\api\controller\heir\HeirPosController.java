package com.yxt.talent.bk.api.controller.heir;

import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.spsdk.audit.annotations.EasyAuditLogSelect;
import com.yxt.spsdk.common.utils.YxtExportUtils;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.dashboard.bean.DashboardPersonalDetailExportParam;
import com.yxt.talent.bk.core.heir.bean.HeirPosCountBean;
import com.yxt.talent.bk.core.heir.bean.HeirUserPosBriefBean;
import com.yxt.talent.bk.core.heir.bean.PosExportParam;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.mq.RocketMqProducerRepository;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.svc.export.SyncExportEnum;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.bean.req.HeirPosEditBean;
import com.yxt.talent.bk.svc.heir.bean.req.HeirPosTreeReq;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPosNodeBean;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPosTreeResp;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirUserPosBriefResp;
import com.yxt.talent.bk.svc.heir.bean.resp.OpenHeirPosResp;
import com.yxt.talent.bk.svc.heir.bean.resp.OpenHeirPosUserResp;
import com.yxt.talent.bk.svc.heir.bean.resp.PosBenchmarkUserResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * HeirPosController
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 10:36 am
 */
@RestController
@RequestMapping("/mgr/heir/pos")
@AllArgsConstructor
@Tag(name = "继任地图")
@Slf4j
public class HeirPosController extends BaseController {

    private final HeirPosService heirPosService;
    private final HeirPosRepository heirPosRepository;
    private final RocketMqProducerRepository rocketMqProducerRepository;

    @Operation(summary = "添加继任岗位")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_ADD_POS)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @PostMapping("/add")
    public List<String> addPos(@RequestBody List<String> positionIds) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("HeirAddPos userId : {} req: {}", user.getUserId(), JSON.toJSONString(positionIds));
        return heirPosService.addPos(user, positionIds);
    }

    @Operation(summary = "继任岗位编辑")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_MOD_POS, paramExp = "#editBean")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN)
    @PutMapping("/update")
    public void updatePos(@Valid @RequestBody HeirPosEditBean editBean) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("HeirUpdatePos userId : {} req: {}", user.getUserId(), JSON.toJSONString(editBean));
        heirPosService.updatePos(user, editBean);
    }

    @Operation(summary = "继任部门编辑")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_MOD_DEPT, paramExp = "#editBean")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN)
    @PutMapping("/deptUpdate")
    public void deptUpdatePos(@Valid @RequestBody HeirPosEditBean editBean) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("HeirDeptUpdatePos userId : {} req: {}", user.getUserId(), JSON.toJSONString(editBean));
        heirPosService.updateDeptPos(user, editBean);
    }

    @Operation(summary = "继任岗位删除")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_DEL_POS)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN)
    @DeleteMapping("/del/{posId}")
    public void delPos(@PathVariable String posId, @RequestParam boolean childrenAsRoot) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("HeirUpdatePos userId {} posId {}, childrenAsRoot {}", user.getUserId(), posId, childrenAsRoot);
        heirPosService.delPos(user, posId, childrenAsRoot);
    }

    @Operation(summary = "继任地图(岗位或者部门)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @PostMapping("/pos_tree")
    public HeirPosTreeResp posTree(@RequestParam int posType, @RequestBody HeirPosTreeReq treeReq) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheDetail());
        log.info("HeirPosTree userId : {} posType : {}", user.getUserId(), posType);
        return heirPosService.posBriefTree(user, posType, treeReq);
    }

    @Operation(summary = "继任地图(岗位或者部门)导出")
    @Auditing
    @EasyAuditLogSelect({@EasyAuditLog(value = AuditLogConstants.HEIR_POS_DETAIL_EXPORT, conditionExp = "#posType == 0"),
            @EasyAuditLog(value = AuditLogConstants.HEIR_DEPT_DETAIL_EXPORT, conditionExp = "#posType == 1")})
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @PostMapping("/pos_export")
    public void posExport(@RequestParam int posType, @RequestBody HeirPosTreeReq treeReq) {
        UserCacheDetail user = getUserCacheDetail();
        log.info("heirPosExport userId : {} posType : {}", user.getUserId(), posType);
        PosExportParam param = new PosExportParam();
        param.setPosType(posType);
        BeanCopierUtil.copy(treeReq, param);
        YxtExportUtils.prepareSyncExport(user, null, SyncExportEnum.HEIR_POS_EXPORT, param);
        rocketMqProducerRepository.send(TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_SYNC,
                BeanHelper.bean2Json(param, JsonInclude.Include.NON_NULL));
    }

    @Operation(summary = "继任地图详情(岗位或者部门)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @PostMapping("/pos_detail")
    public List<HeirPosNodeBean> posDetail(@RequestParam int posType, @RequestBody List<String> posIds) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("HeirPosDetail userId : {} posIds : {}", user.getUserId(), posIds);
        return heirPosService.queryPosDetail(user.getOrgId(), posType, posIds);
    }

    @Operation(summary = "继任标杆用户ids")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @GetMapping("/benchmark_user/{posId}")
    public List<UdpUserBriefBean> benchmarkUser(@PathVariable String posId,
                                                @RequestParam int posType) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("benchmarkUser userId : {} {posId} : {}", user.getUserId(), posId);
        return heirPosService.allBenchmarkUserId(user, posId, posType);
    }

    @Operation(summary = "继任标杆用户(启用/不启用)")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_BENCHMARK_USER_ENABLE, paramExp = "{'posId':#posId,'userId':#userId,'enabled':#enabled}")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @PutMapping("/benchmark_user/enabled/{posId}")
    public void benchmarkUserEnabled(@PathVariable String posId,
                                     @RequestParam String userId,
                                     @RequestParam boolean enabled) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("benchmarkUserEnabled userId : {} posId : {} enabled : {}", user.getUserId(), posId, enabled);
        heirPosService.addBenchmarkUser(user, posId, userId, enabled);
    }

    @Operation(summary = "继任岗位用户(标杆用户标记)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @GetMapping("/position_user/{posId}")
    public PagingList<PosBenchmarkUserResp> listPositionUser(@PathVariable String posId,
                                                             @RequestParam(required = false) String name) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("listPositionUser userId : {} posId : {}", user.getUserId(), posId);
        return heirPosService.listPositionUser(getPage(), user.getOrgId(), posId, name);
    }

    @Operation(summary = "继任汇总数量")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @GetMapping("/dept/summary")
    public HeirPosCountBean deptSummary() {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("deptSummary userId : {} ", user.getUserId());
        return heirPosRepository.deptPosCount(user.getOrgId());
    }

    @Operation(summary = "用户继任详情")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    @GetMapping("/user/heir_brief")
    public HeirUserPosBriefResp userPosBrief(@RequestParam String userId) {
        UserBasicBean user = UserBasicBean.createBy(getUserCacheBasic());
        log.info("UserHeirBrief userId : {} ", user.getUserId());
        List<HeirUserPosBriefBean> list = heirPosService.userPosList(user.getOrgId(), userId);
        HeirUserPosBriefResp ret = new HeirUserPosBriefResp();
        ret.setDeptList(list.stream().filter(item -> item.getPosType() != HeirPosTypeEnum.POSITION.getType())
            .collect(Collectors.toList()));
        ret.setPositionList(list.stream().filter(item -> item.getPosType() == HeirPosTypeEnum.POSITION.getType())
            .collect(Collectors.toList()));
        return ret;
    }

    @Operation(summary = "继任brief列表(T+1)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/brief_list")
    public List<OpenHeirPosResp> allHeir(@RequestParam int posType,
                                         @RequestParam(required = false) String deptId) {
        String orgId = getUserCacheDetail().getOrgId();
        TalentbkUtil.isUuid(deptId, "apis.talentbk.param.error");
        return heirPosService.allHeir4Model(orgId, deptId, posType);
    }

    @Operation(summary = "(继任用户/标杆用户)brief列表(T+1)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/user_brief_list")
    public List<OpenHeirPosUserResp> heirUserList(@RequestParam String posId, @RequestParam int userType) {
        String orgId = getUserCacheDetail().getOrgId();
        TalentbkUtil.isUuid(posId, "apis.talentbk.param.error");
        return heirPosService.heirUser4Model(orgId, posId, userType, null);
    }

    @Operation(summary = "直接记录日志")
    @Parameter(name = "auditLogType", description = "0：岗位地图导出，1：部门地图导出", in = ParameterIn.QUERY)
    @Auditing
    @EasyAuditLogSelect({@EasyAuditLog(value = AuditLogConstants.HEIR_POS_EXPORT, conditionExp = "#auditLogType == 0"),
            @EasyAuditLog(value = AuditLogConstants.HEIR_DEPT_EXPORT, conditionExp = "#auditLogType == 1")})
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_EXPORT, type = AuthType.TOKEN)
    @GetMapping("/direct_audit_log")
    public int directAuditLog(@RequestParam int auditLogType) {
        return auditLogType;
    }
}
