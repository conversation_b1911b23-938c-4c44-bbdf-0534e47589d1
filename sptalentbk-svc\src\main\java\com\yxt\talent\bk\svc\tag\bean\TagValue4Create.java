package com.yxt.talent.bk.svc.tag.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/***
 * <AUTHOR>
 * @since 2022/8/10 16:12
 */
@Data
@Schema(name = "分层标签值")
public class TagValue4Create {

    @Schema(description = "分层标签值id")
    private String id;

    @Schema(description = "标签值/分层标签值")
    private String valueName;

    @Schema(description = "排序")
    private Integer orderIndex;
}
