package com.yxt.talent.bk.core.usergroup.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.usergroup.bean.SchemeRuleVO;
import com.yxt.talent.bk.core.usergroup.entity.SearchScheme;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SearchSchemeMapper extends BaseMapper<SearchScheme> {
    int updateBatch(List<SearchScheme> list);

    int updateBatchSelective(List<SearchScheme> list);

    int batchInsert(@Param("list") List<SearchScheme> list);

    int insertOrUpdate(SearchScheme record);

    int insertOrUpdateSelective(SearchScheme record);


    List<SchemeRuleVO> findSchemeRuleVO(@Param("orgId") String orgId, @Param("userId") String userId);

    @Select("select scheme_name from bk_search_scheme where id = #{id}")
    String getNameById(@Param("id") Long id);
}
