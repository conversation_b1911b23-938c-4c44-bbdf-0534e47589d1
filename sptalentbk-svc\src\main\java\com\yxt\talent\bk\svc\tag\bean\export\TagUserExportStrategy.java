package com.yxt.talent.bk.svc.tag.bean.export;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.ExcelUtil;
import com.yxt.export.OutputStrategy;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 *  标签人员导出模板
 * <AUTHOR>
 * @since 2022/8/10 13:40
 * @version 1.0
 */
@Component
@AllArgsConstructor
public class TagUserExportStrategy implements OutputStrategy {

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        TagUser4AdapterExport tagUser4AdapterExport = (TagUser4AdapterExport) data;

        if (tagUser4AdapterExport.getTagType() == 0) {
            Map headers = TalentbkUtil.getAnyMessage(null,
                    "apis.talentbk.tag.user.export.tem.head.username",
                    "apis.talentbk.tag.user.export.tem.head.fullname",
                    "apis.talentbk.tag.user.export.tem.head.statusStr",
                    "apis.talentbk.tag.user.export.tem.head.departmentName",
                    "apis.talentbk.tag.user.export.tem.head.positionName");
            ExcelUtil.exportWithTemplate(headers,
                    tagUser4AdapterExport.getData(), filePath, ExportConstants.TAG_USER_EXPORT_TEMPLATE_PATH_4_SINGLE);
        } else {
            Map headers = TalentbkUtil.getAnyMessage(null,
                    "apis.talentbk.tag.user.export.tem.head.username",
                    "apis.talentbk.tag.user.export.tem.head.fullname",
                    "apis.talentbk.tag.user.export.tem.head.statusStr",
                    "apis.talentbk.tag.user.export.tem.head.departmentName",
                    "apis.talentbk.tag.user.export.tem.head.positionName",
                    "apis.talentbk.tag.user.export.tem.head.tagValues");
            ExcelUtil.exportWithTemplate(headers,
                    tagUser4AdapterExport.getData(), filePath, ExportConstants.TAG_USER_EXPORT_TEMPLATE_PATH_4_MUL);
        }
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE).appCode(ModuleConstants.APP_CODE)
                .moduleCode(ModuleConstants.MODULE_CODE).fileName(fileName).name("标签人员导出").build();
    }
}
