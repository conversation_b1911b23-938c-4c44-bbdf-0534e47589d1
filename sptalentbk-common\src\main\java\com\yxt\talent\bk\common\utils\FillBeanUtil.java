package com.yxt.talent.bk.common.utils;

import com.yxt.export.bean.FillBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/29 17:56
 * @version 1.0
 */
public class FillBeanUtil {


    @SuppressWarnings("unchecked")
    public static List<FillBean> generationFillBenList(Object data){

        List<FillBean> fillBeanList = new ArrayList<>();
        List<Object> listObj = null;
        if(data instanceof List) {
            listObj = (List<Object>)data ;
            for(Object obj : listObj) {
                FillBean temp = FillBean.builder()
                        .data(obj).header(null)
                        .build();
                fillBeanList.add(temp);
            }
        }

        return fillBeanList;
    }

    private FillBeanUtil() {
    }
}
