package com.yxt.talent.bk.common.es;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AbstractAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.SourceFilter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户提取nativeQuery中的查询参数，组装成一个json返回，方便日志跟踪，排查问题
 */
@Slf4j
@Component
public class ESQueryConveter {

    public static final String[] EMPTY_STRING_ARRAY = new String[0];

    public String extractJson(NativeSearchQuery nativeSearchQuery) {
        try {
            SearchRequest searchRequest = buildSearchRequest(nativeSearchQuery);
            return searchRequest.source().toString();
        } catch (Exception e) {
            log.error("LOG11160:", e);
        }
        return "";
    }

    private SearchRequest buildSearchRequest(NativeSearchQuery nativeSearchQuery) {
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(nativeSearchQuery.getIndices().toArray(EMPTY_STRING_ARRAY));
        searchRequest.types(nativeSearchQuery.getTypes().toArray(EMPTY_STRING_ARRAY));

        searchRequest.searchType(SearchType.DEFAULT);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        if (nativeSearchQuery.getQuery() != null) {
            searchSourceBuilder.query(nativeSearchQuery.getQuery());
        } else {
            searchSourceBuilder.query(QueryBuilders.matchAllQuery());
        }

        Pageable pageable = nativeSearchQuery.getPageable();
        if (pageable != null && pageable.isPaged()) {
            searchSourceBuilder.from(Math.toIntExact(pageable.getOffset()));
            searchSourceBuilder.size(pageable.getPageSize());
        }

        SourceFilter sourceFilter = nativeSearchQuery.getSourceFilter();
        if (sourceFilter != null) {
            searchSourceBuilder.fetchSource(sourceFilter.getIncludes(), sourceFilter.getExcludes());
        }

        //noinspection rawtypes
        List<AbstractAggregationBuilder> aggregations = nativeSearchQuery.getAggregations();
        if (aggregations != null) {
            aggregations.forEach(searchSourceBuilder::aggregation);
        }

        searchRequest.source(searchSourceBuilder);

        return searchRequest;
    }

}
