package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * DwdHeirPosResp
 *
 * <AUTHOR> geyan
 * @Date 14/9/23 6:30 pm
 */
@Data
public class DwdHeirPosResp {

    @JsonProperty("thirdPosId")
    @Schema(description = "pk-岗位或部门id")
    private String posId;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "0:岗位，1:部门")
    private Integer posType;

    @Schema(description = "名称")
    private String posName;

    @Schema(description = "目标继任数量,0:未设置")
    private Integer heirTargetQty;

    @Schema(description = "有效人数")
    private Integer heirValidQty;

    @Schema(description = "风险规则名称")
    private String riskLevelName;

    @Schema(description = "风险规则颜色")
    private String riskLevelColor;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date updateTime;

    @Schema(description = "是否删除.0：未删除，1：已删除")
    private Integer deleted;
}
