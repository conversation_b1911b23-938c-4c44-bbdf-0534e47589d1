package com.yxt.talent.bk.api.component;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.service.ILock;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.talent.bk.common.constants.*;
import com.yxt.talent.bk.common.utils.UserSearchUtils;
import com.yxt.talent.bk.core.tag.bean.*;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import com.yxt.talent.bk.core.tag.entity.UserTagEntity;
import com.yxt.talent.bk.core.tag.entity.UserTagValueEntity;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.TagValueRepository;
import com.yxt.talent.bk.svc.tag.TagService;
import com.yxt.talent.bk.svc.tag.UserTagService;
import com.yxt.talent.bk.svc.tag.UserTagValueService;
import com.yxt.talent.bk.svc.tag.bean.DelUserTagBean;
import com.yxt.talent.bk.svc.tag.bean.Tag4Create;
import com.yxt.talent.bk.svc.tag.bean.TagBean;
import com.yxt.talent.bk.svc.tag.bean.UserTagBean;
import com.yxt.talent.bk.svc.tag.bean.export.TagUser4AdapterExport;
import com.yxt.talent.bk.svc.tag.bean.export.TagUser4Export;
import com.yxt.talent.bk.svc.tag.bean.export.TagUserExportStrategy;
import com.yxt.talent.bk.svc.tag.enums.TagTypeEnum;
import com.yxt.talent.bk.svc.tag.enums.TagValueChooseModelEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户标签component
 *
 * <AUTHOR>
 * @since 2022/8/15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserTagComponent {

    private final UserTagService userTagService;
    private final UserTagValueService userTagValueService;
    private final TagService tagService;
    private final ILock lockService;
    private final I18nComponent i18nComponent;
    private final DlcComponent dlcComponent;
    private final TagUserExportStrategy tagUserExportStrategy;
    private final TagRepository tagRepository;
    private final TagValueRepository tagValueRepository;

    /**
     * 最大导出条数
     */
    private static final long EXPORT_MAX_SIZE = 30000L;

    /**
     * 给用户贴标签
     *
     * @param orgId       机构id
     * @param userTagBean 用户标签信息
     * @param operatorId  操作人id
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void tagUser(String orgId, UserTagBean userTagBean, String operatorId) {
        List<String> userIds = userTagBean.getUserIds();
        List<TagBean> tagBeanList = userTagBean.getTagBeanList();
        Set<String> tagIds = StreamUtil.map2set(tagBeanList, TagBean::getTagId);

        Map<String, List<UserTagBaseBean>> existsTagUserMap = userTagService.getTagUserMap(orgId, tagIds, userIds);
        Set<String> tagValueIds = userTagBean.listTagValueId();
        Map<String, List<UserTagValueBaseBean>> existsTagValueUserMap = userTagValueService
                .getTagValueUserMap(orgId, tagValueIds, userIds);
        Map<String, List<TagInfoBean>> existsTagInfoMap = tagService.getTagMap(orgId, tagIds);

        Set<String> delTagIdSet = Sets.newConcurrentHashSet();
        Set<String> delTagValueIdSet = Sets.newConcurrentHashSet();
        // 本次新增的userTag关系
        List<UserTagEntity> userTagList = Collections.synchronizedList(Lists.newArrayList());
        //
        List<UserTagValueEntity> userTagValueList = Collections.synchronizedList(Lists.newArrayList());

        tagBeanList.stream().forEach(tagBean -> {
            String tagId = tagBean.getTagId();
            userTagList.addAll(
                userTagService.initUserTagList(orgId, tagId, existsTagUserMap.get(tagId), userIds, operatorId)
            );
            List<String> tagValueIdList = tagBean.getTagValueIdList();
            userTagValueList.addAll(
                userTagValueService.initUserTagValueList(
                    orgId, tagId, tagValueIdList, existsTagValueUserMap.get(tagId), userIds, operatorId
                )
            );
            List<TagInfoBean> existTagInfoBeans = existsTagInfoMap.get(tagId);
            if (CollectionUtils.isEmpty(existTagInfoBeans)) {
                throw new ApiException(BkApiErrorKeys.ERROR_KEY_TAG_IS_NULL);
            }
            TagInfoBean tagInfoBean = existTagInfoBeans.get(0);
            if (Objects.equals(TagTypeEnum.TYPE_1.getType(), tagInfoBean.getTagType()) &&
                Objects.equals(TagValueChooseModelEnum.CHOOSE_MODEL_0.getType(), tagInfoBean.getValueChooseModel())) {
                // 如果是分层标签，且是单选，需要把之前选的用户标签值给删除
                delTagIdSet.add(tagId);
                Set<String> tagValueIdSet = StreamUtil.map2set(existTagInfoBeans, TagInfoBean::getTagValueId);
                if (CollectionUtils.isNotEmpty(tagValueIdSet)) {
                    tagValueIdSet.remove(tagValueIdList.get(0));
                }
                delTagValueIdSet.addAll(tagValueIdSet);
            }
        });
        if (userIds.size() == 1) {
            handleDelTag(userTagBean.getDelTagBeanList(), delTagIdSet, delTagValueIdSet);
        }
        if (CollectionUtils.isNotEmpty(delTagIdSet) && CollectionUtils.isNotEmpty(delTagValueIdSet)) {
            log.debug("LOG10660:{}", delTagIdSet);
            log.debug("LOG10670:{}", delTagValueIdSet);
            userTagValueService.delTagValueUser(orgId, delTagIdSet, userIds, operatorId, delTagValueIdSet);
        }
        if (CollectionUtils.isNotEmpty(userTagList)) {
            userTagService.batchSave(userTagList);
        }
        if (CollectionUtils.isNotEmpty(userTagValueList)) {
            userTagValueService.batchSave(userTagValueList);
        }
        handleUserDelTag(orgId, userIds, operatorId, userTagBean);
        log.debug("LOG10630:orgId={}, userTagBean={}", orgId, BeanHelper.bean2Json(userTagBean));
        userTagService.tagUser(orgId, userTagBean);
    }

    private void handleDelTag(List<TagBean> delTagBeanList, Set<String> delTagIdSet, Set<String> delTagValueIdSet) {
        if (CollectionUtils.isEmpty(delTagBeanList)) {
            return;
        }
        delTagBeanList.forEach(tagBean -> {
            delTagIdSet.add(tagBean.getTagId());
            delTagValueIdSet.addAll(tagBean.getTagValueIdList());
        });
    }

    /**
     * 处理用户需要删除的标签
     *
     * @param orgId      机构id
     * @param userIdList 用户id列表
     * @param operatorId 操作人id
     */
    private void handleUserDelTag(String orgId, List<String> userIdList, String operatorId, UserTagBean userTagBean) {
        Set<String> tagIds = userTagBean.listDelTagId();
        if (1 == userIdList.size() && CollectionUtils.isNotEmpty(tagIds)) {
            Map<String, List<UserTagBaseBean>> userTagValueMap = userTagValueService
                    .getUserTagValueMap(orgId, tagIds, userIdList);
            List<String> delUserIds = Lists.newArrayListWithCapacity(1);
            userIdList.forEach(userId -> {
                List<UserTagBaseBean> userTagBaseBeans = userTagValueMap.get(userId);
                if (CollectionUtils.isEmpty(userTagBaseBeans)) {
                    delUserIds.add(userId);
                }
            });
            if (CollectionUtils.isNotEmpty(delUserIds)) {
                // 用户标签值已全部删除，需要同步删除用户标签
                userTagService.delUserTag(orgId, tagIds, delUserIds, operatorId);
            }
            userTagService.delSingleUserTag(orgId, userIdList.get(0), userTagBean);
        }
    }

    /**
     * ES 查询
     * @param orgId
     * @param keyword
     * @param tagId
     * @param tagValueId
     * @param pageRequest
     * @return
     */
    public PagingList<UserBaseInfoBean> listPageTagUserForEs(String orgId, String keyword, String tagId, String tagValueId,
                                                        PageRequest pageRequest) {

        PagingList<UserBaseInfoBean> result = new PagingList<>();
        TagEntity tagEntity = tagRepository.queryTagById(orgId, tagId);
        List<TagValueEntity> tagValueEntityList = tagValueRepository.findByTagIds(orgId, Arrays.asList(tagId));
        Map<String, String> valueNameMap = tagValueEntityList.stream().collect(Collectors.toMap(TagValueEntity::getValueName, TagValueEntity::getId));
        Map<String, String> valueIdMap = tagValueEntityList.stream().collect(Collectors.toMap(TagValueEntity::getId, TagValueEntity::getValueName));
        Page<UserTagListBean4Es> page = tagService.listPageTagUserForEs(orgId, keyword, tagId, tagValueId, pageRequest);

        // 转换对象
        List<UserBaseInfoBean> list = new ArrayList<>();
        page.getContent().forEach(bean ->{
            UserBaseInfoBean temp = new UserBaseInfoBean();
            String [] fields= {"diylabels","leadershipStyle"};
            BeanCopierUtil.copy(bean,temp,fields,false);
            temp.setFullname(bean.getCnname());
            temp.setStatus(StringUtils.isBlank(bean.getUserStatus()) ? 0 : Integer.valueOf(bean.getUserStatus()) );

            try {
                String tageKey = tagEntity.getTagKey();
                Map<String, String> xxGroupTagEnumMap = UserSearchConstants.getXxGroupTagEnumMap();
                if(xxGroupTagEnumMap.containsKey(tageKey)){
                    tageKey = xxGroupTagEnumMap.get(tageKey);
                }else if(!UserSearchConstants.DIRECTLY.contains(tageKey)){
                    tageKey = UserSearchConstants.EsLabelLevelName.DIYLABELS.getValue();
                }

                Field field = bean.getClass().getDeclaredField(tageKey);
                field.setAccessible(true);
                if(field.getType() == List.class){
                    // 任职风险 职业驱动力 性格特点 等
                    List<String> tempValueList = (List<String>) field.get(bean);
                    List<UserTagBaseBean> userTagBaseBeanList =
                            generateUserTagBaseBeanList(tempValueList,tagId,temp.getUserId(),valueIdMap,valueNameMap);
                    temp.setTagBaseBeanList(userTagBaseBeanList);
                }else if(field.getType() == Map.class){
                    Map<String,List<String>> diylablesMap = (Map<String,List<String>>) field.get(bean);
                    Set<String> tempValueList = diylablesMap.get(tagEntity.getTagKey()).stream().collect(Collectors.toSet());
                    List<UserTagBaseBean> userTagBaseBeanList =
                            generateUserTagBaseBeanList(tempValueList,tagId,temp.getUserId(),valueIdMap,valueNameMap);
                    temp.setTagBaseBeanList(userTagBaseBeanList);
                } else if(field.getType() == String.class){ // UserTagListBean4Es 中 String 类型的单选标签

                    String tvName = (String) field.get(bean);
                    if(tagEntity.getTagKey().equals(UserSearchConstants.XxGroupTagEnum.AGE_GROUP.getKey())) {
                        tvName = UserSearchUtils.dateToAgeTag(DateUtil.formatDate(tvName));
                    }
                    if(UserSearchConstants.ES_SCORE_RATIO.equals(tagEntity.getTagKey())
                            || UserSearchConstants.ES_LRNDURATION_RATIO.equals(tagEntity.getTagKey())){
                        tvName =  UserSearchUtils.valueToRatio(tvName);
                    }
                    if(UserSearchConstants.ES_UNSPACED_LRN.equals(tagEntity.getTagKey())){
                        tvName =  UserSearchUtils.valueToUnspacecLrn(tagEntity.getTagName(), tvName);
                    }

                    UserTagBaseBean userTagBaseBean = new UserTagBaseBean();
                    userTagBaseBean.setTagId(tagId);
                    userTagBaseBean.setUserId(temp.getUserId());
                    userTagBaseBean.setTagValueId(valueNameMap.get(tvName));
                    userTagBaseBean.setTagValueName(tvName);
                    temp.setTagBaseBeanList(Arrays.asList(userTagBaseBean));

                }
            }catch (Exception e) {
                log.error(e.getMessage(),e);
            }

            list.add(temp);
        });

        // 构建设置Paging信息
        Paging paging = new Paging();
        paging.setOffset((pageRequest.getCurrent() - 1) * pageRequest.getSize());
        paging.setLimit(pageRequest.getSize());
        paging.setCount(page.getTotalElements());
        paging.setPages(page.getTotalPages());
        result.setDatas(list);
        result.setPaging(paging);

        return result;
    }

    private List<UserTagBaseBean> generateUserTagBaseBeanList(Collection<String> collection,String tagId,String userId,
                     Map<String, String> valueIdMap ,
                     Map<String, String> valueNameMap){

        List<UserTagBaseBean> userTagBaseBeanList = new ArrayList<>();

        collection.forEach(tv -> {
            UserTagBaseBean userTagBaseBean = new UserTagBaseBean();
            userTagBaseBean.setTagId(tagId);
            userTagBaseBean.setUserId(userId);
            String tvid = "";
            String tvName = "";
            if(valueIdMap.containsKey(tv)){
                tvid = tv;
                tvName = valueIdMap.get(tvid);
            }else{
                tvName = tv;
                tvid = valueNameMap.get(tvName);
            }
            userTagBaseBean.setTagValueId(tvid);
            userTagBaseBean.setTagValueName(tvName);
            userTagBaseBeanList.add(userTagBaseBean);
        });

        return userTagBaseBeanList;
    }

    /**
     * 删除用户标签
     *
     * @param orgId          机构id
     * @param delUserTagBean 标签用户信息
     * @param operatorId     操作人id
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void delUserTag(String orgId, DelUserTagBean delUserTagBean, String operatorId) {
        List<String> userIdList = delUserTagBean.getUserIdList();
        String tagId = delUserTagBean.getTagId();
        List<String> tagIds = Lists.newArrayList(tagId);
        Set<String> tagValueIdSet = Sets.newHashSet(delUserTagBean.getTagValueId());
        userTagValueService.delTagValueUser(orgId, tagIds, userIdList, operatorId, tagValueIdSet);
        Map<String, List<UserTagBaseBean>> userTagValueMap = userTagValueService
                .getUserTagValueMap(orgId, tagIds, userIdList);
        List<String> delUserIds = Collections.synchronizedList(Lists.newArrayListWithCapacity(userIdList.size()));
        userIdList.parallelStream().forEach(userId -> {
            List<UserTagBaseBean> userTagBaseBeans = userTagValueMap.get(userId);
            if (CollectionUtils.isEmpty(userTagBaseBeans)) {
                delUserIds.add(userId);
            }
        });
        if (CollectionUtils.isNotEmpty(delUserIds)) {
            // 用户标签值已全部删除，需要同步删除用户标签
            userTagService.delUserTag(orgId, tagIds, delUserIds, operatorId);
        }
        delUserTagBean.setTagValueIdSet(tagValueIdSet);
        userTagService.delEsUserTag(orgId, delUserTagBean, operatorId);
    }

    /**
     * 删除标签值
     *
     * @param orgId       机构id
     * @param operatorId  操作人id
     * @param tagId       标签id
     * @param tagValueIds 删除的标签值id列表
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void delTagValueId(String orgId, String operatorId, String tagId, Collection<String> tagValueIds) {
        List<String> allUserIdList = userTagValueService.listTagValueAllUserId(orgId, tagId, tagValueIds);
        if (CollectionUtils.isEmpty(allUserIdList)) {
            return;
        }
        DelUserTagBean delUserTagBean = DelUserTagBean.builder().tagId(tagId)
                .tagValueIdSet(Sets.newHashSet(tagValueIds)).build();
        List<String> tagIds = Lists.newArrayList(tagId);
        List<List<String>> splitUserIdList = Lists.partition(allUserIdList, 200);
        splitUserIdList.forEach(userIdList -> {
            userTagValueService.delTagValueUser(orgId, tagIds, userIdList, operatorId, tagValueIds);
            Map<String, List<UserTagBaseBean>> userTagValueMap = userTagValueService
                    .getUserTagValueMap(orgId, tagIds, userIdList);
            List<String> delUserIds = Collections.synchronizedList(Lists.newArrayListWithCapacity(userIdList.size()));
            userIdList.parallelStream().forEach(userId -> {
                List<UserTagBaseBean> userTagBaseBeans = userTagValueMap.get(userId);
                if (CollectionUtils.isEmpty(userTagBaseBeans)) {
                    delUserIds.add(userId);
                }
            });
            if (CollectionUtils.isNotEmpty(delUserIds)) {
                // 用户标签值已全部删除，需要同步删除用户标签
                userTagService.delUserTag(orgId, tagIds, delUserIds, operatorId);
            }
            delUserTagBean.setUserIdList(userIdList);
            userTagService.delEsUserTag(orgId, delUserTagBean, operatorId);
        });
    }

    /**
     * 导出用户标签<br>
     *     ES 数据源
     * @param orgId
     * @param userId
     * @param keyword
     * @param tagId
     * @param tagValueId
     * @return
     */
    public Map<String, String> exportUserTag(String orgId,String userId, String keyword, String tagId, String tagValueId){

        String lockKey = String.format(TalentBkRedisKeys.TALENTBK_TAG_VALUE_USER_EXPORT_CACHE_KEY, orgId, userId);
        String filePath;
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                TagUser4AdapterExport tagUser4AdapterExport = getExportData(orgId, keyword, tagId, tagValueId);
                String fileName = i18nComponent.getI18nValue(ExportConstants.TAG_USER_EXPORT_TEMPLATE_FILE_NAME)
                                + System.currentTimeMillis()
                                + ExportConstants.FILE_SUFFIX_XLSX;
                long taskId = dlcComponent.prepareExport(fileName, tagUserExportStrategy);
                filePath = dlcComponent.upload2Disk(fileName, tagUser4AdapterExport, tagUserExportStrategy, taskId);
                log.debug("exportUserTag-path={}", filePath);
            } catch (ApiException e){
                throw e;
            } catch (Exception e) {
                log.error("标签人员模板导出异常", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }

        Map<String, String> map = new HashMap<>(1);
        map.put(ExportConstants.EXPORT_URL_KEY, filePath);
        return map;
    }

    private TagUser4AdapterExport getExportData(String orgId, String keyword, String tagId, String tagValueId){

        PageRequest pageRequest = new PageRequest();
        pageRequest.setCurrent(1);
        pageRequest.setSize(EXPORT_MAX_SIZE);

        long count = tagService.countTagUserForEs(orgId, keyword, tagId, tagValueId, pageRequest);
        if (count > EXPORT_MAX_SIZE) {
            throw new ApiException("apis.talentbk.usertag.export.overrange");
        }

        PagingList<UserBaseInfoBean> userBaseInfoBeanPagingList = listPageTagUserForEs(orgId, keyword, tagId, tagValueId, pageRequest);
        List<TagUser4Export> exportDatas = new ArrayList<>();
        userBaseInfoBeanPagingList.getDatas().forEach(bean -> {
            TagUser4Export exportBean = new TagUser4Export();
            BeanCopierUtil.copy(bean,exportBean);

            StringBuilder tagValuesSb = new StringBuilder();
            bean.getTagBaseBeanList().forEach(a->
                tagValuesSb.append(a.getTagValueName()).append(",")
            );
            String tagValues = tagValuesSb.length() > 0 ? tagValuesSb.substring(0,tagValuesSb.length()-1) : "";

            exportBean.setTagValues(tagValues);
            nullDataFormat(exportBean);
            exportDatas.add(exportBean);

        });

        TagUser4AdapterExport tagUser4AdapterExport = new TagUser4AdapterExport();
        Tag4Create tagDetail = tagService.getTagDetail(orgId, tagId);
        tagUser4AdapterExport.setTagType(tagDetail.getTagType());
        tagUser4AdapterExport.setData(exportDatas);

        return tagUser4AdapterExport;
    }

    private void nullDataFormat(TagUser4Export data) {
        if (StringUtils.isBlank(data.getUsername())) {
            data.setUsername("-");
        }
        if (StringUtils.isBlank(data.getFullname())) {
            data.setFullname("-");
        }
        if (StringUtils.isBlank(data.getDepartmentName())) {
            data.setDepartmentName("-");
        }
        if (StringUtils.isBlank(data.getPositionName())) {
            data.setPositionName("-");
        }
        if (StringUtils.isBlank(data.getTagValues())) {
            data.setTagValues("-");
        }
    }

    public PagingList<UserBaseInfoBean> listPageTagUse(String orgId, String keyword, String tagId, String tagValueId,
        PageRequest pageRequest) {
        IPage<UserBaseInfoBean> pageResults = userTagService.listPageTagValueUser(pageRequest, orgId, keyword,
            tagId, tagValueId);
        return BeanCopierUtil.toPagingList(pageResults);
    }
}

