alter table bk_heir_pos_prepare_cfg add column rule_cfg_md5 varchar(32) COMMENT '规则配置md5';

alter table bk_heir_pos_user add column modify_status tinyint NOT NULL DEFAULT -1 COMMENT '-1:重置,0:暂不处理,1:已处理';

CREATE TABLE `bk_package_data` (
`id` bigint(20) unsigned NOT NULL COMMENT '雪花id',
`org_id` char(36) NOT NULL DEFAULT '' COMMENT '机构id',
`biz_type` varchar(16) NOT NULL DEFAULT '' COMMENT '业务类型',
`master_id` varchar(36) NOT NULL DEFAULT '' COMMENT '业务数据id',
`biz_data_md5` varchar(32) NOT NULL DEFAULT '' COMMENT '业务数据包md5',
`biz_data` mediumtext COMMENT '业务数据包',
`create_time` datetime(3) NOT NULL COMMENT '创建时间',
`update_time` datetime(3) NOT NULL COMMENT '修改时间',
`db_create_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据创建时间（数据库专用，禁止用于业务）',
`db_update_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据修改时间（数据库专用，禁止用于业务）',
PRIMARY KEY (`id`),
KEY `idx_biz_data_md5` (`org_id`, `biz_type`, `biz_data_md5`, `master_id`)
) COMMENT='业务数据包';

CREATE TABLE `bk_heir_prepare_his` (
`id` bigint(20) unsigned NOT NULL COMMENT '雪花id',
`org_id` char(36) NOT NULL DEFAULT '' COMMENT '机构id',
`pos_id` varchar(36) NOT NULL DEFAULT '' COMMENT 'bk_heir_pos pkId',
`user_id` char(36) NOT NULL DEFAULT '' COMMENT '继任用户id',
`opt_user_id` char(36) NOT NULL DEFAULT '' COMMENT '准备度调整用户id',
`modify_status` tinyint NOT NULL DEFAULT 0 COMMENT '0:暂不处理,1:已处理',
`user_level_id` bigint(20) unsigned DEFAULT NULL COMMENT '调整前准备度规则等级id',
`change_level_id` bigint(20) unsigned DEFAULT NULL COMMENT '调整的准备度规则等级id',
`rule_data_id` bigint(20) unsigned COMMENT '规则数据包id',
`match_data_id` bigint(20) unsigned COMMENT '匹配结果数据包id',
`create_user_id` char(36) NOT NULL DEFAULT '' COMMENT '创建人id',
`create_time` datetime(3) NOT NULL COMMENT '创建时间',
`update_user_id` char(36) NOT NULL DEFAULT '' COMMENT '修改人id',
`update_time` datetime(3) NOT NULL COMMENT '修改时间',
`db_create_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据创建时间（数据库专用，禁止用于业务）',
`db_update_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据修改时间（数据库专用，禁止用于业务）',
PRIMARY KEY (`id`),
KEY `idx_pos_user_ctime` (`pos_id`, `user_id`, `create_time`)
) COMMENT='继任准备度调整历史';

ALTER TABLE bk_heir_org_level_cfg ADD num_range_max DECIMAL(8,2) NULL COMMENT '数值范围最大(<this';
ALTER TABLE bk_heir_org_level_cfg ADD num_range_min DECIMAL(8,2) NULL COMMENT '数值范围最小(>=this';

create or replace view udp_lite_user_sp as
select *
from udp.udp_user_account
where main_platform = 1 and account_type in (1, 2);
