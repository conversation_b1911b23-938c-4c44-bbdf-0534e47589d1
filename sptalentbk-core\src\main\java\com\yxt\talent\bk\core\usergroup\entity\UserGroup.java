package com.yxt.talent.bk.core.usergroup.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 用户群组;
 */
@Getter
@Setter
@TableName(value = "bk_user_group")
public class UserGroup {
    /**
     * 唯一 ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = BkDemoConstants.BK_GROUP_ID)
    private Long id;

    /**
     * 企业id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 群组类型（1:静态，2：动态）
     */
    @TableField(value = "group_type")
    private Integer groupType;

    /**
     * 群组名称
     */
    @TableField(value = "group_name")
    private String groupName;

    /**
     * 是否启用，（0：未启用，1：已启用）
     */
    @TableField(value = "enabled")
    private Integer enabled;

    /**
     * 描述
     */
    @TableField(value = "group_desc")
    private String groupDesc;

    /**
     * 搜索规则 ID
     */
    @TableField(value = "search_rule_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_SEARCH_RULE_ID)
    private Long searchRuleId;

    /**
     * 重新计算时间
     */
    @TableField(value = "caculate_time")
    private LocalDateTime caculateTime;

    /**
     * 群组用户数量
     */
    @TableField(value = "user_count")
    private Integer userCount;

    /**
     * 动态群组计算周期，1：日，2：每周，3：每月
     */
    @TableField(value = "caculate_job")
    private Integer caculateJob;

    /**
     * 动态群组计算周期，为 2 时，表示周一到周日。为 3 时，是具体某一天
     */
    @TableField(value = "caculate_date")
    private Integer caculateDate;

    /**
     * 方案 ID
     */
    @TableField(value = "scheme_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_SEARCH_SCHEME_ID)
    private Long schemeId;

    /**
     * 基础信息搜索 json
     */
    @TableField(value = "base_search")
    private String baseSearch;

    /**
     * 是否是从我的团队-组织人才库中创建的群组,此类群组在人才群组中不可见
     */
    @TableField(value = "team_group")
    private Integer teamGroup;
}
