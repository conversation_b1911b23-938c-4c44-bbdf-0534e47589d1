package com.yxt.talent.bk.common.bean;

import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanCopierUtil;
import jodd.util.StringPool;
import lombok.Data;

/**
 * UserBasicBean
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 11:15 am
 */
@Data
public class UserBasicBean {
    private String orgId;
    private String userId;
    private String userType;
    private String loginType;
    private String mainOrgId;
    /**
     * 是否是管理员:0-否，1-是
     */
    private String admin;
    private String locale;

    public static UserBasicBean createBy(UserCacheBasic userCache) {
        UserBasicBean ret = new UserBasicBean();
        BeanCopierUtil.copy(userCache, ret);
        return ret;
    }

    public static UserBasicBean createBy(UserCacheDetail userCache) {
        UserBasicBean ret = new UserBasicBean();
        BeanCopierUtil.copy(userCache, ret);
        return ret;
    }

    public boolean isAdminUser() {
        return StringPool.ONE.equals(admin);
    }
}
