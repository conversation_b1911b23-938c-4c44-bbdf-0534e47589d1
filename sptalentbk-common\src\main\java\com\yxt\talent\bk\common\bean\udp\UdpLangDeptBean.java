package com.yxt.talent.bk.common.bean.udp;

import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import lombok.Data;

/**
 * UdpLangUserBean
 *
 * <AUTHOR> harley<PERSON>
 * @Date 30/4/24 1:51 pm
 */
@Data
public class UdpLangDeptBean implements L10NContent {
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String id;
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String name;
}
