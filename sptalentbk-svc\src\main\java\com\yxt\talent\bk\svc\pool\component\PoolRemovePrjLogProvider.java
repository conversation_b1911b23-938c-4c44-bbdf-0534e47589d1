package com.yxt.talent.bk.svc.pool.component;

import com.yxt.o2ofacade.bean.response.talent.SpProjectInfoResp;
import com.yxt.o2ofacade.service.ProjectFacade;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.StringConcatBuilder;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.talent.bk.svc.pool.bean.PoolProjectBindBean;
import com.yxt.talent.bk.svc.pool.bean.PoolRemovePrj4Log;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * PoolRemovePrjLogProvider
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 3:42 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class PoolRemovePrjLogProvider implements AuditLogDataProvider<PoolProjectBindBean, PoolRemovePrj4Log> {
    private final PoolRepository poolRepository;
    private final ProjectFacade projectFacade;
    @Override
    public PoolRemovePrj4Log before(PoolProjectBindBean param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public PoolRemovePrj4Log after(PoolProjectBindBean param, AuditLogBasicBean logBasic) {
        PoolRemovePrj4Log prj4Log = new PoolRemovePrj4Log();
        List<Long> prjIds = param.getProjectIds().stream().map(prjId -> CommonUtils.tryParseLong(prjId, null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (!prjIds.isEmpty()) {
            StringConcatBuilder pjNames = new StringConcatBuilder(StringPool.COMMA);
            List<SpProjectInfoResp> spProjectInfoResps = projectFacade.listSpProjects(logBasic.getOrgId(), prjIds);
            for (SpProjectInfoResp spPjInfo : spProjectInfoResps) {
                pjNames.append(spPjInfo.getName()).appendConcat();
            }
            prj4Log.setProjectNames(pjNames.output());
        }
        return prj4Log;
    }

    @Override
    public Pair<String, String> entityInfo(PoolProjectBindBean param, PoolRemovePrj4Log beforeObj, PoolRemovePrj4Log afterObj, AuditLogBasicBean logBasic) {
        if (afterObj == null) {
            //没有操作后数据时
            afterObj = after(param, logBasic);
        }
        return Pair.of(param.getPoolId(),
                String.format("人才池-%s-%s", poolRepository.getNameById(param.getPoolId()), afterObj.getProjectNames()));
    }
}
