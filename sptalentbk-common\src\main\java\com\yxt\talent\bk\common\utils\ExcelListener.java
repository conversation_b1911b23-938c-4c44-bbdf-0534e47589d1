package com.yxt.talent.bk.common.utils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ExcelListener extends AnalysisEventListener<Object> {

    private List<Map<Integer, String>> data = new ArrayList<>();

    private List<Map<Integer, String>> header = new ArrayList<>();

    @Override
    public void invoke(Object o, AnalysisContext analysisContext) {
        if (o instanceof Map) {
            Map m = (Map)o;
            int mapSize =  m.size();
            Map<Integer, String> map = new HashMap<>(mapSize);
            for (int i = 0; i < mapSize; i++) {
                Object v = m.get(i);
                map.put(i, null);
                // 这样能保证map的size和excel列相同
                if (Objects.nonNull(v)) {
                    map.put(i, (String)v);
                }
            }
            data.add(map);
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        header.add(headMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // Do nothing
    }
}
