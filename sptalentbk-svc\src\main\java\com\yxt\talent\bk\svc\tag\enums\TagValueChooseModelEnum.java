package com.yxt.talent.bk.svc.tag.enums;

import lombok.Getter;

/**
 * 标签值选择类型枚举类
 *
 * <AUTHOR>
 * @since 2022/8/11
 */
public enum TagValueChooseModelEnum {
    /**
     * 标签值选择类型(0-单选,1-多选)
     */
    CHOOSE_MODEL_0(0, "单选"), CHOOSE_MODEL_1(1, "多选");

    @Getter
    private int type;

    @Getter
    private String name;

    TagValueChooseModelEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

}
