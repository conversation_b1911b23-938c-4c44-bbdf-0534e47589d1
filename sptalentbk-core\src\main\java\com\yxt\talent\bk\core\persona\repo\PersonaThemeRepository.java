package com.yxt.talent.bk.core.persona.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeEntity;
import com.yxt.talent.bk.core.persona.mapper.PersonaThemeMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class PersonaThemeRepository extends ServiceImpl<PersonaThemeMapper, PersonaThemeEntity> {

    /**
     * 查询机构下主题个数
     * @param orgId
     * @return
     */
    public List<PersonaThemeEntity> queryTheme(String orgId,int sourceType) {
        LambdaQueryWrapper<PersonaThemeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonaThemeEntity::getOrgId, orgId);
        if (sourceType != -1) {
            queryWrapper.eq(PersonaThemeEntity::getThemeSource, sourceType);
        }
        return list(queryWrapper);
    }
}
