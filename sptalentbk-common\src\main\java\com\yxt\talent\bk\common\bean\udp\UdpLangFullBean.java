package com.yxt.talent.bk.common.bean.udp;

import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NKeyPath;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.annotation.L10NValuePath;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import lombok.Data;

import java.util.function.Consumer;

/**
 * UdpLangFullBean
 *
 * <AUTHOR> harleyge
 * @Date 30/4/24 2:52 pm
 */
@Data
public class UdpLangFullBean implements L10NContent {
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String id;
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;
    private Consumer<String> fullnameSetter;

    @L10NKeyPath(ignoreFirstKey = true)
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String deptId;
    @L10NValuePath
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;
    private Consumer<String> deptNameSetter;

    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionName;
    private Consumer<String> positionNameSetter;

    public void setFullname(String fullname) {
        this.fullname = fullname;
        if (fullnameSetter != null) {
            fullnameSetter.accept(fullname);
        }
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
        if (deptNameSetter != null) {
            deptNameSetter.accept(deptName);
        }
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
        if (positionNameSetter != null) {
            positionNameSetter.accept(positionName);
        }
    }
}
