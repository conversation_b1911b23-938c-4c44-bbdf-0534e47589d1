package com.yxt.talent.bk.svc.persona.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "维度详情")
public class Dimension4List {
    @Schema(description = "画像主题-维度主键")
    private String dimensionId;
    @Schema(description = "画像主题id")
    private String themeId;
    @Schema(description = "画像主题-维度来源(0-内置,1-自建)")
    private Integer dimensionSource;
    @Schema(description = "画像主题-维度名称")
    private String dimensionName;
    @Schema(description = "标签列表")
    private List<DimensionTagMap4List> tagMap;
}
