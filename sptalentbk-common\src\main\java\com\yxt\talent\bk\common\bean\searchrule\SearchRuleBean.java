package com.yxt.talent.bk.common.bean.searchrule;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.assertj.core.util.Lists;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Data
public class SearchRuleBean {

    @Schema(description = "筛选规则id，没有传空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ruleId;

    /**
     * 标签搜索类型，1：交集，2：并集
     */
    @Schema(description = "标签搜索类型，1：交集，2：并集", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @Range(min = 1, max = 2, message = "apis.talentbk.userGroup.args.error")
    private Integer tagSearchType;

    /**
     * 标签搜索 json
     */
    @Schema(description = "标签搜索 json", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<SPTagSearchBean> tagSearch;

    public void initSearch() {
        if (tagSearchType == null) {
            tagSearchType = 1;
        }
        if (tagSearch == null) {
            tagSearch = Lists.newArrayList();
        }
    }
}
