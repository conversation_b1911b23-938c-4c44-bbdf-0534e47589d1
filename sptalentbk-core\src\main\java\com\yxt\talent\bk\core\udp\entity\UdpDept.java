package com.yxt.talent.bk.core.udp.entity;

import lombok.Data;

import java.util.Date;

@Data
public class UdpDept {

    /**
     * 微服务小UDP表sample表主键
     */
    private String id;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 父部门id
     */
    private String parentId;

    /**
     * name
     */
    private String name;

    /**
     * 部门编号(内部给每个部门的编号)-不超过20位
     */
    private String code;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 部门id全路径
     */
    private String idFullPath;

    /**
     * 部门路径,如000001.000002.000003或000004.000005)
     */
    private String routingPath;

    /**
     * 第三方系统ID
     */
    private String thirdId;

    /**
     * 排序顺序
     */
    private Integer orderIndex;

    /**
     * 是否包含虚拟部门(0-否,1-是)
     */
    private Integer hasVirtualDept;

    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    private String updateUserId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-否,1-是)
     */
    private Integer deleted;

}
