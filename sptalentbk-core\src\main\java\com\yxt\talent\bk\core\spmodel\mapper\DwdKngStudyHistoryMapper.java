package com.yxt.talent.bk.core.spmodel.mapper;

import com.yxt.talent.bk.core.spmodel.entity.DwdKngStudyHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程学习历史表(DwdKngStudyHistory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 10:58:37
 */
public interface DwdKngStudyHistoryMapper {

    int countByUserId(@Param("orgId") String orgId, @Param("userId") String userId);

    List<DwdKngStudyHistory> listByUserId(@Param("orgId") String orgId, @Param("userId") String userId, @Param("offset") long offset, @Param("limit") long limit);
}

