<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdSspAccSummaryMapper">
    <select id="queryByUserId" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdSspAccSummary">
        select user_id, real_acq_score, actual_point, study_hour
        from dwd_ssp_acc_summary
        where org_id = '${orgId}'
        <if test="userId != null and userId != ''">
            and user_id = '${userId}'
        </if>
        <if test="userId == null or userId == ''">
            <!--@ignoreSql-->
            and 1 != 1
        </if>
    </select>
</mapper>
