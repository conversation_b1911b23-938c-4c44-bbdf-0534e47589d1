package com.yxt.talent.bk.core.heir.repo;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPrepareCfgMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * HeirPosPrepareCfgRepository
 *
 * <AUTHOR> harleyge
 * @Date 19/9/24 4:38 pm
 */
@Slf4j
@Repository
@AllArgsConstructor
public class HeirPosPrepareCfgRepository extends ServiceImpl<HeirPosPrepareCfgMapper, HeirPosPrepareCfgEntity> {

    public List<HeirPosPrepareCfgEntity> findByPosIds(String orgId, Collection<String> posIds) {
        if (CollectionUtils.isEmpty(posIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HeirPosPrepareCfgEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(HeirPosPrepareCfgEntity::getOrgId, orgId);
        queryWrapper.in(HeirPosPrepareCfgEntity::getPosId, posIds);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<HeirPosPrepareCfgEntity> getQueryWrapper() {
        LambdaQueryWrapper<HeirPosPrepareCfgEntity> wrapper = new LambdaQueryWrapper<>();
        return wrapper.eq(HeirPosPrepareCfgEntity::getDeleted, YesOrNo.NO.getValue());
    }
}
