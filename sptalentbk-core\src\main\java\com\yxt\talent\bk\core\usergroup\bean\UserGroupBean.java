package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.bk.common.bean.searchrule.BaseSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * @program: sptalentbkapi
 * @description: 群组创建 Bean
 **/
@Data
public class UserGroupBean {

    /**
     * 唯一 ID
     */
    @Schema(description = "更新时使用，唯一 ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 群组类型（1:静态，2：动态）
     */
    @NotNull(message = "apis.talentbk.userGroup.type.notblank")
    @Range(min = 1, max = 2, message = "apis.talentbk.userGroup.args.error")
    @Schema(description = "群组类型（1:静态，2：动态）", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer groupType;

    /**
     * 群组名称
     */
    @NotBlank(message = "apis.talentbk.userGroup.name.notblank")
    @Size(max = 50, message = "apis.talentbk.userGroup.name.size")
    @Pattern(regexp = "^[a-zA-Z0-9\u4e00-\u9fa5]+$", message = "apis.talentbk.userGroup.name.regex")
    @Schema(description = "群组名称, 在我的团队-人才看板-人才库那边也用作人才库名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String groupName;

    /**
     * 是否启用，（0：未启用，1：已启用）
     */
    @Schema(description = "是否启用，（0：未启用，1：已启用）,默认未启用", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @Range(min = 0, max = 1, message = "apis.talentbk.userGroup.args.error")
    private Integer enabled = 0;

    /**
     * 描述
     */
    @Size(max = 500, message = "apis.talentbk.userGroup.desc.size")
    @Schema(description = "描述", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String groupDesc;

    @Schema(description = "负责人", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 10, message = "apis.talentbk.userGroup.managers.size")
    private List<String> userManagers;

    @Schema(description = "重新计算周期：1：每日，2：每周，3：每月", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @Range(min = 1, max = 3, message = "apis.talentbk.userGroup.args.error")
    private Integer caculateJob;

    @Schema(description = "动态群组计算周期，为 2 时，表示周一到周日。为 3 时，是具体某一天", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @Range(min = 1, max = 31, message = "apis.talentbk.userGroup.args.error")
    private Integer caculateDate;

    @Schema(description = "方案 ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long schemeId;

    @Schema(description = "搜索规则", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private SearchRuleBean searchRuleBean;

    /**
     * 基础信息搜索 json
     */
    @Schema(description = "基础信息搜索 json", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private BaseSearchBean baseSearch;

    @Schema(description = "是否是从团队人才库创建的群组, 这种群组在我的群里列表中不可见", hidden = true)
    private Integer teamGroup;

}
