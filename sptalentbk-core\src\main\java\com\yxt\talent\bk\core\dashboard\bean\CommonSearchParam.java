package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class CommonSearchParam {

    @Schema(description = "部门ID集合", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> deptIds;

    @Schema(description = "用户组ID集合", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<Long> userGroupIds;
}
