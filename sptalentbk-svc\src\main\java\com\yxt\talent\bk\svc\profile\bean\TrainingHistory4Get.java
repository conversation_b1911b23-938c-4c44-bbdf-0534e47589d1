package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.common.Constants;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlOrder;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Setter
@Getter
@SqlTable("dwd_training_history")
@Schema(name = "人才画像-学习培训历史")
public class TrainingHistory4Get {

    @Schema(description = "主键Id")
    private String id;

    @Schema(description = "绚星2.0平台用户id")
    private String userId;

    @JsonIgnore
    @Schema(description = "三方用户id")
    private String thirdUserId;

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "培训开始时间")
    @SqlOrder(order = 1, direction = SqlOrder.DESC)
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime startTime;

    @Schema(description = "培训结束时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime endTime;

    @Schema(description = "完成状态（0-未完成 1-进行中, 2-已完成）")
    private Integer completionStatus;

}
