package com.yxt.talent.bk.svc.heir.bean;

import com.yxt.spsdk.common.annotation.SpExcelProperty;
import lombok.Data;

@Data
public class HeirPositionExportBean {
    @SpExcelProperty(name = "岗位名称", nameKey = "apis.talentbk.heir.export.position.name", index = 0)
    private String name;
    @SpExcelProperty(name = "在职人数", nameKey = "apis.talentbk.heir.export.position.userQty", index = 1)
    private Integer userQty;
    @SpExcelProperty(name = "岗位标杆人员", nameKey = "apis.talentbk.heir.export.position.benchmarkUsers", index = 2)
    private String benchmarkUsers;
    @SpExcelProperty(name = "继任者姓名", nameKey = "apis.talentbk.heir.export.user.fullname", index = 3)
    private String fullname;
    @SpExcelProperty(name = "继任者账号", nameKey = "apis.talentbk.heir.export.user.username", index = 4)
    private String username;
    @SpExcelProperty(name = "继任者部门", nameKey = "apis.talentbk.heir.export.user.deptNamePath", index = 5)
    private String deptNamePath;
    @SpExcelProperty(name = "继任者岗位", nameKey = "apis.talentbk.heir.export.user.positionName", index = 6)
    private String positionName;
    @SpExcelProperty(name = "账号状态", nameKey = "apis.talentbk.heir.export.user.statusDesc", index = 7, i18nValue = true)
    private String statusDesc;
    @SpExcelProperty(name = "继任状态", nameKey = "apis.talentbk.heir.export.user.heirStatusDesc", index = 8, i18nValue = true)
    private String heirStatusDesc;
    @SpExcelProperty(name = "准备度", nameKey = "apis.talentbk.heir.export.user.prepareLevelName", index = 10)
    private String prepareLevelName;
}
