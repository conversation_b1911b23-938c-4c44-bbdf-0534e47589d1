<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yxt.usdk.bom</groupId>
        <artifactId>usdk-bom-waf</artifactId>
        <version>1.2.7-jdk17</version>
    </parent>

    <groupId>com.yxt</groupId>
    <artifactId>sptalentbk-parent</artifactId>
    <version>6.5.1-jdk17</version>
    <packaging>pom</packaging>

    <name>sptalentbk-parent</name>
    <description>新人发-奇点</description>

    <modules>
        <module>sptalentbk-api</module>
        <module>sptalentbk-common</module>
        <module>sptalentbk-core</module>
        <module>sptalentbk-svc</module>
    </modules>

    <properties>
        <root.basedir>${user.dir}</root.basedir>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>

        <json-lib.version>2.4</json-lib.version>
        <asmclient.version>1.0</asmclient.version>
        <xmlbeans.version>3.1.0</xmlbeans.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <lombok.version>1.18.24</lombok.version>

        <spring-cloud-starter-openfeign.version>2.2.5.RELEASE</spring-cloud-starter-openfeign.version>
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
        <maven-antrun-plugin.version>1.8</maven-antrun-plugin.version>
        <usdk.jedis.version>4.3.2</usdk.jedis.version>
        <redisson.version>3.23.3</redisson.version>
        <usdk.redisson-spring-boot-starter>3.23.3</usdk.redisson-spring-boot-starter>
        <spring.cloud.alibaba.version>2022.0.0.0</spring.cloud.alibaba.version>

        <usdk.knife4j-openapi3-jakarta-spring-boot-starter.version>4.4.0-yxt.1</usdk.knife4j-openapi3-jakarta-spring-boot-starter.version>
        <usdk.knife4j-openapi3-ui.version>4.4.0-yxt.1</usdk.knife4j-openapi3-ui.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-cluster</artifactId>
            <version>2.2.0-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-idworker</artifactId>
            <version>2.2.4-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-job</artifactId>
            <version>2.3.8-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt.usdk.components</groupId>
            <artifactId>usdk-components-rocketmq</artifactId>
            <version>2.1.1-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-trace</artifactId>
            <version>2.4.2-jdk17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>10.1.5</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-export</artifactId>
            <version>2.3.10-jdk17</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>o2oapi-facade</artifactId>
            <version>5.6-jdk17</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yxt</groupId>
                    <artifactId>waf-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>polestarapi-facade</artifactId>
            <version>2.4.6-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>coreapi-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.usdk.components</groupId>
            <artifactId>usdk-components-es6configure</artifactId>
            <version>1.0.0-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>udpapi-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>bifrost-sdk-udp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sp-sdk</artifactId>
            <version>1.4.0-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentbk-facade</artifactId>
            <version>6.4-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt.modelhub</groupId>
            <artifactId>modelhub-api</artifactId>
            <version>1.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>ikit-i18n</artifactId>
            <version>1.0.9</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yxt</groupId>
                    <artifactId>waf-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yxt.ubiz</groupId>
            <artifactId>ubiz-auth</artifactId>
            <version>1.3.5</version>
        </dependency>
    </dependencies>



    <dependencyManagement>
        <dependencies>
            <!-- talent jar -->
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>sptalentbk-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>sptalentbk-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>sptalentbk-svc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>waf-export</artifactId>
                <version>2.3.10-jdk17</version>
                <exclusions>
                    <exclusion>
                        <artifactId>poi</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi-ooxml</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>waf-job</artifactId>
                <version>2.3.8-jdk17</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>waf-mq</artifactId>
                <version>2.3.9-jdk17</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>waf-trace</artifactId>
                <version>2.4.2-jdk17</version>
            </dependency>

            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>asmclient</artifactId>
                <version>${asmclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.ubiz</groupId>
                <artifactId>ubiz-export</artifactId>
                <version>1.3.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.xiaoymin</groupId>
                        <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.xiaoymin</groupId>
                        <artifactId>knife4j-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.yxt</groupId>
                        <artifactId>waf-base</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- facade -->
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>udp-facade</artifactId>
                <version>1.1.23</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sf.json-lib</groupId>
                        <artifactId>json-lib</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>udpapi-facade</artifactId>
                <version>2.9.10</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>bifrost-sdk-udp</artifactId>
                <version>1.0.6-jdk17</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>coreapi-facade</artifactId>
                <version>3.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>polestarapi-facade</artifactId>
                <version>2.4.6-jdk17</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>spevalapi-facade</artifactId>
                <version>5.6.1-jdk17</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>spmodelapi-facade</artifactId>
                <version>1.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>sptalentrv-facade</artifactId>
                <version>5.6.2-jdk17</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>sptalentapi-facade</artifactId>
                <version>4.6.3-jdk17</version>
            </dependency>


            <!-- thirdparty -->
            <dependency>
                <groupId>org.apache.xmlbeans</groupId>
                <artifactId>xmlbeans</artifactId>
                <version>${xmlbeans.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${spring-cloud-starter-openfeign.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.json-lib</groupId>
                <artifactId>json-lib</artifactId>
                <version>${json-lib.version}</version>
                <classifier>jdk15</classifier>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- 此插件可以用来批量管理子模块版本 -->
                <!-- 执行mvn versions:set -DnewVersion=xxx 即可批量修改module的版本-->
                <!-- 执行mvn versions:display-dependency- updates 项目依赖有哪些可用的更新 -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>2.11.0</version>
                    <configuration>
                        <generateBackupPoms>false</generateBackupPoms>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>clean</flattenMode>
                    </configuration>
                    <executions>
                        <execution>
                            <!-- Flatten and simplify our own POM for install/deploy -->
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten-clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>copy</id>
                            <phase>package</phase>
                            <configuration>
                                <tasks>
                                    <copy todir="../target/">
                                        <!-- project.build.directory表示各个模块的target目录 -->
                                        <fileset dir="${project.build.directory}">
                                            <!-- 需要复制的jar包文件名称 -->
                                            <include name="*.jar"/>
                                        </fileset>
                                    </copy>

                                </tasks>
                            </configuration>
                            <goals>
                                <goal>run</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
