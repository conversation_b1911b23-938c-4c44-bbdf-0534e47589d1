package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * 课程学习历史表(DwdKngStudyHistory)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 10:58:37
 */
@Data
@TableName(value = "dwd_kng_study_history")
public class DwdKngStudyHistory {
    /**
     * 主键id
     */
    @TableField(value = "id")
    private String id;
    /**
     * 三方用户 id
     */
    @TableField(value = "third_user_id")
    private String thirdUserId;

    @TableField(value = "user_id")
    private String userId;

    /**
     * 机构 id
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 课程/课件 id
     */
    @TableField(value = "kng_id")
    private String kngId;
    /**
     * 课程/课件名称
     */
    @TableField(value = "title")
    private String title;
    /**
     * 课程/课件类型，0.课程，1.文档，2.视频，3.音频，4.微课，5.scorm，6.html，7.压缩包，8.外链课
     */
    @TableField(value = "kng_type")
    private Integer kngType;
    /**
     * 学习来源，o2o：培训，kng：在线课堂，gwnl：人才发展，flip：面授
     */
    @TableField(value = "target_code")
    private Integer targetCode;
    /**
     * 学习进度
     */
    @TableField(value = "study_schedule")
    private BigDecimal studySchedule;
    /**
     * 最近学习时间
     */
    @TableField(value = "last_study_time")
    private LocalDateTime lastStudyTime;
    /**
     * 累计学习时长（秒）
     */
    @TableField(value = "sum_acc_hours")
    private Long sumAccHours;
    /**
     * 是否删除(0-否,1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;


}
