package com.yxt.talent.bk.core.dashboard.bean;

import com.yxt.spsdk.common.bean.AsyncExportBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DashboardPersonalDetailExportParam extends AsyncExportBase<DashboardPersonalDetailExportParam> {
    private List<String> userIds;

    private String orgId;

    private List<String> deptIds;


    private List<Long> userGroupIds;

    private String locale;

    private String optUserId;

    private String admin;


    @Override
    public DashboardPersonalDetailExportParam strategyData() {
        return this;
    }
}
