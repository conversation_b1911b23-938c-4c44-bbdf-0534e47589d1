<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirLangI18nMapper">
    <update id="deleteByGroupId">
        update bk_heir_lang_i18n set deleted = 1,update_time = now()
        where org_id=#{orgId} and group_id=#{groupId}
    </update>

    <insert id="batchInsert">
        insert into bk_heir_lang_i18n
        (id, org_id, group_id, lang_code, lang_value, create_user_id, create_time, update_user_id,update_time,deleted)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id}, #{item.orgId}, #{item.groupId}, #{item.langCode},
            #{item.langValue},#{item.createUserId},#{item.createTime},
            #{item.updateUserId},#{item.updateTime},#{item.deleted})
        </foreach>
    </insert>

    <select id="selectByGroupIds" resultType="com.yxt.talent.bk.core.heir.entity.HeirLangI18nEntity">
        select * from bk_heir_lang_i18n where org_id=#{orgId} and group_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0
    </select>
</mapper>
