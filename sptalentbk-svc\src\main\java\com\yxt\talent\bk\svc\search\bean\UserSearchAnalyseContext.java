package com.yxt.talent.bk.svc.search.bean;

import com.yxt.talent.bk.core.search.bean.UserSearchAnalyseDimensionBean;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import lombok.Data;
import org.elasticsearch.search.aggregations.Aggregations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 透视-聚合上下文变量
 */
@Data
public class UserSearchAnalyseContext {

    /**
     * 机构Id
     */
    private String orgId;
    /**
     * 当前用户id
     */
    private String userId;
    /**
     * 透视项条数，前端传递的
     */
    private int limit;
    /**
     * 前端传递
     */
    private int offset;
    /**
     * 对那个维度执行分页，点击分页页码时候，前端传递
     */
    private String dimensionKey;
    /**
     * 透视搜索条件，前端传递
     */
    private UserSearchBean search;
    /**
     * ES聚合结果
     */
    private Aggregations aggregations;
    /**
     * 聚合返回值列表
     */
    private List<UserSearchListAnalyseBean> result = new ArrayList<>();
    /**
     * 常规透视维度
     */
    private List<UserSearchAnalyseDimensionBean> adList = new ArrayList<>();
    /**
     * 客户自定义标签透视维度集合
     */
    private List<UserSearchAnalyseDimensionBean> adOrdinaryList = new ArrayList<>();
    /**
     * 维度排序Map<key,orderIndex>
     */
    private Map<String, Integer> dimensionOrderMap = new HashMap<>();
    /**
     * 标签值键值对Map<tagId,TagValueEntityList>
     */
    private Map<String, List<TagValueEntity>> tagValueMap = new HashMap<>();
    /**
     * 标签key 和 标签id映射 Map<tagKey, tagId>
     */
    private Map<String, String> tagKeyMap = new HashMap<>();

}
