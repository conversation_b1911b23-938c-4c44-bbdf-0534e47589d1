package com.yxt.talent.bk.core.spmodel.mapper;

import com.yxt.talent.bk.core.spmodel.entity.DwdDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DwdDeptMapper
 *
 * <AUTHOR> harleyge
 * @Date 13/6/24 5:16 pm
 */
@Mapper
public interface DwdDeptMapper {

    List<DwdDept> getDeptPage(@Param("orgId") String orgId, @Param("deptIds") List<String> deptIds,
            @Param("offset") long offset, @Param("pageSize") long pageSize);

    long getDeptTotalCount(@Param("orgId") String orgId, @Param("deptIds") List<String> deptIds);

    List<DwdDept> getByIds(@Param("orgId") String orgId, @Param("deptIds") List<String> deptIds);

    List<DwdDept> getByDeptIds(@Param("orgId") String orgId, @Param("deptIds") List<String> deptIds);
}
