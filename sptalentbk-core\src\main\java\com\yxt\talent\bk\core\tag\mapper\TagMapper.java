package com.yxt.talent.bk.core.tag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.bk.core.tag.bean.*;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
public interface TagMapper extends BaseMapper<TagEntity> {

    List<TagSimpleBean> findTagMsg(@Param("orgId") String orgId, @Param("tagIds") List<String> tagIds);

    List<TagSimpleBean> fingSelfTag(@Param("orgId") String orgId, @Param("tagIds") List<String> tagIds);

    List<TagSimpleBean> findTag4Search(@Param("orgId") String orgId, @Param("catalogId") String catalogId,
            @Param("tagIds") List<String> tagIds, @Param("tagNames") Collection<String> tagNames,
            @Param("tagValueNames") Collection<String> tagValueNames);

    /**
     * 获取标签信息
     *
     * @param orgId  机构id
     * @param tagIds 标签id列表
     * @return 标签信息
     */
    List<TagInfoBean> listTag(@Param("orgId") String orgId, @Param("tagIds") Collection<String> tagIds);

    /**
     * 获取标签信息
     *
     * @param orgId  机构id
     * @param tagIds 标签id列表
     * @return 标签信息
     */
    List<TagInfoBean> listTagValue(@Param("orgId") String orgId, @Param("tagIds") Collection<String> tagIds);


    IPage<Tag4Page> findTag4Page(
        @Param("page") IPage<Tag4Page> page, @Param("orgId") String orgId, @Param("tagSearchBean") TagSearchBean tagSearchBean);

    int tagNameCheck(@Param("orgId") String orgId, @Param("tagName") String tagName, @Param("id") String id);

    List<TagValueSimpleBean> findfindTagValueList(@Param("orgId") String orgId, @Param("tagKey") String tagKey);
    Set<String> findCatalogIdByShowTypeInCatalogIds(@Param("orgId") String orgId, @Param("catalogIds") Collection<String> catalogIds);
    List<String> findTagNameLike(@Param("orgId") String orgId, @Param("keyword") String keyword);
    List<String> findTagNameByIds(@Param("orgId") String orgId, @Param("ids") Collection<String> ids);


    List<TagNameBean> findSelfTagByOrgId(@Param("orgId") String orgId);

}
