package com.yxt.talent.bk.svc.rpc;

import com.yxt.spevalfacade.bean.demo.OrgCopyBean;
import com.yxt.spevalfacade.bean.enums.DemoIdMapKeyEnum;
import com.yxt.spevalfacade.service.SpEvalApiFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * EvalRpc
 *
 * <AUTHOR> harleyge
 * @Date 10/10/24 9:52 am
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EvalRpc {
    private final SpEvalApiFacade spEvalApiFacade;

    public Map<String, String> getDemoIdInfo(String sourceOrgId, String targetOrgId) {
        try {
            OrgCopyBean demoReqBean = new OrgCopyBean();
            demoReqBean.setSourceOrgId(sourceOrgId);
            demoReqBean.setTargetOrgId(targetOrgId);
            demoReqBean.setIdMapKey(DemoIdMapKeyEnum.MAP_KEY_SPEVAL_EVALUATION_ID.getIdMapKey());
            return spEvalApiFacade.getEntityIdMap(demoReqBean);
        } catch (Exception e) {
            log.warn("获取eval映射关系失败:{}", targetOrgId, e);
        }
        return null;
    }
}
