package com.yxt.talent.bk.core.pool.bean;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Setter
@Getter
@Schema(name = "绑定培训项目入参")
public class Training4Bind {

    @NotEmpty(message = BkApiErrorKeys.TALENT_BK_PROJECT_BIND_TRAINING_USER_ID_EMPTY)
    @Schema(description = "添加到培训项目中的学员id")
    private List<String> userIds;

    @NotEmpty(message = BkApiErrorKeys.TALENT_BK_PROJECT_BIND_TRAINING_ID_EMPTY)
    @Schema(description = "要绑定的培训项目id，支持绑定同时多个培训项目")
    private List<String> trainingIds;

}
