package com.yxt.talent.bk.core.heir.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.bk.common.bean.EntityDeletedBean;
import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.bean.HeirPosCountBean;
import com.yxt.talent.bk.core.heir.bean.HeirPosPIdBean;
import com.yxt.talent.bk.core.heir.bean.HeirPosRawBean;
import com.yxt.talent.bk.core.heir.bean.HeirUserPosBriefBean;
import com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosBean;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosExt;
import com.yxt.talent.bk.core.udp.bean.IdAndPosIdBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * HeirPosMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 2:30 pm
 */
@Mapper
public interface HeirPosMapper extends BkBaseMapper<HeirPosEntity> {

    List<EntityDeletedBean> existIds(@Param("orgId") String orgId, @Param("ids") List<String> ids);

    List<HeirPosPIdBean> selectPIdList(@Param("orgId") String orgId, @Param("posType") int posType);

    int replacePId(@Param("orgId") String orgId,
                   @Param("posType") int posType,
                   @Param("fromParentId") String fromParentId,
                   @Param("toParentId") String toParentId);

    void removePosRelated(@Param("orgId") String orgId,
                          @Param("posId") String posId);

    List<HeirPosRawBean> allPos(@Param("orgId") String orgId,
                                @Param("posType") int posType);

    IPage<HeirPosRawBean> selectDeptHeir(Page page,
                                        @Param("orgId") String orgId,
                                        @Param("userId") String userId,
                                        @Param("deptId") String deptId,
                                        @Param("deptRoutingPath") String deptRoutingPath);

    void update(HeirPosEntity updateEntity);

    HeirPosExt getById(@Param("orgId") String orgId, @Param("id") String id);

    @Select("select pos_type from bk_heir_pos where id = #{id}")
    Integer getPosType(@Param("id") String id);

    List<String> getIdByParentIds(@Param("orgId") String orgId, @Param("pIds") List<String> pIds);
    List<String> removeByIds(@Param("orgId") String orgId, @Param("ids") Collection<String> ids);

    List<HeirPosEntity> selectNeedReadinessCompute();

    List<HeirPosEntity> selectNeedReadinessRemind();

    HeirPosCountBean deptPosCount(@Param("orgId") String orgId);

    List<HeirUserPosBriefBean> userPosList(@Param("orgId") String orgId, @Param("userId") String userId);

    IPage<DwdHeirPosBean> listPage4Open(Page page, @Param("orgId") String orgId);

    List<IdAndPosIdBean> queryDeptOrPosIdByIds(@Param("ids") Collection<String> ids);

    List<HeirPosEntity> listByOrgId(@Param("orgId") String orgId);
}
