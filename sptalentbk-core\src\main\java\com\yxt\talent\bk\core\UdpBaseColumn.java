package com.yxt.talent.bk.core;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  udp基础字段
 * <AUTHOR>
 * @since 2022/9/8 13:28
 * @version 1.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UdpBaseColumn extends BaseColumn implements UdpLangSupport {

    @Schema(description = "账号")
    private String username;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "工号")
    private String userNo;

    @Schema(description = "部门ID")
    private String deptId;

    @Schema(description = "主岗位ID ")
    private String positionId;

    @Schema(description = "主岗位名称")
    private String positionName;

    @Schema(description = "主部门全路径名称，例：研发中心->园区 ")
    private String deptName;

    @Schema(description = "职级ID")
    private String gradeId;

    @Schema(description = "职级名称")
    private String gradeName;

    @Schema(description = "用户头像")
    private String imgUrl;

    @Override
    public UdpLangUnitBean deptLangProperty() {
        return new UdpLangUnitBean(deptId, deptName, this::setDeptName);
    }

    @Override
    public UdpLangUnitBean positionLangProperty() {
        return new UdpLangUnitBean(positionId, positionName, this::setPositionName);
    }
}
