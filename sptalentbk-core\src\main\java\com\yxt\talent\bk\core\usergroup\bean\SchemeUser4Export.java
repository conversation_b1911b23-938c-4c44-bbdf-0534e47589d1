package com.yxt.talent.bk.core.usergroup.bean;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.common.Constants;
import com.yxt.spsdk.common.annotation.SpExcelProperty;
import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Description TODO
 *
 * <AUTHOR>
 * @Date 2023/8/21 14:03
 **/
@Data
public class SchemeUser4Export implements UdpLangSupport {
    private String id;

    @Schema(description = "姓名")
    @SpExcelProperty(nameKey = "apis.talentbk.scheme.user.export.header.fullname", index = 0)
    private String fullname;

    @Schema(description = "账号")
    @SpExcelProperty(nameKey = "apis.talentbk.scheme.user.export.header.username", index = 1)
    private String username;

    @JsonIgnore
    private String deptId;

    @Schema(description = "主部门全路径名称，例：研发中心->园区 ")
    @SpExcelProperty(nameKey = "apis.talentbk.scheme.user.export.header.deptName", index = 2)
    private String deptName;

    @JsonIgnore
    private String positionId;

    @Schema(description = "主岗位名称")
    @SpExcelProperty(nameKey = "apis.talentbk.scheme.user.export.header.positionName", index = 3)
    private String positionName;

    @Schema(description = "用户状态，0-禁用，1-启用")
    private int status;

    @SpExcelProperty(nameKey = "apis.talentbk.scheme.user.export.header.udpStatus", index = 4, i18nValue = true)
    private String udpStatus;

    @Schema(description = "入职时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MILLSECOND, timezone = Constants.STR_GMT8)
    private Date hireDate;

    @SpExcelProperty(nameKey = "apis.talentbk.scheme.user.export.header.hireDate", index = 5)
    public String getHireDateStr() {
        return DateUtil.formatDate(hireDate);
    }

    @Schema(description = "职级名称")
    @SpExcelProperty(nameKey = "apis.talentbk.scheme.user.export.header.gradeName", index = 6)
    private String gradeName;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(id, fullname, this::setFullname);
    }

    @Override
    public UdpLangUnitBean deptLangProperty() {
        return new UdpLangUnitBean(deptId, deptName, this::setDeptName);
    }

    @Override
    public UdpLangUnitBean positionLangProperty() {
        return new UdpLangUnitBean(positionId, positionName, this::setPositionName);
    }
}
