package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.bk.svc.heir.bean.UserBriefBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * HeirPosTreeResp
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 7:22 pm
 */
@Data
public class HeirPosNodeBean {
    private String id;
    @Schema(description = "继任名称")
    private String name;
    private String idFullPath;
    @Schema(description = "源岗位是不是已删除")
    private Integer sourceDeleted;
    @Schema(description = "空表示没有上级")
    @JsonIgnore
    private String parentPosId;
    @Schema(description = "目标继任数量,0:未设置")
    private Integer heirTargetQty;
    @Schema(description = "有效继任者人数")
    private Integer heirValidQty;
    @Schema(description = "0:自动规则，1:手动规则")
    private Integer riskRuleType;
    @Schema(description = "风险规则id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long riskLevelId;
    @Schema(description = "岗位员工数")
    private Integer positionUserQty;
    @Schema(description = "岗位>标杆用户，部门>部门经理")
    private List<UserBriefBean> BenchmarkUsers;
    @Schema(description = "0：补全数据，1：有效数据")
    private int dataState;
    private int hasPerm;
    /**
     * 子节点
     */
    private List<HeirPosNodeBean> children;
}
