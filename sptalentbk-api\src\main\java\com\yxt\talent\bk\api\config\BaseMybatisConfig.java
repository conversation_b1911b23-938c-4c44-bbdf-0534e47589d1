package com.yxt.talent.bk.api.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.yxt.common.interceptor.DbHintInterceptor;
import com.yxt.talent.bk.common.extend.mybatis.CustomizedSqlInjector;
import org.apache.ibatis.logging.slf4j.Slf4jImpl;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.ClassUtils;

import java.io.IOException;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/1/16
 */
abstract class BaseMybatisConfig {

    private static final ResourcePatternResolver RESOURCE_RESOLVER = new PathMatchingResourcePatternResolver();

    protected static final boolean USE_MYBATIS_PLUS = ClassUtils.isPresent(
        "com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean",
        Thread.currentThread().getContextClassLoader()
    );

    protected void applyConfiguration(MybatisSqlSessionFactoryBean factory) {
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setLogImpl(Slf4jImpl.class);
        factory.setConfiguration(configuration);
        GlobalConfig globalConfig = GlobalConfigUtils.defaults();
        globalConfig.setSqlInjector(new CustomizedSqlInjector());
        factory.setGlobalConfig(globalConfig);
    }

    protected Resource[] resolveMapperLocations(String... mapperLocations) {
        return Stream
            .of(Optional.ofNullable(mapperLocations).orElse(new String[0]))
            .flatMap(location -> Stream.of(getResources(location)))
            .toArray(Resource[]::new);
    }

    protected DbHintInterceptor dbHintInterceptor() {
        return new DbHintInterceptor();
    }

    private Resource[] getResources(String location) {
        try {
            return RESOURCE_RESOLVER.getResources(location);
        } catch (IOException e) {
            return new Resource[0];
        }
    }

}
