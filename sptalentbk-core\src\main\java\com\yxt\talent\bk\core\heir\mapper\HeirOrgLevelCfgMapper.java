package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.entity.HeirOrgLevelCfgEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * HeirOrgLevelCfgMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 2:29 pm
 */
@Mapper
public interface HeirOrgLevelCfgMapper extends BkBaseMapper<HeirOrgLevelCfgEntity> {
    /**
     * 批量插入
     * @param list
     */
    void batchInsert(@Param("list") List<HeirOrgLevelCfgEntity> list);

    List<HeirOrgLevelCfgEntity> getHeirPrepareData(@Param("orgId") String orgId,@Param("levelType") int levelType);

    void deleteById(@Param("id") Long id);

    List<Long> selectPrepareLevel(@Param("orgId") String orgId);

    @Select("select level_name from bk_heir_org_level_cfg where id=#{id}")
    String getNameById(@Param("id") Long id);

    @Select("select order_index from bk_heir_org_level_cfg where id=#{id}")
    Integer getOrderIndexById(@Param("id") Long id);
}
