package com.yxt.talent.bk.core.usergroup.entity;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 用户自用的与部门挂钩的人才群组
 */
@Getter
@Setter
public class UserDeptUgroup {
    /**
     * 主键
     */
    private Long id;

    /**
     * 企业id
     */
    private String orgId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 人才群组id,对应bk_user_group表主键
     */
    private Long ugroupId;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    private Integer deleted;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
