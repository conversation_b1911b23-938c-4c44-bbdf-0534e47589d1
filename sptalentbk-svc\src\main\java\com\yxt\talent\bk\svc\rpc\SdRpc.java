package com.yxt.talent.bk.svc.rpc;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spsdfacade.bean.spsd.*;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.talent.bk.core.sd.bean.SdModelIndicator4ResultDto;
import com.yxt.talent.bk.core.sd.bean.SdModelIndicatorDto;
import com.yxt.talent.bk.core.sd.bean.SdModelInfoDto;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;


@Slf4j
@Component
@RequiredArgsConstructor
public class SdRpc {
    private final SptalentsdFacade sptalentsdFacade;

    public List<ModelDto> getModelDetailList(String orgId, List<String> modelIds) {
        List<ModelDto> modelBeanList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(modelIds)) {
            return modelBeanList;
        }
        try {
            ModelFacadeReq req = new ModelFacadeReq();
            req.setOrgId(orgId);
            req.setModelIds(modelIds);
            return sptalentsdFacade.getModelDetail(req);
        } catch (Exception e) {
            log.info("LOG001 getSkillModelDetailList error, orgId:{}, modelIds:{}", orgId, modelIds, e);
        }
        return modelBeanList;
    }


    public ModelInfo getModelDetailData(String orgId, String skillModelId) {
        try {
            return sptalentsdFacade.getModelInfo(orgId, skillModelId);
        } catch (Exception e) {
            log.info("getSkillModelDetailData error, orgId:{}, skillModelIds:{}", orgId, skillModelId, e);
        }
        return null;
    }


    public List<ModelDto> getRtModelDetailList(String orgId, List<String> modelIds) {
        List<ModelDto> rtModelBeanList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(modelIds)) {
            return rtModelBeanList;
        }
        try {
            ModelFacadeReq rtReq = new ModelFacadeReq();
            rtReq.setOrgId(orgId);
            rtReq.setModelIds(modelIds);
            log.info("getRtModelDetailList param={}", JSON.toJSONString(rtReq));
            return sptalentsdFacade.getModelDetail(rtReq);
        } catch (Exception e) {
            log.info("LOG002 getSkillModelDetailList error, orgId:{}, skillModelIds:{}", orgId, modelIds, e);
        }
        return rtModelBeanList;
    }

    public List<IndicatorDto> getIndicatorByModelId(String orgId, String modelId) {
        return sptalentsdFacade.getIndicatorByModelId(orgId, modelId);
    }

    public SdModelInfoDto convertAsResult(ModelInfo modelInfo, int indicatorType) {
        if (modelInfo == null) {
            return null;
        }
        SdModelInfoDto info = new SdModelInfoDto();
        BeanCopierUtil.copy(modelInfo, info);
        List<ModelRequireDetailInfo> requireDetails = new ArrayList<>();
        addModelRequireDetails(modelInfo.getDms(), requireDetails, indicatorType);
        info.setIndicators(BeanCopierUtil.convertList(requireDetails,
                detail -> modelRequireBase2Indicator(null, detail, SdModelIndicator4ResultDto::new)));
        return info;
    }

    public String modelNameWithVersion(ModelInfo modelInfo) {
        if (modelInfo == null) {
            return null;
        }
        return modelInfo.getTitle() + (modelInfo.getVersionNumber() != null ? "(" + showModelVersion(modelInfo.getVersionNumber()) + ")" : StringPool.EMPTY);
    }

    public String showModelVersion(BigDecimal modelVersion) {
        return modelVersion != null ? modelVersion.setScale(1).toString() : StringPool.EMPTY;
    }

    private void addModelRequireDetails(List<ModelRequireBaseInfo> dims,
                                        List<ModelRequireDetailInfo> requireDetails,
                                        int indicatorType) {
        if (dims != null) {
            for (ModelRequireBaseInfo dim : dims) {
                addModelRequireDetails(dim.getChilds(), requireDetails, indicatorType);
                if (Objects.equals(dim.getIndicatorType(), indicatorType) && dim.getDetails() != null) {
                    requireDetails.addAll(dim.getDetails());
                }
            }
        }
    }

    private <T extends SdModelIndicatorDto> T modelRequireBase2Indicator(T parentIndicator,
                                                                         ModelRequireDetailInfo detailInfo,
                                                                         Supplier<T> createIndicator) {
        T indicatorDto = createIndicator.get();
        BeanCopierUtil.copy(detailInfo, indicatorDto);
        if (detailInfo.getRt() != null) {
            indicatorDto.setRtName(detailInfo.getRt().getName());
            indicatorDto.setRtDesc(detailInfo.getRt().getRtDesc());
            indicatorDto.setName(detailInfo.getRt().getName());
            indicatorDto.setDescription(detailInfo.getRt().getRtDesc());
        } else if (detailInfo.getItem() != null) {
            indicatorDto.setLevel(detailInfo.getItem().getLevel());
            indicatorDto.setName(detailInfo.getItem().getName());
            indicatorDto.setDescription(detailInfo.getItem().getDescription());
        } else {
            indicatorDto.setName(detailInfo.getItemValue());
        }
        if (parentIndicator != null) {
            indicatorDto.setParentName(parentIndicator.getName());
        }
        if (detailInfo.getChilds() != null) {
            indicatorDto.setChildren(BeanCopierUtil.convertList(detailInfo.getChilds(),
                    detail -> modelRequireBase2Indicator(indicatorDto, detail, createIndicator)));
        }
        return indicatorDto;
    }
}
