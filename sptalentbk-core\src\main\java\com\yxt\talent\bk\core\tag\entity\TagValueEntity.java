package com.yxt.talent.bk.core.tag.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("bk_tag_value")
@EqualsAndHashCode(callSuper = true)
public class TagValueEntity extends CreatorEntity {
    @TableId
    private String id;
    /**
     * 机构id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * '标签id=bk_tag.id
     */
    @TableField("tag_id")
    private String tagId;
    /**
     * 标签值/分层标签值
     */
    @TableField("value_name")
    private String valueName;
    /**
     * '排序'
     */
    @TableField("order_index")
    private Integer orderIndex;
}
