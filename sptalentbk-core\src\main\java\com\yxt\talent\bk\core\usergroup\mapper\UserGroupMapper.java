package com.yxt.talent.bk.core.usergroup.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.bk.core.dashboard.bean.DashBoardGroupDimVO;
import com.yxt.talent.bk.core.tag.bean.Tag4Page;
import com.yxt.talent.bk.core.usergroup.bean.UserGroup4Get;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupModulePageVO;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface UserGroupMapper extends BaseMapper<UserGroup> {
    int updateBatch(List<UserGroup> list);

    int batchInsert(@Param("list") List<UserGroup> list);

    int insertOrUpdate(UserGroup record);

    int insertOrUpdateSelective(UserGroup record);

    IPage<UserGroup4Get> find4Page(@Param("page") IPage<Tag4Page> page, @Param("orgId") String orgId,
            @Param("keyword") String keyword, @Param("deptIds") List<String> deptIds,
            @Param("userIds") List<String> userIds, @Param("teamGroup") int teamGroup);

    int deleteLogic(@Param("userId") String userId, @Param("id") Long id);

    IPage<UserGroup> selectCaculateGroup(@Param("page") IPage<UserGroup> page, @Param("weekdate") int weekdate,
            @Param("day") int day, @Param("orgIds") List<String> orgIds);

    List<UserGroup> selectAllCaculateGroup(@Param("weekdate") int weekdate, @Param("day") int day,
            @Param("orgIds") List<String> orgIds);

    Long checkGroupNameExists(@Param("orgId") String orgId, @Param("userId") String userId,
            @Param("deptId") String deptId, @Param("groupName") String groupName);

    @Select("select group_name from bk_user_group where id = #{id}")
    String getNameById(@Param("id") Long id);

    Page<UserGroupModulePageVO> findAuthData4PageModule(IPage<UserGroupModulePageVO> pageable,
            @Param("orgId") String orgId, @Param("rangeUserIds") List<String> rangeUserIds,
            @Param("groupName") String groupName);

    long getAllCount(@Param("orgId") String orgId, @Param("groupIds") List<Long> groupIds);

    List<UserGroup> findAllAuthData(@Param("orgId") String orgId, @Param("rangeUserIds") List<String> rangeUserIds);

    Page<UserGroup> findAuthData4PageByIds(IPage<UserGroup> pageable, @Param("orgId") String orgId,
            @Param("groupIds") List<Long> groupIds);

    List<Long> resTransferGroupIds(@Param("orgId") String orgId, @Param("userId") String userId);

    int execResTransfer(@Param("orgId") String orgId,
                        @Param("createUserId") String createUserId,
                        @Param("newCreateUserId") String newCreateUserId,
                        @Param("optUserId") String optUserId);
}
