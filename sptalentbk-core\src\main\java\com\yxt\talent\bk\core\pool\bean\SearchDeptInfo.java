package com.yxt.talent.bk.core.pool.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  部门组件查询参数
 * <AUTHOR>
 * @since 2022/9/29 9:11
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchDeptInfo {
    @Schema(description = "部门id list")
    private String deptId;

    @Schema(description = "是否统计子部门人员（0：不包含，1：包含）")
    private Integer includeAll;
}
