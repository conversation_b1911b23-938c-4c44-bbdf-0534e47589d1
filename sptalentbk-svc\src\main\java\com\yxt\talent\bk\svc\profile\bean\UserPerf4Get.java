package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Setter
@Getter
@SqlTable("dwd_user_perf")
@Schema(name = "人才画像-绩效表现")
public class UserPerf4Get {

    @Schema(description = "主键Id")
    private String id;

    @Schema(description = "绚星2.0平台用户id")
    private String userId;

    @JsonIgnore
    @Schema(description = "用户id")
    private String thirdUserId;

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "年度")
    private int yearly;

    @Schema(description = "月度:1-12, 季度:1-4, 年份:20xx")
    private int period;

    @Schema(description = "0-月度绩效, 1-季度绩效, 2-年度绩效")
    private int cycle;

    @Schema(description = "(月度/季度/年度)绩效值，如S、A、B等")
    private String perf;

    @Schema(description = "第三方绩效 ID")
    private String thirdPerfId;

    @Schema(description = "第三方绩效名称")
    private String thirdPerfName;


    @Schema(description = "绩效排序 ID")
    private Integer orderIndex;

    @Schema(description = "绩效得分")
    private BigDecimal perfPoint;

    @Schema(description = "绩效总分")
    private BigDecimal perfScore;

}
