package com.yxt.talent.bk.common.utils;

import com.yxt.common.component.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.Future;

/**
 * @Author: geyan
 * @Date: 2021/7/1 上午10:38
 */
@Slf4j
@Component
public class CallUtils {
    private static CallUtils self;

    @Async
    public Future<Void> doAsyncCall(Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.info("asyncAll error", e);
            throw e;
        }
        return new AsyncResult<>(null);
    }

    public static void asyncCall(Runnable runnable) {
        getSelf().doAsyncCall(runnable);
    }

    private static CallUtils getSelf() {
        if (self == null) {
            self = SpringContextHolder.getBean(CallUtils.class);
        }
        return self;
    }
}
