package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: sptalentbkapi
 * @description: 学习培训
 **/
@Data
public class UserCompareDetailReward {

    @Schema(description = "奖惩类型（1-奖项 2-惩罚）", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer rpType;
    @Schema(description = "奖惩名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rpName;
    @Schema(description = "获取时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime acqTime;
    @Schema(description = "发布时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pubFrom;

}
