package com.yxt.talent.bk.core.pool.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.CreatorEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  人才池准备度表
 * <AUTHOR>
 * @since 2022/9/8 9:41
 * @version 1.0
 */
@Data
@NoArgsConstructor
@TableName("bk_pool_readiness")
@EqualsAndHashCode(callSuper = true)
public class PoolReadiness extends CreatorEntity {

    @TableField("id")
    @Schema(description = "主键")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = BkDemoConstants.BK_POOL_READINESS_ID)
    private String id;

    @TableField("org_id")
    @Schema(description = "机构id")
    private String orgId;

    @TableField("readiness_name")
    @Schema(description = "准备度名称")
    private String readinessName;

    @TableField("order_index")
    @Schema(description = "排序【ASC 排序，值越小准备度越高】")
    private Integer orderIndex;
}
