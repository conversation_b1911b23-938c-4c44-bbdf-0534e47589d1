package com.yxt.talent.bk.core.tag.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/***
 * <AUTHOR>
 * @since 2022/8/10 14:43
 */
@Data
@Schema(name = "标签列表查询结果对象")
public class Tag4Page {

    @Schema(description = "标签id")
    private String id;

    @Schema(description = "标签名称(性别|出生日期...)")
    private String tagName;

    @Schema(description = "标签key")
    private String tagKey;

    @Schema(description = "标签类型(0-普通标签,1-分层标签)")
    private Integer tagType;

    @Schema(description = "说明/定义")
    private String description;

    @Schema(description = "标签分类id/字段空是未分组")
    private String catalogId;

    @Schema(description = "标签分类名称")
    private String catalogName;

    @Schema(description = "创建方式(0-静态,1-规则,2-模型)")
    private Integer createType;

    @Schema(description = "启用状态(0-禁用,1-启用)")
    private Integer tagEnable;

    @Schema(description = "可见状态(0-不可见,1-可见)")
    private Integer showType;

    @Schema(description = "标签来源(0-内置,1-自建,2-固定)")
    private Integer tagSource;

    @Schema(description = "标签人数")
    private Integer tagUserCount = 0;
}
