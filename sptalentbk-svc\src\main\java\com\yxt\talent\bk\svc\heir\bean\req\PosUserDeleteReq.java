package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.svc.heir.bean.HeirEditParam4Log;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Schema(name = "删除继任者")
public class PosUserDeleteReq {

    @Schema(description = "继任id")
    @NotNull(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String posId;

    @Schema(description = "选中的用户id（最多1000个）")
    @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    @Size(message = BkApiErrorKeys.PARAM_SIZE_INVALID_MESSAGE, min = 1, max = 1000)
    private List<String> userIds;

    public HeirEditParam4Log editParam4Log() {
        HeirEditParam4Log ret = new HeirEditParam4Log();
        ret.setPosId(posId);
        ret.setUserIds(userIds);
        return ret;
    }
}
