package com.yxt.talent.bk.svc.export;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.yxt.common.exception.ApiException;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.util.MapBuilder;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.I18nComponent;
import com.yxt.talent.bk.common.constants.*;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.common.utils.DownInfoUtil;
import com.yxt.talent.bk.core.dashboard.bean.*;
import com.yxt.talent.bk.core.dashboard.bean.CommonExportParam;
import com.yxt.talent.bk.core.dashboard.bean.DashBoardGroupDimVO;
import com.yxt.talent.bk.core.dashboard.bean.GroupUserCountDTO;
import com.yxt.talent.bk.core.dashboard.bean.GroupUserSkillRtReachDTO;
import com.yxt.talent.bk.core.dashboard.bean.UserDeptAuthDTO;
import com.yxt.talent.bk.core.dashboard.bean.UserGroupAuthDTO;
import com.yxt.talent.bk.core.spmodel.entity.DwdDept;
import com.yxt.talent.bk.core.spmodel.mapper.DwdDeptMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserSkillRtStatisticsMapper;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupRepository;
import com.yxt.talent.bk.svc.dashboard.TalentBoardService;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.usergroup.UserGroupService;
import com.yxt.ubiz.export.bean.ExportFileInfo;
import com.yxt.ubiz.export.bean.ExportParam;
import com.yxt.ubiz.export.bean.SimpleTemplateParam;
import com.yxt.ubiz.export.common.enums.ExportFileTypeEnum;
import com.yxt.ubiz.export.core.AbstractExportWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.util.CellAddress;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/2
 */
@Service
@Slf4j
@AllArgsConstructor
public class DashBoardGroupDimExportService extends AbstractExportWrapper {

    private final ILock lockService;
    private final AuthService authService;
    private final I18nComponent i18nComponent;
    private final UserAuthService userAuthService;
    private final TalentBoardService talentBoardService;
    private final DwdUserSkillRtStatisticsMapper dwdUserSkillRtStatisticsMapper;
    private final DwdUserMapper dwdUserMapper;
    private final UserGroupRepository userGroupRepository;
    private final UserGroupService userGroupService;
    private final DwdDeptMapper dwdDeptMapper;

    private static final int MAX_LEASE_TIME = 100;

    private final Map<String, String> headData = MapBuilder.<String, String>newMapBuilder()
            .put("groupName", "apis.talentbk.dashboard.group.dim.temp.groupName")
            .put("enableUserCount", "apis.talentbk.dashboard.dept.dim.temp.enableUserCount")
            .put("skillReachCount", "apis.talentbk.dashboard.dept.dim.temp.skillReachCount")
            .put("skillReachRate", "apis.talentbk.dashboard.dept.dim.temp.skillReachRate")
            .put("taskReachCount", "apis.talentbk.dashboard.dept.dim.temp.taskReachCount")
            .put("taskReachRate", "apis.talentbk.dashboard.dept.dim.temp.taskReachRate").immutableMap();

    @Override
    public Map<String, String> getExportHeader(Object o) {
        return headData;
    }

    @Override
    public void loadData(Object b, BiConsumer<List<?>, ExportParam> consumerList) {
        //查询所有的符合参数的部门数据
        CommonExportParam exportParam = JSON.parseObject(JSON.toJSONString(b), CommonExportParam.class);
        List<Long> groupIds = exportParam.getUserGroupIds();
        List<String> deptIds = exportParam.getDeptIds();
        String userId = exportParam.getOptUserId();
        String orgId = exportParam.getOrgId();
        SimpleTemplateParam tempParam = new SimpleTemplateParam();
        tempParam.setHasRemarkRows(new boolean[]{false});
        tempParam.setHeadCells(org.assertj.core.util.Lists.newArrayList(CellAddress.A1));
        tempParam.setSheetNum(0);
        List<DashBoardGroupDimVO> emptyDataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deptIds)) {
            //查询当前人员的权限范围
            UserDeptAuthDTO userDeptAuthDTO = talentBoardService.getUserAuthDept(orgId, userId, exportParam.getAdmin());
            if (!userDeptAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userDeptAuthDTO.getDeptIds())) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
            //如果是空，说明该用户拥有所有部门的权限
            deptIds = userDeptAuthDTO.getDeptIds();
        }
        //获取权限下的群组集合。如果没有传，则查询下权限下的群组数据
        if (CollectionUtils.isEmpty(groupIds)) {
            UserGroupAuthDTO userGroupAuthDTO = talentBoardService.getUserAuthGroupIds(orgId, userId,
                    exportParam.getAdmin());
            if (!userGroupAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userGroupAuthDTO.getGroupIds())) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
            //如果是空，说明该用户拥有所有群组的权限
            groupIds = userGroupAuthDTO.getGroupIds();
        }
        //校验部门ID是否有效
        //        List<String> dwdDeptIds = talentBoardService.transferDeptId(deptIds, orgId);
        List<String> dwdDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
            dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dwdDeptIds)) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
        }
        //查询权限部门下的人员和权限群组下的人 交集。根据交集的人员查询出能看到哪些群组ID集合
        List<Long> authGroupIds = talentBoardService.getUserAuthGroupListByDeptAndGroup(orgId, dwdDeptIds,
                groupIds);
        if (CollectionUtils.isEmpty(authGroupIds)) {
            consumerList.accept(emptyDataList, tempParam);
            return;
        }
        //查询
        List<UserGroup> dataList = userGroupRepository.findByIds(orgId, authGroupIds);
        if (CollectionUtils.isEmpty(dataList)) {
            consumerList.accept(emptyDataList, tempParam);
            return;
        }
        List<List<UserGroup>> partDataList = Lists.partition(dataList, 100);
        List<String> finalDeptIds = deptIds;
        partDataList.forEach(part -> {
            List<DashBoardGroupDimVO> resDataList = new ArrayList<>();
            //只需要查询分页展示群组信息
            List<Long> groupIdData = dataList.stream().map(UserGroup::getId).distinct().collect(Collectors.toList());
            List<String> deptIds2 = talentBoardService.transferDeptId(finalDeptIds, orgId);
            //查询用户组下的人员数据
            List<GroupUserCountDTO> groupUserCountList = dwdUserMapper.getGroupUserCount(orgId, deptIds2,
                    groupIdData);
            Map<Long, Long> groupUserCountMap = StreamUtil.list2map(groupUserCountList, GroupUserCountDTO::getGroupId,
                    GroupUserCountDTO::getUserCount);
            //查询一起用人数能力和任务达标情况
            List<GroupUserSkillRtReachDTO> groupUserSkillRtReachList = dwdUserSkillRtStatisticsMapper.getGroupUserSkillReachCount(
                    orgId, deptIds2, groupIdData);
            Map<Long, GroupUserSkillRtReachDTO> groupUserSkillRtReachMap = StreamUtil.list2map(
                    groupUserSkillRtReachList, GroupUserSkillRtReachDTO::getGroupId);
            talentBoardService.setGroupDashboardData(dataList, groupUserCountMap, resDataList,
                    groupUserSkillRtReachMap);
            resDataList.forEach(resData -> {
                resData.setSkillReachRate(resData.getSkillReachRate() + "%");
                resData.setTaskReachRate(resData.getTaskReachRate() + "%");
            });
            consumerList.accept(resDataList, tempParam);
            resDataList.clear();
        });
    }

    public void exportGroupDimDashBoard(CommonExportParam searchParam, String orgId, String userId, String fullname) {
        //校验参数
        if (CollectionUtils.isEmpty(searchParam.getDeptIds()) && CollectionUtils.isEmpty(
                searchParam.getUserGroupIds())) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        }
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_DASHBOARD_GROUP_DIM_EXPORT, orgId, userId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                ExportFileInfo exportFileInfo = new ExportFileInfo();
                //设置业务的查询参数
                exportFileInfo.setQueryParams(searchParam);
                exportFileInfo.setTemplatePath("excel/dashboard_group_dim.xlsx");
                //设置下载信息
                exportFileInfo.setDownInfo(DownInfoUtil.getDownInfo(ModuleConstants.FUNCTION_NAME));
                exportFileInfo.setLocale(authService.getLocale());
                exportFileInfo.setFileType(ExportFileTypeEnum.EXCEL);
                //不需要后缀.后缀由fileType的suffix决定,此文件名必须唯一
                String fileName = i18nComponent.getI18nValue(ExportConstants.DASHBOARD_GROUP_DIM_EXPORT_FILE_NAME);
                exportFileInfo.setName(fileName + "_" + System.currentTimeMillis());
                exportFileInfo.setFileName(fileName + System.nanoTime());
                exportFileInfo.setOrgId(orgId);
                exportFileInfo.setUserId(userId);
                exportFileInfo.setFullname(fullname);
                exportFileInfo.setExportTopic(TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_FILE);
                exportFileInfo.setUsemq(true);
                exportFileInfo.setDownloadI18n(true);
                export(exportFileInfo);
            } catch (Exception ex) {
                log.warn("exportDeptDimDashBoard  orgId={},userId={};err", orgId, userId, ex);
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_FILE_EXPORT_ING);
        }
    }
}
