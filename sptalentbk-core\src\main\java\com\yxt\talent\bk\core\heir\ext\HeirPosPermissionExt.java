package com.yxt.talent.bk.core.heir.ext;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * HeirPosUserEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:54 am
 */
@Getter
@Setter
public class HeirPosPermissionExt implements Serializable, UdpLangSupport {

    @Schema(description = "账号")
    private String username;

    @Schema(description = "姓名")
    private String fullName;

    private String deptId;
    @Schema(description = "主部门全路径名称，例：研发中心->园区 ")
    private String deptName;

    @Schema(description = "用户id")
    private String userId;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(userId, fullName, this::setFullName);
    }

    @Override
    public UdpLangUnitBean deptLangProperty() {
        return new UdpLangUnitBean(deptId, deptName, this::setDeptName);
    }
}
