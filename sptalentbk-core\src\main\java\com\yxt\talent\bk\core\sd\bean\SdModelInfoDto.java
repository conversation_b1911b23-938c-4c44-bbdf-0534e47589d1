package com.yxt.talent.bk.core.sd.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.function.Consumer;

@Data
public class SdModelInfoDto {
    @Schema(description = "id")
    private String id;

    @Schema(description = "模型编号")
    private String modelNum;

    @Schema(description = "模型名称")
    private String title;

    @Schema(description = "等级类型，0-无类型 1-有等级模型 2-无等级模型")
    private Integer levelType;

    private List<SdModelIndicator4ResultDto> indicators;

    public void eachIndicator(Consumer<SdModelIndicator4ResultDto> consumer) {
        forEachIndicator(indicators, consumer);
    }

    public static void forEachIndicator(List<SdModelIndicator4ResultDto> indicatorResults,
                                 Consumer<SdModelIndicator4ResultDto> consumer) {
        if (indicatorResults != null) {
            for (SdModelIndicator4ResultDto indicatorResult : indicatorResults) {
                consumer.accept(indicatorResult);
                forEachIndicator(indicatorResult.getChildren(), consumer);
            }
        }
    }
}
