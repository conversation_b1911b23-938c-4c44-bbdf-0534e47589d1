package com.yxt.talent.bk.api.controller.pool;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.pool.PoolReadinessService;
import com.yxt.talent.bk.svc.pool.bean.readiness.PoolReadiness4List;
import com.yxt.talent.bk.svc.pool.bean.readiness.PoolReadiness4SaveBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/mgr/pool/readiness")
@Slf4j
@AllArgsConstructor
@Tag(name = "人才池准备度")
public class PoolReadinessController extends BaseController {
    private final PoolReadinessService poolReadinessService;

    @Operation(summary = "准备度-列表")
    @GetMapping(value = "/list", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<PoolReadiness4List> findByOrgId() {
        UserCacheDetail userDetail = getUserCacheDetail();
        List<PoolReadiness4List> list = poolReadinessService.findByOrgId(userDetail.getOrgId(),userDetail.getUserId());
        return new CommonList<>(list);
    }

    @Operation(summary = "准备度-创建")
    @PostMapping(value = "", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public void create(@Valid @RequestBody PoolReadiness4SaveBean data) {
        UserCacheDetail userDetail = getUserCacheDetail();
        poolReadinessService.create(userDetail.getOrgId() ,userDetail.getUserId(), data);
    }

    @Operation(summary = "准备度-编辑")
    @PutMapping(value = "", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public void update(@Valid @RequestBody PoolReadiness4SaveBean data) {
        UserCacheDetail userDetail = getUserCacheDetail();
        poolReadinessService.update(userDetail.getOrgId() ,userDetail.getUserId(), data);
    }

    @Operation(summary = "准备度-删除")
    @DeleteMapping(value = "/{id}", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN)
    public void delete(@PathVariable String id) {
        UserCacheDetail userDetail = getUserCacheDetail();
        poolReadinessService.delete(userDetail.getOrgId(), id);
    }

}
