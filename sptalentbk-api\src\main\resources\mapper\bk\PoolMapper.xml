<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.pool.mapper.PoolMapper">
    <sql id="Base_Columns">
        t1.id AS  id,
        t1.org_id AS  orgId,
        t1.pool_name AS  poolName,
        t1.catalog_id AS  catalogId,
        t1.expect_num AS  expectNum,
        t1.real_num AS  realNum,
        ROUND(t1.saturability) AS  saturability,
        t1.remark AS  remark,
        t1.create_user_id AS  createUserId,
        t1.create_time AS  createTime,
        t1.update_user_id AS  updateUserId,
        t1.update_time AS  updateTime
    </sql>

    <select id="findPageBy" resultType="com.yxt.talent.bk.core.pool.entity.Pool">

        SELECT
        <include refid="Base_Columns"/>
        FROM bk_pool t1
        WHERE t1.org_id = #{orgId}
          AND t1.deleted = 0
        <if test="poolIds != null and poolIds.size() > 0">
            AND t1.id IN
            <foreach collection="poolIds" item="poolId" open="(" separator="," close=")">
                #{poolId}
            </foreach>
        </if>
        <if test="catalogId != null and catalogId != ''">
            AND t1.catalog_id = #{catalogId}
        </if>
        <if test="keyword != null and keyword != ''">
            AND t1.pool_name LIKE CONCAT('%', #{keyword}, '%') ESCAPE '\\'
        </if>
        ORDER BY
        <choose>
            <when test="orderBy != null and orderBy == 'saturability'">
                saturability
            </when>
            <when test="orderBy != null and orderBy == 'realNum'">
                real_num
            </when>
            <otherwise>
                create_time
            </otherwise>
        </choose>
        <choose>
            <when test="direction != null and direction == 'asc'">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>

    <select id="listByIds" resultType="com.yxt.talent.bk.core.pool.entity.Pool">
        SELECT
        <include refid="Base_Columns"/>
        FROM bk_pool t1
        WHERE t1.org_id = #{orgId}
        AND t1.deleted = 0
        <if test="poolIds != null and poolIds.size() > 0">
            AND t1.id IN
            <foreach collection="poolIds" item="poolId" open="(" separator="," close=")">
                #{poolId}
            </foreach>
        </if>
    </select>

    <update id="updateSaturabilityById" >
        UPDATE bk_pool t1
        SET t1.real_num = (SELECT count(*) FROM bk_pool_user t2 WHERE t2.event_pool_out = 0 AND t2.pool_id = t1.id AND t2.org_id =t1.org_id),
        	t1.saturability = (CASE WHEN t1.expect_num = 0 THEN 0  ELSE ROUND(t1.real_num/t1.expect_num*100,2) END)
        WHERE t1.org_id = #{orgId} AND t1.id = #{id}
    </update>

    <select id="getById4AuditLog" resultType="com.yxt.talent.bk.core.pool.bean.Pool4AuditLog">
        select p.org_id,pool_name,pc.bk_catalog_name as catalog_name,expect_num,remark
        from bk_pool p
        left join bk_catalog pc on pc.id = p.catalog_id
        where p.id = #{id} and p.deleted = 0
    </select>

    <select id="resTransferPoolIds" resultType="string">
        select a.id from bk_pool a where a.org_id = #{orgId} and a.deleted = 0
        and (a.create_user_id = #{userId} or
        exists(select 1 from bk_pool_mgr where org_id = #{orgId} and pool_id = a.id and deleted = 0 and mgr_id = #{userId}))
    </select>

    <update id="execResTransfer">
        update bk_pool set create_user_id = #{newCreateUserId},update_time = now(),update_user_id = #{optUserId}
        where org_id = #{orgId} and deleted = 0 and create_user_id = #{createUserId}
    </update>

    <select id="queryPoolNameByIds" resultType="com.yxt.talentbkfacade.bean.StrIdNameBean">
        select id,pool_name as name,deleted from bk_pool where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
