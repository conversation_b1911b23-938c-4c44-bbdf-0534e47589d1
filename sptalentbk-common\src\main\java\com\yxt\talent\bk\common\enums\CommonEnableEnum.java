package com.yxt.talent.bk.common.enums;


public enum CommonEnableEnum {
    /**
     * 启用状态
     */

    ALL("全部", -1),
    DISABLED("禁用", 0),
    ENABLED("启用", 1);

    private String name;
    private int type;

    CommonEnableEnum(String name, int type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public int getType() {
        return type;
    }

    public static String getEnableName(int type){
        if(type==CommonEnableEnum.ENABLED.type){
            return CommonEnableEnum.ENABLED.name;
        }else if(type==CommonEnableEnum.DISABLED.type){
            return CommonEnableEnum.DISABLED.name;
        }else{
            return CommonEnableEnum.ALL.name;
        }
    }
}
