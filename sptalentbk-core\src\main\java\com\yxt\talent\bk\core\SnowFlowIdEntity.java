package com.yxt.talent.bk.core;

import com.baomidou.mybatisplus.annotation.TableId;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.DateUtil;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * SnowFlowIdEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:25 am
 */
@Data
public class SnowFlowIdEntity extends CreatorEntity{
    @TableId
    private Long id;
    private Integer deleted;
    protected void setOrgId(String orgId) {
        //NO SONAR
    }
    public void init(String orgId) {
        init(orgId, null);
    }
    public void init(String orgId, String userId) {
        if (StringUtils.isEmpty(userId)) {
            userId = TalentBkConstants.CODE_OPERATOR_ID;
        }
        setUpdateTime(DateUtil.currentTime());
        setUpdateUserId(userId);
        if (deleted == null) {
            deleted = YesOrNo.NO.getValue();
        }
        if (id == null) {
            this.id = TalentbkUtil.snowFLowId();
            this.setCreateTime(getUpdateTime());
            setCreateUserId(userId);
            setOrgId(orgId);
        } else if (StringUtils.isNotBlank(orgId)) {
            setOrgId(orgId);
        }
    }
}
