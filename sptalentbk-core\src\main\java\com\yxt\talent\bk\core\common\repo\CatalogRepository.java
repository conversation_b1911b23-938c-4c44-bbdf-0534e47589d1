package com.yxt.talent.bk.core.common.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.core.tag.mapper.CatalogMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class CatalogRepository extends ServiceImpl<CatalogMapper, TagCatalog> {

    public TagCatalog getOne(String orgId, String id) {
        LambdaQueryWrapper<TagCatalog> wrapper = getQueryWrapper();
        wrapper.eq(TagCatalog::getId, id);
        wrapper.eq(TagCatalog::getOrgId, orgId);
        wrapper.eq(TagCatalog::getShowType, 1);
        return getOne(wrapper);
    }

    public List<TagCatalog> listByName(String orgId, String catalogName,int catalogSource) {
        LambdaQueryWrapper<TagCatalog> wrapper = getQueryWrapper();
        wrapper.eq(TagCatalog::getCatalogName, catalogName);
        wrapper.eq(TagCatalog::getCatalogSource, catalogSource);
        wrapper.eq(TagCatalog::getOrgId, orgId);
        wrapper.eq(TagCatalog::getShowType, 1);
        return list(wrapper);
    }

    /**
     * 查询除内置分类以外的分类
     * @param orgId
     * @return java.util.List<com.yxt.talent.bk.core.TagCatalog>
     * <AUTHOR>
     * @since 2022/8/22
     */
    public List<TagCatalog> listByCatalogType(String orgId,int catalogSource, boolean rvOrNot) {
        LambdaQueryWrapper<TagCatalog> queryWrapper = getQueryWrapper();
        queryWrapper.eq(TagCatalog::getOrgId, orgId);
        // 查询除内置分类以外的分类
        queryWrapper.eq(TagCatalog::getCatalogSource, catalogSource);
        queryWrapper.orderByDesc(TagCatalog::getCreateTime);
        queryWrapper.eq(TagCatalog::getShowType, 1);
        // 添加盘点要素校验
        if (!rvOrNot) {
            queryWrapper.ne(TagCatalog::getCatalogName, "人才盘点");
        }
        return list(queryWrapper);
    }

    public List<TagCatalog> listByOrgId(String orgId, int catalogSource) {
        LambdaQueryWrapper<TagCatalog> queryWrapper = getQueryWrapper();
        queryWrapper.eq(TagCatalog::getCatalogSource, catalogSource);
        queryWrapper.eq(TagCatalog::getOrgId, orgId);
        return list(queryWrapper);
    }

    public boolean removeById(String orgId, String id) {
        LambdaQueryWrapper<TagCatalog> queryWrapper = getQueryWrapper();
        queryWrapper.eq(TagCatalog::getId, id);
        queryWrapper.eq(TagCatalog::getOrgId, orgId);
        return super.remove(queryWrapper);
    }

    public TagCatalog getDefaultCatalog(String orgId, int catalogSource) {
        LambdaQueryWrapper<TagCatalog> wrapper = getQueryWrapper();
        wrapper.eq(TagCatalog::getOrgId, orgId);
        wrapper.eq(TagCatalog::getCatalogSource, catalogSource);
        // 默认分类值为 0
        wrapper.eq(TagCatalog::getCatalogType, 0);
        wrapper.eq(TagCatalog::getShowType, 1);
        List<TagCatalog> catalogs = list(wrapper);
        if (CollectionUtils.isNotEmpty(catalogs)) {
            return catalogs.get(0);
        }
        return null;
    }

    private LambdaQueryWrapper<TagCatalog> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }


    public List<TagCatalog> findBySourceAndType(String orgId, Integer catalogSource,Integer catalogType) {
        LambdaQueryWrapper<TagCatalog> queryWrapper = getQueryWrapper();
        queryWrapper.eq(TagCatalog::getOrgId, orgId);
        queryWrapper.eq(TagCatalog::getCatalogSource, catalogSource);
        if(catalogType != null){
            queryWrapper.eq(TagCatalog::getCatalogType, catalogType);
        }
        queryWrapper.orderByDesc(TagCatalog::getCatalogType);
        queryWrapper.orderByDesc(TagCatalog::getCreateTime);
        queryWrapper.eq(TagCatalog::getShowType, 1);
        return list(queryWrapper);
    }

    public Page<TagCatalog> listPage(String orgId, Integer catalogSource, Page page, String catalogName) {
        LambdaQueryWrapper<TagCatalog> queryWrapper = getQueryWrapper();
        queryWrapper.like(StringUtils.isNotBlank(catalogName),
                TagCatalog::getCatalogName, SqlUtils.escapeLike(catalogName));
        queryWrapper.eq(TagCatalog::getOrgId, orgId);
        queryWrapper.eq(TagCatalog::getCatalogSource, catalogSource);
        queryWrapper.eq(TagCatalog::getShowType, 1);
        queryWrapper.orderByDesc(TagCatalog::getCreateTime);
        return page(page, queryWrapper);
    }
}
