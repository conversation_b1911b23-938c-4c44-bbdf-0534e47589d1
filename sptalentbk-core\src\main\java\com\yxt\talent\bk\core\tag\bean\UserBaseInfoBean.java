package com.yxt.talent.bk.core.tag.bean;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Document;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/8/10
 */
@Data
@NoArgsConstructor
@Document(indexName = "#{@bkConfigService.indexName}", type = TalentBkConstants.ES_TYPE)
public class UserBaseInfoBean {
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "用户帐号")
    private String username;
    @Schema(description = "用户姓名")
    private String fullname;
    @Schema(description = "帐号状态 0-禁用 1-启用")
    private int status=1;
    @Schema(description = "部门名称")
    private String departmentName;
    @Schema(description = "岗位名称")
    private String positionName;
    @Schema(description = "标签值名称列表")
    private List<String> tagValueNameList = Lists.newLinkedList();
    @Schema(description = "标签值列表")
    private List<UserTagBaseBean> tagBaseBeanList = Lists.newArrayList();
    @Schema(description = "领导风格标签（内置标签，可手动修改）")
    private Set<String> leadershipStyle = Sets.newHashSet();
    @Schema(description = "自建标签")
    private Map<String, Set<String>> diylabels = Maps.newHashMap();
    @Schema(description = "标签id")
    private String tagId;
}
