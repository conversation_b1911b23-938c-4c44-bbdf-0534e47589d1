package com.yxt.talent.bk.svc.tag;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.tag.bean.UserBaseInfoBean;
import com.yxt.talent.bk.core.tag.bean.UserTagBaseBean;
import com.yxt.talent.bk.core.tag.bean.UserTagValueBaseBean;
import com.yxt.talent.bk.core.tag.entity.UserTagValueEntity;
import com.yxt.talent.bk.core.tag.mapper.UserTagValueMapper;
import com.yxt.talent.bk.core.tag.repo.UserTagValueRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户标签值service
 *
 * <AUTHOR>
 * @since 2022/8/15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserTagValueService {

    private final UserTagValueRepository userTagValueRepository;
    private final UserTagValueMapper userTagValueMapper;

    public IPage<UserBaseInfoBean> listPageTagValueUser(PageRequest pageRequest, String orgId, String keyword,
            String tagId, String tagValueId) {
        IPage<UserBaseInfoBean> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        return userTagValueMapper.listPageTagValueUser(page, orgId, keyword, tagId, tagValueId);
    }

    public List<UserBaseInfoBean> listTagValueUser(String orgId, String keyword, String tagId, String tagValueId) {
        return userTagValueMapper.listPageTagValueUser(orgId, keyword, tagId, tagValueId);
    }

    public Map<String, List<UserTagValueBaseBean>> getTagValueUserMap(String orgId, Collection<String> tagValueIds,
            Collection<String> userIds) {
        if (CollectionUtils.isEmpty(tagValueIds) || CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        List<UserTagValueBaseBean> userTagValueBaseBeans = userTagValueMapper
                .listUserTagValue(orgId, tagValueIds, userIds);
        if (CollectionUtils.isNotEmpty(userTagValueBaseBeans)) {
            return userTagValueBaseBeans.stream().collect(Collectors.groupingBy(UserTagValueBaseBean::getTagId));
        }
        return Maps.newHashMap();
    }

    public List<UserTagValueEntity> initUserTagValueList(String orgId, String tagId, List<String> tagValueIds,
            List<UserTagValueBaseBean> existsUserTagValueBaseBeans, List<String> userIds, String operatorId) {
        Date now = DateUtil.currentTime();
        Map<String, List<UserTagValueBaseBean>> tagValueMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(existsUserTagValueBaseBeans)) {
            tagValueMap = existsUserTagValueBaseBeans.stream()
                    .collect(Collectors.groupingBy(UserTagValueBaseBean::getTagValueId));
        }
        List<UserTagValueEntity> userTagValueList = Lists.newArrayListWithCapacity(userIds.size());
        for (String tagValueId : tagValueIds) {
            List<String> tagValueUserIds = Lists.newArrayList();
            List<UserTagValueBaseBean> userTagValueBaseBeansTemp = tagValueMap.get(tagValueId);
            if (CollectionUtils.isNotEmpty(userTagValueBaseBeansTemp)) {
                tagValueUserIds = StreamUtil.mapList(userTagValueBaseBeansTemp, UserTagValueBaseBean::getUserId);
            }
            List<String> insertUserIds = TalentbkUtil.calculateDifferenceUserIds(userIds, tagValueUserIds);
            log.debug("LOG10650:{}", insertUserIds);
            userTagValueList.addAll(generateUserTagValues(orgId, tagId, tagValueId, insertUserIds, now, operatorId));
        }
        return userTagValueList;
    }

    private List<UserTagValueEntity> generateUserTagValues(String orgId, String tagId, String tagValueId,
            List<String> userIds, Date now, String operatorId) {
        List<UserTagValueEntity> userTagValueList = Lists.newArrayListWithExpectedSize(userIds.size());
        userIds.forEach(userId -> {
            UserTagValueEntity userTagValue = new UserTagValueEntity();
            userTagValue.setId(ApiUtil.getUuid());
            userTagValue.setOrgId(orgId);
            userTagValue.setUserId(userId);
            userTagValue.setTagId(tagId);
            userTagValue.setTagValueId(tagValueId);
            userTagValue.setCreateTime(now);
            userTagValue.setCreateUserId(operatorId);
            userTagValue.setUpdateTime(now);
            userTagValue.setUpdateUserId(operatorId);
            userTagValueList.add(userTagValue);
        });
        return userTagValueList;
    }

    public void batchSave(List<UserTagValueEntity> userTagValueList) {
        userTagValueRepository.saveBatch(userTagValueList);
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void delTagValueUser(String orgId, Collection<String> tagIds, List<String> userIds, String operatorId,
            Collection<String> tagValueIds) {
        Date now = DateUtil.currentTime();
        userTagValueMapper.delTagValueUser(orgId, tagIds, userIds, operatorId, now, tagValueIds);
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void delTagOtherValueUser(String orgId, Collection<String> delTagIdSet, List<String> userIds,
            String operatorId, Collection<String> tagValueIds) {
        Date now = DateUtil.currentTime();
        userTagValueMapper.delTagOtherValueUser(orgId, delTagIdSet, userIds, operatorId, now, tagValueIds);
    }

    /**
     * 获取用户标签值信息
     *
     * @param orgId   机构id
     * @param tagId   标签id
     * @param userIds 用户id列表
     * @return 用户标签值信息
     */
    public Map<String, List<UserTagBaseBean>> getUserTagValueNameMap(String orgId, String tagId,
            Collection<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        List<UserTagBaseBean> userTagBaseBeans = userTagValueMapper.listUserTagValueName(orgId, tagId, userIds);
        if (CollectionUtils.isNotEmpty(userTagBaseBeans)) {
            return userTagBaseBeans.stream().collect(Collectors.groupingBy(UserTagBaseBean::getUserId));
        }
        return Maps.newHashMap();
    }

    /**
     * 获取用户标签值map
     *
     * @param orgId   机构id
     * @param tagIds  标签id列表
     * @param userIds 用户id列表
     * @return 用户标签值map
     */
    public Map<String, List<UserTagBaseBean>> getUserTagValueMap(String orgId, Collection<String> tagIds,
            Collection<String> userIds) {
        if (CollectionUtils.isEmpty(tagIds) || CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        List<UserTagBaseBean> userTagBaseBeanList = userTagValueMapper.listUserTagValueByTagId(orgId, tagIds, userIds);
        if (CollectionUtils.isNotEmpty(userTagBaseBeanList)) {
            return userTagBaseBeanList.stream().collect(Collectors.groupingBy(UserTagBaseBean::getUserId));
        }
        return Maps.newHashMap();
    }

    /**
     * 获取标签值关联的所有用户id列表
     *
     * @param orgId       机构id
     * @param tagId       标签id
     * @param tagValueIds 标签值id列表
     * @return 用户id列表
     */
    public List<String> listTagValueAllUserId(String orgId, String tagId, Collection<String> tagValueIds) {
        return userTagValueMapper.listTagValueAllUserId(orgId, tagId, tagValueIds);
    }

}
