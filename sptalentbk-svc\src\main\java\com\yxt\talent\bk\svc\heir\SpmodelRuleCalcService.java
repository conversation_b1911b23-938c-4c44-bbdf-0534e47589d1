package com.yxt.talent.bk.svc.heir;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.spmodel.facade.bean.indicators.MetaTableColumnEnumInfo;
import com.yxt.spmodel.facade.bean.indicators.UserIndicatorsListParam;
import com.yxt.spmodel.facade.bean.indicators.UserIndicatorsListVO;
import com.yxt.spmodel.facade.bean.indicators.UserIndicatorsValueListVO;
import com.yxt.spmodel.facade.bean.label.LabelVO;
import com.yxt.spmodel.facade.bean.label.LabelValueVO;
import com.yxt.spmodel.facade.bean.label.UserLabelParam;
import com.yxt.spmodel.facade.bean.label.UserLabelVO;
import com.yxt.spmodel.facade.bean.rule.LabelConditionInfo;
import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import com.yxt.spmodel.facade.bean.rule.LabelRuleInfo;
import com.yxt.spmodel.facade.service.SpmodelIndicatorsService;
import com.yxt.spmodel.facade.service.SpmodelLabelService;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.svc.common.bean.BkLabelConditionBean;
import com.yxt.talent.bk.svc.common.bean.BkLabelConditionInfo;
import com.yxt.talent.bk.svc.common.bean.BkLabelRuleInfo;
import com.yxt.talent.bk.svc.heir.component.DmpRuleUserIndicatorComparator;
import com.yxt.talent.bk.svc.heir.component.DmpRuleUserLabelComparator;
import com.yxt.talent.bk.svc.heir.enums.DimLabelType;
import com.yxt.talent.bk.svc.heir.enums.LogicEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SpmodelRuleService
 *
 * <AUTHOR> geyan
 * @Date 12/10/23 11:27 am
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SpmodelRuleCalcService {
    private final SpmodelLabelService spmodelLabelService;
    private final SpmodelIndicatorsService spmodelIndicatorsService;
    private final DmpRuleUserLabelComparator dmpRuleUserLabelComparator;
    private final DmpRuleUserIndicatorComparator dmpRuleUserIndicatorComparator;

    public <T> void calcRuleMatch(String orgId, BkLabelConditionBean ruleConfig,
                                  List<T> userList, Function<T, String> userIdGetter,
                                  BiConsumer<T, Boolean> resultConsume) {
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        List<String> userIds = userList.stream().map(userIdGetter).collect(Collectors.toList());
        List<BkLabelConditionInfo> conditionGroups = ruleConfig.getConditions();
        // 提取规则中涉及到的指标id和标签id
        LabelAndIndicatorIdHolder labelAndIndicatorHolder = extractLabelIdAndIndicatorIds(conditionGroups);
        List<Long> indicatorIds = labelAndIndicatorHolder.indicatorIds;
        List<Long> labelIds = labelAndIndicatorHolder.labelIds;
        if (CollectionUtils.isEmpty(indicatorIds) && CollectionUtils.isEmpty(labelIds)) {
            return;
        }
        // 查询学员实际的标签，spmodelapi项目中给用户打标签的job是T+1的，所以可以缓存
        Map<String, UserLabelVO> userLabelMap = getUserLabelMap(userIds, orgId, labelIds);
        // 查询学员实际的指标，spmodelapi也是从机构私有库数据集中实时获取，不是T+1，所以没办法缓存
        Map<String, UserIndicatorsListVO> userIndicatorMap = getUserIndicatorMap(userIds, orgId, indicatorIds);

        for (T userInfo : userList) {
            String userId = userIdGetter.apply(userInfo);
            LabelAndIndicatorValueHolder valueHolder = getLabelAndIndicatorValueHolder(userId, userLabelMap, userIndicatorMap);
            List<LabelVO> userLabels = valueHolder.userLabels;
            List<UserIndicatorsValueListVO> userIndicators = valueHolder.userIndicators;
            // 判断学员是否满足规则设定的条件
            boolean isSatisfied =
                checkGroupConditions(userId, ruleConfig.getLogic(), conditionGroups, userLabels, userIndicators);
            resultConsume.accept(userInfo, isSatisfied);
        }
    }

    private boolean checkGroupConditions(String userId, Integer groupLogic,
                                         List<BkLabelConditionInfo> conditionGroups,
                                         List<LabelVO> userLabels,
                                         List<UserIndicatorsValueListVO> userIndicators) {

        List<Boolean> groupSatisfied = new ArrayList<>();
        for (BkLabelConditionInfo conditionGroup : conditionGroups) {
            if (conditionGroup.getState() != 0) {
                log.warn("LOG60170:规则组[{}]状态异常",
                    BeanHelper.bean2Json(conditionGroup, JsonInclude.Include.ALWAYS));
            }

            Integer ruleLogic = conditionGroup.getLogic();
            List<BkLabelRuleInfo> rules = conditionGroup.getRules();
            groupSatisfied.add(
                checkRules(ruleLogic, rules, userLabels, userIndicators));
        }

        return evaluateGroupLogic(groupLogic, groupSatisfied);
    }

    private boolean checkRules(Integer ruleLogic,
        List<BkLabelRuleInfo> rules,
        List<LabelVO> userLabels,
        List<UserIndicatorsValueListVO> userIndicators) {

        Map<Long, LabelVO> userLabelMap = StreamUtil.list2map(userLabels, LabelVO::getLabelId);
        Map<Long, UserIndicatorsValueListVO> userIndicatorMap =
            StreamUtil.list2map(userIndicators, UserIndicatorsValueListVO::getIndicatorsId);

        List<Boolean> ruleSatisfied = new ArrayList<>();
        for (BkLabelRuleInfo rule : rules) {
            CheckRuleResultHolder ruleCheckResultHolder = CheckRuleResultHolder.EMPTY;

            if (rule.getState() != 0) {
                log.warn("LOG60190:规则[{}]状态[{}]已被禁用或删除", rule.getId(), rule.getState());
            } else if (DimLabelType.isLabel(rule.getType())) {
                ruleCheckResultHolder = checkRuleLabel(rule, userLabelMap);
            } else if (DimLabelType.isIndicator(rule.getType())) {
                ruleCheckResultHolder = checkIndicatorRule(rule, userIndicatorMap);
            }
            rule.setUserValues(ruleCheckResultHolder.labelValues);
            ruleSatisfied.add(ruleCheckResultHolder.isRuleSatisfied);
            rule.setUserMatchRule(ruleCheckResultHolder.isRuleSatisfied);
        }

        // 1-与 2-或
        return ruleLogic == 1
            ? ruleSatisfied.stream().allMatch(Boolean::booleanValue)
            : ruleSatisfied.stream().anyMatch(Boolean::booleanValue);
    }

    private boolean evaluateGroupLogic(Integer groupLogic, List<Boolean> groupSatisfied) {
        if (LogicEnum.isAnd(groupLogic)) {
            return groupSatisfied.stream().allMatch(Boolean::booleanValue);
        } else {
            return groupSatisfied.stream().anyMatch(Boolean::booleanValue);
        }
    }

    private Map<String, UserLabelVO> getUserLabelMap(List<String> userIds, String orgId, List<Long> labelIds) {
        if (CollectionUtils.isEmpty(labelIds)) {
            return new HashMap<>();
        }
        // 再从标签库中取
        UserLabelParam userLabelParam = new UserLabelParam();
        userLabelParam.setOrgId(orgId);
        userLabelParam.setUserIds(userIds);
        userLabelParam.setLabelIds(labelIds);
        return StreamUtil.list2map(Optional.ofNullable(spmodelLabelService.getUserLabelList(userLabelParam))
            .orElse(new ArrayList<>()), UserLabelVO::getUserId);
    }

    private Map<String, UserIndicatorsListVO> getUserIndicatorMap(List<String> userIds, String orgId, List<Long> indicatorIds) {
        if (CollectionUtils.isEmpty(indicatorIds)) {
            return new HashMap<>();
        }
        UserIndicatorsListParam indicatorsListParam = new UserIndicatorsListParam();
        indicatorsListParam.setOrgId(orgId);
        indicatorsListParam.setUserIds(userIds);
        indicatorsListParam.setIndicatorsIds(indicatorIds);
        return StreamUtil.list2map(Optional.ofNullable(spmodelIndicatorsService.getUserIndicatorsList(indicatorsListParam))
            .orElse(new ArrayList<>()), UserIndicatorsListVO::getUserId);
    }

    @NotNull
    private static LabelAndIndicatorIdHolder extractLabelIdAndIndicatorIds(List<BkLabelConditionInfo> conditionGroups) {
        Map<Integer, List<LabelRuleInfo>> labelGroupMap = conditionGroups.stream()
            .filter(e -> CollectionUtils.isNotEmpty(e.getRules()))
            .flatMap(e -> e.getRules().stream())
            .collect(Collectors.groupingBy(LabelRuleInfo::getType));

        List<Long> indicatorIds = extractRuleIds(labelGroupMap, DimLabelType.INDICATOR);
        List<Long> labelIds = extractRuleIds(labelGroupMap, DimLabelType.LABEL);

        return new LabelAndIndicatorIdHolder(indicatorIds, labelIds);
    }

    private static List<Long> extractRuleIds(Map<Integer, List<LabelRuleInfo>> labelGroupMap, DimLabelType labelType) {
        List<LabelRuleInfo> rules = labelGroupMap.getOrDefault(labelType.getCode(), new ArrayList<>());
        return StreamUtil.mapList(rules, LabelRuleInfo::getId);
    }

    private static class LabelAndIndicatorIdHolder {
        private final List<Long> indicatorIds;
        private final List<Long> labelIds;

        public LabelAndIndicatorIdHolder(List<Long> indicatorIds, List<Long> labelIds) {
            this.indicatorIds = Collections.unmodifiableList(indicatorIds);
            this.labelIds = Collections.unmodifiableList(labelIds);
        }
    }

    private CheckRuleResultHolder checkRuleLabel(LabelRuleInfo rule, Map<Long, LabelVO> userLabelMap) {
        List<Long> labelValueIds = Lists.newArrayList();
        List<String> labelValues = Lists.newArrayList();
        LabelVO userLabel = userLabelMap.get(rule.getId());
        if (userLabel != null && CollectionUtils.isNotEmpty(userLabel.getLabelValueList())) {
            labelValueIds = StreamUtil.mapList(userLabel.getLabelValueList(), LabelValueVO::getLabelValueId);
            labelValues = StreamUtil.mapList(userLabel.getLabelValueList(), LabelValueVO::getLabelValue);
        }

        boolean isRuleSatisfied = dmpRuleUserLabelComparator.checkLabelRule(rule, userLabel);
        return new CheckRuleResultHolder(labelValues, labelValueIds, isRuleSatisfied);
    }

    @NotNull
    private CheckRuleResultHolder checkIndicatorRule(
        LabelRuleInfo rule, Map<Long, UserIndicatorsValueListVO> userIndicatorMap) {
        List<Long> labelValueIds = Lists.newArrayList();
        List<String> labelValues = Lists.newArrayList();
        UserIndicatorsValueListVO userIndicator = userIndicatorMap.get(rule.getId());
        if (userIndicator != null) {
            if (CollectionUtils.isNotEmpty(CommonUtils.filterNull(userIndicator.getEnums()))) {
                // 是一个枚举类型的指标
                log.debug("LOG60200:{}", rule.getColumnType());
                List<MetaTableColumnEnumInfo> enums = userIndicator.getEnums();
                labelValueIds = StreamUtil.mapList(enums, MetaTableColumnEnumInfo::getEnumId);
                labelValues = StreamUtil.mapList(enums, e -> String.valueOf(e.getEnumLabel()));
            } else if (CollectionUtils.isNotEmpty(CommonUtils.filterNullAndBlank(userIndicator.getIndicatorValues()))) {
                // 是一个非枚举类型的指标
                labelValues = userIndicator.getIndicatorValues();
            }
        }

        boolean isRuleSatisfied = dmpRuleUserIndicatorComparator.checkIndicatorRule(rule, userIndicator);
        return new CheckRuleResultHolder(labelValues, labelValueIds, isRuleSatisfied);
    }

    @NotNull
    private static LabelAndIndicatorValueHolder getLabelAndIndicatorValueHolder(
        String userId, Map<String, UserLabelVO> userLabelMap, Map<String, UserIndicatorsListVO> userIndicatorMap) {
        UserLabelVO userLabelVo = userLabelMap.getOrDefault(userId, new UserLabelVO());
        UserIndicatorsListVO userIndicator = userIndicatorMap.getOrDefault(userId, new UserIndicatorsListVO());
        List<LabelVO> userLabels = Optional.ofNullable(userLabelVo.getLabelList()).orElse(new ArrayList<>());
        List<UserIndicatorsValueListVO> userIndicators =
            Optional.ofNullable(userIndicator.getIndicatorsList()).orElse(new ArrayList<>());
        return new LabelAndIndicatorValueHolder(userLabels, userIndicators);
    }

    private static class LabelAndIndicatorValueHolder {
        public final List<LabelVO> userLabels;
        public final List<UserIndicatorsValueListVO> userIndicators;

        public LabelAndIndicatorValueHolder(List<LabelVO> userLabels, List<UserIndicatorsValueListVO> userIndicators) {
            this.userLabels = userLabels;
            this.userIndicators = userIndicators;
        }
    }

    private static class CheckRuleResultHolder {
        public final List<String> labelValues;
        public final List<Long> labelValueIds;
        public final boolean isRuleSatisfied;

        public static final CheckRuleResultHolder EMPTY = new CheckRuleResultHolder(
                Lists.newArrayList(), Lists.newArrayList(),
                false);

        public CheckRuleResultHolder(
                List<String> labelValueStr, List<Long> labelValueIdStr, boolean isRuleSatisfied) {
            this.labelValues = labelValueStr;
            this.labelValueIds = labelValueIdStr;
            this.isRuleSatisfied = isRuleSatisfied;
        }
    }
}
