<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirPosPrepareCfgMapper">

    <update id="updateRuleCfgDataByPrimaryKey">
        update bk_heir_pos_prepare_cfg
        set rule_cfg_id = #{ruleCfgId},
            rule_cfg_data = #{ruleCfgData},
            rule_cfg_md5 = #{ruleCfgMd5},
            update_user_id = #{updateUserId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectNeedReadinessCompute" resultType="com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity">
        select * from bk_heir_pos_prepare_cfg where deleted=0 and date_format(next_calc_time,'%Y-%m-%d')= date_format(now(),'%Y-%m-%d')
    </select>

    <select id="selectNeedReadinessRemind" resultType="com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity">
        select * from bk_heir_pos_prepare_cfg where deleted=0 and date_format(next_remind_time,'%Y-%m-%d')= date_format(now(),'%Y-%m-%d')
    </select>

    <select id="getPrepareCfgDetail" resultType="com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity">
        select * from bk_heir_pos_prepare_cfg where org_id = #{orgId} and prepare_level_id = #{prepareLevelId}
        and pos_id = #{posId} and deleted = 0 limit 1
    </select>

    <select id="getPrepareCfgData" resultType="com.yxt.talent.bk.core.heir.bean.HeirPrepareCfgDataBean">
        select prepare_level_id,rule_cfg_id,rule_cfg_data from bk_heir_pos_prepare_cfg where org_id = #{orgId}
        and pos_id = #{posId} and deleted = 0 and prepare_level_id in
        <foreach collection="prepareLevelIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectContainPrepareCfgDetail" resultType="java.lang.Long">
        select prepare_level_id from bk_heir_pos_prepare_cfg where org_id=#{orgId} and prepare_level_id in
        <foreach collection="plIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByPosId" resultType="com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity">
        select * from bk_heir_pos_prepare_cfg where org_id=#{orgId} and pos_id = #{posId} and deleted = 0
    </select>
</mapper>
