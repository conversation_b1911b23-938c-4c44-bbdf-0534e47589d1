package com.yxt.talent.bk.svc.heir;

import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.svc.heir.bean.req.PosPermissionUserAddReq;
import com.yxt.talent.bk.svc.heir.bean.resp.PosPermissionListResp;

public interface HeirPosPermissionService {

    void enable(UserCacheBasic currentUser, String posId, Integer enabled);

    Integer getEnableStatus(UserCacheBasic currentUser, String posId, Integer posType);

    CommonList<PosPermissionListResp> listUser(UserCacheBasic currentUser, String posId);

    void saveUser(UserCacheBasic currentUser, PosPermissionUserAddReq req);
}
