package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.common.Constants;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlOrder;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Setter
@Getter
@SqlTable("dwd_reward_punishment_history")
@Schema(name = "人才画像-奖惩信息历史")
public class RewardPunishmentHistory4Get {

    @Schema(description = "主键Id")
    private String id;

    @Schema(description = "绚星2.0平台用户id")
    private String userId;

    @JsonIgnore
    @Schema(description = "用户id")
    private String thirdUserId;

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "奖惩类型（1-奖项 2-惩罚）")
    private int rpType;

    @Schema(description = "奖惩名称")
    private String rpName;

    @Schema(description = "获得时间")
    @SqlOrder(order = 1, direction = SqlOrder.DESC)
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime acqTime;

    @Schema(description = "发布方")
    private String pubFrom;
}
