package com.yxt.talent.bk.svc.heir.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.Data;

/**
 * HeirBmUserEdit4Log
 *
 * <AUTHOR> geyan
 * @Date 21/3/24 10:10 am
 */
@Data
public class HeirBmUserEdit4Log {
    @AuditLogField(name = "标杆员工", orderIndex = 0, fullEqual = true)
    private String userName;
    @AuditLogField(name = "状态", orderIndex = 0)
    private String enabledDesc;
}
