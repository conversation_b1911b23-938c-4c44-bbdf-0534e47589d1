package com.yxt.talent.bk.common.constants;


import com.yxt.spsdk.common.SpContants;
import lombok.Data;

@Data
public class ExportConstants {


    private ExportConstants() {
        //NO SONAR
    }
    /**
     * .xlsx
     **/
    public static final String FILE_SUFFIX_XLSX = ".xlsx";

    public static final String FILE_SUFFIX_ZIP_ORIG = ".zip" + SpContants.FILE_SUFFIX_ORID;
    public static final String FILE_SUFFIX_XLSX_ORIG = FILE_SUFFIX_XLSX + SpContants.FILE_SUFFIX_ORID;

    public static final String FILE_SUFFIX_ZIP = ".zip";
    public static final String EXPORT_URL_KEY = "filePath";

    public static final String EXPORT_USER_SEARCH_TEMPLATE_PATH = "excel/export_user_search_template.xlsx";
    public static final String EXPORT_USER_SEARCH_RV_TEMPLATE_PATH = "excel/export_user_search_rv_template.xlsx";

    /**
     * 人才池导出
     */
    public static final String EXPORT_POOL_TEMPLATE_PATH = "excel/export_pool_template.xlsx";

    /**
     * 标签值人员导出
     */
    public static final String TAG_VALUE_USER_EXPORT_ERROR_FILE_NAME = "apis.talentbk.tag.value.user.export.error.file.name";
    public static final String TAG_USER_EXPORT_TEMPLATE_FILE_NAME = "apis.talentbk.tag.user.export.tem.file.name";
    public static final String TAG_USER_EXPORT_TEMPLATE_PATH_4_SINGLE = "excel/tag_user_export4single.xlsx";
    public static final String TAG_USER_EXPORT_TEMPLATE_PATH_4_MUL = "excel/tag_user_export4mul.xlsx";
    public static final String POOL_USER_INFO_EXPORT_TEMPLATE_PATH = "excel/pool_user_info_export.xlsx";
    public static final String TAG_USER_EXPORT_ERROR_PATH_4_SINGLE = "excel/tag_user_export4single_error.xlsx";
    public static final String TAG_USER_EXPORT_ERROR_PATH_4_MUL = "excel/tag_user_export4mul_error.xlsx";

    /**
     * 人才用户导入
     */
    public static final String POOL_USER_IMPORT_TEMP_NAME = "apis.talentbk.pool.user.temp.name";
    public static final String POOL_USER_IMPORT_TEMP_FILE = "excel/pool_user_import.xlsx";
    /**
     * 人才池用户导入错误数据
     */
    public static final String POOL_USER_FAIL_EXPORT_FILE_NAME = "apis.talentbk.pool.user.export.error.name";
    public static final String POOL_USER_FAIL_EXPORT_TEMP_FILE_PATH = "excel/pool_user_err_temp.xlsx";
    /**
     * 人才池准备度
     */
    public static final String POOL_READONESS_IMPORT_TEMP_NAME = "apis.talentbk.pool.readiness.temp.name";
    public static final String POOL_READONESS_IMPORT_TEMP_PATH = "excel/pool_readiness_temp.xlsx";

    /**
     * 人才池准备度错误导出名称
     */
    public static final String POOL_READONESS_IMPORT_ERROR_NAME = "apis.talentbk.pool.readiness.import.error.name";
    public static final String POOL_READONESST_ERROR_TEMP_FILE_PATH = "excel/readiness_import_error_temp.xlsx";

    /**
     * 人才列表导出excel字段名称
     */
    public static final String SCHEME_USER_EXPORT_NAME = "apis.talentbk.scheme.user.export.name";

    // 人才池列表
    public static final String SCHEME_POOL_EXPORT_NAME = "apis.talentbk.pool.export.name";

    public static final String SCHEME_USER_TEMP_FILE_PATH = "excel/scheme_user_template.xlsx";

    public static final String DASHBOARD_DEPT_DIM_EXPORT_FILE_NAME = "apis.talentbk.dashboard.dept.dim.temp.file.name";

    public static final String DASHBOARD_GROUP_DIM_EXPORT_FILE_NAME = "apis.talentbk.dashboard.group.dim.temp.file.name";

    public static final String DASHBOARD_PERSONAL_DIM_EXPORT_FILE_NAME = "apis.talentbk.dashboard.personal.dim.temp.file.name";

    public static final String DASHBOARD_PERSONAL_DETAIL_EXPORT_FILE_NAME = "apis.talentbk.dashboard.personal.detail.temp.file.name";

    public static final String DASHBOARD_PERSONAL_DETAIL_SUB_EXPORT_FILE_NAME = "apis.talentbk.dashboard.personal.detail.sub.temp.file.name";

    public static final String COMMON_ENABLE = "apis.talentbk.common.enable";
    public static final String COMMON_DISABLE = "apis.talentbk.common.disable";
    public static final String COMMON_DELETED = "apis.talentbk.common.deleted";

    // 人才池人员导出
    public static final String POOL_USER_EXPORT = "apis.talentbk.pool.user.export.filename";

    //账号未填写
    public static final String POOL_USER_IMPORT_USERNAME_EMPTY = "apis.talentbk.pool.user.import.username.empty";
    //账号不存在
    public static final String POOL_USER_IMPORT_USERNAME_INVALID = "apis.talentbk.pool.user.import.username.invalid";
    //没有权限导入该用户
    public static final String POOL_USER_IMPORT_USERNAME_NO_PERM = "apis.talentbk.pool.user.import.username.no.perm";
    //账号被禁用
    public static final String POOL_USER_IMPORT_USERNAME_DISABLED = "apis.talentbk.pool.user.import.username.disabled";
    //当前人员在人才池中
    public static final String POOL_USER_IMPORT_USERNAME_EXIST = "apis.talentbk.pool.user.import.username.exist";
    //导入人员重复
    public static final String POOL_USER_IMPORT_USERNAME_DUP = "apis.talentbk.pool.user.import.username.dup";

    //账号不在人才池中
    public static final String POOL_READINESS_IMPORT_USERNAME_MISS = "apis.talentbk.pool.readiness.import.username.miss";
    //准备度不能为空
    public static final String POOL_READINESS_IMPORT_READINESS_EMPTY = "apis.talentbk.pool.readiness.import.readiness.empty";
    //准备度不存在
    public static final String POOL_READINESS_IMPORT_READINESS_MISS = "apis.talentbk.pool.readiness.import.readiness.miss";
    public static final String SDINDICATOR_TYPE_TASK = "apis.talentbk.sdindicator.type.task";
    public static final String SDINDICATOR_TYPE_ABILITY = "apis.talentbk.sdindicator.type.ability";
}
