package com.yxt.talent.bk.svc.heir.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.bk.svc.common.bean.BkLabelConditionBean;
import com.yxt.talent.bk.svc.common.bean.BkLabelConditionInfo;
import com.yxt.talent.bk.svc.common.bean.BkLabelRuleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Locale;
import java.util.function.Consumer;

/**
 * HeirPrepare4Dump
 *
 * <AUTHOR> harleyge
 * @Date 25/7/24 2:57 pm
 */
@Data
public class HeirPrepare4Dump {
    @Schema(description = "主键")
    private Long id;
    @Schema(description = "自定义名称（简体）")
    private String levelName1;
    @Schema(description = "自定义名称（英文）")
    private String levelName2;
    @Schema(description = "自定义名称（繁体）")
    private String levelName3;
    @Schema(description = "颜色设置")
    private String colorCode;
    private BkLabelConditionBean ruleConfig;
    @JSONField(deserialize = false)
    @JsonIgnore
    private int levelMatched;

    public void forEachRule(Consumer<BkLabelRuleInfo> consumer) {
        if (ruleConfig != null && ruleConfig.getConditions() != null) {
            for (BkLabelConditionInfo condition : ruleConfig.getConditions()) {
                if (condition.getRules() != null) {
                    condition.getRules().forEach(consumer);
                }
            }
        }
    }

    public String queryLevelName(Locale locale) {
        if (Locale.SIMPLIFIED_CHINESE.equals(locale)) {
            return levelName1;
        } else if (Locale.TRADITIONAL_CHINESE.equals(locale)) {
            return levelName3;
        } else if (Locale.ENGLISH.equals(locale)) {
            return levelName2;
        } else{
            return levelName1;
        }
    }
}
