package com.yxt.talent.bk.svc.search.bean;

import com.yxt.common.pojo.api.PagingList;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 人才透视Bean
 */
@Schema(name = "人才搜索-主页-人才透视")
@Data
public class UserSearchListAnalyseBean {
    @Schema(description = "透视维度",example = "age")
    private String dimensionKey;
    @Schema(description = "透视维度名称",example = "年龄范围")
    private String dimensionName;
    @Schema(description = "排序")
    private Integer orderIndex=0;
    @Schema(description = "透视结果")
    private PagingList<UserSearchListAnalyseItemBean> items = new PagingList<>();

}
