package com.yxt.talent.bk.api.controller.dashboard;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.dashboard.bean.CommonExportParam;
import com.yxt.talent.bk.core.dashboard.bean.CommonSearchParam;
import com.yxt.talent.bk.core.dashboard.bean.DashBoardDeptDimVO;
import com.yxt.talent.bk.core.dashboard.bean.DashBoardGroupDimVO;
import com.yxt.talent.bk.core.dashboard.bean.DashBoardPersonalDetailVO;
import com.yxt.talent.bk.core.dashboard.bean.DashBoardPersonalDimVO;
import com.yxt.talent.bk.core.dashboard.bean.DashboardPersonalDetailExportParam;
import com.yxt.talent.bk.core.dashboard.bean.PersonalDetailSearchParam;
import com.yxt.talent.bk.core.dashboard.bean.PersonalEvalModelVO;
import com.yxt.talent.bk.svc.export.DashBoardDeptDimExportService;
import com.yxt.talent.bk.svc.dashboard.TalentBoardService;
import com.yxt.talent.bk.svc.export.DashBoardGroupDimExportService;
import com.yxt.talent.bk.svc.export.DashBoardPersonalDetailExportService;
import com.yxt.talent.bk.svc.export.DashBoardPersonalDimExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Tag(name = "胜任看板")
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mgr/dashboard")
public class TalentGroupBoardController extends BaseController {

    private final TalentBoardService talentBoardService;
    private final DashBoardDeptDimExportService dashBoardDeptDimExportService;
    private final DashBoardGroupDimExportService dashBoardGroupDimExportService;
    private final DashBoardPersonalDimExportService dashBoardPersonalDimExportService;
    private final DashBoardPersonalDetailExportService dashBoardPersonalDetailExportService;

    @Operation(summary = "胜任看板—查询部门维度数据")
    @Parameters({@Parameter(name = "limit", description = "每页记录数. 默认值为20.", in = ParameterIn.QUERY),
            @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "
                    + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY)})
    @PostMapping(value = "/dept/dimension")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<DashBoardDeptDimVO> getDeptDimDashBoard(@RequestBody CommonSearchParam searchParam) {
        TalentbkUtil.isUuids(searchParam.getDeptIds(), BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserCacheDetail userDetail = getUserCacheDetail();
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return talentBoardService.getDeptDimDashBoard(userDetail, searchParam, paging);
    }

    @Operation(summary = "胜任看板—导出部门维度数据")
    @PostMapping(value = "/dept/dimension/export")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void exportDeptDimDashBoard(@RequestBody CommonSearchParam searchParam) {
        TalentbkUtil.isUuids(searchParam.getDeptIds(), BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserCacheDetail userDetail = getUserCacheDetail();
        CommonExportParam exportParam = getCommonExportParam(searchParam, userDetail);
        dashBoardDeptDimExportService.exportDeptDimDashBoard(exportParam, userDetail.getOrgId(), userDetail.getUserId(),
                userDetail.getFullname());
    }


    @Operation(summary = "胜任看板—查询群组维度数据")
    @Parameters({@Parameter(name = "limit", description = "每页记录数. 默认值为20.", in = ParameterIn.QUERY),
            @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "
                    + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY)})
    @PostMapping(value = "/usergroup/dimension")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<DashBoardGroupDimVO> getGroupDimDashBoard(@RequestBody CommonSearchParam searchParam) {
        TalentbkUtil.isUuids(searchParam.getDeptIds(), BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        return talentBoardService.getGroupDimDashBoard(userBasic, searchParam, pageRequest);
    }

    @Operation(summary = "胜任看板—导出群组维度数据")
    @PostMapping(value = "/usergroup/dimension/export")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void exportGroupDimDashBoard(@RequestBody CommonSearchParam searchParam) {
        TalentbkUtil.isUuids(searchParam.getDeptIds(), BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserCacheDetail userDetail = getUserCacheDetail();
        CommonExportParam exportParam = getCommonExportParam(searchParam, userDetail);
        dashBoardGroupDimExportService.exportGroupDimDashBoard(exportParam, userDetail.getOrgId(),
                userDetail.getUserId(), userDetail.getFullname());
    }


    @Operation(summary = "胜任看板—查询个人维度列表")
    @Parameters({@Parameter(name = "limit", description = "每页记录数. 默认值为20.", in = ParameterIn.QUERY),
            @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "
                    + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY)})
    @PostMapping(value = "/personal/dimension")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<DashBoardPersonalDimVO> getPersonalDimDashBoard(@RequestBody CommonSearchParam searchParam) {
        TalentbkUtil.isUuids(searchParam.getDeptIds(), BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return talentBoardService.getPersonalDimDashBoard(userBasic, paging, searchParam);
    }

    @Operation(summary = "胜任看板—导出个人维度列表数据")
    @PostMapping(value = "/personal/dimension/export")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void exportPersonalDimDashBoard(@RequestBody CommonSearchParam searchParam) {
        TalentbkUtil.isUuids(searchParam.getDeptIds(), BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserCacheDetail userDetail = getUserCacheDetail();
        CommonExportParam exportParam = getCommonExportParam(searchParam, userDetail);
        dashBoardPersonalDimExportService.exportPersonalDimDashBoard(exportParam, userDetail.getOrgId(),
                userDetail.getUserId(), userDetail.getFullname());

    }

    @Operation(summary = "胜任看板—查询个人详情数据")
    @Parameter(name = "userId", description = "选择的用户记录", in = ParameterIn.PATH)
    @PostMapping(value = "/personal/{userId}/detail")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public DashBoardPersonalDetailVO getPersonalDetail(@PathVariable String userId) {
        TalentbkUtil.isUuid(userId, BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        String orgId = userBasic.getOrgId();
        return talentBoardService.getPersonalDetail(orgId, userId);
    }

    @Operation(summary = "胜任看板—导出个人详情数据")
    @Parameter(name = "userId", description = "选择的用户记录", in = ParameterIn.PATH)
    @PostMapping(value = "/personal/detail/export")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public void exportPersonalDetail(@RequestBody PersonalDetailSearchParam searchParam) {
        TalentbkUtil.isUuids(searchParam.getDeptIds(), BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        TalentbkUtil.isUuids(searchParam.getUserIds(), BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserCacheDetail userDetail = getUserCacheDetail();
        dashBoardPersonalDetailExportService.exportPersonalDetailDashBoard(userDetail,searchParam);
    }


    @Operation(summary = "胜任看板—查询个人详情的能力模型数据")
    @Parameter(name = "userId", description = "选择的用户记录", in = ParameterIn.PATH)
    @PostMapping(value = "/personal/{userId}/{modelType}/{modelId}/detail")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public PersonalEvalModelVO getPersonalModelDetail(@PathVariable String userId, @PathVariable Integer modelType,
            @PathVariable String modelId) {
        TalentbkUtil.isUuid(userId, BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        TalentbkUtil.isUuid(modelId, BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        String orgId = userBasic.getOrgId();
        return talentBoardService.getPersonalModelDetail(orgId, userId, modelType, modelId);
    }

    @NotNull
    private CommonExportParam getCommonExportParam(CommonSearchParam searchParam, UserCacheDetail userDetail) {
        CommonExportParam exportParam = new CommonExportParam();
        BeanHelper.copyProperties(searchParam, exportParam);
        exportParam.setOrgId(userDetail.getOrgId());
        exportParam.setLocale(userDetail.getLocale());
        exportParam.setOptUserId(userDetail.getUserId());
        exportParam.setAdmin(userDetail.getAdmin());
        return exportParam;
    }

}
