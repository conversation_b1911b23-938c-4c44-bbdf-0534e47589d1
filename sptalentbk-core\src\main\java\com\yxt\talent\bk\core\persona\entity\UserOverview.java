package com.yxt.talent.bk.core.persona.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用户扩展信息表
*
 * <AUTHOR>
 * @since 2022/8/12 14:48
 */
@Data
@NoArgsConstructor
@TableName("bk_user_overview")
@EqualsAndHashCode(callSuper = true)
public class UserOverview extends CreatorEntity {
    @TableId("id")
    private String id;

    @TableField("org_id")
    private String orgId;

    @TableField("user_id")
    private String userId;

    /**
     * 个人特征综述
     */
    @TableField("feature_overview")
    private String featureOverview;
}
