package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.entity.HeirLangI18nEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * HeirLangI18nMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 2:29 pm
 */
@Mapper
public interface HeirLangI18nMapper extends BkBaseMapper<HeirLangI18nEntity> {
    /**
     * 按组删除
     * @param orgId
     * @param groupId
     * @return
     */
    int deleteByGroupId(@Param("orgId") String orgId,@Param("groupId") Long groupId);

    /**
     * 批量插入
     * @param list
     */

    void batchInsert(@Param("list") List<HeirLangI18nEntity> list);

    /**
     * 批量查询
     * @param orgId
     * @param list
     * @return
     */
    List<HeirLangI18nEntity> selectByGroupIds(@Param("orgId") String orgId,@Param("list") Collection<Long> list);
}
