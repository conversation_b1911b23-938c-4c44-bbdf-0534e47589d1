<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdDeptMapper">


    <select id="getDeptPage" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdDept">
        select dept_id,third_dept_id,routing_path_name,third_dept_name
        from dwd_dept
        where org_id ='${orgId}'
        <if test="deptIds != null and deptIds.size() > 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
        </if>
        order by routing_path,dept_id asc
        <!--@ignoreSql-->
        limit ${offset},${pageSize}
    </select>
    <select id="getDeptTotalCount" resultType="java.lang.Long">
        select count(1)
        from dwd_dept
        where org_id ='${orgId}'
        <if test="deptIds != null and deptIds.size() > 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
        </if>
    </select>
    <select id="getByIds" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdDept">
        select dept_id,third_dept_id,routing_path_name,third_dept_name
        from dwd_dept
        where org_id ='${orgId}'
        <if test="deptIds != null and deptIds.size() >0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
        </if>
        order by routing_path,dept_id asc
    </select>
    <select id="getByDeptIds" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdDept">
        select dept_id,third_dept_id,routing_path_name,third_dept_name
        from dwd_dept
        where org_id ='${orgId}'
        <if test="deptIds != null and deptIds.size() > 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
        </if>
        order by routing_path,dept_id asc
    </select>
</mapper>
