package com.yxt.talent.bk.svc.persona.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "主题详情")
public class Theme4List {

    @Schema(description = "画像主题id")
    private String themeId;
    @Schema(description = "画像主题名称")
    private String themeName;
    @Schema(description = "画像主题来源(0-内置,1-自建)")
    private Integer themeSource;
    @Schema(description = "画像主题-维度列表")
    private List<Dimension4List> dimension;

}
