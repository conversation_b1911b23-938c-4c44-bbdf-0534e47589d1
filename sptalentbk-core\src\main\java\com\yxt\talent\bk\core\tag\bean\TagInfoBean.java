package com.yxt.talent.bk.core.tag.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标签信息bean
 *
 * <AUTHOR>
 * @since 2022/8/8
 */
@Data
@NoArgsConstructor
public class TagInfoBean {

    /**
     * 标签id
     */
    private String id;
    /**
     * 标签key
     */
    private String tagKey;
    /**
     * 标签类型(0-普通标签,1-分层标签)
     */
    private Integer tagType = 0;
    /**
     * 标签值选择类型(0-单选,1-多选)
     */
    private Integer valueChooseModel = 0;
    /**
     * 标签来源(0-内置,1-自建,2-固定)
     */
    private Integer tagSource = 0;

    /**
     * 标签值id
     */
    private String tagValueId;

}
