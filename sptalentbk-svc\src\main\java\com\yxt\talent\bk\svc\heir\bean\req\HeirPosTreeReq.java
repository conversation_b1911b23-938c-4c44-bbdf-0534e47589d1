package com.yxt.talent.bk.svc.heir.bean.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * HeirPosMapReq
 *
 * <AUTHOR> geyan
 * @Date 17/8/23 1:39 pm
 */
@Data
public class HeirPosTreeReq {
    @Schema(description = "部分或者岗位ids")
    private List<String> posIds;
    @Schema(description = "继任者用户ids")
    private List<String> userIds;
    @Schema(description = "继任风险id")
    private Long riskLevelId;
}
