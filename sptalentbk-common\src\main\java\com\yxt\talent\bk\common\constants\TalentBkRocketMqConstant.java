package com.yxt.talent.bk.common.constants;

import lombok.experimental.UtilityClass;

@UtilityClass
public class TalentBkRocketMqConstant {

    /**
     * 往培训项目加人的topic
     */
    public static final String TOPIC_S_TALENT_ADD_GROUP_MEMBER = "talent-add-group-member";

    public static final String TOPIC_SPTALENTBK_EXPORT_FILE = "sptalentBk-export-file";

    public static final String GROUP_PREFIX = "sptalentBk-group-";

    /**
     * sptalentbk异步导出
     */
    public static final String TOPIC_SPTALENTBK_EXPORT_SYNC = "sptalentbk-export-async";

    /**
     * spmodel复制完成通知
     */
    public static final String TOPIC_SPMODEL_ORG_COPY_SUCCESS = "spmodel-org-copy-success";

    public static final String TOPIC_SHARE_TRANSFER_USER_RESOURCE = "share-transfer-user-resource";

    public static final String TOPIC_SHARE_TRANSFER_USER_RESOURCE_CALLBACK = "share-transfer-user-resource-callback";
}
