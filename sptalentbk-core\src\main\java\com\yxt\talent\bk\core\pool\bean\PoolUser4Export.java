package com.yxt.talent.bk.core.pool.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.spsdk.common.annotation.SpExcelProperty;
import com.yxt.talent.bk.common.utils.DateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 人才池人员导出
 */
@Data
public class PoolUser4Export {

    @Schema(description = "姓名")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.fullName", index = 0)
    private String fullName;

    @Schema(description = "账号")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.username", index = 1)
    private String username;

    @Schema(description = "主部门全路径名称，例：研发中心->园区 ")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.deptName", index = 2)
    private String deptName;

    @Schema(description = "主岗位名称")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.positionName", index = 3)
    private String positionName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+0800")
    private Date createTime;

    // ，入池时间
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.createTimeStr", index = 4)
    private String createTimeStr;

    @Schema(description = "准备度页面显示值Str")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.readinessValueStr", index = 5)
    private String readinessValueStr;



    @Schema(description = "职级名称")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.gradeName", index = 6)
    private String gradeName;

    /**
     * 标签字段
     */
    @Schema(description="司龄")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.entryDate", index = 7)
    private String entryDate;

    @Schema(description="能力等级（高、中、低）")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.abilityLevel", index = 8)
    private String abilityLevel;

    @Schema(description="潜力等级（高、中、低）")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.potentialLevel", index = 9)
    private String potentialLevel;

    @Schema(description="绩效等级（高、中、低）")
    @SpExcelProperty(nameKey = "apis.talentbk.pool.user.export.performanceLevel", index = 10)
    private String performanceLevel;

    @Schema(description = "准备度页面显示值")
    private Integer readinessValue;



    @Schema(description = "准备度(当前值)")
    private Integer orderIndex;

    @Schema(description = "准备度(饱和度)")
    private Integer readiness;










    /**
     * 计算准备度
     */
    public Integer getReadinessValue() {
        if (Objects.nonNull(orderIndex) && Objects.nonNull(readiness)) {
            // 准备度计算方式 准备度(饱和度) - 准备度(当前值)
            return readiness - orderIndex;
        }
        return null;
    }

    /**
     * 计算准备度str
     */
    public String getReadinessValueStr() {
        if (StringUtils.isNotBlank(this.readinessValueStr)) {
            return this.readinessValueStr;
        }
        return "-";
    }

    /**
     * 计算准备度str
     */
    public String getCreateTimeStr() {
        if (Objects.isNull(createTime)) {
            return "-";
        }
        return DateUtils.dateToString(createTime, "yyyy-MM-dd");
    }

    public String getFullName() {
        if (StringUtils.isBlank(fullName)) {
            return "-";
        }
        return fullName;
    }

    public String getUsername() {
        if (StringUtils.isBlank(username)) {
            return "-";
        }
        return username;
    }

    public String getDeptName() {
        if (StringUtils.isBlank(deptName)) {
            return "-";
        }
        return deptName;
    }

    public String getPositionName() {
        if (StringUtils.isBlank(positionName)) {
            return "-";
        }
        return positionName;
    }

    public String getGradeName() {
        if (StringUtils.isBlank(gradeName)) {
            return "-";
        }
        return gradeName;
    }

    public String getEntryDate() {
        if (StringUtils.isBlank(entryDate)) {
            return "-";
        }
        return entryDate;
    }

    public String getAbilityLevel() {
        if (StringUtils.isBlank(abilityLevel)) {
            return "-";
        }
        return abilityLevel;
    }

    public String getPotentialLevel() {
        if (StringUtils.isBlank(potentialLevel)) {
            return "-";
        }
        return potentialLevel;
    }

    public String getPerformanceLevel() {
        if (StringUtils.isBlank(performanceLevel)) {
            return "-";
        }
        return performanceLevel;
    }

    @Override
    public String toString() {
        return "PoolUser4Export{" + "fullName='" + fullName + '\'' + ", username='" + username + '\'' + ", deptName='"
                + deptName + '\'' + ", positionName='" + positionName + '\'' + ", createTime=" + createTime
                + ", createTimeStr='" + createTimeStr + '\'' + ", orderIndex=" + orderIndex + ", readiness=" + readiness
                + ", readinessValue=" + readinessValue + ", readinessValueStr='" + readinessValueStr + '\''
                + ", gradeName='" + gradeName + '\'' + ", entryDate='" + entryDate + '\'' + ", abilityLevel='"
                + abilityLevel + '\'' + ", potentialLevel='" + potentialLevel + '\'' + ", performanceLevel='"
                + performanceLevel + '\'' + '}';
    }
}
