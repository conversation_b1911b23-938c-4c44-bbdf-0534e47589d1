package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 此类不要修改！！！！！ by  zs
 */
@Data
public class TagGroupBean {

    private String tagId;

    private String tagName;
    @Schema(description = "标签可以使用且或情况，0 只能使用 或，1 且 或 都能使用")
    private Integer logic = 0;

    /**
     * 标签类型, 0-普通标签,1-分层标签
     */
    private Integer type = 0;

    /**
     * 0-单选，1-多选
     */
    private Integer valueChooseModel;


    private List<TagValueBean> tagValueBeanList;

    private Integer logicValue;

    private List<TagValueBean4Check> checkedValue;

    private List<String> date;
}
