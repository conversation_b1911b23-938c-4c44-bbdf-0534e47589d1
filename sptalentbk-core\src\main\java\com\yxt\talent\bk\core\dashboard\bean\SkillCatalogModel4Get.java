package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class SkillCatalogModel4Get {
    @Schema(description = "能力模板能力分类uuid")
    private String id;

    @Schema(description = "能力模板能力分类名称")
    private String name;

    @Schema(description = "排序", example = "1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer orderIndex;

    @Schema(description = "二级分类或能力列表", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<SkillModelClassification4Get> skillModelClassifications= new ArrayList<>();
}
