package com.yxt.talent.bk.common.bean.searchrule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @program: sptalentb<PERSON><PERSON>
 * @description:
 **/
@Data
public class BaseSearchBean {
    @Schema(description = "基础默认搜索类型，1：交集，2：并集", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer searchType;
    @Schema(description = "部门列表", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Dept> depts;
    @Schema(description = "岗位 IDs", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> positionIds;
    @Schema(description = "职级 IDs", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> gradeIds;
    @Schema(description = "人员 ids", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> userIds;

    @Schema(description = "搜索，姓名 账号")
    private String keyword;

    @Schema(description = "禁用：0，启用：1", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    @Data
    public static class Dept{
        @Schema(description = "部门 ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
        private String deptId;
        @Schema(description = "是否包含子部门，1：包含，0：不包含", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer includeAll;
    }

    public boolean emptyCondition() {
        return CollectionUtils.isEmpty(depts)
                && CollectionUtils.isEmpty(positionIds)
                && CollectionUtils.isEmpty(gradeIds)
                && CollectionUtils.isEmpty(userIds);
    }
}
