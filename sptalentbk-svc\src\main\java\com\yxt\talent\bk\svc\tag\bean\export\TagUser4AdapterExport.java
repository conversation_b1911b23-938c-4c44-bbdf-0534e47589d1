package com.yxt.talent.bk.svc.tag.bean.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * : 标签值导入人员 数据结构
 * <AUTHOR>
 * @since 2022/8/10 16:27
 */
@Data
@NoArgsConstructor
public class TagUser4AdapterExport {

    /**
     * '标签值选择类型(0-单选,1-多选)'
     */
    private Integer tagType;

    @Schema(description = "用户账号")
    private List<TagUser4Export> data;
}
