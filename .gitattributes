## GITATTRIBUTES FOR WEB PROJECTS
#
# These settings are for any web project.
#
# Details per file setting:
#   text    These files should be normalized (i.e. convert CRLF to LF).
#   binary  These files are binary and should be left untouched.
#
# Note that binary is a macro for -text -diff.
######################################################################

## AUTO-DETECT - Handle line endings automatically for files detected
## as text and leave all files detected as binary untouched.
## This will handle all files NOT defined below.
* text=auto

## Scripts
mvnw            text eol=lf
*.sh            text eol=lf
*.ps1           text
*.cmd           text eol=crlf
*.bat           text eol=crlf

## Documentation
license         text eol=lf
LICENSE         text eol=lf

## Configs
*.cnf           text eol=lf
*.conf          text eol=lf
*.config        text eol=lf
.editorconfig   text eol=lf
.gitattributes  text eol=lf
.gitconfig      text eol=lf
.gitignore      text eol=lf
*.yaml          text eol=lf
*.yml           text eol=lf
*.xml           text eol=lf
