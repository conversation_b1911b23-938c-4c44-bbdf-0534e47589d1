package com.yxt.talent.bk.api.component;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PageBean;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.ILock;
import com.yxt.common.util.*;
import com.yxt.idworker.YxtIdWorker;
import com.yxt.spmodel.facade.bean.label.LabelUserVO;
import com.yxt.spsdk.common.bean.ExportParam;
import com.yxt.spsdk.common.utils.YxtExportUtils;
import com.yxt.talent.bk.common.bean.searchrule.BaseSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SPTagSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import com.yxt.talent.bk.common.bean.udp.DeptConditionBean;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.common.validator.PermissionValidator;
import com.yxt.talent.bk.core.pool.bean.SearchDeptInfo;
import com.yxt.talent.bk.core.udp.entity.UdpDept;
import com.yxt.talent.bk.core.udp.mapper.UdpDeptMapper;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.core.usergroup.bean.Scheme4Search;
import com.yxt.talent.bk.core.usergroup.bean.SchemeBean;
import com.yxt.talent.bk.core.usergroup.bean.SchemeRuleVO;
import com.yxt.talent.bk.core.usergroup.bean.SchemeUser4Export;
import com.yxt.talent.bk.core.usergroup.entity.SearchRule;
import com.yxt.talent.bk.core.usergroup.entity.SearchScheme;
import com.yxt.talent.bk.core.usergroup.repo.SearchRuleRepository;
import com.yxt.talent.bk.core.usergroup.repo.SearchSchemeRepository;
import com.yxt.talent.bk.svc.scheme.bean.Scheme4List;
import com.yxt.talent.bk.svc.scheme.bean.SearchSchemeBean;
import com.yxt.talent.bk.svc.udp.DeptHierarchyUtils;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.udp.rpc.UdpOrgSearchRpc;
import com.yxt.talent.bk.svc.usergroup.rpc.TagSearchRpc;
import com.yxt.udpfacade.bean.dept.DeptTreeId;
import com.yxt.udpfacade.service.UdpFacade;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/8/16 15:57
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class TalentSchemeComponent {

    private static final String NAVCODE = "sp_gwnl_file_search";
    /**
     * 员工人员管辖范围
     */
    private static final String PERMISSION_CODE = "sp_file_talentlist_get_extent";
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final SearchSchemeRepository schemeRepository;
    private final SearchRuleRepository ruleRepository;
    private final UdpOrgSearchRpc udpOrgSearchRpc;
    private final TagSearchRpc tagSearchRpc;
    private final ILock lockService;
    private final UdpFacade udpFacade;
    private final PermissionValidator permissionValidator;
    private final UserAuthService userAuthService;
    private final UdpDeptMapper udpDeptMapper;

    /**
     * 人才列表
     *
     * @param pageRequest
     * @param orgId
     * @param query
     * @return
     */
    public PagingList<SchemeBean> findTaletScheme(
            HttpServletRequest request, PageRequest pageRequest, String orgId, Scheme4Search query,
            UserCacheDetail userDetail) {

        //List<String> deptIds = new ArrayList<>();
        // 如果不是管理员
        List<String> deptIds = StreamUtil.mapList(query.getDepts(), SearchDeptInfo::getDeptId);
        if (!StringPool.ONE.equalsIgnoreCase(userDetail.getAdmin())) {
            if (CollectionUtils.isEmpty(deptIds)) {
                Map<String, List<String>> perm =
                        permissionValidator.getPermissionMsg(
                                orgId, userDetail.getUserId(), deptIds, NAVCODE, PERMISSION_CODE, userDetail);
                List<String> userIds = perm.get("userIds");
                if (userIds.size() > 1000) {
                    log.warn("用户权限过多,请联系管理员: userCount={}", userIds.size());
                }
                //query.setUserIds(userIds);
                deptIds = perm.get("deptIds");
                if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(deptIds)) {
                    return BeanCopierUtil.toPagingList(new Page<>(pageRequest.getCurrent(), pageRequest.getSize()));
                }
            } else {
                log.debug("使用传入的部门={}", deptIds);
            }
        }

        // 优化：部门权限过多时，使用闭包表思想将deptIds按照父子层级进行过滤，找出最顶层的几个部门带入查询
        if (CollectionUtils.isNotEmpty(deptIds)) {
            log.warn("部门过多: deptCount={}", deptIds.size());
            deptIds = optimizeDeptHierarchy(orgId, deptIds);
            query.setDepts(deptIds.stream().map(e -> new SearchDeptInfo(e, 1)).collect(Collectors.toList()));
        }

        List<SPTagSearchBean> tagSearch = query.getTagSearch();
        if (CollectionUtils.isNotEmpty(tagSearch)) {
            // 自定义筛选
            //query.setUserIds(userAuthService.mergeAuthDeptUser(orgId, query.getUserIds(), deptIds));
            return getSchemeBeanPagingList(request, pageRequest, orgId, query);
        } else if (query.getSchemeId() != null) {
            // 常用筛选器
            //query.setUserIds(userAuthService.mergeAuthDeptUser(orgId, query.getUserIds(), deptIds));
            return findSchemeBySchemeId(request, pageRequest, orgId, query);
        }

        // 其他正常搜索
        IPage<SchemeBean> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<SchemeBean> pageList = udpLiteUserRepository.findUserSchemePage(page, orgId, query,
                DeptConditionBean.createBy(query.getDepts(), SearchDeptInfo::getDeptId, SearchDeptInfo::getIncludeAll));
        return BeanCopierUtil.toPagingList(pageList);
    }

    private PagingList<SchemeBean> findSchemeBySchemeId(
            HttpServletRequest request, PageRequest pageRequest, String orgId, Scheme4Search query) {
        SearchScheme scheme = schemeRepository.findSchemeById(orgId, query.getSchemeId());
        Validate.isNotNull(scheme, "apis.talentbk.tag.scheme.exist");
        SearchRule rule = ruleRepository.queryRuleById(orgId, scheme.getSearchRuleId());
        BaseSearchBean baseSearch = new BaseSearchBean();
        BeanCopierUtil.copy(query, baseSearch);
        // 处理部门
        if (CollectionUtils.isNotEmpty(query.getDepts())) {
            List<BaseSearchBean.Dept> depts = new ArrayList<>();
            for (SearchDeptInfo dept : query.getDepts()) {
                BaseSearchBean.Dept tempDept = new BaseSearchBean.Dept();
                tempDept.setDeptId(dept.getDeptId());
                tempDept.setIncludeAll(CommonUtils.bool2Int(CommonUtils.isTrue(dept.getIncludeAll())));
                depts.add(tempDept);
            }
            baseSearch.setDepts(depts);
        }

        SearchRuleBean searchRuleBean = new SearchRuleBean();
        List<SPTagSearchBean> spTagSearchBeans =
                BeanHelper.json2Bean(rule.getTagSearch(), List.class, SPTagSearchBean.class);
        searchRuleBean.setTagSearch(spTagSearchBeans);
        searchRuleBean.setTagSearchType(rule.getTagSearchType());

        PageBean pageBean = new PageBean();
        String offset = request.getParameter("offset");
        long resOffset;
        if (StringUtils.isNotBlank(offset)) {
            resOffset = Long.parseLong(offset);
        } else {
            resOffset = 0;
        }
        pageBean.setOffset(resOffset);
        pageBean.setLimit(pageRequest.getSize());
        PagingList<LabelUserVO> userVOPagingList =
                tagSearchRpc.searchUserByTag(orgId, searchRuleBean, baseSearch, pageBean);
        PagingList<SchemeBean> resPageList = new PagingList<>();
        if (CollectionUtils.isEmpty(userVOPagingList.getDatas())) {
            IPage<SchemeBean> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
            return BeanCopierUtil.toPagingList(page);
        }

        resPageList.setPaging(userVOPagingList.getPaging());
        List<SchemeBean> tempList = new ArrayList<>();
        userVOPagingList.getDatas().forEach(e -> {
            SchemeBean schemeBean = new SchemeBean();
            BeanCopierUtil.copy(e, schemeBean);
            tempList.add(schemeBean);
        });
        resPageList.setDatas(tempList);
        return resPageList;
    }

    private PagingList<SchemeBean> getSchemeBeanPagingList(
            HttpServletRequest request, PageRequest pageRequest, String orgId, Scheme4Search query) {
        SearchRuleBean searchRuleBean = new SearchRuleBean();
        searchRuleBean.setTagSearch(query.getTagSearch());
        searchRuleBean.setTagSearchType(query.getTagSearchType());
        BaseSearchBean baseSearch = new BaseSearchBean();
        BeanCopierUtil.copy(query, baseSearch);
        // 处理部门
        if (CollectionUtils.isNotEmpty(query.getDepts())) {
            List<BaseSearchBean.Dept> depts = new ArrayList<>();
            for (SearchDeptInfo searchDept : query.getDepts()) {
                BaseSearchBean.Dept tempDept = new BaseSearchBean.Dept();
                tempDept.setDeptId(searchDept.getDeptId());
                tempDept.setIncludeAll(CommonUtils.bool2Int(CommonUtils.isTrue(searchDept.getIncludeAll())));
                depts.add(tempDept);
            }
            baseSearch.setDepts(depts);
        }

        PageBean pageBean = new PageBean();
        String offset = request.getParameter("offset");
        long resOffset;
        if (StringUtils.isNotBlank(offset)) {
            resOffset = Long.parseLong(offset);
        } else {
            resOffset = 0;
        }
        pageBean.setOffset(resOffset);
        pageBean.setLimit(pageRequest.getSize());
        PagingList<LabelUserVO> userVOPagingList =
                tagSearchRpc.searchUserByTag(orgId, searchRuleBean, baseSearch, pageBean);
        PagingList<SchemeBean> resPageList = new PagingList<>();
        if (CollectionUtils.isEmpty(userVOPagingList.getDatas())) {
            IPage<SchemeBean> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
            return BeanCopierUtil.toPagingList(page);
        }

        resPageList.setPaging(userVOPagingList.getPaging());
        List<SchemeBean> tempList = new ArrayList<>();
        userVOPagingList.getDatas().forEach(e -> {
            SchemeBean schemeBean = new SchemeBean();
            BeanCopierUtil.copy(e, schemeBean);
            tempList.add(schemeBean);
        });
        resPageList.setDatas(tempList);
        return resPageList;
    }

    /**
     * 常用筛选器列表,只查看自己创建的
     *
     * @param orgId
     * @return
     */
    public List<Scheme4List> findSchemeList(String orgId, String userId) {
        List<SchemeRuleVO> schemeRules = schemeRepository.findSchemeRuleVO(orgId, userId);
        List<Scheme4List> res = new ArrayList<>();

        for (SchemeRuleVO schemeRule : schemeRules) {
            Scheme4List scheme = new Scheme4List();
            scheme.setId(schemeRule.getSchemeId());
            scheme.setSchemeName(schemeRule.getSchemeName());
            scheme.setSchemeDesc(schemeRule.getSchemeDesc());
            scheme.setRuleId(schemeRule.getRuleId());
            List<SPTagSearchBean> spTagSearchBeans =
                    BeanHelper.json2Bean(schemeRule.getTagSearch(), List.class, SPTagSearchBean.class);
            scheme.setTagSearch(spTagSearchBeans);
            scheme.setTagSearchType(schemeRule.getTagSearchType());
            res.add(scheme);
        }
        return res;
    }

    /**
     * 获取筛选方案内容明细
     *
     * @param orgId
     * @param schemeId
     * @return
     */
    public SearchRuleBean findSchemeDetail(String orgId, Long schemeId) {
        SearchScheme scheme = schemeRepository.findSchemeById(orgId, schemeId);
        Validate.isNotNull(scheme, "apis.talentbk.tag.scheme.exist");
        SearchRule searchRule = ruleRepository.queryRuleById(orgId, scheme.getSearchRuleId());
        List<SPTagSearchBean> spTagSearchBeans =
                BeanHelper.json2Bean(searchRule.getTagSearch(), List.class, SPTagSearchBean.class);
        Integer tagSearchType = searchRule.getTagSearchType();
        SearchRuleBean ruleBean = new SearchRuleBean();
        ruleBean.setTagSearchType(tagSearchType);
        ruleBean.setTagSearch(spTagSearchBeans);
        ruleBean.setRuleId(searchRule.getId());
        return ruleBean;
    }

    /**
     * 创建筛选方案
     *
     * @param orgId
     * @param bean
     * @return
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public SearchSchemeBean createSchemeRule(String orgId, String userId, SearchSchemeBean bean) {
        SearchScheme scheme = new SearchScheme();
        Long schemeId = YxtIdWorker.getId();

        BeanCopierUtil.copy(bean, scheme);
        scheme.setOrgId(orgId);
        scheme.setDeleted(YesOrNo.NO.getValue());
        scheme.setId(schemeId);
        EntityUtil.setCreateInfo(userId, scheme);

        SearchRule rule = new SearchRule();
        rule.setDeleted(YesOrNo.NO.getValue());
        Long ruleId = YxtIdWorker.getId();
        rule.setId(ruleId);
        rule.setOrgId(orgId);
        rule.setTagSearchType(bean.getTagSearchType());
        rule.setTagSearch(BeanHelper.bean2Json(bean.getTagSearch()));
        EntityUtil.setCreateInfo(userId, rule);

        scheme.setSearchRuleId(ruleId);


        schemeRepository.save(scheme);
        ruleRepository.save(rule);
        bean.setId(schemeId);
        bean.setRuleId(ruleId);

        return bean;
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void editSchemeRule(String orgId, String userId, SearchSchemeBean bean) {
        Long schemeId = bean.getId();
        SearchScheme scheme = schemeRepository.findSchemeById(orgId, schemeId);
        Validate.isNotNull(scheme, "apis.talentbk.tag.scheme.exist");
        scheme.setSchemeName(bean.getSchemeName());
        scheme.setSchemeDesc(bean.getSchemeDesc());
        EntityUtil.setUpdatedInfo(userId, scheme);

        Long ruleId = scheme.getSearchRuleId();
        if (ruleId != null) {
            SearchRule rule = ruleRepository.queryRuleById(orgId, ruleId);
            Validate.isNotNull(rule, "apis.talentbk.tag.scheme.rule.exist");
            rule.setTagSearchType(bean.getTagSearchType());
            rule.setTagSearch(BeanHelper.bean2Json(bean.getTagSearch()));
            EntityUtil.setUpdatedInfo(userId, rule);
            ruleRepository.updateById(rule);
        }

        schemeRepository.updateById(scheme);
    }

    public void deleteScheme(String orgId, String userId, Long schemeId) {
        schemeRepository.removeScheme(orgId, schemeId, userId);
    }

    /**
     * 人才列表导出
     *
     * @param userDetail
     * @param bean
     * @return
     */
    public Map<String, String> export(UserCacheDetail userDetail, Scheme4Search bean) {
        String orgId = userDetail.getOrgId();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_SCHEME_USER_OPERATE, "schemeuserexport", orgId);
        String path = "";
        String exportName = TalentbkUtil.getMessage(ExportConstants.SCHEME_USER_EXPORT_NAME, userDetail.getLocale());
        String downFileName = exportName + ExportConstants.FILE_SUFFIX_XLSX + TalentBkConstants.FILE_ORID;
        if (lockService.tryLock(lockKey, TalentBkRedisKeys.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                List<SchemeUser4Export> scheme4Export = findScheme4Export(orgId, bean);
                ExportParam exportParam = new ExportParam();
                exportParam.setModuleCode(ModuleConstants.MODULE_CODE);
                exportParam.setFileName(downFileName);
                exportParam.setName(exportName);
                path = YxtExportUtils.exportList(userDetail, exportParam,
                        SchemeUser4Export.class, scheme4Export);
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        Map<String, String> map = new HashMap<>(1);
        map.put(ExportConstants.EXPORT_URL_KEY, path);
        return map;
    }

    private List<SchemeUser4Export> findScheme4Export(String orgId, Scheme4Search bean) {
        List<SearchDeptInfo> depts = bean.getDepts();
        List<SPTagSearchBean> tagSearch = bean.getTagSearch();
        if (CollectionUtils.isNotEmpty(tagSearch)) {
            return getSchemeExportRpc(orgId, bean);
        }

        List<SchemeUser4Export> user4Export = udpLiteUserRepository.findUser4Export(orgId, bean,
                DeptConditionBean.createBy(depts, SearchDeptInfo::getDeptId, SearchDeptInfo::getIncludeAll));
        user4Export.forEach(x -> {
            x.setUdpStatus(x.getStatus() == 0 ? ExportConstants.COMMON_DISABLE : ExportConstants.COMMON_ENABLE);
        });
        return user4Export;

    }

    private List<SchemeUser4Export> getSchemeExportRpc(String orgId, Scheme4Search bean) {
        SearchRuleBean searchRuleBean = new SearchRuleBean();
        searchRuleBean.setTagSearch(bean.getTagSearch());
        searchRuleBean.setTagSearchType(bean.getTagSearchType());
        BaseSearchBean baseSearch = new BaseSearchBean();
        BeanCopierUtil.copy(bean, baseSearch);
        List<SchemeUser4Export> exports = new ArrayList<>();
        int index = 0;
        boolean resFlag = true;
        while (resFlag) {
            PageBean pageBean = new PageBean();
            pageBean.setOffset(index);
            pageBean.setLimit(500);
            PagingList<LabelUserVO> userVOPagingList =
                    tagSearchRpc.searchUserByTag(orgId, searchRuleBean, baseSearch, pageBean);
            List<LabelUserVO> datas = userVOPagingList.getDatas();
            if (CollectionUtils.isEmpty(datas)) {
                resFlag = false;
                continue;
            }
            List<SchemeUser4Export> tempexports = new ArrayList<>();
            for (LabelUserVO data : datas) {
                SchemeUser4Export export = new SchemeUser4Export();
                export.setFullname(data.getFullname());
                export.setUsername(data.getUsername());
                export.setDeptName(data.getDeptName());
                export.setPositionName(data.getPositionName());
                export.setStatus(data.getStatus());
                export.setGradeName(data.getGradeName());
                if (data.getHireDate() != null) {
                    export.setHireDate(Date.from(data.getHireDate().atZone(ZoneId.systemDefault()).toInstant()));
                }
                tempexports.add(export);
            }
            tempexports.forEach(x -> {
                x.setUdpStatus(x.getStatus() == 0 ? "已禁用" : "已启用");
            });
            exports.addAll(tempexports);
            index += 500;
        }
        return exports;
    }

    /**
     * 获取学员端
     *
     * @param request
     * @param pageRequest
     * @param userCache
     * @param bean
     * @return
     */
    public PagingList<SchemeBean> findUser4Client(
            HttpServletRequest request, PageRequest pageRequest, UserCacheBasic userCache, Scheme4Search bean) {
        String orgId = userCache.getOrgId();
        String operator = userCache.getUserId();
        // 获取操作人管理的部门
        Set<String> manageDeptIds = udpOrgSearchRpc.findManageDeptIds(orgId, operator);
        log.info(
                "findUser4Client manageDeptIds org_id={}, operator={}, manageDeptIds={}", orgId, operator,
                BeanHelper.bean2Json(manageDeptIds));
        List<String> deptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(manageDeptIds)) {
            List<String> tempList = new ArrayList<>(manageDeptIds);
            CommonList<String> res = new CommonList<>(tempList);
            CommonList<DeptTreeId> deptTree = udpFacade.getDeptTreeIds(orgId, res);
            for (DeptTreeId data : deptTree.getDatas()) {
                deptIds.addAll(data.getTreeIds());
            }

        } else {
            IPage<SchemeBean> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
            return BeanCopierUtil.toPagingList(page);
        }
        List<SearchDeptInfo> depts = new ArrayList<>();
        for (String deptId : manageDeptIds) {
            SearchDeptInfo deptInfo = new SearchDeptInfo();
            deptInfo.setDeptId(deptId);
            deptInfo.setIncludeAll(1);
            depts.add(deptInfo);
        }
        bean.setDepts(depts);
        if (bean.getSchemeId() != null) {
            return findSchemeBySchemeId(request, pageRequest, orgId, bean);
        }

        //
        IPage<SchemeBean> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<SchemeBean> pageList = udpLiteUserRepository.findUserPage(page, orgId, bean, deptIds);
        return BeanCopierUtil.toPagingList(pageList);

    }

    private List<String> optimizeDeptHierarchy(String orgId, List<String> deptIds) {
        if (CollectionUtils.isEmpty(deptIds) || deptIds.size() <= 100) {
            // 如果部门数量不多，直接返回原列表
            return deptIds;
        }

        try {
            // 查询所有相关部门的信息
            List<UdpDept> allDepts = udpDeptMapper.selectBatchIds(deptIds);
            if (CollectionUtils.isEmpty(allDepts)) {
                return deptIds;
            }

            // 使用部门层级工具类过滤出最顶层的部门
            List<String> topLevelDeptIds = DeptHierarchyUtils.filterTopLevelDeptIds(deptIds, allDepts);

            log.info("部门层级优化完成: 原始部门数量={}, 优化后部门数量={}", deptIds.size(), topLevelDeptIds.size());
            return topLevelDeptIds;

        } catch (Exception e) {
            log.error("部门层级优化失败，使用原始部门列表: orgId={}, deptCount={}", orgId, deptIds.size(), e);
            return deptIds;
        }
    }
}
