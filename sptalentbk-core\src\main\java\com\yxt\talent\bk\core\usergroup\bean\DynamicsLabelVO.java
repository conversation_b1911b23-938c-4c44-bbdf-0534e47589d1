package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/18
 */
@Data
@Schema
public class DynamicsLabelVO {
    @Schema(description = "标签名称")
    private String labelName;
    @Schema(description = "标签Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long labelId;
    @Schema(description = "标签值")
    private List<LabelBizVO> labelList = new ArrayList<>();
}
