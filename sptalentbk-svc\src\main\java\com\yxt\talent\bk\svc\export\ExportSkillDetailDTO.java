package com.yxt.talent.bk.svc.export;

import com.yxt.spsdk.common.annotation.SpExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */
@Data
public class ExportSkillDetailDTO {
//    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.sk.catalogName", name = "一级分类", index = 0)
//    private String catalogName;
//
//    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.sk.secCatalogName", name = "二级分类", index = 1)
//    private String secCatalogName;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.sk.skillName", name = "能力名称", index = 2)
    private String skillName;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.sk.skillLevelCount", name = "总等级数", index = 3)
    private String skillLevelCount;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.sk.skillStandard", name = "能力标准", index = 4)
    private String skillStandard;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.sk.skillScore", name = "评估得分", index = 5)
    private String skillScore;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.sk.skillResult", name = "评估结果", index = 6)
    private String skillResult;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.sk.skillReached", name = "是否达标", index = 7)
    private String skillReached;
}
