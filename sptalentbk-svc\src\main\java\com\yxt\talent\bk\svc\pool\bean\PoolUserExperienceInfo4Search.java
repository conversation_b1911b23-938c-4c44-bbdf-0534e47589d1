package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.ArrayList;

@Data
@Schema(name = "用户储备历程搜索")
public class PoolUserExperienceInfo4Search {
    @Schema(description = "用户Id")
    @NotBlank(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    String userId;
    @Schema(description = "出池事件状态【0:在池 1:合格出池且任用; 2:合格出池; 3:不合格出池; 4:未完成中途退出; 5:离职】")
    ArrayList<Integer> eventPoolOutList;
}
