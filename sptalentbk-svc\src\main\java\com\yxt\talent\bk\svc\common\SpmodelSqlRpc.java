package com.yxt.talent.bk.svc.common;

import com.alibaba.fastjson.JSONObject;
import com.yxt.common.enums.YesOrNo;
import com.yxt.spmodel.facade.bean.sql.SqlParam;
import com.yxt.spmodel.facade.service.SpmodelSqlService;
import com.yxt.talent.bk.common.constants.ConstantPool;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.common.utils.SqlUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * SpmodelSqlRpc
 *
 * <AUTHOR> geyan
 * @Date 15/9/23 5:00 pm
 */
@Component
@AllArgsConstructor
@Slf4j
public class SpmodelSqlRpc {
    private final static Map<Class, List<String>> BASE_CON = new ConcurrentHashMap<>();
    private final SpmodelSqlService spmodelSqlService;

    public <T> List<T> query(String orgId, Class<T> clazz,
                             Map<String, Object> queryParams) {
        return query(orgId, clazz, null, queryParams, null);
    }
    public <T> List<T> query(String orgId, Class<T> clazz,
                             Map<String, Object> queryParams, String sqlLast) {
        return query(orgId, clazz, null, queryParams, sqlLast);
    }
    public <T> List<T> query(String orgId, Class<T> clazz,
                             List<String> acceptFields, Map<String, Object> queryParams) {
        return query(orgId, clazz, acceptFields, queryParams, null);
    }
    public <T> List<T> query(String orgId, Class<T> clazz, List<String> acceptFields,
                             Map<String, Object> queryParams, String sqlLast) {
        if (!BASE_CON.containsKey(clazz)) {
            List<String> baseFields = new ArrayList<>();
            for (Field field : clazz.getDeclaredFields()) {
                if (ConstantPool.F_ORG_ID.equals(field.getName())) {
                    baseFields.add(ConstantPool.F_ORG_ID);
                } else if (ConstantPool.F_DELETED.equals(field.getName())) {
                    baseFields.add(ConstantPool.F_DELETED);
                }
            }
            BASE_CON.put(clazz, baseFields);
        }
        List<String> baseFields = BASE_CON.get(clazz);
        if (baseFields.contains(ConstantPool.F_ORG_ID)) {
            queryParams.put("org_id", orgId);
        }
        if (baseFields.contains(ConstantPool.F_DELETED)) {
            queryParams.put(ConstantPool.F_DELETED, YesOrNo.NO.getValue());
        }
        return doQuerySql(orgId, clazz, SqlUtils.buildSqlQuery(clazz, acceptFields, queryParams, sqlLast));
    }

    private <T> List<T> doQuerySql(String orgId, Class<T> clazz, String query) {
        log.debug("LOG50000 orgId {} sql {}", orgId, query);
        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(query);
        List<JSONObject> results = new ArrayList<>();
        try {
            results = spmodelSqlService.sql(param);
        } catch (Exception e) {
            log.error("LOG50010:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        return IArrayUtils.jsonToBeanOfList(results, clazz);
    }
}
