package com.yxt.talent.bk.core.pool.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.yxt.common.Constants.SDF_YEAR2MINUTE;
import static com.yxt.common.Constants.STR_GMT8;

@Setter
@Getter
@Schema(name = "培训项目详情")
public class BindTraining4Get {

    @Schema(description = "培训项目id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "培训项目名称")
    private String name;

    @Schema(description = "培训项目状态（1：未发布；2：进行中；3：已结束；4：已归档；5：已删除；6：已暂停；7：已撤回；）")
    private Integer status;

    @Schema(description = "项目时间模式（0默认起止时间  1周期）")
    private int timeType;

    @Schema(description = "周期天数 默认0")
    private int cycle;

    @Schema(description = "培训项目开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2MINUTE, timezone = STR_GMT8)
    private Date startTime;

    @Schema(description = "培训项目结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2MINUTE, timezone = STR_GMT8)
    private Date endTime;

    @Schema(description = "培训项目完成率")
    private BigDecimal finishRate = BigDecimal.ZERO;

    @Schema(description = "培训项目参与人数")
    private int stuCount;

    @Schema(description = "培训项目负责人")
    private List<String> principals;

    @Schema(description = "培训项目创建人")
    private String createUser;

    @Schema(description = "培训项目创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2MINUTE, timezone = STR_GMT8)
    private Date createTime;

}

