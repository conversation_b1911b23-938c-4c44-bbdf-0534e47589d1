package com.yxt.talent.bk.api.controller.pool;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.export.ImportResult;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.spsdk.audit.annotations.EasyAuditLogSelect;
import com.yxt.spsdk.common.utils.YxtExportUtils;
import com.yxt.spsdk.udpbase.bean.UdpUserBriefBean;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.TalentBkAuthCodes;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.imports.ImportRequestBean;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.pool.bean.PoolUser4List;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Param;
import com.yxt.talent.bk.core.pool.bean.UserPoolInfoBean;
import com.yxt.talent.bk.core.udp.bean.UdpUserNameBean;
import com.yxt.talent.bk.svc.pool.PoolService;
import com.yxt.talent.bk.svc.pool.PoolUserService;
import com.yxt.talent.bk.svc.pool.bean.PoolUser4Add;
import com.yxt.talent.bk.svc.pool.bean.PoolUser4Update;
import com.yxt.talent.bk.svc.pool.bean.PoolUserExperienceInfo4Search;
import com.yxt.talent.bk.svc.pool.bean.PoolUserExperienceInfoBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人才池人员控管理制层
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-08
 */
@Tag(name = "人才池人员管理控制层")
@RestController
@RequestMapping("/mgr/pooluser")
public class PoolUserController extends BaseController {
    @Resource
    private PoolUserService poolUserService;
    @Resource
    private PoolService poolService;

    @Operation(summary = "查询人才池人员page")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @PostMapping("/page")
    public PagingList<PoolUser4List> page(@RequestBody PoolUser4Param param) {
        UserCacheBasic userCache = getUserCacheBasic();
        param.setOrgId(userCache.getOrgId());
        param.setUserId(userCache.getUserId());
        return poolUserService.page(ApiUtil.getPageRequest(getRequest()), param);
    }

    @Operation(summary = "查询人才池人员page（标记测评人员）")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @PostMapping("/pageforeval")
    public PagingList<PoolUser4List> page4Eval(@RequestBody PoolUser4Param param) {
        UserCacheBasic userCache = getUserCacheBasic();
        param.setOrgId(userCache.getOrgId());
        param.setUserId(userCache.getUserId());
        return poolUserService.page4Eval(ApiUtil.getPageRequest(getRequest()), param);
    }

    @Operation(summary = "导出查询人才池人员")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_USER_EXPORT, paramExp = "#param")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_USER_SEARCH_EXPORT, action = Constants.LOG_TYPE_EXPORT, type = AuthType.TOKEN)
    @PostMapping("/export")
    public Map<String, String> export(@RequestBody PoolUser4Param param) {
        UserCacheDetail userDetail = getUserCacheDetail();
        param.setOrgId(userDetail.getOrgId());
        param.setUserId(userDetail.getUserId());

        return poolUserService.export(param, userDetail);
    }

    @Operation(summary = "添加人才池人员")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_USER_ADD)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN, codes = {
            TalentBkAuthCodes.BK_AUTH_CODE_POOL_IMPORT})
    @PostMapping("/save")
    public void save(@RequestBody @Validated PoolUser4Add param) {
        UserCacheBasic userCache = getUserCacheBasic();
        param.setUserId(userCache.getUserId());
        poolUserService.save(userCache.getOrgId(), userCache.getUserId(), param);
        String poolId = param.getPoolId();
        // 异步：更新人才池现有数
        poolService.updateSaturabilityById(userCache.getOrgId(), poolId);
    }

    @Operation(summary = "更新人才池人员准备度/出池")
    @Auditing
    @EasyAuditLogSelect({
            @EasyAuditLog(value = AuditLogConstants.POOL_USER_PREPARE, conditionExp = "#update.updateType != 1",
                    paramExp = "#update"),
            @EasyAuditLog(value = AuditLogConstants.POOL_USER_OUT, conditionExp = "#update.updateType == 1",
                    paramExp = "#update")})
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN)
    @PostMapping("/update")
    public void update(@RequestBody @Validated PoolUser4Update update) {
        UserCacheBasic userCache = getUserCacheBasic();
        String poolId = update.getPoolUser().getPoolId();
        Integer updateType = update.getUpdateType();
        poolUserService.updateBatch(userCache.getOrgId(), userCache.getUserId(), update);
        // 1:出池
        if (updateType == 1) {
            // 更新人才池现有数
            poolService.updateSaturabilityById(userCache.getOrgId(), poolId);
        }
    }

    @Operation(summary = "导入用户模板下载")
    @GetMapping(value = "/template/userimport", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_EXPORT, type = AuthType.TOKEN)
    public Map<String, String> templateUserImport() {
        UserCacheDetail userCache = getUserCacheDetail();
        return Maps.of(ExportConstants.EXPORT_URL_KEY, poolUserService.templateUserImport(userCache));
    }

    @Operation(summary = "人才池人员导入")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.POOL_USER_ADD)
    @PostMapping(value = "/import/{poolId}", produces = Constants.MEDIATYPE)
    @Parameters({             @Parameter(name = "poolId", description = "人才池id", in = ParameterIn.PATH)})
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN, codes = {
            TalentBkAuthCodes.BK_AUTH_CODE_POOL_IMPORT})
    public ImportResult importPoolUser(ImportRequestBean bean,
            @RequestParam(value = "file", required = false) MultipartFile file, @PathVariable String poolId) {
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        return poolUserService.importPoolUser(userBasic, bean, file, poolId);
    }

    @Operation(summary = "准备度模板下载")
    @PostMapping(value = "/readinessexport/{poolId}", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_EXPORT, type = AuthType.TOKEN)
    public Map<String, String> exportReadiness(@PathVariable String poolId) {
        UserCacheBasic userCache = getUserCacheBasic();
        return poolUserService.exportReadiness(userCache.getOrgId(), poolId);
    }


    @Operation(summary = "导入准备度")
    @PostMapping(value = "/readinessimport/{poolId}", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.TOKEN)
    public ImportResult importReadiness (ImportRequestBean bean,
            @RequestParam(value = "file", required = false) MultipartFile file, @PathVariable String poolId) {
        UserBasicBean userBasic = UserBasicBean.createBy(getUserCacheDetail());
        return poolUserService
                .importreadiness(userBasic, poolId, bean, file);
    }

    @Operation(summary = "用户储备经历")
    @Parameters({             @Parameter(name = "limit", description = "每页记录数. 默认值为20.", in = ParameterIn.QUERY),             @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "                     + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY),             @Parameter(name = "direction", description = "joinTime排序方式【asc、desc】", in = ParameterIn.QUERY) })
    @PostMapping(value = "/experience")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public PagingList<PoolUserExperienceInfoBean> getPoolUserExperienceInfo(@Valid @RequestBody PoolUserExperienceInfo4Search search ) {
        UserCacheDetail userDetail = getUserCacheDetail();

        return poolUserService.findPoolUserExperienceInfo(ApiUtil.getPageRequest(getRequest())
                ,userDetail.getOrgId(),search.getUserId(),search.getEventPoolOutList());
    }

    @Operation(summary = "用户人才池列表")
    @PostMapping(value = "/userPoolList")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public List<UserPoolInfoBean> userPoolList(@RequestParam(required = false) String userId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        if (StringUtils.isBlank(userId)) {
            userId = userDetail.getUserId();
        }
        return poolUserService.userPoolList(userDetail.getOrgId(), userId);
    }

    @Operation(summary = "查询多个人才池用户")
    @PostMapping(value = "/anypool/pageuser")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public PagingList<UdpUserBriefBean> pageUser(@RequestBody List<String> poolIds) {
        UserCacheBasic userCache = getUserCacheBasic();
        return poolUserService.pageUser(ApiUtil.getPageRequest(getRequest()), userCache.getOrgId(), poolIds);
    }

    @Operation(summary = "查询多个人才池用户ids")
    @PostMapping(value = "/anypool/userids")
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public List<UdpUserNameBean> listUserIds(@RequestBody List<String> poolIds) {
        UserCacheBasic userCache = getUserCacheBasic();
        return poolUserService.listUserIds(userCache.getOrgId(), poolIds);
    }
}

