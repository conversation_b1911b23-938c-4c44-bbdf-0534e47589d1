package com.yxt.talent.bk.svc.common.bean;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * DemoCopyResult
 *
 * <AUTHOR> harleyge
 * @Date 13/9/24 9:31 am
 */
@Data
public class DemoCopyResult {
    private String targetOrgId;
    private long createTime;
    /**
     * 开始和开始触发时间
     */
    private boolean start;
    private Long startTime;
    /**
     * 初始化和初始化触发时间
     */
    private boolean copy;
    private Long copyTime;
    /**
     * 结束和结束时间
     */
    private boolean end;
    private Long endTime;
    /**
     * 是否已经超时通知了
     */
    private boolean timeoutNotify;
    private List<CopyBizResult> copyResult = new ArrayList<>();

    @Data
    public static class CopyBizResult {
        private String bizCode;
        private int status;
        private long receiveTime;
    }
}
