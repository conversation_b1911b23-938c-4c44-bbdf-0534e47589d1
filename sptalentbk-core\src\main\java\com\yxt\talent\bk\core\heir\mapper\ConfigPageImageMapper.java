package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.entity.ConfigPageImageEntity;
import com.yxt.talent.bk.core.heir.ext.ConfigPageImageExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
*  @date 2023/9/14
**/
@Mapper
public interface ConfigPageImageMapper extends BkBaseMapper<ConfigPageImageEntity> {
    /**
     * 机构首页设置
     * @param orgId
     * @return
     */
    List<ConfigPageImageExt> listByOrgId(@Param("orgId") String orgId);

    /**
     * 删除设置
     * @param orgId
     * @param currentUserId
     * @param id
     * @return
     */
    int deleteByOrgIdAndId(@Param("orgId") String orgId, @Param("currentUserId") String currentUserId, @Param("id") String id);

    @Select("select count(*) from bk_config_page_image where org_id = #{orgId} and deleted = 0")
    int countByOrgId(@Param("orgId") String orgId);
}
