package com.yxt.talent.bk.core.heir.repo;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.heir.entity.HeirPosUserEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosUserExt;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * HeirPosRepository
 *
 * <AUTHOR> geyan
 * @Date 18/8/23 4:32 pm
 */
@Slf4j
@Repository
@AllArgsConstructor
public class HeirPosUserRepository extends ServiceImpl<HeirPosUserMapper, HeirPosUserEntity> {

    public IPage<HeirPosUserExt> list(Page page, String orgId, String posId,
                                      Integer heirStatus, Long prepareLevelId) {
        IPage<HeirPosUserExt> ret = baseMapper.list(page, orgId, posId, heirStatus, prepareLevelId);
        TalentbkUtil.bkUdpTranslate(orgId, true, ret.getRecords());
        return ret;
    }

    public HeirPosUserEntity getByUserId(String orgId, String posId, String userId) {
        LambdaQueryWrapper<HeirPosUserEntity> wrapper = getQueryWrapper().eq(HeirPosUserEntity::getOrgId, orgId)
                .eq(HeirPosUserEntity::getPosId, posId)
                .eq(HeirPosUserEntity::getUserId, userId).last(TalentBkConstants.SQL_LIMIT_ONE);
        return baseMapper.selectOne(wrapper);
    }

    public List<HeirPosUserEntity> findByPosIds(String orgId, Collection<String> posIds) {
        if (CollectionUtils.isEmpty(posIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HeirPosUserEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(HeirPosUserEntity::getOrgId, orgId);
        queryWrapper.eq(HeirPosUserEntity::getDeleted, YesOrNo.NO.getValue());
        queryWrapper.in(HeirPosUserEntity::getPosId, posIds);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<HeirPosUserEntity> getQueryWrapper() {
        LambdaQueryWrapper<HeirPosUserEntity> wrapper = new LambdaQueryWrapper<>();
        return wrapper.eq(HeirPosUserEntity::getDeleted, YesOrNo.NO.getValue());
    }
}
