package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * DwdHeirPosResp
 *
 * <AUTHOR> geyan
 * @Date 14/9/23 6:30 pm
 */
@Data
public class OpenHeirPosResp {

    @JsonProperty("id")
    @Schema(description = "pk-岗位或部门id")
    private String posId;

    @Schema(description = "名称")
    private String posName;

    @Schema(description = "目标继任数量,0:未设置")
    private Integer heirTargetQty;

    @Schema(description = "有效人数")
    private Integer heirValidQty;

    @Schema(description = "风险规则名称")
    private String riskLevelName;

    @Schema(description = "风险规则颜色")
    private String riskLevelColor;
}
