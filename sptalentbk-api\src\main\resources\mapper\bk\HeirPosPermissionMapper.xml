<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirPosPermissionMapper">

    <sql id="resTransferQuery">
        from bk_heir_pos_permission perm
        join bk_heir_pos p on p.id = perm.pos_id and p.deleted = 0
        left join udp_dept d on d.id = perm.pos_id and d.deleted = 0
        where perm.deleted = 0 and perm.org_id = #{orgId}
        and perm.user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and (p.pos_type = 0 or d.id is not null)
    </sql>

    <select id="resTransferCount" resultType="int">
        select count(distinct perm.pos_id) <include refid="resTransferQuery"/>
    </select>
    <select id="resTransferList" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosPermUserBean">
        select perm.id,perm.pos_id,perm.user_id,perm.deleted <include refid="resTransferQuery"/>
    </select>

    <update id="execResTransfer">
        <foreach collection="list" item="item" separator=";">
            update bk_heir_pos_permission set user_id = #{item.userId},deleted = #{item.deleted}
            where org_id = #{orgId} and id = #{item.id}
        </foreach>
    </update>

    <select id="list" resultType="com.yxt.talent.bk.core.heir.ext.HeirPosPermissionExt">
        SELECT
        u.username, u.fullname, u.dept_id,u.dept_name,
        p.user_id
        FROM bk_heir_pos_permission p
        LEFT JOIN udp_lite_user_sp u ON p.user_id = u.id AND p.org_id = u.org_id
        WHERE p.org_id = #{orgId} and p.pos_id = #{posId} and p.deleted = 0
        and u.deleted = 0
        order by p.id
    </select>

    <select id="listUserId" resultType="java.lang.String">
        select user_id from bk_heir_pos_permission where org_id = #{orgId} and pos_id = #{posId} and deleted = 0
    </select>

    <select id="listExistUserId" resultType="java.lang.String">
        select user_id from bk_heir_pos_permission where org_id = #{orgId} and pos_id = #{posId} and deleted = 0
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="listUserPosIds" resultType="string">
        select distinct pos_id from bk_heir_pos_permission
        where user_id = #{userId} and deleted = 0 and pos_id in
        <foreach collection="posIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByUserIds">
        update bk_heir_pos_permission set update_user_id = #{currentUserId}, update_time = #{currentTime}, deleted = 1
        where org_id = #{orgId} and pos_id = #{posId} and deleted = 0 and user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
