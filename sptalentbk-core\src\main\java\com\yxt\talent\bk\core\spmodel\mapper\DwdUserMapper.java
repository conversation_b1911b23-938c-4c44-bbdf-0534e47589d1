package com.yxt.talent.bk.core.spmodel.mapper;

import com.yxt.talent.bk.core.dashboard.bean.DeptUserCountDTO;
import com.yxt.talent.bk.core.dashboard.bean.GroupUserCountDTO;
import com.yxt.talent.bk.core.spmodel.entity.DwdUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DwdUserMapper
 *
 * <AUTHOR> harleyge
 * @Date 25/7/24 10:16 am
 */
@Mapper
public interface DwdUserMapper {

    List<String> userUnderDept(
            @Param("orgId") String orgId, @Param("deptId") String deptId,
            @Param("userIds") List<String> userIds);

    List<DeptUserCountDTO> getDeptUserCount(@Param("orgId") String orgId,
            @Param("deptIds") List<String> deptIds, @Param("groupIds") List<Long> groupIds);

    List<GroupUserCountDTO> getGroupUserCount(@Param("orgId") String orgId,
            @Param("deptIds") List<String> deptIds, @Param("groupIds") List<Long> groupIds);

    List<DwdUser> getUserInfoByUserIds(@Param("orgId") String orgId,
            @Param("userIds") List<String> userIds);

    List<String> getDeptAndGroupByUserMix(@Param("orgId") String orgId, @Param("groupIds") List<Long> groupIds,
            @Param("deptIds") List<String> deptIds);

    List<Long> getUserAuthGroupListByDeptAndGroup(@Param("orgId") String orgId,
            @Param("deptIds") List<String> deptIds, @Param("groupIds") List<Long> groupIds);
}
