package com.yxt.talent.bk.svc.persona.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/***
 * <AUTHOR>
 * @since 2022/8/12 14:57
 */
@Data
@Schema(name = "个人特征综述")
public class UserOverviewBean {

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;

    @Schema(description = "个人特征综述", requiredMode = Schema.RequiredMode.REQUIRED)
    private String featureOverview;
}
