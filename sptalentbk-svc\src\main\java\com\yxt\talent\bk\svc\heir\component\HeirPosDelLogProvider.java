package com.yxt.talent.bk.svc.heir.component;

import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.udpbase.QueryUdpUtils;
import com.yxt.talent.bk.svc.heir.bean.HeirPosDel4Log;
import com.yxt.talent.bk.svc.udp.UdpQueryService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * HeirPosDelLogProvider
 *
 * <AUTHOR> harleyge
 * @Date 6/8/24 11:48 am
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirPosDelLogProvider implements AuditLogDataProvider<Set<String>, List<HeirPosDel4Log>> {
    private final UdpQueryService udpQueryService;
    @Override
    public List<HeirPosDel4Log> before(Set<String> param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public List<HeirPosDel4Log> after(Set<String> param, AuditLogBasicBean logBasic) {
        if (CollectionUtils.isEmpty(param)) {
            return null;
        }
        return BeanCopierUtil.convertList(QueryUdpUtils.getPositionIdNameByIds(param.stream().toList()), idName -> {
            HeirPosDel4Log del4Log = new HeirPosDel4Log();
            del4Log.setId(idName.getId());
            del4Log.setPosName(idName.getName());
            return del4Log;
        });
    }
}
