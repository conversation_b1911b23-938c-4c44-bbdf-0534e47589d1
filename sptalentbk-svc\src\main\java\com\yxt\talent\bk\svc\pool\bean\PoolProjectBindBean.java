package com.yxt.talent.bk.svc.pool.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "人才池绑定项目")
public class PoolProjectBindBean {

    @Schema(description = "人才池id")
    private String poolId;

    @Schema(description = "绑定的培训项目id")
    private List<String> projectIds;

    @Schema(description = "目标类型,1:测评,3-培训项目")
    private Integer targetType;

    public PoolEvalParam4Log evalParam4Log() {
        PoolEvalParam4Log ret = new PoolEvalParam4Log();
        ret.setPoolId(poolId);
        ret.setEvalIds(projectIds);
        return ret;
    }
}
