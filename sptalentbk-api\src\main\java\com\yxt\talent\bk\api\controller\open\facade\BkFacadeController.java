package com.yxt.talent.bk.api.controller.open.facade;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spsdk.common.bean.FillJoinPair;
import com.yxt.spsdk.common.bean.SpIdNameBean;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.core.common.repo.CatalogRepository;
import com.yxt.talent.bk.core.component.RedisComponent;
import com.yxt.talent.bk.core.pool.entity.Pool;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.svc.common.DemoCopyService;
import com.yxt.talent.bk.svc.common.enums.ResourceTransferEnum;
import com.yxt.talent.bk.svc.pool.PoolService;
import com.yxt.talent.bk.svc.pool.PoolTrainingService;
import com.yxt.talent.bk.svc.pool.PoolUserService;
import com.yxt.talent.bk.svc.pool.bean.Pool4Get;
import com.yxt.talent.bk.svc.pool.bean.PoolUser4Add;
import com.yxt.talent.bk.svc.profile.UserProfileService;
import com.yxt.talent.bk.svc.profile.bean.*;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.core.udp.bean.QueryUserBean;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talentbkfacade.bean.*;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.yxt.common.Constants.*;
import static com.yxt.common.enums.AuthType.AKSK;
import static com.yxt.talent.bk.common.constants.TalentBkConstants.LOG_OBJ_POOL;
import static com.yxt.talent.bk.common.constants.TalentBkConstants.LOG_TALENT_BANK_TAG;

/**
 * <AUTHOR> @date
 */
@RestController
@AllArgsConstructor
@RequestMapping("/facade")
@Slf4j
public class BkFacadeController {

    private final PoolUserService poolUserService;
    private final PoolRepository poolRepository;
    private final PoolService poolService;
    private final PoolTrainingService poolTrainingService;
    private final RedisComponent redisComponent;
    private final DemoCopyService demoCopyService;
    private final CatalogRepository catalogRepository;
    private final UserProfileService userProfileService;
    private final UdpLiteUserRepository udpLiteUserRepository;

    @Operation(summary = "facade添加人才池人员")
    @Auth(value = LOG_TALENT_BANK_TAG, action = LOG_TYPE_CREATESINGLE, type = AKSK)
    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/pool/adduser")
    public void savePoolUser(@RequestBody PoolUser4Add param) {
        poolUserService.save(param.getOrgId(), param.getUserId(), param);
        String poolId = param.getPoolId();
        // 异步：更新人才池现有数
        poolService.updateSaturabilityById(param.getOrgId(), poolId);
    }

    @Operation(summary = "sptalent删除学员时检查培训项目是否被人才池使用")
    @GetMapping(value = "/{orgId}/pool/{trainingId}/used")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = LOG_TYPE_VERIFY, type = AKSK)
    public boolean hasTrainingUsedInPool(@PathVariable String orgId, @PathVariable String trainingId) {
        return poolTrainingService.hasTrainingUsedInPool(orgId, trainingId);
    }

    @Operation(summary = "sptalent删除学员时检查培训项目是否被人才池使用")
    @PostMapping(value = "/transfer/resource/num")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = Constants.LOG_TYPE_SEARCH, type = AKSK)
    public ResourceTransferRsp getTransferSourceQty(@RequestBody ResourceTransferReq transferReq) {
        ResourceTransferRsp transferRsp = new ResourceTransferRsp();
        transferRsp.setOrgId(transferReq.getOrgId());
        transferRsp.setUserId(transferReq.getUserId());
        transferRsp.setResources(new ArrayList<>());
        for (ResourceTransferEnum transferEnum : ResourceTransferEnum.values()) {
            ResourceTransferRsp.UserResourceTransfer transferQty = new ResourceTransferRsp.UserResourceTransfer();
            transferQty.setResourceCode(transferEnum.getCode());
            transferQty.setCount(transferEnum.getCountFunc().apply(transferReq.getOrgId(), transferReq.getUserId()));
            transferRsp.getResources().add(transferQty);
        }
        return transferRsp;
    }

    @Operation(summary = "getudpinit4mq")
    @GetMapping(value = "/getudpinit4mq/{orgId}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = LOG_TYPE_GETSINGLE, type = AKSK)
    public String getUdpInit4Mq(@PathVariable String orgId) {
        String orgInitDataKey = String.format(TalentBkRedisKeys.CACHE_COPY_ORG_DATA, orgId);
        return redisComponent.getValue(orgInitDataKey);
    }

    @Operation(summary = "queryUserPoolProject")
    @PostMapping(value = "/queryUserPoolProject")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = LOG_TYPE_GETLIST, type = AKSK)
    public List<UserPoolProjectResp> queryUserPoolProject(@RequestBody UserPoolProjectReq req) {
        return poolUserService.inPoolTargetIdList(req);
    }

    @Operation(summary = "queryUserPoolProject")
    @PostMapping(value = "/queryPoolNameByIds")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = LOG_TYPE_GETLIST, type = AKSK)
    public List<StrIdNameBean> queryPoolNameByIds(@RequestBody List<String> poolIds) {
        return poolRepository.getNameByIds(poolIds);
    }

    @Operation(summary = "listPoolByIds")
    @PostMapping(value = "/listPoolByIds")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = LOG_TYPE_GETLIST, type = AKSK)
    public List<BkPoolBean> listPoolByIds(@RequestParam String orgId, @RequestBody List<String> poolIds) {
        if (CollectionUtils.isEmpty(poolIds)) {
            return Lists.emptyList();
        }
        return BeanCopierUtil.convertList(poolRepository.getBaseMapper().listByIds(orgId, poolIds), Pool.class, BkPoolBean.class);
    }

    @Operation(summary = "poolPage")
    @PostMapping(value = "/pool/page")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = LOG_OBJ_POOL, action = LOG_TYPE_GETLIST, type = AKSK)
    public PagingList<Pool4List> poolPage(@RequestParam String orgId, @RequestBody PoolPageReq pageReq) {
        IPage<Pool> poolPage = poolRepository.getBaseMapper().findPageBy(CommonUtils.toPage(pageReq.getPageRequest()),
                pageReq.getCategoryId(), orgId, pageReq.getPoolName(), null,null,null);
        return CommonUtils.convertTo(poolPage, pool -> {
            Pool4List pool4Get = new Pool4List();
            pool4Get.setId(pool.getId());
            pool4Get.setName(pool.getPoolName());
            pool4Get.setCreateTime(pool.getCreateTime());
            pool4Get.setUpdateTime(pool.getUpdateTime());
            pool4Get.setActualPerson(pool.getRealNum());
            pool4Get.setExpectedPerson(pool.getExpectNum());
            pool4Get.setCategoryId(pool.getCatalogId());
            return pool4Get;
        }, new FillJoinPair<Pool, String, Pool4List>(catalogRepository.getBaseMapper()::getNameByIds,
                Pair.of(Pool::getCatalogId, (pool, cateName) -> pool.setCategoryName(((SpIdNameBean)cateName).getName()))));
    }

    @Operation(summary = "获取映射关系")
    @GetMapping(value = "/demoCopy/idMap", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = LOG_TYPE_GETLIST, type = AuthType.AKSK)
    public Map<String,String> demoCopyIdMap(@RequestParam(name = "sourceOrgId") String sourceOrgId, @RequestParam(name = "targetOrgId") String targetOrgId, @RequestParam(name = "mapKey") String mapKey) {
        OrgInit4Mq orgInit4Mq = new OrgInit4Mq();
        orgInit4Mq.setTargetOrgId(targetOrgId);
        orgInit4Mq.setSourceOrgId(sourceOrgId);
        return demoCopyService.queryPreSetIdMap(orgInit4Mq, mapKey);
    }

    @Operation(summary = "获取用户所在部门的指定岗位下的人员的个人画像数据")
    @PostMapping(value = "/querySamePositionUser")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = LOG_TYPE_GETLIST, type = AuthType.AKSK)
    public List<UserProfileResp> querySamePositionUser(@Validated @RequestBody UserProfileReq req) {
        return userProfileService.querySamePositionUsers(req);
    }

}
