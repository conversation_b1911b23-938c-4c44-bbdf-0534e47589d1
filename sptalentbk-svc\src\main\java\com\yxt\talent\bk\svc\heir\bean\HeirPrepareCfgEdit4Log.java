package com.yxt.talent.bk.svc.heir.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.Data;

/**
 * HeirBmUserEdit4Log
 *
 * <AUTHOR> geyan
 * @Date 21/3/24 10:10 am
 */
@Data
public class HeirPrepareCfgEdit4Log {
    @AuditLogField(name = "执行周期", orderIndex = 0)
    private String calcCycle;
    @AuditLogField(name = "提醒周期", orderIndex = 1)
    private String remindCycle;
    @AuditLogField(name = "提醒时间", orderIndex = 2)
    private String remindTime;
    @AuditLogField(name = "提醒人员", orderIndex = 3)
    private String remindUser;
}
