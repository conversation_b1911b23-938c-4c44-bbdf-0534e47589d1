package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @program: sptalentbkapi
 * @description: 人才对比的 Bean
 **/
@Data
public class UserCompareSettingBean {
    @NotBlank(message = "apis.talentbk.usercompare.setting.null")
    @Schema(description = "配置的 json", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String setting;
}
