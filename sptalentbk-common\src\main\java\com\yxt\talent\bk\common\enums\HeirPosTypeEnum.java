package com.yxt.talent.bk.common.enums;

/**
 * HeirRiskRuleTypeEnum
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 11:45 am
 */
public enum HeirPosTypeEnum {
    /**
     * 0:岗位，1:部门
     */
    POSITION(0, "岗位"),
    DEPT(1, "部门");
    private int type;
    private String name;

    HeirPosTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
