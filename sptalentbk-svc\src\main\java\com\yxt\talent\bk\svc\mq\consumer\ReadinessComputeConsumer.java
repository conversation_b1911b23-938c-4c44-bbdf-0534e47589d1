package com.yxt.talent.bk.svc.mq.consumer;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPrepareCfgMapper;
import com.yxt.talent.bk.svc.heir.ReadinessComputeService;
import com.yxt.talent.bk.svc.mq.constant.HeirRocketMqConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = HeirRocketMqConstants.GROUP_PREFIX + HeirRocketMqConstants.TOPIC_HEIR_READINESS_COMPUTE,     topic = HeirRocketMqConstants.TOPIC_HEIR_READINESS_COMPUTE, consumeThreadNumber = 3, consumeTimeout = 30)
@RequiredArgsConstructor
public class ReadinessComputeConsumer implements RocketMQListener<HeirPosEntity> {

    private final RocketMQTemplate rocketMQTemplate;

    private final ReadinessComputeService readinessComputeService;

    private final HeirPosMapper heirPosMapper;
    @Override
    public void onMessage(HeirPosEntity heirPosEntity) {
        try {
            log.info("readinessComputeConsumer start heirPosEntity:{}",JSON.toJSONString(heirPosEntity));
            int version = heirPosEntity.getNextCalcVersion();
            HeirPosEntity posEntity = heirPosMapper.selectById(heirPosEntity.getId());
            if (version != posEntity.getNextCalcVersion()) {
                log.info("readinessComputeConsumer version change posId:{}",heirPosEntity.getId());
                //版本号发生变化，数据丢弃
                return;
            }
            Date nextCalcTime = heirPosEntity.getNextCalcTime();
            if (nextCalcTime !=null && (nextCalcTime.getTime()- System.currentTimeMillis())/1000< ReadinessComputeService.FLOAT_SEC) {
                log.info("readinessComputeConsumer calculateReadinessRules heirPosEntity:{}",JSON.toJSONString(heirPosEntity));
                readinessComputeService.setComputeNextExecutionTime(heirPosEntity);
            }else{
                log.info("readinessComputeConsumer syncDelaySend posId:{},topic:{}",heirPosEntity.getId(),HeirRocketMqConstants.TOPIC_HEIR_READINESS_COMPUTE);
                rocketMQTemplate.syncDelaySend(HeirRocketMqConstants.TOPIC_HEIR_READINESS_COMPUTE, JSON.toJSONString(heirPosEntity),
                        readinessComputeService.calcDelayLevel(nextCalcTime).getDelayLevel());
            }
        } catch (Exception e) {
            log.error("readinessComputeConsumer failed posId {}", heirPosEntity.getId(), e);
        }
    }
}
