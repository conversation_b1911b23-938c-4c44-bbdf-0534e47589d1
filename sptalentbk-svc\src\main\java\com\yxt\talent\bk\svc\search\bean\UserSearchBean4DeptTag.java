package com.yxt.talent.bk.svc.search.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserSearchBean4DeptTag {
    @Schema(description = "部门Id")
    private String departmentId;
    @JsonIgnore
    private String departmentName;
    @JsonIgnore
    private String parentId;
    @Schema(description = "是否统计子部门人员",example = "1")
    private Integer includeAll = 1;
}
