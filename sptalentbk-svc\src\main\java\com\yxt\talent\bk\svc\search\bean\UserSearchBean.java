package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "人才搜索-主页-列表-搜索入参")
public class UserSearchBean {
    @Schema(description = "搜索关键字（姓名/工号/岗位/职级）")
    private String keyword;
    @Schema(description = "用户状态过滤开关（1-过滤禁用用户，0-不过滤禁用用户）")
    private Integer userStatusFilterSwitch  ;
    @Schema(description = "筛选标签")
    private List<UserSearchBean4LayeredTag> standardTags;
    @Schema(description = "企业自建的所有普通标签")
    private List<UserSearchBean4OrdinaryTag> ordinaryTags;
    @Schema(description = "其他非标准标签（如 入职日期范围 ）")
    private UserSearchBean4OtherTag otherTags;
    @Schema(description = "透视区条件【点击某个透视项筛选】")
    private UserSearchBean4AnalyseTag analyseTag;

}



