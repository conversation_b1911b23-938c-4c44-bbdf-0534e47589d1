package com.yxt.talent.bk.common.bean.searchrule;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Data
public class SPTagSearchBean {

    @Schema(description = "标签ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tagId;
    @Schema(description = "标签值列表", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> tagValues;
    @Schema(description = "运算符，1：等于，2：不等于", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer symbol;


}
