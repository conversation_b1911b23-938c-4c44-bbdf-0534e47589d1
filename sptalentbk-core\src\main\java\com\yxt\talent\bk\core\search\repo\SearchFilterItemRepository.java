package com.yxt.talent.bk.core.search.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.search.entity.SearchFilterItem;
import com.yxt.talent.bk.core.search.mapper.SearchFilterItemMapper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Deprecated
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class SearchFilterItemRepository extends ServiceImpl<SearchFilterItemMapper, SearchFilterItem> {

    private static final Logger log1 = LoggerFactory.getLogger(SearchFilterItemRepository.class);

    public List<SearchFilterItem> findFilterByOrgId(String orgId) {
        LambdaQueryWrapper<SearchFilterItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SearchFilterItem::getOrgId, orgId);
        wrapper.orderByAsc(SearchFilterItem::getOrderIndex);
        return list(wrapper);
    }

    public List<SearchFilterItem> findTagsByItemKeys(String orgId, List<String> itemKeys) {
        if (CollectionUtils.isEmpty(itemKeys)) {
            log1.warn("LOG10250:orgId={}", orgId);
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SearchFilterItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SearchFilterItem::getOrgId, orgId);
        wrapper.in(SearchFilterItem::getItemKey, itemKeys);
        return list(wrapper);
    }

}
