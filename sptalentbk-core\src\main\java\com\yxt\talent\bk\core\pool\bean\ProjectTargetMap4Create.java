package com.yxt.talent.bk.core.pool.bean;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 *  人才池人员页面交互Vo
 * <AUTHOR>
 * @since 2022/9/8 10:46
 * @version 1.0
 */
@Data
public class ProjectTargetMap4Create {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "人才池id")
    @NotBlank(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String poolId;

    @Schema(description = "人员id")
    @NotBlank(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String userId;

    @Schema(description = "准备度id")
    private String readinessId;

    @Schema(description = "出池事件枚举(1:合格出池且任用;2:合格出池;3:不合格出池;4:未完成中途退出;5:离职)")
    private Integer eventPoolOut;

    @Schema(description = "备注/去向")
    private String remark;
}
