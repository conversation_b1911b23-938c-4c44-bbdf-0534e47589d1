package com.yxt.talent.bk.svc.common;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.es.ESQueryConveter;
import com.yxt.talent.bk.core.tag.bean.TagInfoBean;
import com.yxt.talent.bk.core.tag.entity.UserTagEntity;
import com.yxt.talent.bk.core.tag.entity.UserTagValueEntity;
import com.yxt.talent.bk.core.tag.mapper.TagMapper;
import com.yxt.talent.bk.core.tag.repo.UserTagRepository;
import com.yxt.talent.bk.core.tag.repo.UserTagValueRepository;
import com.yxt.talent.bk.svc.base.BkConfigService;
import com.yxt.talent.bk.svc.tag.bean.EsUserBean;
import com.yxt.talent.bk.svc.tag.bean.TagBean;
import com.yxt.talent.bk.svc.tag.bean.UserTag4ImportBean;
import com.yxt.talent.bk.svc.tag.enums.TagSourceEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.action.update.UpdateRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *  人员标签业务处理实现层
 * <AUTHOR>
 * @since 2022/8/19 8:44
 * @version 1.0
 */
@Service
@AllArgsConstructor
@Slf4j
public class UserTagAndValueBusinessService {

    private final UserTagRepository userTagRepository;
    private final UserTagValueRepository userTagValueRepository;
    private final ElasticsearchRestTemplate elasticsearchRestTemplate;
    private final TagMapper tagMapper;
    private final BkConfigService bkConfigService;
    private final ESQueryConveter esQueryConveter;

    public boolean saveUserTagAndValue4Import(String orgId, String userId, List<UserTag4ImportBean> saves) {
        Set<String> tagIdSet = saves.stream().map(UserTag4ImportBean::getTagBean).map(TagBean::getTagId)
                .collect(Collectors.toSet());
        Set<String> userIdSet = saves.stream().map(UserTag4ImportBean::getUserId).collect(Collectors.toSet());
        // step1:删除人和标签值的关系【prd要求覆盖导入（旧标签值 设置删除状态再新增新标签值）】
        userTagValueRepository.deleteByTagIdsAndUserIds(orgId, tagIdSet, userIdSet, userId);
        // step2:查询人和标签关系，如果存在删除
        userTagRepository.deleteByTagIdsAndUserIds(orgId, tagIdSet, userIdSet, userId);
        Set<UserTagEntity> userTagSet = Sets.newConcurrentHashSet();
        Set<UserTagValueEntity> userTagValueSet = Sets.newConcurrentHashSet();
        // step3:转换人和标签关系实体填充
        this.saveDataFormat4UserTagValueEntity(orgId, userId, saves, userTagSet, userTagValueSet);
        // step4:保存人和标签值关系
        if (CollectionUtils.isNotEmpty(userTagSet) && CollectionUtils.isNotEmpty(userTagValueSet)) {
            userTagRepository.saveBatch(userTagSet);
            userTagValueRepository.saveBatch(userTagValueSet);
            batchTagUser(orgId, userId, saves);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private void batchTagUser(String orgId, String operatorId, List<UserTag4ImportBean> saves) {
        log.debug("import batch tag user orgId=[{}] operatorId=[{}]", orgId, operatorId);
        TagBean tagBean = saves.get(0).getTagBean();
        List<TagInfoBean> tagInfoBeans = tagMapper.listTag(orgId, Lists.newArrayList(tagBean.getTagId()));
        if (!Objects.equals(TagSourceEnum.SOURCE_1.getType(), tagInfoBeans.get(0).getTagSource())) {
            // 不是自建标签不处理
            return;
        }
        Set<String> userIds = StreamUtil.map2set(saves, UserTag4ImportBean::getUserId);
        NativeSearchQuery query = new NativeSearchQueryBuilder().withIds(userIds).build();
        log.debug("LOG11250:{}{}", System.lineSeparator(), esQueryConveter.extractJson(query));
        List<EsUserBean> tagBeans = elasticsearchRestTemplate.multiGet(query, EsUserBean.class);
        Map<String, EsUserBean> esUserMap = StreamUtil.list2map(tagBeans, EsUserBean::getUserId);

        List<UpdateQuery> queries = Lists.newArrayListWithCapacity(userIds.size());
        saves.forEach(importBean -> {
            EsUserBean esUserBean = esUserMap.get(importBean.getUserId());
            if (Objects.isNull(esUserBean)) {
                return;
            }
            Map<String, Object> result = getUpdateMap(esUserBean, importBean.getTagBean());
            if (MapUtils.isNotEmpty(result)) {
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.doc(result);
                UpdateQuery updateQuery = new UpdateQueryBuilder().withId(importBean.getUserId())
                    .withIndexName(bkConfigService.getIndexName()).withType(TalentBkConstants.ES_TYPE).build();
                updateQuery.setUpdateRequest(updateRequest);
                queries.add(updateQuery);
            }
        });
        if (CollectionUtils.isNotEmpty(queries)) {
            // 更新用户标签
            elasticsearchRestTemplate.bulkUpdate(queries);
        }
    }

    private Map<String, Object> getUpdateMap(EsUserBean esUserBean, TagBean tagBean) {
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Set<String>> diylabels = esUserBean.getDiylabels();
        if (MapUtils.isNotEmpty(diylabels)) {
            // 自建标签不为空，需要先删除全部，再做新增
            Set<String> list = diylabels.get(tagBean.getTagId());
            if (CollectionUtils.isEmpty(list)) {
                list = Sets.newHashSet();
                list.addAll(tagBean.getTagValueIdList());
            } else {
                list.clear();
                list.addAll(tagBean.getTagValueIdList());
            }
            diylabels.put(tagBean.getTagId(), list);
        } else {
            // 为空，则全部新增即可
            diylabels = Maps.newHashMap();
            Set<String> list = Sets.newHashSet(tagBean.getTagValueIdList());
            diylabels.put(tagBean.getTagId(), list);
        }
        if (MapUtils.isNotEmpty(diylabels)) {
            result.put("diylabels", diylabels);
        }
        return result;
    }

    private void saveDataFormat4UserTagValueEntity(String orgId, String userId,
            List<UserTag4ImportBean> saves, Set<UserTagEntity> userTagSet, Set<UserTagValueEntity> userTagValueSet) {
        saves.forEach(data -> {
            // 标签 转换
            UserTagEntity save4Tag = new UserTagEntity();
            save4Tag.setId(ApiUtil.getUuid());
            save4Tag.setOrgId(orgId);
            save4Tag.setUserId(data.getUserId());
            save4Tag.setTagId(data.getTagBean().getTagId());
            EntityUtil.setCreateInfo(userId, save4Tag);
            userTagSet.add(save4Tag);
            // 标签值 转换
            List<String> tagValueIdList = data.getTagBean().getTagValueIdList();
            tagValueIdList.forEach(tagValueId -> {
                UserTagValueEntity save4Value = new UserTagValueEntity();
                save4Value.setId(ApiUtil.getUuid());
                save4Value.setOrgId(orgId);
                save4Value.setUserId(data.getUserId());
                save4Value.setTagId(data.getTagBean().getTagId());
                save4Value.setTagValueId(tagValueId);
                EntityUtil.setCreateInfo(userId, save4Value);
                userTagValueSet.add(save4Value);
            });
        });
    }
}
