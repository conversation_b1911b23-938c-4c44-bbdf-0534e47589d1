package com.yxt.talent.bk.svc.dashboard;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.StreamUtil;
import com.yxt.spsdfacade.bean.spsd.ModelDto;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.dashboard.bean.*;
import com.yxt.talent.bk.core.spmodel.entity.*;
import com.yxt.talent.bk.core.spmodel.mapper.*;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupRepository;
import com.yxt.talent.bk.svc.rpc.SdRpc;
import com.yxt.talent.bk.core.sd.bean.SdModelInfoDto;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean4Dept;
import com.yxt.talent.bk.svc.usergroup.UserGroupService;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.yxt.talent.bk.common.constants.TalentBkAuthCodes.*;

/**
 * <AUTHOR>
 * @since 2024/7/30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TalentBoardService {

    private final DwdDeptMapper dwdDeptMapper;

    private final DwdUserMapper dwdUserMapper;
    private final DwdUserSkillRtStatisticsMapper dwdUserSkillRtStatisticsMapper;
    private final UserAuthService userAuthService;
    private final UserGroupService userGroupService;
    private final UserGroupRepository userGroupRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final DwdUserEvalSkillRtMapper dwdUserEvalSkillRtMapper;
    private final DwdUserJqTaskRvMapper dwdUserJqTaskRvMapper;
    private final SdRpc sdRpc;

    /**
     * 查询看板部门维度
     *
     * @param userDetail
     * @param searchParam
     * @return
     */
    public PagingList<DashBoardDeptDimVO> getDeptDimDashBoard(UserCacheDetail userDetail, CommonSearchParam searchParam,
            Paging paging) {
        List<String> deptIds = searchParam.getDeptIds();
        List<Long> groupIds = searchParam.getUserGroupIds();
        String userId = userDetail.getUserId();
        String orgId = userDetail.getOrgId();
        if (CollectionUtils.isEmpty(deptIds) && CollectionUtils.isEmpty(groupIds)) {
            return TalentbkUtil.emptyPage((int) paging.getLimit());
        }
        if (CollectionUtils.isEmpty(deptIds)) {
            //查询当前人员的权限范围
            UserDeptAuthDTO userDeptAuthDTO = getUserAuthDept(orgId, userId, userDetail.getAdmin());
            log.debug("getDeptDimDashBoard userId={},userDeptAuthDTO={}", userId, JSON.toJSONString(userDeptAuthDTO));
            if (!userDeptAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userDeptAuthDTO.getDeptIds())) {
                return TalentbkUtil.emptyPage((int) paging.getLimit());
            }
            //如果是空，说明该用户拥有所有部门的权限
            deptIds = userDeptAuthDTO.getDeptIds();
        }
        //获取权限下的群组集合。如果没有传，则查询下权限下的群组数据
        if (CollectionUtils.isEmpty(groupIds)) {
            UserGroupAuthDTO userGroupAuthDTO = getUserAuthGroupIds(userDetail.getOrgId(), userId,
                    userDetail.getAdmin());
            log.debug("getDeptDimDashBoard userId={},userGroupAuthDTO={}", userId, JSON.toJSONString(userGroupAuthDTO));
            if (!userGroupAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userGroupAuthDTO.getGroupIds())) {
                return TalentbkUtil.emptyPage((int) paging.getLimit());
            }
            //如果是空，说明该用户拥有所有群组的权限
            groupIds = userGroupAuthDTO.getGroupIds();
        }
        log.debug("getDeptDimDashBoard authData deptIds={},groupIds={}", JSON.toJSONString(deptIds),
                JSON.toJSONString(groupIds));
        List<String> dwdDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
            dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
            //查询有部门ID，但是宽表不存在，则返回结果空数据，不然会被认为拥有所有权限
            if (CollectionUtils.isEmpty(dwdDeptIds)) {
                return TalentbkUtil.emptyPage((int) paging.getLimit());
            }
        }
        log.debug("getDeptDimDashBoard authData dwdDeptIds={}", JSON.toJSONString(dwdDeptIds));
        //查询权限部门下的人员和权限群组下的人 交集。根据交集的人员查询出能看到哪些部门ID集合
        List<String> deptIdsRes = getDeptAndGroupByUserMix(orgId, groupIds, dwdDeptIds);
        log.debug("getDeptDimDashBoard deptIdsRes={}", JSON.toJSONString(deptIdsRes));
        if (CollectionUtils.isEmpty(deptIdsRes)) {
            return TalentbkUtil.emptyPage((int) paging.getLimit());
        }
        //过滤部门已经删除的
        long totalCount = dwdDeptMapper.getDeptTotalCount(orgId, deptIdsRes);
        if (totalCount == 0L) {
            return TalentbkUtil.emptyPage((int) paging.getLimit());
        }
        //查询部门数据
        List<DwdDept> deptList = dwdDeptMapper.getDeptPage(orgId, deptIdsRes, paging.getOffset(),
                paging.getLimit());
        if (CollectionUtils.isEmpty(deptList)) {
            return TalentbkUtil.emptyPage((int) paging.getLimit());
        }
        List<DashBoardDeptDimVO> resultData = new ArrayList<>();
        List<String> deptIds2 = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
        //根据部门数据组装人员数据,查询部门已启用的人数
        List<DeptUserCountDTO> deptUserCountList = dwdUserMapper.getDeptUserCount(orgId, deptIds2, groupIds);
        Map<String, Long> deptUserCountMap = StreamUtil.list2map(deptUserCountList, DeptUserCountDTO::getDeptId,
                DeptUserCountDTO::getUserCount);
        //查询一起用人数能力和任务达标情况
        List<DeptUserSkillRtReachDTO> deptUserSkillRtReachList = dwdUserSkillRtStatisticsMapper.getDeptUserSkillReachCount(
                orgId, deptIds2, groupIds);
        Map<String, DeptUserSkillRtReachDTO> deptUserSkillRtReachMap = StreamUtil.list2map(deptUserSkillRtReachList,
                DeptUserSkillRtReachDTO::getDeptId);
        setDeptVO(deptList, resultData, deptUserCountMap, deptUserSkillRtReachMap);
        long totalPages = totalCount % paging.getLimit() == 0 ?
                totalCount / paging.getLimit() :
                totalCount / paging.getLimit() + 1;
        return new PagingList<>(resultData, new Paging(paging.getLimit(), paging.getOffset(), totalPages, totalCount));
    }

    public List<String> getDeptAndGroupByUserMix(String orgId, List<Long> groupIds, List<String> deptIds) {
        List<String> resList = dwdUserMapper.getDeptAndGroupByUserMix(orgId, groupIds, deptIds);
        if (CollectionUtils.isEmpty(resList)) {
            return resList;
        }
        return resList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    public void setDeptVO(List<DwdDept> deptList, List<DashBoardDeptDimVO> resultData,
            Map<String, Long> deptUserCountMap, Map<String, DeptUserSkillRtReachDTO> deptUserSkillRtReachMap) {
        deptList.forEach(deptInfo -> {
            DashBoardDeptDimVO dashBoardDeptDimVO = new DashBoardDeptDimVO();
            dashBoardDeptDimVO.setDeptId(deptInfo.getDeptId());
            dashBoardDeptDimVO.setThirdDeptId(deptInfo.getThirdDeptId());
            dashBoardDeptDimVO.setDeptName(StringUtils.isBlank(deptInfo.getRoutingPathName()) ?
                    deptInfo.getThirdDeptName() :
                    deptInfo.getRoutingPathName());
            dashBoardDeptDimVO.setSkillReachRate("0");
            dashBoardDeptDimVO.setTaskReachRate("0");
            resultData.add(dashBoardDeptDimVO);
            String deptId = deptInfo.getDeptId();
            if (StringUtils.isBlank(deptId)) {
                log.warn("deptId is null");
                return;
            }
            long totalUserCount = deptUserCountMap.getOrDefault(deptId, 0L);
            dashBoardDeptDimVO.setEnableUserCount(totalUserCount);
            DeptUserSkillRtReachDTO deptUserSkillRtReachDTO = deptUserSkillRtReachMap.getOrDefault(deptId, null);
            if (Objects.nonNull(deptUserSkillRtReachDTO)) {
                dashBoardDeptDimVO.setSkillReachCount(deptUserSkillRtReachDTO.getSkillReachCount());
                dashBoardDeptDimVO.setTaskReachCount(deptUserSkillRtReachDTO.getRtReachCount());
            }
            String skillReachRate = getRateCal(totalUserCount, dashBoardDeptDimVO.getSkillReachCount());
            dashBoardDeptDimVO.setSkillReachRate(skillReachRate);
            String rtReachRate = getRateCal(totalUserCount, dashBoardDeptDimVO.getTaskReachCount());
            dashBoardDeptDimVO.setTaskReachRate(rtReachRate);
        });
    }

    public List<String> setAuthDeptIds(List<String> deptIds, UserAuthBean4Dept userAuthBean4Dept) {
        if (CollectionUtils.isEmpty(deptIds)) {
            if (userAuthBean4Dept.getIsAll() == 1) {
                deptIds = new ArrayList<>();
            } else {
                deptIds = userAuthBean4Dept.getDeptIds();
            }
        }
        return deptIds;
    }

    /**
     * 展示形式为百分比，四舍五入保留百分比前整数，不满100%最大为99%
     *
     * @param totalUserCount
     * @param skillReachCount
     * @return
     */
    public String getRateCal(long totalUserCount, long skillReachCount) {
        if (totalUserCount <= 0L || skillReachCount <= 0L) {
            return "0";
        }
        BigDecimal skillReachRate = BigDecimal.valueOf(skillReachCount)
                .divide(BigDecimal.valueOf(totalUserCount), 3, RoundingMode.DOWN).multiply(BigDecimal.valueOf(100));
        if (skillReachRate.compareTo(BigDecimal.valueOf(99.5)) >= 0
                && skillReachRate.compareTo(BigDecimal.valueOf(99.999)) <= 0) {
            return BigDecimal.valueOf(99).stripTrailingZeros().toPlainString();
        }
        return BigDecimal.valueOf(skillReachCount).divide(BigDecimal.valueOf(totalUserCount), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString();
    }

    public PagingList<DashBoardGroupDimVO> getGroupDimDashBoard(UserBasicBean userBasic, CommonSearchParam searchParam,
            PageRequest pageRequest) {
        List<String> deptIds = searchParam.getDeptIds();
        List<Long> groupIds = searchParam.getUserGroupIds();
        if (CollectionUtils.isEmpty(deptIds) && CollectionUtils.isEmpty(groupIds)) {
            return TalentbkUtil.emptyPage((int) pageRequest.getSize());
        }
        String orgId = userBasic.getOrgId();
        String userId = userBasic.getUserId();
        if (CollectionUtils.isEmpty(deptIds)) {
            //查询当前人员的权限范围
            UserDeptAuthDTO userDeptAuthDTO = getUserAuthDept(orgId, userId, userBasic.getAdmin());
            if (!userDeptAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userDeptAuthDTO.getDeptIds())) {
                return TalentbkUtil.emptyPage((int) pageRequest.getSize());
            }
            //如果是空，说明该用户拥有所有部门的权限
            deptIds = userDeptAuthDTO.getDeptIds();
        }
        //获取权限下的群组集合。如果没有传，则查询下权限下的群组数据
        if (CollectionUtils.isEmpty(groupIds)) {
            UserGroupAuthDTO userGroupAuthDTO = getUserAuthGroupIds(userBasic.getOrgId(), userId, userBasic.getAdmin());
            if (!userGroupAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userGroupAuthDTO.getGroupIds())) {
                return TalentbkUtil.emptyPage((int) pageRequest.getSize());
            }
            //如果是空，说明该用户拥有所有群组的权限
            groupIds = userGroupAuthDTO.getGroupIds();
        }
        //校验部门ID是否有效
        List<String> dwdDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
            dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dwdDeptIds)) {
                return TalentbkUtil.emptyPage((int) pageRequest.getSize());
            }
        }
        //查询权限部门下的人员和权限群组下的人 交集。根据交集的人员查询出能看到哪些群组ID集合
        List<Long> authGroupIds = getUserAuthGroupListByDeptAndGroup(orgId, dwdDeptIds, groupIds);
        if (CollectionUtils.isEmpty(authGroupIds)) {
            return TalentbkUtil.emptyPage((int) pageRequest.getSize());
        }
        //查询
        IPage<UserGroup> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        Page<UserGroup> pageResult = userGroupRepository.findAuthData4PageByIds(pageable, orgId, authGroupIds);
        List<UserGroup> dataList = pageResult.getRecords();
        List<DashBoardGroupDimVO> resDataList = new ArrayList<>();
        //补充额外字段数据
        if (CollectionUtils.isNotEmpty(dataList)) {
            //只需要查询分页展示群组信息
            List<Long> groupIdData = dataList.stream().map(UserGroup::getId).distinct().collect(Collectors.toList());
            List<String> deptIds2 = transferDeptId(deptIds, orgId);
            //查询用户组下的人员数据
            List<GroupUserCountDTO> groupUserCountList = dwdUserMapper.getGroupUserCount(orgId, deptIds2, groupIdData);
            Map<Long, Long> groupUserCountMap = StreamUtil.list2map(groupUserCountList, GroupUserCountDTO::getGroupId,
                    GroupUserCountDTO::getUserCount);
            //查询一起用人数能力和任务达标情况
            List<GroupUserSkillRtReachDTO> groupUserSkillRtReachList = dwdUserSkillRtStatisticsMapper.getGroupUserSkillReachCount(
                    orgId, deptIds2, groupIdData);
            Map<Long, GroupUserSkillRtReachDTO> groupUserSkillRtReachMap = StreamUtil.list2map(
                    groupUserSkillRtReachList, GroupUserSkillRtReachDTO::getGroupId);
            setGroupDashboardData(dataList, groupUserCountMap, resDataList, groupUserSkillRtReachMap);
        }
        Paging resPage = new Paging(pageResult.getSize(), (pageResult.getCurrent() - 1) * pageResult.getSize(),
                pageResult.getPages(), pageResult.getTotal());
        return new PagingList<>(resDataList, resPage);
    }

    public List<Long> getUserAuthGroupListByDeptAndGroup(String orgId, List<String> deptIds,
            List<Long> groupIds) {
        List<Long> res = dwdUserMapper.getUserAuthGroupListByDeptAndGroup(orgId, deptIds, groupIds);
        if (CollectionUtils.isEmpty(res)) {
            return new ArrayList<>();
        }
        return res.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    public List<String> transferDeptId(List<String> deptIds, String orgId) {
        List<String> dwdDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
            dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
        }
        return dwdDeptIds;
    }

    public UserDeptAuthDTO getUserAuthDept(String orgId, String userId, String admin) {
        UserDeptAuthDTO userDeptAuthDTO = new UserDeptAuthDTO();
        userDeptAuthDTO.setDeptIds(new ArrayList<>());
        userDeptAuthDTO.setHasAllAuth(false);
        if ("1".equals(admin)) {
            userDeptAuthDTO.setHasAllAuth(true);
            return userDeptAuthDTO;
        }
        //查询当前人员的权限范围
        UserAuthBean4Dept userAuthBean4Dept = userAuthService.getAuthDeptIds(orgId, userId, GROUP_BOARD_NAV,
                GROUP_BOARD_DEPT_DATA_PERMISSION);
        if (userAuthBean4Dept.getIsAll() != 1 && CollectionUtils.isEmpty(userAuthBean4Dept.getDeptIds())) {
            return userDeptAuthDTO;
        }
        if (userAuthBean4Dept.getIsAll() == 1) {
            userDeptAuthDTO.setHasAllAuth(true);
            return userDeptAuthDTO;
        }
        //入参不为空，则按照传参的部门ID集合为准
        List<String> deptIds = new ArrayList<>();
        userDeptAuthDTO.setDeptIds(setAuthDeptIds(deptIds, userAuthBean4Dept));
        return userDeptAuthDTO;
    }

    public UserGroupAuthDTO getUserAuthGroupIds(String orgId, String userId, String admin) {
        UserGroupAuthDTO userGroupAuthDTO = new UserGroupAuthDTO();
        userGroupAuthDTO.setHasAllAuth(false);
        userGroupAuthDTO.setGroupIds(new ArrayList<>());
        if ("1".equals(admin)) {
            userGroupAuthDTO.setHasAllAuth(true);
            return userGroupAuthDTO;
        }
        //获取群组的负责人和创建人
        List<String> verifyUserIds = userGroupService.getAllDataAuthUserIds(orgId);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(verifyUserIds)) {
            return userGroupAuthDTO;
        }
        //获取用户权限范围的数据
        UserBasicBean userBasic = new UserBasicBean();
        userBasic.setOrgId(orgId);
        userBasic.setUserId(userId);
        userBasic.setAdmin(admin);
        UserAuthBean userAuthBean = userAuthService.verifyPermission(userBasic, GROUP_BOARD_NAV,
                GROUP_BOARD_GROUP_DATA_PERMISSION, verifyUserIds);
        //没有所有的用户权限并且管辖范围内的人员ID为空，则返回空
        if (!userAuthBean.isAllUserAllow() && CollectionUtils.isEmpty(userAuthBean.getUserIds())) {
            return userGroupAuthDTO;
        }
        //如果拥有所有的权限，群组ID为空
        if (userAuthBean.isAllUserAllow()) {
            userGroupAuthDTO.setHasAllAuth(true);
            return userGroupAuthDTO;
        }
        List<String> rangeUserIds = userAuthBean.getUserIds();
        log.debug("getUserAuthGroupIds orgId={} rangeUserIds={}", orgId, JSON.toJSONString(rangeUserIds));
        List<UserGroup> userGroupList = userGroupRepository.findAllAuthData(orgId, rangeUserIds);
        if (CollectionUtils.isEmpty(userGroupList)) {
            return userGroupAuthDTO;
        }
        List<Long> groupIds = userGroupList.stream().map(UserGroup::getId).distinct().collect(Collectors.toList());
        userGroupAuthDTO.setGroupIds(groupIds);
        return userGroupAuthDTO;
    }

    public void setGroupDashboardData(List<UserGroup> dataList, Map<Long, Long> deptUserCountMap,
            List<DashBoardGroupDimVO> resDataList, Map<Long, GroupUserSkillRtReachDTO> groupUserSkillRtReachMap) {
        dataList.forEach(data -> {
            DashBoardGroupDimVO dashBoardGroupDimVO = new DashBoardGroupDimVO();
            dashBoardGroupDimVO.setGroupId(data.getId());
            dashBoardGroupDimVO.setGroupName(data.getGroupName());
            long enableUserCount = deptUserCountMap.getOrDefault(data.getId(), 0L);
            dashBoardGroupDimVO.setEnableUserCount(enableUserCount);
            GroupUserSkillRtReachDTO groupUserSkillRtReachDTO = groupUserSkillRtReachMap.getOrDefault(data.getId(),
                    null);
            if (Objects.nonNull(groupUserSkillRtReachDTO)) {
                dashBoardGroupDimVO.setSkillReachCount(groupUserSkillRtReachDTO.getSkillReachCount());
                dashBoardGroupDimVO.setTaskReachCount(groupUserSkillRtReachDTO.getRtReachCount());
            }
            String skillReachRate = getRateCal(enableUserCount, dashBoardGroupDimVO.getSkillReachCount());
            dashBoardGroupDimVO.setSkillReachRate(skillReachRate);
            String rtReachRate = getRateCal(enableUserCount, dashBoardGroupDimVO.getTaskReachCount());
            dashBoardGroupDimVO.setTaskReachRate(rtReachRate);
            resDataList.add(dashBoardGroupDimVO);
        });
    }

    public PagingList<DashBoardPersonalDimVO> getPersonalDimDashBoard(UserBasicBean userBasic, Paging paging,
            CommonSearchParam searchParam) {
        List<String> deptIds = searchParam.getDeptIds();
        List<Long> groupIds = searchParam.getUserGroupIds();
        String userId = userBasic.getUserId();
        if (CommonUtils.isAllEmpty(deptIds, groupIds)) {
            return TalentbkUtil.emptyPage((int) paging.getLimit());
        }
        String orgId = userBasic.getOrgId();
        if (CollectionUtils.isEmpty(deptIds)) {
            //查询当前人员的权限范围
            UserDeptAuthDTO userDeptAuthDTO = getUserAuthDept(orgId, userId, userBasic.getAdmin());
            if (!userDeptAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userDeptAuthDTO.getDeptIds())) {
                return TalentbkUtil.emptyPage((int) paging.getLimit());
            }
            //如果是空，说明该用户拥有所有部门的权限
            deptIds = userDeptAuthDTO.getDeptIds();
        }
        //获取权限下的群组集合。如果没有传，则查询下权限下的群组数据
        if (CollectionUtils.isEmpty(groupIds)) {
            UserGroupAuthDTO userGroupAuthDTO = getUserAuthGroupIds(userBasic.getOrgId(), userId, userBasic.getAdmin());
            if (!userGroupAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userGroupAuthDTO.getGroupIds())) {
                return TalentbkUtil.emptyPage((int) paging.getLimit());
            }
            //如果是空，说明该用户拥有所有群组的权限
            groupIds = userGroupAuthDTO.getGroupIds();
        }
        List<String> dwdDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
            dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dwdDeptIds)) {
                return TalentbkUtil.emptyPage((int) paging.getLimit());
            }
        }
        //查询总人数
        long totalCount = dwdUserSkillRtStatisticsMapper.getDeptGroupAllCount(orgId, dwdDeptIds, groupIds);
        if (totalCount <= 0L) {
            return TalentbkUtil.emptyPage((int) paging.getLimit());
        }
        List<DwsUserSkillRtDTO> userList = dwdUserSkillRtStatisticsMapper.getPageByDeptGroup(orgId, dwdDeptIds,
                groupIds, paging.getOffset(), paging.getLimit());
        List<DashBoardPersonalDimVO> resultData = new ArrayList<>();
        long totalPages = totalCount % paging.getLimit() == 0 ?
                totalCount / paging.getLimit() :
                totalCount / paging.getLimit() + 1;
        if (CollectionUtils.isEmpty(userList)) {
            return new PagingList<>(Collections.emptyList(),
                    new Paging(paging.getLimit(), paging.getOffset(), totalPages, totalCount));
        }
        List<String> userIds = userList.stream().map(DwsUserSkillRtDTO::getUserId).distinct()
                .collect(Collectors.toList());
        List<DwdUser> dwdUsers = dwdUserMapper.getUserInfoByUserIds(orgId, userIds);
        Map<String, DwdUser> dwdUserIdMap = StreamUtil.list2map(dwdUsers, DwdUser::getUserId);
        userList.forEach(user -> {
            DashBoardPersonalDimVO dashBoardPersonalDimVO = new DashBoardPersonalDimVO();
            dashBoardPersonalDimVO.setThirdUserId(user.getUserId());
            DwdUser dwdUser = dwdUserIdMap.getOrDefault(user.getUserId(), null);
            if (Objects.nonNull(dwdUser)) {
                dashBoardPersonalDimVO.setFullName(dwdUser.getFullName());
                dashBoardPersonalDimVO.setUserId(dwdUser.getUserId());
                dashBoardPersonalDimVO.setUsername(dwdUser.getUserName());
            }
            dashBoardPersonalDimVO.setSkillReachCount(user.getMatchedSkillCnt());
            if (user.getTotalSkillCnt() <= 0L || user.getMatchedSkillCnt() <= 0L) {
                dashBoardPersonalDimVO.setSkillReachRate("0");
            } else {
                String skillReachRate = getRateCal(user.getTotalSkillCnt(), user.getMatchedSkillCnt());
                dashBoardPersonalDimVO.setSkillReachRate(skillReachRate);
            }
            dashBoardPersonalDimVO.setTaskReachCount(user.getMatchedRtCnt());
            if (user.getTotalRtCnt() <= 0L || user.getMatchedRtCnt() <= 0L) {
                dashBoardPersonalDimVO.setTaskReachRate("0");
            } else {
                String rtReachRate = getRateCal(user.getTotalRtCnt(), user.getMatchedRtCnt());
                dashBoardPersonalDimVO.setTaskReachRate(rtReachRate);
            }
            resultData.add(dashBoardPersonalDimVO);
        });
        return new PagingList<>(resultData, new Paging(paging.getLimit(), paging.getOffset(), totalPages, totalCount));
    }

    public DashBoardPersonalDetailVO getPersonalDetail(String orgId, String userId) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(orgId)) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        }
        UdpLiteUser udpLiteUser = udpLiteUserRepository.getUdpUserInfoById(orgId, userId);
        if (Objects.isNull(udpLiteUser)) {
            throw new ApiException(BkApiErrorKeys.USER_NOT_EXIST);
        }
        DashBoardPersonalDetailVO resVo = new DashBoardPersonalDetailVO();
        resVo.setUserId(userId);
        resVo.setThirdUserId(udpLiteUser.getThirdUserId());
        resVo.setUsername(udpLiteUser.getUsername());
        resVo.setImgUrl(udpLiteUser.getImgUrl());
        resVo.setFullName(udpLiteUser.getFullname());
        resVo.setDeptId(udpLiteUser.getDeptId());
        resVo.setDeptName(udpLiteUser.getDeptName());
        resVo.setPositionId(udpLiteUser.getPositionId());
        resVo.setPositionName(udpLiteUser.getPositionName());
        List<PersonalEvalModelSimpleVO> models = new ArrayList<>();
        resVo.setModels(models);
        //dwd_user_eval_skill_rt   dwd_user_jq_task_rv
        //查询能力模型集合
        List<PersonalEvalModelSimpleVO> skillModelList = dwdUserEvalSkillRtMapper.getUsedSkillModelListByUserId(orgId,
                udpLiteUser.getId());
        if (CollectionUtils.isNotEmpty(skillModelList)) {
            //根据模型ID查询基本信息
            List<String> modelIds = skillModelList.stream().map(PersonalEvalModelSimpleVO::getModelId)
                    .collect(Collectors.toList());
            List<ModelDto> modelBeanList = sdRpc.getModelDetailList(orgId, modelIds);
            Map<String, ModelDto> modelIdNameMap = StreamUtil.list2map(modelBeanList, ModelDto::getId);
            skillModelList.forEach(el -> {
                ModelDto skillModel = modelIdNameMap.get(el.getModelId());
                if (skillModel != null) {
                    el.setModelName(skillModel.getTitle());
                    el.setModelVersion(sdRpc.showModelVersion(skillModel.getVersionNumber()));
                }
                el.setModelType(1);
            });
            models.addAll(skillModelList);
        }
        List<PersonalEvalModelSimpleVO> rtModelList = dwdUserJqTaskRvMapper.getUsedRtModelListByUserId(orgId,
                udpLiteUser.getId());
        if (CollectionUtils.isNotEmpty(rtModelList)) {
            log.debug("getPersonalDetail rtModelList={}", JSON.toJSONString(rtModelList));
            //根据模型ID查询基本信息
            List<String> rtModelIds = rtModelList.stream().map(PersonalEvalModelSimpleVO::getModelId)
                    .collect(Collectors.toList());
            List<ModelDto> rtModelBeanList = sdRpc.getRtModelDetailList(orgId, rtModelIds);
            log.debug("getPersonalDetail rtModelBeanList={}", JSON.toJSONString(rtModelBeanList));
            Map<String, ModelDto> rtModelIdNameMap = StreamUtil.list2map(rtModelBeanList, ModelDto::getId);
            rtModelList.forEach(el -> {
                ModelDto rtModel = rtModelIdNameMap.get(el.getModelId());
                if (rtModel != null) {
                    el.setModelName(rtModel.getTitle());
                    el.setModelVersion(sdRpc.showModelVersion(rtModel.getVersionNumber()));
                }
                el.setModelType(2);
            });
            models.addAll(rtModelList);
        }
        return resVo;
    }

    public PersonalEvalModelVO getPersonalModelDetail(String orgId, String userId, Integer modelType, String modelId) {
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(userId) || Objects.isNull(modelType)
                || StringUtils.isBlank(modelId)) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        }
        if (!Lists.newArrayList(1, 2).contains(modelType)) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        }
        PersonalEvalModelVO modelVO = new PersonalEvalModelVO();
        modelVO.setModelId(modelId);
        modelVO.setModelType(modelType);
        SdModelInfoDto modelInfo = null;
        if (modelType == 1) {
            modelInfo = sdRpc.convertAsResult(sdRpc.getModelDetailData(orgId, modelId),1);
        } else if (modelType == 2) {
            modelInfo = sdRpc.convertAsResult(sdRpc.getModelDetailData(orgId, modelId),4);
        }
        if (Objects.isNull(modelInfo)) {
            return modelVO;
        }
        List<DwdUserIndicatorResult> indicatorResList = dwdUserEvalSkillRtMapper.queryAllByUserIdAndModelIds(
                orgId, userId, Lists.newArrayList(modelId));
        Map<String, DwdUserIndicatorResult> indicatorResMap = StreamUtil.list2map(indicatorResList, DwdUserIndicatorResult::getIndicatorId);
        modelInfo.eachIndicator(indicator -> {
            DwdUserIndicatorResult indicatorResult = indicatorResMap.get(indicator.getItemId());
            if (indicatorResult != null) {
                indicator.setQualified(indicatorResult.getQualified());
                if (Integer.valueOf(4).equals(indicator.getItemType())) {
                    indicator.setEvaluateResult(Optional.ofNullable(indicatorResult.getScore()).map(BigDecimal::toPlainString).orElse(StringPool.EMPTY));
                } else {
                    String scoreTenStr = Optional.ofNullable(indicatorResult.getScoreTen())
                            .map(BigDecimal::stripTrailingZeros)
                            .map(BigDecimal::toPlainString)
                            .orElse("0");
                    if (StringUtils.isNotBlank(indicatorResult.getLevelName())) {
                        indicator.setEvaluateResult(String.format("%s（%s）", scoreTenStr,
                                indicatorResult.getLevelName()));
                    } else {
                        indicator.setEvaluateResult(scoreTenStr);
                    }
                }
            }
        });
        modelVO.setIndicatorResults(modelInfo.getIndicators());
        return modelVO;
    }

    public Collector<DwdUserIndicatorResult, ?, Map<String, DwdUserIndicatorResult>> getScoreMapCollector() {
        return Collectors.toMap(DwdUserIndicatorResult::getIndicatorId, Function.identity(), (o1, o2) -> {
            BigDecimal score1 = Optional.ofNullable(o1.getScoreTen()).orElse(BigDecimal.ZERO);
            BigDecimal score2 = Optional.ofNullable(o2.getScoreTen()).orElse(BigDecimal.ZERO);
            return score1.compareTo(score2) > 0 ? o1 : o2;
        });
    }
}
