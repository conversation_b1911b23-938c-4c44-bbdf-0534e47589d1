package com.yxt.talent.bk.core.heir.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirPrepareCfgDataBean
 *
 * <AUTHOR> geyan
 * @Date 19/8/23 3:45 pm
 */
@Data
public class HeirPrepareCfgDataBean {
    @Schema(description = "准备度规则等级id")
    private Long prepareLevelId;
    @Schema(description = "规则配置id")
    private Long ruleCfgId;
    @Schema(description = "准备度规则配置数据")
    private String ruleCfgData;
}
