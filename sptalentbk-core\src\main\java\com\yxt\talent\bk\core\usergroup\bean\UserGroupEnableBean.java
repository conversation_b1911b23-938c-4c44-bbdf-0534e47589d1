package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;

/**
 * @program: sptalentb<PERSON>pi
 * @description:
 **/
@Data
public class UserGroupEnableBean {
    @NotNull(message = "apis.talentbk.userGroup.enable.blank")
    @Range(min = 0, max = 1, message = "apis.talentbk.userGroup.args.error")
    @Schema(description = "启用，禁用，1：启用，0：禁用", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer enabled;
}
