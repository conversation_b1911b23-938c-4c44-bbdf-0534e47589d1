package com.yxt.talent.bk.api.controller.heir;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.heir.HeirOrgLevelConfigService;
import com.yxt.talent.bk.svc.heir.bean.req.LevelConfigReq;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareResp;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirRiskResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/mgr/heir/levelConfig")
@Slf4j
@AllArgsConstructor
@Tag(name = "继任配置数据接口")
public class HeirOrgLevelConfigController extends BaseController {

    private final HeirOrgLevelConfigService heirOrgLevelConfigService;

    @Operation(summary = "继任配置数据保存")
    @Parameters({         @Parameter(name = "levelConfigBean", description = "继任设置")})
    @PostMapping(value = "/save")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public void saveLevelConfig(@RequestBody LevelConfigReq levelConfigReq) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        heirOrgLevelConfigService.saveLevelConfig(userCacheBasic, levelConfigReq,true);
    }

    @Operation(summary = "获取继任准备度数据")
    @GetMapping(value = "/getHeirPrepareData")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public List<HeirPrepareResp> getHeirPrepareData() {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return heirOrgLevelConfigService.getHeirPrepareData(userCacheBasic.getOrgId());
    }

    @Operation(summary = "获取继任风险数据")
    @GetMapping(value = "/getHeirRiskData")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public List<HeirRiskResp> getHeirRiskData() {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return heirOrgLevelConfigService.getHeirRiskData(userCacheBasic.getOrgId());
    }

    @Operation(summary = "继任数据删除")
    @Parameters({         @Parameter(name = "id", description = "id", in = ParameterIn.PATH)})
    @DeleteMapping(value = "/delete/{id}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN)
    public void deleteById(@PathVariable Long id) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        heirOrgLevelConfigService.deleteById(id,userCacheBasic.getOrgId());
    }

    @Operation(summary = "获取学员端设置")
    @GetMapping(value = "/getStudentConfig")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public int getStudentConfig() {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return heirOrgLevelConfigService.getStudentConfig(userCacheBasic.getOrgId());
    }
}
