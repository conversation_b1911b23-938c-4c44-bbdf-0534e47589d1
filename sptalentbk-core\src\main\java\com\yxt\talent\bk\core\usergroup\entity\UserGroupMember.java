package com.yxt.talent.bk.core.usergroup.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import lombok.Getter;
import lombok.Setter;

/**
    * 群组人员;
    */
@Getter
@Setter
@TableName(value = "bk_user_group_member")
public class UserGroupMember {
    /**
     * 唯一 ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 企业id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 群组 ID
     */
    @TableField(value = "group_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_GROUP_ID)
    private Long groupId;

    /**
     * 用户 ID
     */
    @TableField(value = "user_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String userId;
}
