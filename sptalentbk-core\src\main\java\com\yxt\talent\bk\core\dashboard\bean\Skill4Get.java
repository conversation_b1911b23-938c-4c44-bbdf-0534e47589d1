package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * Skill4Get
 *
 * <AUTHOR>
 * @since 2020-08-25 15:33:35
 */
@Data
public class Skill4Get {

    @Schema(description = "能力Id")
    private String id;

    @Schema(description = "能力名称")
    private String name;

    @Schema(description = "能力等级")
    private Integer level ;

    @Schema(description = "能力评估结果")
    private String evaluateResult;

    @Schema(description = "是否达标 0-否 1-是")
    private Integer reached;

    @Schema(description = "能力等级列表")
    private List<SkillLevelMap4Get> skillLevelMaps = new ArrayList<>(0);
}
