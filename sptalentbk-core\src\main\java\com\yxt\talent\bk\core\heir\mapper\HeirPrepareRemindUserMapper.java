package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.entity.HeirPrepareRemindUserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * HeirPrepareRemindUserMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 6:22 pm
 */
@Mapper
public interface HeirPrepareRemindUserMapper extends BkBaseMapper<HeirPrepareRemindUserEntity> {
    void batchInsert(@Param("list") List<HeirPrepareRemindUserEntity> list);

    List<HeirPrepareRemindUserEntity> selectUserIdsByPrepareCfgId(@Param("posId") String posId);

    void deleteUsers(@Param("orgId") String orgId,@Param("posId") String posId);

    List<String> posRemindUserIds(@Param("posId") String posId);
}
