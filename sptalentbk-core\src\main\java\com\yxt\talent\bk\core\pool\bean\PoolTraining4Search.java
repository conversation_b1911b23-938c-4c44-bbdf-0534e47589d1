package com.yxt.talent.bk.core.pool.bean;

import com.yxt.common.pojo.api.PageBean;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Schema(name = "搜索人才池绑定的培训项目列表")
public class PoolTraining4Search extends PageBean {

    @Schema(description = "培训项目名称")
    private String trainingName;

}
