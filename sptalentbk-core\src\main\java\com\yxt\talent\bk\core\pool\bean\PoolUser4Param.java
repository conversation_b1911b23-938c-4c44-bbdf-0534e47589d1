package com.yxt.talent.bk.core.pool.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.bk.common.bean.udp.DeptConditionBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangKeywordBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *  人才池人员sql查参
 * <AUTHOR>
 * @since 2022/9/8 10:46
 * @version 1.0
 */
@Data
@NoArgsConstructor
public class PoolUser4Param {

    @Schema(description = "主键（更新专用）")
    private String id;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "人才池id")
    private String poolId;

    @Schema(description = "准备度id")
    private String readinessId;


    @Schema(description = "搜索关键字（姓名/工号/岗位）")
    private String keyword;

    @JsonIgnore
    private UdpLangKeywordBean keywordLangMatch;

    @Schema(description = "排序列（准备度：order_index；创建日期：create_time(默认)）")
    private String orderColumn;

    @Schema(description = "排序（0：正序；1：倒叙）")
    private Integer orderBy;

    @Schema(description = "用户id list【用于keyword查询】")
    private List<String> userIdList;

    @Schema(description = "测评id")
    private String evalId;

    @Schema(description = "部门选择信息")
    private List<SearchDeptInfo> searchDeptInfoList;

    @Schema(description = "部门查询条件")
    private DeptConditionBean deptIdCon;

}
