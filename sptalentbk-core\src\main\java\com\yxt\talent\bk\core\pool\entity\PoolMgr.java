package com.yxt.talent.bk.core.pool.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("bk_pool_mgr")
@EqualsAndHashCode(callSuper = true)
public class PoolMgr extends CreatorEntity {

    @TableField("id")
    /**
     * 主键id
     */
    private String id;
    /**
     * 人才池id
     */
    @TableField("pool_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_POOL_ID)
    private String poolId;
    /**
     * 管理者id
     */
    @TableField("mgr_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String mgrId;
    /**
     * 机构id
     */
    @TableField("org_id")
    private String orgId;

}
