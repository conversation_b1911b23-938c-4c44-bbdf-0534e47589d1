<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirOrgLevelCfgMapper">
    <insert id="batchInsert">
        insert into bk_heir_org_level_cfg
        (id, org_id, level_type, level_name, order_index, color_code, remark,
        cfg_value, num_range_max, num_range_min, create_user_id, create_time, update_user_id,update_time,deleted)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id}, #{item.orgId}, #{item.levelType}, #{item.levelName},#{item.orderIndex},
            #{item.colorCode}, #{item.remark}, #{item.cfgValue},#{item.numRangeMax},#{item.numRangeMin},
            #{item.createUserId},#{item.createTime},
            #{item.updateUserId},#{item.updateTime},#{item.deleted})
        </foreach>
    </insert>

    <select id="getHeirPrepareData" resultType="com.yxt.talent.bk.core.heir.entity.HeirOrgLevelCfgEntity">
        select * from bk_heir_org_level_cfg where org_id= #{orgId} and deleted=0 and level_type=#{levelType}
        order by order_index
    </select>

    <update id="deleteById">
        update bk_heir_org_level_cfg set deleted=1,update_time = now() where id=#{id}
    </update>

    <select id="selectPrepareLevel" resultType="java.lang.Long">
        select id from bk_heir_org_level_cfg where org_id= #{orgId} and level_type=0 and deleted=0
    </select>
</mapper>
