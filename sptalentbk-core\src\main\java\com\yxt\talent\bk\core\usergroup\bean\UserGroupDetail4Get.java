package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Data
public class UserGroupDetail4Get {

    @Schema(description = "配置规则", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private UserRuleDetail4Get userRuleDetail4Get;

    @Schema(description = "管理员", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> userManagers;

    /**
     * 群组类型（1:静态，2：动态）
     */
    @Schema(description = "群组类型（1:静态，2：动态）", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer groupType;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称, 在我的团队-人才看板-人才库那边也用作人才库名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String groupName;

    /**
     * 是否启用，（0：未启用，1：已启用）
     */
    @Schema(description = "是否启用，（0：未启用，1：已启用）,默认未启用", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer enabled = 0;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String groupDesc;

    @Schema(description = "创建人员", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String createName;

    /**
     * 动态群组计算周期，1：日，2：每周，3：每月
     */
    @Schema(description = "动态群组计算周期，1：日，2：每周，3：每月", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer caculateJob;

    /**
     * 动态群组计算周期，为 2 时，表示周一到周日。为 3 时，是具体某一天
     */
    @Schema(description = "动态群组计算周期，为 2 时，表示周一到周日。为 3 时，是具体某一天", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer caculateDate;

    /**
     * 方案名称
     */
    @Schema(description = "方案名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String schemeName;


    @Schema(description = "常用筛选方案id,如果是采用常用筛选方案创建的人才库,这里会有值", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long schemeId;

}
