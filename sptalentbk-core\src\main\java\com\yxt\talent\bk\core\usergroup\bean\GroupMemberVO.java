package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/18
 */
@Data
@Schema
public class GroupMemberVO {
    @Schema(description = "主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @Schema(description = "群组人员id")
    private String userId;
    @Schema(description = "群组id")
    private String groupId;
    @Schema(description = "姓名")
    private String fullname;
    @Schema(description = "账号")
    private String username;
    @Schema(description = "部门名称")
    private String deptName;
    @Schema(description = "岗位名称")
    private String positionName;
    @Schema(description = "职级名称")
    private String gradeName;
    @Schema(description = "加入时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime joinTime;
    @Schema(description = "动态标签")
    private List<DynamicsLabelVO> dynamicsLabelList = new ArrayList<>();

}

