<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.tag.mapper.UserTagValueMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.tag.entity.UserTagValueEntity">
        <!--@mbg.generated-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="tag_id" jdbcType="CHAR" property="tagId"/>
        <result column="tag_value_id" jdbcType="CHAR" property="tagValueId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="listUserTagValue" resultType="com.yxt.talent.bk.core.tag.bean.UserTagValueBaseBean">
        select user_id userId, tag_value_id tagValueId, tag_id tagId
        from
        bk_user_tag_value
        where org_id = #{orgId}
        and tag_value_id in
        <foreach collection="tagValueIds" item="tagValueId" open="(" separator="," close=")">
            #{tagValueId}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and deleted = 0
    </select>

    <select id="listPageTagValueUser" resultType="com.yxt.talent.bk.core.tag.bean.UserBaseInfoBean">
        select b.id userId, b.fullname, b.username, b.status, b.dept_name departmentName,
            b.position_name positionName
        from
        bk_user_tag_value a
        inner join
        udp_lite_user_sp b
        on a.user_id = b.id and a.org_id = b.org_id
        where
        a.org_id = #{orgId}
        and a.tag_id = #{tagId}
        and a.tag_value_id = #{tagValueId} and a.deleted = 0
        <if test="keyword != null and keyword != ''">
            and ((b.username like CONCAT('%', #{keyword}, '%'))
            or (b.fullname like CONCAT('%', #{keyword}, '%')))
        </if>
        and b.deleted = 0
    </select>

    <select id="listUserTagValueName" resultType="com.yxt.talent.bk.core.tag.bean.UserTagBaseBean">
        select a.user_id userId, b.value_name tagValueName, b.id tagValueId, a.tag_id
        from
        bk_user_tag_value a
        left join
        bk_tag_value b
        on a.org_id = b.org_id and a.tag_value_id = b.id
        where
        a.org_id = #{orgId} and a.tag_id = #{tagId} and a.deleted = 0
        and a.user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <update id="delTagValueUser">
        update
        bk_user_tag_value
        set deleted = 1, update_user_id = #{operatorId}, update_time = #{updateTime}
        where org_id = #{orgId}
        and tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and tag_value_id in
        <foreach collection="tagValueIds" item="tagValueId" open="(" separator="," close=")">
            #{tagValueId}
        </foreach>
    </update>

    <update id="deleteByTagIdsAndUserIds">
        update
        bk_user_tag_value
        set deleted = 1, update_user_id = #{operatorId}, update_time = #{updateTime}
        where org_id = #{orgId}
        and tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <update id="delTagOtherValueUser">
        update
        bk_user_tag_value
        set deleted = 1, update_user_id = #{operatorId}, update_time = #{updateTime}
        where org_id = #{orgId}
        and tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and tag_value_id not in
        <foreach collection="tagValueIds" item="tagValueId" open="(" separator="," close=")">
            #{tagValueId}
        </foreach>
    </update>

    <select id="listUserTagValueByTagId" resultType="com.yxt.talent.bk.core.tag.bean.UserTagBaseBean">
        select user_id userId, tag_value_id tagValueId
        from
        bk_user_tag_value
        where org_id = #{orgId}
        and tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and deleted = 0
    </select>

    <select id="listTagValueAllUserId" resultType="java.lang.String">
        select user_id
        from
        bk_user_tag_value
        where org_id = #{orgId}
        and tag_id = #{tagId}
        and tag_value_id in
        <foreach collection="tagValueIds" item="tagValueId" open="(" separator="," close=")">
            #{tagValueId}
        </foreach>
        and deleted = 0
    </select>

    <select id="findValueNameTagIdLike" resultType="com.yxt.talent.bk.core.tag.bean.TagValueBean">
        SELECT
        tag_id as tagId,
        value_name as valueName
        FROM
        bk_tag_value
        WHERE
        org_id = #{orgId}
        <if test="keyword != null and keyword != ''">
            AND value_name like CONCAT('%', #{keyword} ,'%') ESCAPE '\\'
        </if>
    </select>
</mapper>
