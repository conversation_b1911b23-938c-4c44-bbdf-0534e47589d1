package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Schema(name = "修改继任者")
public class PosUserUpdateReq {

    @Schema(description = "继任id")
    @NotNull(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String posId;

    @Schema(description = "待修改的记录id（最多1000个）")
    @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    @Size(message = BkApiErrorKeys.PARAM_SIZE_INVALID_MESSAGE, min = 1, max = 1000)
    private List<Long> ids;

    @Schema(description = "准备度规则等级id（为0表示没有准备度）")
    private Long prepareLevelId;

    @Schema(description = "继任状态：0:进行中，1:已退出")
    private Integer heirStatus;

    @Schema(description = "退出原因")
    private String quitReason;
}
