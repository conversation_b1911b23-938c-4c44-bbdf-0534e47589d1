package com.yxt.talent.bk.core.heir.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.core.heir.entity.HeirOrgLevelCfgEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirOrgLevelCfgMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * HeirOrgLevelCfgRepository
 *
 * <AUTHOR> harleyge
 * @Date 19/9/24 3:45 pm
 */
@Slf4j
@Repository
@AllArgsConstructor
public class HeirOrgLevelCfgRepository extends ServiceImpl<HeirOrgLevelCfgMapper, HeirOrgLevelCfgEntity> {

    public List<HeirOrgLevelCfgEntity> listByOrgId(String orgId) {
        LambdaQueryWrapper<HeirOrgLevelCfgEntity> queryWrapper = getQueryWrapper();
        queryWrapper.eq(HeirOrgLevelCfgEntity::getOrgId, orgId);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<HeirOrgLevelCfgEntity> getQueryWrapper() {
        LambdaQueryWrapper<HeirOrgLevelCfgEntity> wrapper = new LambdaQueryWrapper<>();
        return wrapper.eq(HeirOrgLevelCfgEntity::getDeleted, YesOrNo.NO.getValue());
    }
}
