package com.yxt.talent.bk.core.sd.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SdModelIndicator4ResultDto extends SdModelIndicatorDto {
    @Schema(description = "能力评估结果")
    private String evaluateResult;

    @Schema(description = "是否达标 0-否 1-是")
    private Integer qualified;

    @Schema(description = "子指标信息")
    private List<SdModelIndicator4ResultDto> children;


    public void setChildren(List children) {
        this.children = children;
    }
}
