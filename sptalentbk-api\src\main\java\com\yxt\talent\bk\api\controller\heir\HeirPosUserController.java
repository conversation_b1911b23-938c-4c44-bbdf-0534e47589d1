package com.yxt.talent.bk.api.controller.heir;

import com.alibaba.fastjson2.JSON;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.util.ApiUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.spsdk.audit.annotations.EasyAuditLogSelect;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.heir.bean.PosUserBriefBean;
import com.yxt.talent.bk.svc.heir.HeirPosUserService;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserAddReq;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserBatchAddReq;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserDeleteReq;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserListReq;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserUpdateReq;
import com.yxt.talent.bk.svc.heir.bean.resp.PosUserListResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
* <AUTHOR>
*  @date 2023/8/16
**/
@Slf4j
@Tag(name = "继任者")
@RestController
@RequestMapping("/mgr/heir/pos_user")
@RequiredArgsConstructor
public class HeirPosUserController extends BaseController {

    private final HeirPosUserService heirPosUserService;

    @Operation(summary = "添加继任者")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_ADD_USER, paramExp = "#req.editParam4Log()")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    @PostMapping("/add")
    public void add(@RequestBody @Valid PosUserAddReq req) {
        log.info("addPosUser req: {}", JSON.toJSONString(req));
        UserCacheBasic currentUser = getUserCacheBasic();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_USER_OPERATE, currentUser.getOrgId(), req.getPosId());
        CommonUtils.lockRun(lockKey,30, TimeUnit.SECONDS, () -> {
            heirPosUserService.createPosUsers(currentUser, req.getPosId(), req.getPosType(), req.getUserIds());
        });
    }

    @Operation(summary = "批量向多个继任添加继任者", description = "（外部）添加继任人员组件 使用")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    @PostMapping("/batch_add")
    public void batchAdd(@RequestBody @Valid PosUserBatchAddReq req) {
        log.info("batchAddPosUser req: {}", JSON.toJSONString(req));
        UserCacheBasic currentUser = getUserCacheBasic();
        for (PosUserBatchAddReq.HeirPosReq heirPosReq : req.getHeirPosList()) {
            String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_USER_OPERATE, currentUser.getOrgId(), heirPosReq.getPosId());
            CommonUtils.lockRun(lockKey,30, TimeUnit.SECONDS, () -> {
                heirPosUserService.createPosUsers(currentUser, heirPosReq.getPosId(), heirPosReq.getPosType(), req.getUserIds());
            });
        }
    }

    @Operation(summary = "修改继任者")
    @Auditing
    @EasyAuditLogSelect({@EasyAuditLog(value = AuditLogConstants.HEIR_USER_PREPARE, conditionExp = "#req.prepareLevelId != null", paramExp = "#req"),
            @EasyAuditLog(value = AuditLogConstants.HEIR_USER_EXIT, conditionExp = "#req.heirStatus == 1", paramExp = "#req"),
            @EasyAuditLog(value = AuditLogConstants.HEIR_USER_REJOIN, conditionExp = "#req.heirStatus == 0", paramExp = "#req")})
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    @PutMapping("/update")
    public void update(@RequestBody @Valid PosUserUpdateReq req) {
        log.info("updatePosUser req: {}", JSON.toJSONString(req));
        UserCacheBasic currentUser = getUserCacheBasic();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_USER_OPERATE, currentUser.getOrgId(), req.getPosId());
        CommonUtils.lockRun(lockKey,30, TimeUnit.SECONDS, () -> {
            heirPosUserService.updatePosUsers(currentUser, req);
        });
    }

    @Operation(summary = "继任者删除")
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_REMOVE_USER, paramExp = "#req.editParam4Log()")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN)
    @DeleteMapping("/delete")
    public void delete(@RequestBody @Valid PosUserDeleteReq req) {
        log.info("deletePosUser req: {}", JSON.toJSONString(req));
        UserCacheBasic currentUser = getUserCacheBasic();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_USER_OPERATE, currentUser.getOrgId(), req.getPosId());
        CommonUtils.lockRun(lockKey,30, TimeUnit.SECONDS, () -> {
            heirPosUserService.delete(currentUser, req.getPosId(), req.getUserIds());
        });
    }

    @Operation(summary = "继任者列表")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @PostMapping("/list")
    public PagingList<PosUserListResp> list(HttpServletRequest request, @RequestBody @Valid PosUserListReq req) {
        UserCacheBasic currentUser = getUserCacheBasic();
        return heirPosUserService.list(ApiUtil.getPageRequest(request), currentUser, req);
    }

    @Operation(summary = "有效继任者列表(只返回名字,头像)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/list_valid_brief")
    public PagingList<PosUserBriefBean> listValidBrief(HttpServletRequest request, @RequestParam String posId) {
        UserCacheBasic currentUser = getUserCacheBasic();
        return heirPosUserService.validBriefPage(currentUser, ApiUtil.getPageRequest(request), posId);
    }
}

