package com.yxt.talent.bk.svc.persona.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "维度标签详情")
public class DimensionTagMap4List {
    @Schema(description = "维度标签关系主键")
    private String dimensionTagMapId;
    @Schema(description = "画像主题id")
    private String themeId;
    @Schema(description = "画像主题-维度Id")
    private String dimensionId;
    @Schema(description = "画像主题-标签id")
    private String tagId;
    @Schema(description = "画像主题-标签来源(标签来源(0-内置,1-自建,2-固定))")
    private Integer tagSource;
    @Schema(description = "标签描述/定义")
    private String description;
    @Schema(description = "标签名称")
    private String tagName;
}
