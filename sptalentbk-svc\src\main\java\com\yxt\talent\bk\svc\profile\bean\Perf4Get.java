package com.yxt.talent.bk.svc.profile.bean;

import com.yxt.talent.bk.common.aop.sqlgenerator.SqlOrder;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@SqlTable("dwd_perf")
@Schema(name = "人才画像-绩效表现")
public class Perf4Get {

    @Schema(description = "第三方绩效 ID")
    private String thirdPerfId;

    @Schema(description = "第三方绩效名称")
    private String thirdPerfName;

    @SqlOrder
    @Schema(description = "绩效排序 ID")
    private Integer orderIndex;

}
