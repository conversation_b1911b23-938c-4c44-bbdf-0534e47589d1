package com.yxt.talent.bk.svc.strategy;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.ExcelUtil;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 人才池导入，错误导出
 */
@Component
@AllArgsConstructor
public class PoolUser4ErrorStrategy implements OutputStrategy {
    public static final String TEMP_VAR_POOLUSERIMPORT = "poolUserImport";
    public static final String TEMP_VAR_POOLUSERIMPORTRULE1 = "poolUserImportRule1";
    public static final String TEMP_VAR_POOLUSERFULLNAME = "poolUserFullname";
    public static final String TEMP_VAR_POOLUSERNAME = "poolUsername";
    public static final String TEMP_VAR_FULLNAME = "fullName";
    public static final String TEMP_VAR_USERNAME = "userName";
    public static final String TEMP_VAR_ERRMSG = "errMsg";
    private final I18nComponent i18nComponent;
    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put(TEMP_VAR_FULLNAME,
                i18nComponent.getI18nValue("apis.talentbk.pool.user.export.header.fullName"));
        headerMap.put(TEMP_VAR_USERNAME,
                i18nComponent.getI18nValue("apis.talentbk.pool.user.export.header.userName"));
        headerMap.put(TEMP_VAR_ERRMSG,
                i18nComponent.getI18nValue("apis.talentbk.pool.user.export.header.errMsg"));
        headerMap.put(TEMP_VAR_POOLUSERIMPORT,
                i18nComponent.getI18nValue("apis.talentbk.pool.user.import.desc"));
        headerMap.put(TEMP_VAR_POOLUSERIMPORTRULE1,
                i18nComponent.getI18nValue("apis.talentbk.pool.user.import.rule1"));
        ExcelUtil.exportWithTemplate(headerMap,
                data, filePath, ExportConstants.POOL_USER_FAIL_EXPORT_TEMP_FILE_PATH);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE).appCode(ModuleConstants.APP_CODE)
                .moduleCode(ModuleConstants.MODULE_CODE).fileName(fileName)
                .name(i18nComponent.getI18nValue(ExportConstants.POOL_USER_FAIL_EXPORT_FILE_NAME)).build();
    }
}
