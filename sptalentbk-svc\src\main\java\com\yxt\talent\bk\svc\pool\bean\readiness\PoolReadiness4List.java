package com.yxt.talent.bk.svc.pool.bean.readiness;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "准备度列表")
@Data
public class PoolReadiness4List {
    @Schema(description = "准备度主键")
    private String id;
    @Schema(description = "准备度名称")
    private String readinessName;
    @Schema(description = "排序【ASC 排序，值越小准备度越高】")
    private Integer orderIndex;
}
