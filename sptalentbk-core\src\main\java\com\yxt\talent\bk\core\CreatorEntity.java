package com.yxt.talent.bk.core;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class CreatorEntity {

    @Schema(description = "创建人主键")
    @TableField("create_user_id")
    private String createUserId;

    @TableField("create_time")
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    @TableField("update_user_id")
    @Schema(description = "更新人主键")
    private String updateUserId;

    @TableField("update_time")
    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;
}
