package com.yxt.talent.bk.core.heir.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirDeptInfoDTO
 *
 * <AUTHOR> geyan
 * @Date 20/11/23 5:41 pm
 */
@Data
public class HeirDeptInfoDTO implements HeirPosRiskSupport {
    private String id;

    private String name;

    private String manageName;

    @Schema(description = "目标继任数量,0:未设置")
    private Integer heirTargetQty;

    @Schema(description = "有效人数")
    private Integer heirValidQty;

    @Schema(description = "0:自动规则，1:手动规则")
    private Integer riskRuleType;

    @Schema(description = "风险规则id")
    private Long riskLevelId;

    @Schema(description = "风险规则名称")
    private String riskLevelName;

    @Schema(description = "风险规则颜色")
    private String riskLevelColor;
}
