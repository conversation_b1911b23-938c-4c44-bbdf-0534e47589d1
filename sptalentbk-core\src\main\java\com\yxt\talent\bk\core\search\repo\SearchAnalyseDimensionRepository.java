package com.yxt.talent.bk.core.search.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.search.entity.SearchAnalyseDimension;
import com.yxt.talent.bk.core.search.mapper.SearchAnalyseDimensionMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Deprecated
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class SearchAnalyseDimensionRepository extends ServiceImpl<SearchAnalyseDimensionMapper, SearchAnalyseDimension> {

    public List<SearchAnalyseDimension> findDimensionByOrgId(String orgId) {
        LambdaQueryWrapper<SearchAnalyseDimension> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SearchAnalyseDimension::getOrgId, orgId);
        queryWrapper.orderByAsc(SearchAnalyseDimension::getOrderIndex);
        return list(queryWrapper);
    }

    public void deleteByOrgId(String orgId, String userId) {
        LambdaQueryWrapper<SearchAnalyseDimension> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SearchAnalyseDimension::getOrgId, orgId);
        queryWrapper.eq(SearchAnalyseDimension::getUserId, userId);
        remove(queryWrapper);
    }

    public long chkExistByOrgId(String orgId, String userId) {
        LambdaQueryWrapper<SearchAnalyseDimension> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SearchAnalyseDimension::getOrgId, orgId);
        queryWrapper.eq(SearchAnalyseDimension::getUserId, userId);
        return count(queryWrapper);
    }
}
