package com.yxt.talent.bk.core.pool.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(name = "用户储备历程")
public class PoolUserExperienceInfo4List {
    @Schema(description = "人才池Id")
    private String poolId;
    @Schema(description = "人才池名称")
    private String poolName;
    @Schema(description = "入池时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date joinTime;
    @Schema(description = "出池时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date outTime;
    @Schema(description = "准备度Id")
    private String readinessId;
    @Schema(description = "在池天数")
    private Integer dayCount=0;
    @Schema(description = "准备度名称")
    private String readinessName;
    @Schema(description = "出池结果【0:在池 1:合格出池且任用;2:合格出池;3:不合格出池;4:未完成中途退出;5:离职】")
    private Integer eventPoolOut;
    @Schema(description = "出池去向")
    private String remark;
}
