package com.yxt.talent.bk.svc.search;

import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.core.search.bean.UserSearchAnalyseDimensionBean;
import com.yxt.talent.bk.core.search.entity.SearchAnalyseDimension;
import com.yxt.talent.bk.core.search.mapper.SearchAnalyseDimensionMapper;
import com.yxt.talent.bk.core.search.repo.SearchAnalyseDimensionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class SearchAnalyseDimensionService {

    private final SearchAnalyseDimensionRepository searchAnalyseDimensionRepository;
    private final SearchAnalyseDimensionMapper searchAnalyseDimensionMapper;
    private final ILock lockService;

    public List<UserSearchAnalyseDimensionBean> initAnalyseDimensionLock(String orgId, String userId) {
        List<UserSearchAnalyseDimensionBean> resList;
        String lockKey = String.format(TalentBkRedisKeys.TALENTBK_INIT_DIMENSION_SEARCH_LOCK_KEY, orgId, userId);
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                resList = initAnalyseDimension(orgId, userId);
            } catch (Exception e) {
                log.error("initAnalyseDimensionLock error orgId={}， errormsg={}", orgId , e.getMessage());
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST) ;
        }
        return resList;
    }

    private List<UserSearchAnalyseDimensionBean> initAnalyseDimension(String orgId, String userId) {
        // 查询维度标签表中是否有数据
        long num = searchAnalyseDimensionRepository.chkExistByOrgId(orgId, userId);
        if (num > 0) {
            return new ArrayList<>();
        }
        List<SearchAnalyseDimension> list = new ArrayList<>();
        insertAnalyseDimension(orgId, "departmentId", "部门", 0,list, userId);
        insertAnalyseDimension(orgId, "positionId", "岗位", 1,list, userId);
        insertAnalyseDimension(orgId, "gradeName", "职级", 2,list, userId);
        insertAnalyseDimension(orgId, "sex", "性别", 3,list, userId);
        insertAnalyseDimension(orgId, "ageGroup", "年龄段", 4,list, userId);

        searchAnalyseDimensionRepository.saveBatch(list);
        List<UserSearchAnalyseDimensionBean> resList = new ArrayList<>();
        list.forEach(e ->{
            UserSearchAnalyseDimensionBean user = new UserSearchAnalyseDimensionBean();
            user.setItemKey(e.getItemKey());
            user.setItemName(e.getItemValue());
            user.setOrderIndex(e.getOrderIndex());
            resList.add(user);
        });
        return resList;
    }

    @DbHintMaster
    public List<UserSearchAnalyseDimensionBean> findByOrgId(String orgId, String userId){

        List<UserSearchAnalyseDimensionBean> list = searchAnalyseDimensionMapper.findByOrgId(orgId, userId);
        if(CollectionUtils.isEmpty(list)){
            list = initAnalyseDimensionLock(orgId, userId);
        }

        return list;
    }

    private void insertAnalyseDimension(String orgId, String itemKey, String itemValue, int orderIndex, List<SearchAnalyseDimension> list,
            String userId) {
        SearchAnalyseDimension search = new SearchAnalyseDimension();
        search.setId(ApiUtil.getUuid());
        search.setOrgId(orgId);
        search.setItemKey(itemKey);
        search.setItemValue(itemValue);
        search.setOrderIndex(orderIndex);
        search.setUserId(userId);
        EntityUtil.setCreateInfo(userId, search);
        list.add(search);
    }


}
