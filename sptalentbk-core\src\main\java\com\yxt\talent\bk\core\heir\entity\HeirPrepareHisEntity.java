package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.DateUtil;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.CreatorEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
*  bk_heir_prepare_his
* <AUTHOR>
* @since 2022/9/8 9:41
* @version 1.0
*/
@Data
@NoArgsConstructor
@TableName("bk_heir_prepare_his")
@EqualsAndHashCode(callSuper = true)
public class HeirPrepareHisEntity extends CreatorEntity {

    @Schema(description = "雪花id")
    private Long id;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "bk_heir_pos pkId")
    private String posId;

    @Schema(description = "继任用户id")
    private String userId;

    @Schema(description = "准备度调整用户id")
    private String optUserId;

    @Schema(description = "处理状态：0:暂不处理,1:已处理")
    private Integer modifyStatus;

    @Schema(description = "调整前准备度规则等级id")
    private Long userLevelId;

    @Schema(description = "调整的准备度规则等级id")
    private Long changeLevelId;

    @Schema(description = "规则数据包id")
    private Long ruleDataId;

    @Schema(description = "匹配结果数据包id")
    private Long matchDataId;

    public void init(String orgId, String userId) {
        if (StringUtils.isEmpty(userId)) {
            userId = TalentBkConstants.CODE_OPERATOR_ID;
        }
        setUpdateTime(DateUtil.currentTime());
        setUpdateUserId(userId);
        if (id == null) {
            this.id = TalentbkUtil.snowFLowId();
            this.setCreateTime(getUpdateTime());
            setCreateUserId(userId);
            setOrgId(orgId);
        } else if (StringUtils.isNotBlank(orgId)) {
            setOrgId(orgId);
        }
    }
}
