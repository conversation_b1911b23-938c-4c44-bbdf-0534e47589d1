package com.yxt.talent.bk.svc.pool.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Data
@Schema(name = "人才池添更新/批量更新准备度/出池")
public class PoolUser4Update{
    @Schema(description = "人才池添加人员")
    @NotNull(message = "poolUser can not be null!")
    private PoolUser4Add poolUser;

    @Schema(description = "准备度id")
    private String readinessId;

    @Schema(description = "出池事件枚举(0：在池;1:合格出池且任用;2:合格出池;3:不合格:未完成中途退出;5:离职)")
    private Integer eventPoolOut;

    @Schema(description = "备注/去向")
    @Size(message = " the list length between {min} and {max} ", min = 0, max = 200)
    private String remark;

    @Schema(description = "0:准备度；1：出池")
    private Integer updateType;
}
