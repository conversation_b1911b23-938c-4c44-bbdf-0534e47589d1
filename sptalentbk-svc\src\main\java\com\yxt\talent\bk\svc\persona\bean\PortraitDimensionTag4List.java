package com.yxt.talent.bk.svc.persona.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "人才画像-维度标签信息")
public class PortraitDimensionTag4List {
    @Schema(description = "标签Id")
    private String tagId;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "标签描述/定义")
    private String description;

    @Schema(description = "标签key")
    private String tagKey;

    @Schema(description = "标签对应的值集合")
    private List<String> tagValue;

    @Schema(description = "仅通用，专业维度的优劣势 -1:暂无，0:优势 1:劣势")
    private Integer valueType;
}
