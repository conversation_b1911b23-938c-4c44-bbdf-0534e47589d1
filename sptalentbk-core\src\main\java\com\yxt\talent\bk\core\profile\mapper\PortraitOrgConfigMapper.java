package com.yxt.talent.bk.core.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.profile.entity.PortraitOrgConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 个人画像配置表(BkPortraitOrgConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-12 13:54:17
 */
public interface PortraitOrgConfigMapper extends BaseMapper<PortraitOrgConfig> {

    /**
     * 通过ID查询单条数据
     *
     * @param orgId 主键
     * @return 实例对象
     */
    PortraitOrgConfig queryByOrgId(String orgId);

    List<PortraitOrgConfig> listByOrgIds(List<String> orgIds);

    /**
     * 新增数据
     *
     * @param portraitOrgConfig 实例对象
     * @return 影响行数
     */
    int insert(PortraitOrgConfig portraitOrgConfig);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BkPortraitOrgConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PortraitOrgConfig> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BkPortraitOrgConfig> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PortraitOrgConfig> entities);

    /**
     * 修改数据
     *
     * @param portraitOrgConfig 实例对象
     * @return 影响行数
     */
    int update(PortraitOrgConfig portraitOrgConfig);

}

