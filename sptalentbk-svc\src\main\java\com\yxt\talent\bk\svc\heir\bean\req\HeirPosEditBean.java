package com.yxt.talent.bk.svc.heir.bean.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * HeirPosEditBean
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 2:20 pm
 */
@Data
public class HeirPosEditBean {
    @NotBlank
    private String id;

    @Schema(description = "空表示没有上级")
    private String parentPosId;

    @Schema(description = "目标继任数量,0:未设置")
    private Integer heirTargetQty;

    @Schema(description = "0:自动规则，1:手动规则")
    private Integer riskRuleType;

    @Schema(description = "风险规则id")
    private Long riskLevelId;

    @Schema(description = "风险规则id")
    private List<String> benchmarkUserIds;
}
