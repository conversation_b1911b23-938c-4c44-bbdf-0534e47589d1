package com.yxt.talent.bk.svc.heir.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "准备度规则等级配置状态")
public class PrepareLevelBean {

    @Schema(description = "准备度规则等级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long prepareLevelId;

    @Schema(description = "准备度规则等级名称")
    @JsonSerialize(using = ToStringSerializer.class)
    private String prepareLevelName;

    @Schema(description = "准备度规则等级配置状态，0：未设置，1：已设置")
    private int prepareLevelStatus;
}
