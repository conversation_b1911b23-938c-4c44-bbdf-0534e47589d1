package com.yxt.talent.bk.svc.pool;

import com.google.common.collect.Lists;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.o2ofacade.bean.request.groupmember.Progress4Request;
import com.yxt.o2ofacade.bean.response.talent.GroupMember4Progress;
import com.yxt.o2ofacade.bean.response.talent.SpProjectInfoResp;
import com.yxt.o2ofacade.service.GroupMemberFacade;
import com.yxt.o2ofacade.service.ProjectFacade;
import com.yxt.spevalfacade.bean.evaluation.Evaluation4List;
import com.yxt.spevalfacade.bean.evaluation.EvaluationFacade;
import com.yxt.spevalfacade.service.SpEvalApiFacade;
import com.yxt.sptalentapifacade.bean.eval.TrainingSimpleBaseInfoBean;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.pool.entity.ProjectTargetMap;
import com.yxt.talent.bk.core.pool.mapper.ProjectTargetMapMapper;
import com.yxt.talent.bk.svc.pool.bean.PoolProjectBindBean;
import com.yxt.talent.bk.svc.pool.bean.TrainingBaseInfoBean;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PoolProjectService {

    private final SpEvalApiFacade spEvalApiFacade;
    private final ProjectTargetMapMapper projectTargetMapMapper;
    private final ProjectFacade projectFacade;
    private final GroupMemberFacade groupMemberFacade;


    /**
     * 人才池创建测评
     *
     * @param orgId
     * @param userId
     * @param poolId
     * @param evalIds
     * @return
     */
    public void save4PoolEval(String orgId, String userId, String poolId, List<String> evalIds, int targetType) {
        List<ProjectTargetMap> projectList = new ArrayList<>();
        if (CollectionUtils.isEmpty(evalIds)) {
            return;
        }
        Date now = DateUtil.currentTime();
        // 直接绑定
        for (String targetId : evalIds) {
            ProjectTargetMap project = new ProjectTargetMap();
            project.setId(ApiUtil.getUuid());
            project.setOrgId(orgId);
            project.setProjectId(poolId);
            project.setProjectType(1);
            project.setTargetId(targetId);
            if (targetType == 1) {
                project.setTargetType(1);
            } else {
                project.setTargetType(2);
            }
            project.setCreateTime(now);
            project.setUpdateTime(now);
            project.setUpdateUserId(userId);
            project.setCreateUserId(userId);
            projectList.add(project);
        }
        if (CollectionUtils.isNotEmpty(projectList)) {
            projectTargetMapMapper.batchInsert(projectList);
        }
    }

    /**
     * 查询人才池测评list
     *
     * @param orgId
     * @param poolId
     * @param keyword
     * @return
     */
    public CommonList<Evaluation4List> findByPoolId(String orgId, String poolId, String keyword) {
        // 测评关联项目查询
        int targetType = 1;
        // 查询人才池对应的测评ids
        EvaluationFacade facadeParam = new EvaluationFacade();
        CommonList<Evaluation4List> commonList;
        // 获取测评id
        List<String> projectIds =
                projectTargetMapMapper.selectTargetIdByProjectIdAndTargetType(orgId, poolId, targetType);
        if (CollectionUtils.isEmpty(projectIds)) {
            return null;
        }
        // 组装facade接口参数
        facadeParam.setOrgId(orgId);
        facadeParam.setEvalIds(projectIds);
        // 根据测评ids查询测评详情
        log.debug("根据测评ids查询测评详情 参数如下->orgId:{}projectIds: {}", orgId, projectIds);
        commonList = spEvalApiFacade.searchEvalList(facadeParam);
        if (StringUtils.isNotBlank(keyword) && Objects.nonNull(commonList)) {
            commonList.setDatas(commonList.getDatas()
                    .stream()
                    .filter(evaluation4List -> evaluation4List.getName().contains(keyword))
                    .collect(Collectors.toList()));
        }
        return commonList;
    }

    /**
     * 删除人才池测训项目
     *
     * @param bean
     */
    public void deletePoolProject(PoolProjectBindBean bean, String orgId, String operator) {
        if (CollectionUtils.isEmpty(bean.getProjectIds())) {
            throw new ApiException("apis.talentbk.param.miss.error", "projectIds");
        }
        List<ProjectTargetMap> projectTargetMaps =
                projectTargetMapMapper.selectByOrgIdAndProjectIdAndTargetIdIn(orgId, bean.getPoolId(),
                        bean.getProjectIds());
        if (CollectionUtils.isEmpty(projectTargetMaps)) {
            return;
        }
        projectTargetMapMapper.removeByProjectIdAndTargetIdIn(orgId, bean.getPoolId(), bean.getProjectIds());

        // 同步删除测评
        for (ProjectTargetMap projectTargetMap : projectTargetMaps) {
            try {
                // 1:测评
                if (projectTargetMap.getTargetType() == 1) {
                    spEvalApiFacade.deleteEvaluation(projectTargetMap.getTargetId(), orgId, operator);
                }
            } catch (Exception e) {
                log.error("LOG50030:", e);
            }
        }
    }

    public List<TrainingBaseInfoBean> trainingDevelopmentBaseInfo(String orgId, String userId, String poolId) {
        // target_type => 1:测评项目 2:测训项目(奇点已废弃) 3:培训项目
        List<String> projectIds =
                projectTargetMapMapper.selectTargetIdByProjectIdAndTargetType(orgId, poolId, 3);
        List<TrainingBaseInfoBean> result = Lists.newArrayListWithCapacity(0);
        List<Long> o2oProjectIds = projectIds.stream().map(item -> CommonUtils.tryParseLong(item, null))
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(o2oProjectIds)) {
            return result;
        }
        log.debug("LOG21020:根据测训ids查询测训项目 参数如下->orgId:{}; userId: {}; projectIds: {}", orgId, userId,
                projectIds);
        List<GroupMember4Progress> memberProgressList = BatchOperationUtil.batchQuery(o2oProjectIds, subList -> {
            Progress4Request req = new Progress4Request();
            req.setOrgId(orgId);
            req.setUserId(userId);
            req.setProjectIds(subList);
            return groupMemberFacade.getUserProgress(req);
        });
        if (CollectionUtils.isNotEmpty(memberProgressList)) {
            Map<Long, SpProjectInfoResp> projectMap = StreamUtil.list2map(projectFacade.listSpProjects(orgId, memberProgressList.stream()
                            .map(GroupMember4Progress::getProjectId)
                            .collect(Collectors.toList())),
                    SpProjectInfoResp::getId);
            result = memberProgressList.stream().map(memberProgress -> {
                SpProjectInfoResp spProjectInfo = projectMap.get(memberProgress.getProjectId());
                if (spProjectInfo == null) {
                    return null;
                }
                TrainingBaseInfoBean baseInfoBean = new TrainingBaseInfoBean();
                baseInfoBean.setBaseInfoBean(new TrainingSimpleBaseInfoBean());
                baseInfoBean.setStudyProgressConfig(1);
                baseInfoBean.getBaseInfoBean().setTrainingName(spProjectInfo.getName());
                baseInfoBean.getBaseInfoBean().setScore(memberProgress.getProjectScore());
                baseInfoBean.getBaseInfoBean().setStudyPlanRate(Optional.ofNullable(memberProgress.getAllProcessRate())
                        .map(rateVal -> TalentBkConstants.BIG_DECIMAL_100.multiply(rateVal)).orElse(null));
                baseInfoBean.getBaseInfoBean().setRequiredRate(Optional.ofNullable(memberProgress.getRequiredProcessRate())
                        .map(rateVal -> TalentBkConstants.BIG_DECIMAL_100.multiply(rateVal)).orElse(null));
                return baseInfoBean;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return result;
    }

    public List<String> findEvalIdsByPoolId(String orgId, String poolId) {
        // target_type => 1:测评项目 2:测训项目(奇点已废弃) 3:培训项目
        return projectTargetMapMapper.selectTargetIdByProjectIdAndTargetType(orgId, poolId, 1);
    }

}
