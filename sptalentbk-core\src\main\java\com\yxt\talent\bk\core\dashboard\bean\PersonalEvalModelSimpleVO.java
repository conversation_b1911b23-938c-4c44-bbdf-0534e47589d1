package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class PersonalEvalModelSimpleVO {
    @Schema(description = "模型名称")
    private String modelName;
    @Schema(description = "模型ID")
    private String modelId;
    @Schema(description = "模型编号")
    private String modelNum;
    @Schema(description = "模型类型 1-能力模型 2-任务模型")
    private Integer modelType;
    private String modelVersion;

    @Schema(hidden = true)
    private String sheetName;
}
