package com.yxt.talent.bk.svc.pool.bean.readiness;

import com.yxt.talent.bk.common.constants.PoolConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Schema(name = "准备度")
@Data
public class PoolReadiness4SaveBean {
    @Schema(description = "准备度主键【编辑必填】")
    private String id;
    @Schema(description = "准备度名称")
    @NotBlank(message = PoolConstants.VERIFY_READINESS_SAVE_NAME_SIZE)
    @Size(max = 20, min = 2, message = PoolConstants.VERIFY_READINESS_SAVE_NAME_SIZE)
    private String readinessName;
    @Schema(description = "排序【ASC 排序，值越小准备度越高，目前前端无需传】")
    private String orderIndex;
}
