package com.yxt.talent.bk.svc.tag.bean;

import com.yxt.talent.bk.common.constants.TalentBkConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.elasticsearch.annotations.Document;

import java.util.HashMap;

@Data
@EqualsAndHashCode(callSuper = false)
@Document(indexName = "#{@bkConfigService.indexName}", type = TalentBkConstants.ES_TYPE)
public class UserAllTagInfoBean extends HashMap {

}

