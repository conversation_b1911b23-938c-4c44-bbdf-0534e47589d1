package com.yxt.talent.bk.common.constants;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserSearchConstants {

    public static final String DEPT_INCLUDE_AGGS_SCRIPT_TEMPLATE = " if(doc['%s'].value.indexOf('%s')==0){return '%s';} ";
    public static final String DEPT_AGGS_SCRIPT_TEMPLATE = " if(doc['%s'].value == '%s'){return '%s';} ";
    /**
     * 永远为true的表达式
     */
    public static final String DEPT_ROOT_AGGS_SCRIPT_TEMPLATE = " if('true'.length()>0){return '%s';} ";
    public static final String POSITION_AGGS_SCRIPT_TEMPLATE = " doc['positionName'].value +'_#_'+ doc['positionId'].value ";
    public static final String POSITION_AGGS_SPLIT_STR = "_#_";
    public static final String DEPT_PATH_SPLIT_STR = "->";

    public static final String ES_ORG_ID = "orgId";
    public static final String ES_USER_NAME = "username";
    public static final String ES_CN_NAME = "cnname";
    public static final String ES_LEADERSHIP_STYLE = "leadershipStyle";
    public static final String ES_USER_ID = "userId";
    public static final String ES_DIY_LABELS = "diylabels";
    public static final String ES_ENTRY_DATE = "entryDate";
    public static final String ES_WORK_DATE = "workDate";
    public static final String ES_ADV_PRO_ABILITY = "advantageProAbliity";
    public static final String ES_ADV_COM_ABILITY = "advantageCommAbliity";
    public static final String ES_INF_COM_ABILITY = "inferiorityCommAbliity";
    public static final String ES_INF_PRO_ABILITY = "inferiorityProAbliity";
    public static final String ES_PERFORMANCE_LEVEL ="performanceLevel";
    public static final String ES_ABILITY_LEVEL ="abilityLevel";
    public static final String ES_POTENTIAL_LEVEL ="potentialLevel";
    public static final String ES_UNSPACED_LRN ="unspacedLrn";
    public static final String ES_LRNDURATION_RATIO ="lrnDurationRatio";
    public static final String ES_SCORE_RATIO ="scoreRatio";
    public static final String ES_TALENT_RV_NAME ="人才盘点";
    public static final String ES_USER_STATUS = "userStatus";

    public static final String ES_POSITION_ID = "positionId";
    public static final String ES_DEPT_ID = "departmentId";
    public static final String ES_GRADE_NAME = "gradeName";
    private static final String F_POSITION_NAME = "positionName";
    private static final String F_USER_NO = "userNo";
    private static final String F_BIRTHDAY = "birthday";
    private static final String F_REVIEW_RESULT = "reviewResult";
    private static final String F_CERTS = "certs";
    private static final String F_DEPARTMENT_NAME = "departmentName";


    /**
     * 搜索逻辑
     */
    @Getter
    public enum SearchLogic {
        /**
         * 0：且，1：或
         */
        AND("且", 0),
        OR("或", 1);
        private String name;
        private int value;

        SearchLogic(String name, int value) {
            this.name = name;
            this.value = value;
        }
    }

    /**
     * 参与关键字搜索的标签
     */
    @Getter
    public enum KeyWordTagEnum {
        /**
         * 标签
         */
        CNNAME(ES_CN_NAME, ES_CN_NAME),
        POSITION_NAME(F_POSITION_NAME, F_POSITION_NAME),
        GRADE_NAME(ES_GRADE_NAME, ES_GRADE_NAME),
        USER_NO(F_USER_NO, F_USER_NO);
        private String name;
        private String value;

        KeyWordTagEnum(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }

    /**
     * 人才池关键字搜索枚举
     */
    @Getter
    public enum KeyWordPoolEnum {
        /**
         * 搜索关键字段
         */
        CNNAME(ES_CN_NAME, ES_CN_NAME),
        POSITION_NAME(F_POSITION_NAME, F_POSITION_NAME),
        USER_NO(F_USER_NO, F_USER_NO);
        private String name;
        private String value;

        KeyWordPoolEnum(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }

    @Getter
    public enum XxGroupTagEnum {
        /**
         * 标签key
         */
        WORK_DATE_GROUP("workDateGroup",ES_WORK_DATE),
        AGE_GROUP("ageGroup",F_BIRTHDAY);
        private String key;
        private String value;

        XxGroupTagEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }
    }

    public static Map<String,String> getXxGroupTagEnumMap() {
        Map<String,String> tempMap = new HashMap<>();
        for(XxGroupTagEnum e : XxGroupTagEnum.values()){
            tempMap.put(e.key,e.value);
        }
        return tempMap;
    }

    public static Map<String,String> getXxGroupTagEnumMapVK() {
        Map<String,String> tempMap = new HashMap<>();
        for(XxGroupTagEnum e : XxGroupTagEnum.values()){
            tempMap.put(e.value,e.key);
        }
        return tempMap;
    }

    @Getter
    public enum AgeRangeEnum {
        /**
         * 年龄段
         */
        AR_18_22("18-22","18-22岁"),
        AR_23_30("23-30","23-30岁"),
        AR_31_40("31-40","31-40岁"),
        AR_41_50("41-50","41-50岁"),
        AR_51("51","51岁及以上"),
        AR_0_17("0-17","其他");
        private String key;
        private String value;

        AgeRangeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }
    }

    @Getter
    public enum SexEnum {
        /**
         * 性别
         */
        MALE("1","男"),
        FEMALE("2","女"),
        OTHER("0","未知");
        private String key;
        private String value;

        SexEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }
    }

    public static Map<String,String> getSexEnumMap() {
        Map<String,String> tempMap = new HashMap<>();
        for(SexEnum e : SexEnum.values()){
            tempMap.put(e.key,e.value);
        }
        return tempMap;
    }
    public static Map<String,String> getSexVKEnumMap() {
        Map<String,String> tempMap = new HashMap<>();
        for(SexEnum e : SexEnum.values()){
            tempMap.put(e.value,e.key);
        }
        return tempMap;
    }

    public static Map<String,String> getAgeRangeKVEnumMap() {
        Map<String,String> tempMap = new HashMap<>();
        for(AgeRangeEnum e : AgeRangeEnum.values()){
            tempMap.put(e.key,e.value);
        }
        return tempMap;
    }

    public static Map<String,String> getAgeRangeVKEnumMap() {
        Map<String,String> tempMap = new HashMap<>();
        for(AgeRangeEnum e : AgeRangeEnum.values()){
            tempMap.put(e.value,e.key);
        }
        return tempMap;
    }

    /**
     * ES 标签层级名称
     */
    @Getter
    public enum EsLabelLevelName {
        /**
         * 标签层级
         */
        DIRECTLY("directly", ""),
        DIYLABELS(ES_DIY_LABELS, ES_DIY_LABELS);
        private String name;
        private String value;

        EsLabelLevelName(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }
    @Getter
    public enum EsAnalyseSpecialLabel{
        /**
         * 分析特殊标签
         */
        DEPARTMENT_ID(ES_DEPT_ID, ES_DEPT_ID),
        GRADE_NAME(ES_GRADE_NAME, ES_GRADE_NAME),
        POSITION_ID(ES_POSITION_ID, ES_POSITION_ID);
        private String name;
        private String value;

        EsAnalyseSpecialLabel(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }

    /**
     * ES 证书标签enum <br>
     */
    @Getter
    public enum EsCertsLabel {
        /**
         * 证书标签
         */
        USER_CERT_ID("userCertId", "userCertId"),
        CERT_TEMP_ID("certTempId", "certTempId");
        private String name;
        private String value;

        EsCertsLabel(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }

    /**
     * ES 人才盘点标签enum <br>
     */
    @Getter
    public enum EsRvLabel {
        /**
         * 盘点标签
         */
        PERFORMANCE_LEVEL(ES_PERFORMANCE_LEVEL, ES_PERFORMANCE_LEVEL),
        ABILITY_LEVEL(ES_ABILITY_LEVEL, ES_ABILITY_LEVEL),
        REVIEW_RESULT(F_REVIEW_RESULT, F_REVIEW_RESULT),
        POTENTIAL_LEVEL(ES_POTENTIAL_LEVEL, ES_POTENTIAL_LEVEL);
        private String name;
        private String value;

        EsRvLabel(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }

    public static Map<String,String> getEsRvLabelEnumMap() {
        Map<String,String> tempMap = new HashMap<>();
        for(EsRvLabel e : EsRvLabel.values()){
            tempMap.put(e.name,e.value);
        }
        return tempMap;
    }

    @Getter
    public enum EsSpecialLabel {
        /**
         * 透视维度key
         */
        CERTS(F_CERTS, F_CERTS),
        WORK_DATE(ES_WORK_DATE, ES_WORK_DATE),
        BIRTHDAY(F_BIRTHDAY, F_BIRTHDAY),
        ENTRY_DATE(ES_ENTRY_DATE, ES_ENTRY_DATE),
        DEPARTMENT_NAME(F_DEPARTMENT_NAME, F_DEPARTMENT_NAME),
        DEPARTMENT_ID(ES_DEPT_ID, ES_DEPT_ID),
        POSITION_NAME(F_POSITION_NAME, F_POSITION_NAME),
        POSITION_ID(ES_POSITION_ID, ES_POSITION_ID),
        GRADE_NAME(ES_GRADE_NAME, ES_GRADE_NAME),
        ORDINARY_TAG("普通标签","ordinaryTag"),
        ORG_ID(ES_ORG_ID,ES_ORG_ID),
        USER_ID(ES_USER_ID,ES_USER_ID),
        USER_DELETED("删除标记","userDeleted"),
        USER_VISIABLE("是否显示的标识","visibleState"),
        SEX("sex","sex");
        private final String name;
        private final String value;
        EsSpecialLabel(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }


    /**
     * es中一级属性 <br> diylabels标签是除directly和userLabels之外的其他标签
     */
    public static final List<String> DIRECTLY = Arrays.asList(
            ES_USER_ID,
            ES_USER_NAME,
            ES_CN_NAME,
            F_USER_NO,
            ES_ORG_ID,
            ES_DEPT_ID,
            F_DEPARTMENT_NAME,
            ES_POSITION_ID,
            F_POSITION_NAME,
            "gradeId",
            ES_GRADE_NAME,
            "exams",
            F_CERTS,
            "o2os",
            "sex",
            F_BIRTHDAY,
            "ageGroup",
            "workDateGroup",
            "nativePlace",
            "politicalAffiliation",
            ES_ENTRY_DATE,
            ES_WORK_DATE,
            ES_ADV_COM_ABILITY,
            ES_INF_COM_ABILITY,
            "otherCommAbliity",
            ES_ADV_PRO_ABILITY,
            ES_INF_PRO_ABILITY,
            "otherProAbliity",
            F_REVIEW_RESULT,
            ES_PERFORMANCE_LEVEL,
            ES_ABILITY_LEVEL,
            ES_POTENTIAL_LEVEL,
            "highestEducation",
            "highestSchool",
            "major",
            "professionDriver",
            "tenureRisk",
            "performanceTrend",
            ES_UNSPACED_LRN,
            ES_LRNDURATION_RATIO,
            ES_SCORE_RATIO,
            "lrnPreferences",
            "goodAtAsking",
            "enjoySharing",
            "characterTraits",
            ES_LEADERSHIP_STYLE);

    public static final String [] ES_SOURCE_INCLUDES = {
            ES_USER_ID,
            ES_USER_NAME,
            ES_CN_NAME,
            F_USER_NO,
            ES_DEPT_ID,
            F_DEPARTMENT_NAME,
            ES_POSITION_ID,
            F_POSITION_NAME,
            "gradeId",
            ES_GRADE_NAME,
            ES_ENTRY_DATE,
            ES_PERFORMANCE_LEVEL,
            ES_ABILITY_LEVEL,
            ES_POTENTIAL_LEVEL,
            ES_USER_STATUS
    };


}
