package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 维度透视
 */
@Data
@Schema(name = "搜索维度列表")
public class DimensionBean {

    @Schema(description = "id")
    private String tagId;

    @Schema(description = "标签名称")
    private String tagName;


    @Schema(description = "标签排序")
    private Integer orderIndex;
}
