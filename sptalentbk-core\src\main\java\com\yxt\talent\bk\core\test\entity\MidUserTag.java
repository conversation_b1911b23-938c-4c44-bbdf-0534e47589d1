package com.yxt.talent.bk.core.test.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

/**
    * 用户标签临时表
    */
@TableName(value = "bk_mid_user_tag")
public class MidUserTag {
    /**
     * 用户标签临时表主键（用于es清洗数据）
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 绩效趋势：持续高绩效
     */
    @TableField(value = "performance_trend")
    private String performanceTrend;

    /**
     * 账号
     */
    @TableField(value = "username")
    private String username;

    /**
     * 姓名
     */
    @TableField(value = "fullname")
    private String fullname;

    /**
     * 性别：0-表示不确定 1-表示男 2-表示女
     */
    @TableField(value = "sex")
    private Byte sex;

    /**
     * 生日
     */
    @TableField(value = "birthday")
    private Date birthday;

    /**
     * 入职时间
     */
    @TableField(value = "hire_date")
    private Date hireDate;

    /**
     * 用户状态：用于标识当前用户的状态(0-禁用,1-启用)
     */
    @TableField(value = "user_status")
    private Byte userStatus;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Byte deleted;

    /**
     * 主部门ID
     */
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 主部门全路径名称
     */
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 主岗位ID
     */
    @TableField(value = "position_id")
    private String positionId;

    /**
     * 主岗位名称
     */
    @TableField(value = "position_name")
    private String positionName;

    /**
     * 职级ID
     */
    @TableField(value = "grade_id")
    private String gradeId;

    /**
     * 职级名称
     */
    @TableField(value = "grade_name")
    private String gradeName;

    /**
     * 该用户完成的考试id集合
     */
    @TableField(value = "exams")
    private String exams;

    /**
     * 该用户获得的证书模板id集合
     */
    @TableField(value = "certs")
    private String certs;

    /**
     * 该用户完成的项目id集合
     */
    @TableField(value = "o2os")
    private String o2os;

    /**
     * 优势通用能力
     */
    @TableField(value = "advantage_comm_abliity")
    private String advantageCommAbliity;

    /**
     * 劣势通用能力
     */
    @TableField(value = "inferiority_comm_abliity")
    private String inferiorityCommAbliity;

    /**
     * 其他通用能力
     */
    @TableField(value = "other_comm_abliity")
    private String otherCommAbliity;

    /**
     * 优势专业能力
     */
    @TableField(value = "advantage_pro_abliity")
    private String advantageProAbliity;

    /**
     * 劣势专业能力
     */
    @TableField(value = "inferiority_pro_abliity")
    private String inferiorityProAbliity;

    /**
     * 其他专业能力
     */
    @TableField(value = "other_pro_abliity")
    private String otherProAbliity;

    /**
     * 职业驱动力
     */
    @TableField(value = "profession_driver")
    private String professionDriver;

    /**
     * 性格特点
     */
    @TableField(value = "character_traits")
    private String characterTraits;

    /**
     * 任职风险
     */
    @TableField(value = "tenure_risk")
    private String tenureRisk;

    /**
     * 领导风格
     */
    @TableField(value = "leadership_style")
    private String leadershipStyle;

    /**
     * 工号
     */
    @TableField(value = "user_no")
    private String userNo;

    /**
     * 获取用户标签临时表主键（用于es清洗数据）
     *
     * @return id - 用户标签临时表主键（用于es清洗数据）
     */
    public String getId() {
        return id;
    }

    /**
     * 设置用户标签临时表主键（用于es清洗数据）
     *
     * @param id 用户标签临时表主键（用于es清洗数据）
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取机构id
     *
     * @return org_id - 机构id
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * 设置机构id
     *
     * @param orgId 机构id
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取用户id
     *
     * @return user_id - 用户id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     *
     * @param userId 用户id
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 获取绩效趋势：持续高绩效
     *
     * @return performance_trend - 绩效趋势：持续高绩效
     */
    public String getPerformanceTrend() {
        return performanceTrend;
    }

    /**
     * 设置绩效趋势：持续高绩效
     *
     * @param performanceTrend 绩效趋势：持续高绩效
     */
    public void setPerformanceTrend(String performanceTrend) {
        this.performanceTrend = performanceTrend;
    }

    /**
     * 获取账号
     *
     * @return username - 账号
     */
    public String getUsername() {
        return username;
    }

    /**
     * 设置账号
     *
     * @param username 账号
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 获取姓名
     *
     * @return fullname - 姓名
     */
    public String getFullname() {
        return fullname;
    }

    /**
     * 设置姓名
     *
     * @param fullname 姓名
     */
    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    /**
     * 获取性别：0-表示不确定 1-表示男 2-表示女
     *
     * @return sex - 性别：0-表示不确定 1-表示男 2-表示女
     */
    public Byte getSex() {
        return sex;
    }

    /**
     * 设置性别：0-表示不确定 1-表示男 2-表示女
     *
     * @param sex 性别：0-表示不确定 1-表示男 2-表示女
     */
    public void setSex(Byte sex) {
        this.sex = sex;
    }

    /**
     * 获取生日
     *
     * @return birthday - 生日
     */
    public Date getBirthday() {
        return birthday;
    }

    /**
     * 设置生日
     *
     * @param birthday 生日
     */
    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    /**
     * 获取入职时间
     *
     * @return hire_date - 入职时间
     */
    public Date getHireDate() {
        return hireDate;
    }

    /**
     * 设置入职时间
     *
     * @param hireDate 入职时间
     */
    public void setHireDate(Date hireDate) {
        this.hireDate = hireDate;
    }

    /**
     * 获取用户状态：用于标识当前用户的状态(0-禁用,1-启用)
     *
     * @return user_status - 用户状态：用于标识当前用户的状态(0-禁用,1-启用)
     */
    public Byte getUserStatus() {
        return userStatus;
    }

    /**
     * 设置用户状态：用于标识当前用户的状态(0-禁用,1-启用)
     *
     * @param userStatus 用户状态：用于标识当前用户的状态(0-禁用,1-启用)
     */
    public void setUserStatus(Byte userStatus) {
        this.userStatus = userStatus;
    }

    /**
     * 获取是否删除
     *
     * @return deleted - 是否删除
     */
    public Byte getDeleted() {
        return deleted;
    }

    /**
     * 设置是否删除
     *
     * @param deleted 是否删除
     */
    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * 获取主部门ID
     *
     * @return dept_id - 主部门ID
     */
    public String getDeptId() {
        return deptId;
    }

    /**
     * 设置主部门ID
     *
     * @param deptId 主部门ID
     */
    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    /**
     * 获取主部门全路径名称
     *
     * @return dept_name - 主部门全路径名称
     */
    public String getDeptName() {
        return deptName;
    }

    /**
     * 设置主部门全路径名称
     *
     * @param deptName 主部门全路径名称
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    /**
     * 获取主岗位ID
     *
     * @return position_id - 主岗位ID
     */
    public String getPositionId() {
        return positionId;
    }

    /**
     * 设置主岗位ID
     *
     * @param positionId 主岗位ID
     */
    public void setPositionId(String positionId) {
        this.positionId = positionId;
    }

    /**
     * 获取主岗位名称
     *
     * @return position_name - 主岗位名称
     */
    public String getPositionName() {
        return positionName;
    }

    /**
     * 设置主岗位名称
     *
     * @param positionName 主岗位名称
     */
    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    /**
     * 获取职级ID
     *
     * @return grade_id - 职级ID
     */
    public String getGradeId() {
        return gradeId;
    }

    /**
     * 设置职级ID
     *
     * @param gradeId 职级ID
     */
    public void setGradeId(String gradeId) {
        this.gradeId = gradeId;
    }

    /**
     * 获取职级名称
     *
     * @return grade_name - 职级名称
     */
    public String getGradeName() {
        return gradeName;
    }

    /**
     * 设置职级名称
     *
     * @param gradeName 职级名称
     */
    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    /**
     * 获取该用户完成的考试id集合
     *
     * @return exams - 该用户完成的考试id集合
     */
    public String getExams() {
        return exams;
    }

    /**
     * 设置该用户完成的考试id集合
     *
     * @param exams 该用户完成的考试id集合
     */
    public void setExams(String exams) {
        this.exams = exams;
    }

    /**
     * 获取该用户获得的证书模板id集合
     *
     * @return certs - 该用户获得的证书模板id集合
     */
    public String getCerts() {
        return certs;
    }

    /**
     * 设置该用户获得的证书模板id集合
     *
     * @param certs 该用户获得的证书模板id集合
     */
    public void setCerts(String certs) {
        this.certs = certs;
    }

    /**
     * 获取该用户完成的项目id集合
     *
     * @return o2os - 该用户完成的项目id集合
     */
    public String getO2os() {
        return o2os;
    }

    /**
     * 设置该用户完成的项目id集合
     *
     * @param o2os 该用户完成的项目id集合
     */
    public void setO2os(String o2os) {
        this.o2os = o2os;
    }

    /**
     * 获取优势通用能力
     *
     * @return advantage_comm_abliity - 优势通用能力
     */
    public String getAdvantageCommAbliity() {
        return advantageCommAbliity;
    }

    /**
     * 设置优势通用能力
     *
     * @param advantageCommAbliity 优势通用能力
     */
    public void setAdvantageCommAbliity(String advantageCommAbliity) {
        this.advantageCommAbliity = advantageCommAbliity;
    }

    /**
     * 获取劣势通用能力
     *
     * @return inferiority_comm_abliity - 劣势通用能力
     */
    public String getInferiorityCommAbliity() {
        return inferiorityCommAbliity;
    }

    /**
     * 设置劣势通用能力
     *
     * @param inferiorityCommAbliity 劣势通用能力
     */
    public void setInferiorityCommAbliity(String inferiorityCommAbliity) {
        this.inferiorityCommAbliity = inferiorityCommAbliity;
    }

    /**
     * 获取其他通用能力
     *
     * @return other_comm_abliity - 其他通用能力
     */
    public String getOtherCommAbliity() {
        return otherCommAbliity;
    }

    /**
     * 设置其他通用能力
     *
     * @param otherCommAbliity 其他通用能力
     */
    public void setOtherCommAbliity(String otherCommAbliity) {
        this.otherCommAbliity = otherCommAbliity;
    }

    /**
     * 获取优势专业能力
     *
     * @return advantage_pro_abliity - 优势专业能力
     */
    public String getAdvantageProAbliity() {
        return advantageProAbliity;
    }

    /**
     * 设置优势专业能力
     *
     * @param advantageProAbliity 优势专业能力
     */
    public void setAdvantageProAbliity(String advantageProAbliity) {
        this.advantageProAbliity = advantageProAbliity;
    }

    /**
     * 获取劣势专业能力
     *
     * @return inferiority_pro_abliity - 劣势专业能力
     */
    public String getInferiorityProAbliity() {
        return inferiorityProAbliity;
    }

    /**
     * 设置劣势专业能力
     *
     * @param inferiorityProAbliity 劣势专业能力
     */
    public void setInferiorityProAbliity(String inferiorityProAbliity) {
        this.inferiorityProAbliity = inferiorityProAbliity;
    }

    /**
     * 获取其他专业能力
     *
     * @return other_pro_abliity - 其他专业能力
     */
    public String getOtherProAbliity() {
        return otherProAbliity;
    }

    /**
     * 设置其他专业能力
     *
     * @param otherProAbliity 其他专业能力
     */
    public void setOtherProAbliity(String otherProAbliity) {
        this.otherProAbliity = otherProAbliity;
    }

    /**
     * 获取职业驱动力
     *
     * @return profession_driver - 职业驱动力
     */
    public String getProfessionDriver() {
        return professionDriver;
    }

    /**
     * 设置职业驱动力
     *
     * @param professionDriver 职业驱动力
     */
    public void setProfessionDriver(String professionDriver) {
        this.professionDriver = professionDriver;
    }

    /**
     * 获取性格特点
     *
     * @return character_traits - 性格特点
     */
    public String getCharacterTraits() {
        return characterTraits;
    }

    /**
     * 设置性格特点
     *
     * @param characterTraits 性格特点
     */
    public void setCharacterTraits(String characterTraits) {
        this.characterTraits = characterTraits;
    }

    /**
     * 获取任职风险
     *
     * @return tenure_risk - 任职风险
     */
    public String getTenureRisk() {
        return tenureRisk;
    }

    /**
     * 设置任职风险
     *
     * @param tenureRisk 任职风险
     */
    public void setTenureRisk(String tenureRisk) {
        this.tenureRisk = tenureRisk;
    }

    /**
     * 获取领导风格
     *
     * @return leadership_style - 领导风格
     */
    public String getLeadershipStyle() {
        return leadershipStyle;
    }

    /**
     * 设置领导风格
     *
     * @param leadershipStyle 领导风格
     */
    public void setLeadershipStyle(String leadershipStyle) {
        this.leadershipStyle = leadershipStyle;
    }

    /**
     * 获取工号
     *
     * @return user_no - 工号
     */
    public String getUserNo() {
        return userNo;
    }

    /**
     * 设置工号
     *
     * @param userNo 工号
     */
    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orgId=").append(orgId);
        sb.append(", userId=").append(userId);
        sb.append(", performanceTrend=").append(performanceTrend);
        sb.append(", username=").append(username);
        sb.append(", fullname=").append(fullname);
        sb.append(", sex=").append(sex);
        sb.append(", birthday=").append(birthday);
        sb.append(", hireDate=").append(hireDate);
        sb.append(", userStatus=").append(userStatus);
        sb.append(", deleted=").append(deleted);
        sb.append(", deptId=").append(deptId);
        sb.append(", deptName=").append(deptName);
        sb.append(", positionId=").append(positionId);
        sb.append(", positionName=").append(positionName);
        sb.append(", gradeId=").append(gradeId);
        sb.append(", gradeName=").append(gradeName);
        sb.append(", exams=").append(exams);
        sb.append(", certs=").append(certs);
        sb.append(", o2os=").append(o2os);
        sb.append(", advantageCommAbliity=").append(advantageCommAbliity);
        sb.append(", inferiorityCommAbliity=").append(inferiorityCommAbliity);
        sb.append(", otherCommAbliity=").append(otherCommAbliity);
        sb.append(", advantageProAbliity=").append(advantageProAbliity);
        sb.append(", inferiorityProAbliity=").append(inferiorityProAbliity);
        sb.append(", otherProAbliity=").append(otherProAbliity);
        sb.append(", professionDriver=").append(professionDriver);
        sb.append(", characterTraits=").append(characterTraits);
        sb.append(", tenureRisk=").append(tenureRisk);
        sb.append(", leadershipStyle=").append(leadershipStyle);
        sb.append(", userNo=").append(userNo);
        sb.append("]");
        return sb.toString();
    }
}
