package com.yxt.talent.bk.core.heir.repo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.heir.bean.HeirPosCountBean;
import com.yxt.talent.bk.core.heir.bean.HeirPosRawBean;
import com.yxt.talent.bk.core.heir.bean.HeirUserPosBriefBean;
import com.yxt.talent.bk.core.heir.bean.PosUserBriefBean;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * HeirPosRepository
 *
 * <AUTHOR> geyan
 * @Date 18/8/23 4:32 pm
 */
@Slf4j
@Repository
@AllArgsConstructor
public class HeirPosRepository {

    private final HeirPosMapper heirPosMapper;
    private final HeirPosUserMapper heirPosUserMapper;

    @DbHintMaster
    public int getPosType(String posId) {
        Integer posType = heirPosMapper.getPosType(posId);
        //查不到说明是部门继任
        return posType == null ? HeirPosTypeEnum.DEPT.getType() : posType;
    }

    @DbHintMaster
    public HeirPosEntity selectById(String id) {
        return heirPosMapper.selectById(id);
    }

    @DbHintMaster
    public void updatePosValidQty(String orgId, String posId) {
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_CALCULATE_VALID_QTY, posId);
        CommonUtils.tryLockRun(lockKey, 1,1, TimeUnit.MINUTES,() -> {
            int qty = heirPosUserMapper.posValidUserQty(orgId, posId);
            HeirPosEntity update = new HeirPosEntity();
            update.setId(posId);
            update.setHeirValidQty(qty);
            update.init(null);
            heirPosMapper.updateById(update);
        });
    }

    public HeirPosCountBean deptPosCount(String orgId) {
        HeirPosCountBean countBean = heirPosMapper.deptPosCount(orgId);
        if (countBean == null) {
            countBean = new HeirPosCountBean();
        }
        return countBean;
    }

    public IPage<PosUserBriefBean> listValidBrief(Page page, String orgId, String posId, Integer heirStatus) {
        IPage<PosUserBriefBean> ret = heirPosUserMapper.listValidBrief(page, orgId, posId, heirStatus);
        TalentbkUtil.bkUdpTranslate(orgId, ret.getRecords());
        return ret;
    }

    public List<HeirUserPosBriefBean> userPosList(String orgId, String userId) {
        return TalentbkUtil.bkUdpTranslate(orgId, heirPosMapper.userPosList(orgId, userId));
    }

    public List<HeirPosRawBean> allPos(String orgId, int posType) {
        return TalentbkUtil.bkUdpTranslate(orgId, heirPosMapper.allPos(orgId, posType));
    }

    public IPage<HeirPosRawBean> selectDeptHeir(Page page, String orgId, String userId,
                                                String deptId, String deptRoutingPath) {
        IPage<HeirPosRawBean> ret = heirPosMapper.selectDeptHeir(page, orgId, userId, deptId, deptRoutingPath);
        TalentbkUtil.bkUdpTranslate(orgId, ret.getRecords());
        return ret;
    }
}
