package com.yxt.talent.bk.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum NumberEnum {
    /**
     * 数字等级
     */
    ALL(-1, "", ""),

    ZERO(0, "零", "零级"),

    ONE(1, "一", "一级"),

    TWO(2, "二", "二级"),

    THREE(3, "三", "三级"),

    FOUR(4, "四", "四级"),

    FIVE(5, "五", "五级"),

    SIX(6, "六", "六级"),

    SIXTY(60, "六十", "六十级" ),

    EIGHTY(80, "八十", "八十级"),

    ONE_HUNDRED(100, "一百", "一百级");

    public static String getNumCh1ByNumber(Integer number) {
        for (NumberEnum anEnum : NumberEnum.values()) {
            if (anEnum.getNumber().equals(number)) {
                return anEnum.getNumCh1();
            }
        }
        return "N/A";
    }


    private Integer number;

    private String numCh;

    private String numCh1;


}
