package com.yxt.talent.bk.core.pool.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Import;
import com.yxt.talent.bk.core.pool.bean.PoolUser4List;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Param;
import com.yxt.talent.bk.core.pool.bean.PoolUserQtyBean;
import com.yxt.talent.bk.core.pool.bean.UserPoolInfoBean;
import com.yxt.talent.bk.core.pool.entity.PoolUser;
import com.yxt.talent.bk.core.pool.mapper.PoolUserMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
@Repository
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class PoolUserRepository extends ServiceImpl<PoolUserMapper, PoolUser> {

    private final PoolUserMapper poolUserMapper;

    public List<String> findUserIdByPoolIdAndUserIds(String orgId, String poolId, Collection<String> userIds) {
        return poolUserMapper.findUserIdByPoolIdAndUserIds(orgId, poolId, userIds);
    }

    public List<PoolUser> findByPoolIdAndUserIds(String orgId, String poolId, Collection<String> userIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userIds)) {
            log.warn("LOG10290:orgId={}, poolId={}", orgId, poolId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getPoolId, poolId);
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        // 只计算在池人员
        queryWrapper.eq(PoolUser::getEventPoolOut, 0);
        queryWrapper.in(PoolUser::getUserId, userIds);
        return list(queryWrapper);
    }

    public List<PoolUser> findByUserIdAndPoolIds(String orgId, String userId, Collection<String> poolIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(poolIds)) {
            log.warn("LOG10290:orgId={}, userId={}", orgId, userId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        queryWrapper.eq(PoolUser::getUserId, userId);
        queryWrapper.in(PoolUser::getPoolId, poolIds);
        return list(queryWrapper);
    }

    public List<PoolUser> findPoolIds(String orgId, Collection<String> poolIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(poolIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        queryWrapper.in(PoolUser::getPoolId, poolIds);
        return list(queryWrapper);
    }

    public IPage<PoolUser4List> page(IPage<PoolUser4List> page, PoolUser4Param param) {
        param.setKeywordLangMatch(TalentbkUtil.langKeywordQuery(param.getOrgId(), param.getKeyword(), true, true));
        param.setKeyword(SqlUtils.escapeLike(param.getKeyword()));
        IPage<PoolUser4List> ret = poolUserMapper.page(page, param);
        TalentbkUtil.bkUdpTranslate(param.getOrgId(),true,ret.getRecords());
        return ret;
    }

    public List<PoolUser4List> page(PoolUser4Param param) {
        return TalentbkUtil.bkUdpTranslate(param.getOrgId(), true, poolUserMapper.page(param));
    }

    public long countByPoolId(String orgId, String poolId) {
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getPoolId, poolId);
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        return count(queryWrapper);
    }

    public long countByReadinessId(String orgId, String readinessId) {
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getReadinessId, readinessId);
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        return count(queryWrapper);
    }

    public List<PoolUser> findByPoolId(String orgId, String poolId) {
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        queryWrapper.eq(PoolUser::getPoolId, poolId);
        // 只计算在池人员
        queryWrapper.eq(PoolUser::getEventPoolOut, 0);
        queryWrapper.orderByDesc(PoolUser::getCreateTime);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<PoolUser> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public List<PoolUser4Import> findPoolUser4Import(List<String> userNames, String orgId){
        if (CollectionUtils.isEmpty(userNames)) {
            return new ArrayList<>();
        }
        return TalentbkUtil.bkUdpTranslate(orgId, baseMapper.findPoolUser4Import(orgId, userNames));
    }

    public List<PoolUser> findByUserIds (String orgId, String poolId, List<String> userIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userIds)) {
            log.warn("LOG10290:orgId={}, poolId={}", orgId, poolId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        queryWrapper.eq(PoolUser::getPoolId, poolId);
        queryWrapper.in(PoolUser::getUserId, userIds);
        return list(queryWrapper);

    }

    public List<PoolUser> findPoolUser(String orgId, String poolId) {
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        queryWrapper.eq(PoolUser::getPoolId, poolId);
        queryWrapper.eq(PoolUser::getEventPoolOut, 0);
        return list(queryWrapper);
    }

    public List<String> findAllPoolUser(String orgId, String poolId) {
        return baseMapper.findAllPoolUser(orgId, poolId);
    }

    public List<PoolUser> findPoolUserByOrgId(String orgId) {
        LambdaQueryWrapper<PoolUser> queryWrapper = getQueryWrapper();
        queryWrapper.eq(PoolUser::getOrgId, orgId);
        return list(queryWrapper);
    }

    public List<UserPoolInfoBean> userPoolList(String orgId, String userId) {
        return poolUserMapper.userPoolList(orgId, userId);
    }

    public List<PoolUserQtyBean> poolEnableUserQty(String orgId, List<String> poolIds) {
        if (CollectionUtils.isEmpty(poolIds)) {
            return Lists.newArrayList();
        }
        return poolUserMapper.poolEnableUserQty(orgId, poolIds);
    }
}
