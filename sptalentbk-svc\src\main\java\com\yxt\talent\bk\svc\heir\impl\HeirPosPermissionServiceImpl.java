package com.yxt.talent.bk.svc.heir.impl;

import com.google.common.collect.Lists;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosPermissionEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosExt;
import com.yxt.talent.bk.core.heir.ext.HeirPosPermissionExt;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPermissionMapper;
import com.yxt.talent.bk.core.heir.repo.HeirPosPermissionRepository;
import com.yxt.talent.bk.svc.heir.HeirPosPermissionService;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.bean.req.PosPermissionUserAddReq;
import com.yxt.talent.bk.svc.heir.bean.resp.PosPermissionListResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * HeirPosUserServiceImpl
 *
 * <AUTHOR>
 * @date 2023/08/16
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class HeirPosPermissionServiceImpl implements HeirPosPermissionService {

    private final HeirPosPermissionMapper heirPosPermissionMapper;
    private final HeirPosPermissionRepository heirPosPermissionRepository;
    private final HeirPosMapper heirPosMapper;
    private final HeirPosService heirPosService;


    @Override
    public CommonList<PosPermissionListResp> listUser(UserCacheBasic currentUser, String posId) {
        List<HeirPosPermissionExt> pageList = heirPosPermissionRepository.list(currentUser.getOrgId(), posId);
        return BeanCopierUtil.toCommonList(pageList, HeirPosPermissionExt.class, PosPermissionListResp.class);
    }

    @DbHintMaster
    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    @Override
    public void saveUser(UserCacheBasic currentUser, PosPermissionUserAddReq req) {
        String orgId = currentUser.getOrgId();
        String posId = req.getPosId();
        List<String> userIds = CollectionUtils.isEmpty(req.getUserIds()) ? Lists.newArrayList() : req.getUserIds();
        Set<String> allUserIds = heirPosPermissionMapper.listUserId(orgId, posId);
        Collection<String> deleteUserIds = CollectionUtils.subtract(allUserIds, userIds);
        Collection<String> addUserIds = CollectionUtils.subtract(userIds, allUserIds);

        List<HeirPosPermissionEntity> saveList = new ArrayList<>();
        addUserIds.stream().forEach(userId -> {
            HeirPosPermissionEntity posUser = new HeirPosPermissionEntity();
            posUser.init(orgId, currentUser.getUserId());
            posUser.setPosId(posId);
            posUser.setUserId(userId);
            saveList.add(posUser);
        });

        if (CollectionUtils.isNotEmpty(addUserIds)) {
            BatchOperationUtil.batchSave(saveList, heirPosPermissionMapper::insertBatchSomeColumn);
            log.info("posPermission add users. posId: {} userIds: {}", posId, addUserIds);
        }
        if (CollectionUtils.isNotEmpty(deleteUserIds)) {
            heirPosPermissionMapper.deleteByUserIds(currentUser.getOrgId(), currentUser.getUserId(), DateUtil.currentTime(), req.getPosId(), deleteUserIds);
            log.info("posPermission delete users. posId: {} userIds: {}", posId, deleteUserIds);
        }
    }

    @DbHintMaster
    @Override
    public void enable(UserCacheBasic currentUser, String posId, Integer enabled) {
        HeirPosExt pos = heirPosMapper.getById(currentUser.getOrgId(), posId);
        Validate.isNotNull(pos, BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE);
        if (!Objects.equals(pos.getPermissionFlag(), enabled)) {
            HeirPosEntity updateEntity = new HeirPosEntity();
            updateEntity.setId(pos.getId());
            updateEntity.setOrgId(currentUser.getOrgId());
            updateEntity.setUpdateUserId(currentUser.getUserId());
            updateEntity.setUpdateTime(DateUtil.currentTime());
            updateEntity.setPermissionFlag(enabled);
            heirPosMapper.update(updateEntity);
        }
    }

    @Override
    public Integer getEnableStatus(UserCacheBasic currentUser, String posId, Integer posType) {
        HeirPosExt pos = heirPosMapper.getById(currentUser.getOrgId(), posId);
        if (pos == null && posType == HeirPosTypeEnum.DEPT.getType()) {
            pos = heirPosService.init(currentUser, posId, posType);
        }
        if (pos == null) {
            throw new ApiException(BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE);
        }
        return pos.getPermissionFlag();
    }
}
