package com.yxt.talent.bk.svc.heir.bean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.spmodel.facade.bean.label.LabelValueVO;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * LabelRuleCfgBean
 *
 * <AUTHOR> geyan
 * @Date 18/8/23 7:24 pm
 */
@Data
public class LabelRuleBean {
    private Long id;
    private String name;
    private String description;
    @Schema(description = "标签类型;1=基础标签 2=复合标签 3=人工标签")
    private Integer labelType;
    @Schema(description = "规则类型 1：指标 2：标签",requiredMode = Schema.RequiredMode.REQUIRED)
    private int type;
    private Long targetId;
    private String targetName;
    private String targetDesc;
    @Schema(description = "指标数据类型 0：枚举 1：字符串（文本） 2：整数 3：浮点数 4：日期")
    private Integer columnType;
    private List<CompareOperator> operators;

    @Data
    public static class CompareOperator {
        private Integer operateType;
        private String operatorCnName;
        private Object value;

        @JsonIgnore
        private CompareOperatorValue parsedValue;

        public void parseValue() {
            if (value == null) {
                return;
            }
            if (value instanceof String) {
                parsedValue = new CompareOperatorValue();
                parsedValue.setStrVal((String)value);
            } else if (value instanceof Number) {
                parsedValue = new CompareOperatorValue();
                parsedValue.setNumVal(new BigDecimal(String.valueOf(value)));
            } else if (value instanceof Map) {
                parsedValue = new CompareOperatorValue();
                parsedValue.setOption(JSON.parseObject(JSON.toJSONString(value), CompareOperatorOptionValue.class));
            } else if (value instanceof List) {
                parsedValue = new CompareOperatorValue();
                parsedValue.setOptionList(JSON.parseArray(JSON.toJSONString(value), CompareOperatorOptionValue.class));
            }
        }
    }

    @Data
    public static class CompareOperatorValue {
        private BigDecimal numVal;
        private String strVal;

        private CompareOperatorOptionValue option;
        private List<CompareOperatorOptionValue> optionList;

        public void fillLabelInfo(List<LabelValueVO> labelValueList) {
            IArrayUtils.forEach(labelValueList, labelValueVO -> {
                doFillLabelInfo(labelValueVO, option);
                IArrayUtils.forEach(optionList, item -> doFillLabelInfo(labelValueVO, item));
            });
        }

        private static void doFillLabelInfo(LabelValueVO labelValueVO, CompareOperatorOptionValue optionValue) {
            if (optionValue != null && labelValueVO.getLabelValueId().equals(optionValue.getId())) {
                optionValue.setLabelValue(labelValueVO.getLabelValue());
                optionValue.setOrderIndex(labelValueVO.getOrderIndex());
            }
        }
    }

    @Data
    public static class CompareOperatorOptionValue {
        private Long id;
        private String name;
        @Schema(description = "标签值")
        private String labelValue;
        @Schema(description = "标签值排序")
        private Integer orderIndex;
    }
}
