package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.SnowFlowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("bk_heir_org_config")
public class HeirOrgConfigEntity extends SnowFlowIdEntity {

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "学员端是否展示相关信息（0：不展示；1：展示）")
    private Integer showFlag;
}
