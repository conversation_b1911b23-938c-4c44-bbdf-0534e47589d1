package com.yxt.talent.bk.common.aop.uuid;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * uuid 的 注解
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RUNTIME)
@Constraint(validatedBy = UuidValidator.class)
public @interface Uuid {
    /**
     * : 错误提示
     */
    String message() default "global.id.uuid";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
