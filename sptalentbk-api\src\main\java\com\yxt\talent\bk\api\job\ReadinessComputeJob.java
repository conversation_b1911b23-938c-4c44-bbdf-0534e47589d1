package com.yxt.talent.bk.api.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.talent.bk.svc.heir.ReadinessComputeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ReadinessComputeJob {

    @Autowired
    private ReadinessComputeService readinessComputeService;

    @XxlJob("readinessComputeJob")
    public ReturnT<String> readinessCompute(String param) {
        readinessComputeService.readinessCompute();
        return ReturnT.SUCCESS;
    }

    @XxlJob("readinessRemindJob")
    public ReturnT<String> readinessRemind(String param) {
        readinessComputeService.readinessRemind();
        return ReturnT.SUCCESS;
    }
}
