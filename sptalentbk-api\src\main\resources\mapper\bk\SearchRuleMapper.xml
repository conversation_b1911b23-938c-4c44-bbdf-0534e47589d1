<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.usergroup.mapper.SearchRuleMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.usergroup.entity.SearchRule">
    <!--@mbg.generated-->
    <!--@Table bk_search_rule-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="CHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="tag_search_type" jdbcType="INTEGER" property="tagSearchType" />
    <result column="tag_search" jdbcType="LONGVARCHAR" property="tagSearch" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, tag_search_type, 
    tag_search
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update bk_search_rule
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="tag_search_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.tagSearchType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="tag_search = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.tagSearch,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update bk_search_rule
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deleted != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateUserId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createUserId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tag_search_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tagSearchType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tagSearchType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="tag_search = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tagSearch != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tagSearch,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into bk_search_rule
    (id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, tag_search_type, 
      tag_search)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=CHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.createUserId,jdbcType=CHAR}, #{item.tagSearchType,jdbcType=INTEGER}, #{item.tagSearch,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.bk.core.usergroup.entity.SearchRule">
    <!--@mbg.generated-->
    insert into bk_search_rule
    (id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, tag_search_type, 
      tag_search)
    values
    (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=CHAR}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=CHAR}, 
      #{tagSearchType,jdbcType=INTEGER}, #{tagSearch,jdbcType=LONGVARCHAR})
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    org_id = #{orgId,jdbcType=CHAR}, 
    deleted = #{deleted,jdbcType=TINYINT}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    update_user_id = #{updateUserId,jdbcType=CHAR}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    create_user_id = #{createUserId,jdbcType=CHAR}, 
    tag_search_type = #{tagSearchType,jdbcType=INTEGER}, 
    tag_search = #{tagSearch,jdbcType=LONGVARCHAR}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.bk.core.usergroup.entity.SearchRule">
    <!--@mbg.generated-->
    insert into bk_search_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="tagSearchType != null">
        tag_search_type,
      </if>
      <if test="tagSearch != null">
        tag_search,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=CHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="tagSearchType != null">
        #{tagSearchType,jdbcType=INTEGER},
      </if>
      <if test="tagSearch != null">
        #{tagSearch,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=CHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="tagSearchType != null">
        tag_search_type = #{tagSearchType,jdbcType=INTEGER},
      </if>
      <if test="tagSearch != null">
        tag_search = #{tagSearch,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
    <update id="deleteLogicById">
        update bk_search_rule set deleted = 1 , update_time = now(), update_user_id = #{userId} where org_id = #{orgId} and id = #{id}
    </update>
</mapper>