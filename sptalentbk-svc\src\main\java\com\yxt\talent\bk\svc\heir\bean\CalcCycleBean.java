package com.yxt.talent.bk.svc.heir.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "周期")
public class CalcCycleBean {
    @Schema(description = "每天：everyday，每周：everyweek，每月：everymonth")
    private String cycle;
    @Schema(description = "如果是每周就是周几，每月就是几号，每天的话为null")
    private Integer day;
    @Schema(description = "提醒时间")
    private String remindTime;
}
