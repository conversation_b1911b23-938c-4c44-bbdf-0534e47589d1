package com.yxt.talent.bk.svc.mq.consumer;

import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.BeanHelper;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.svc.common.DemoCopyService;
import com.yxt.talentbkfacade.constant.BkFacadeContants;
import com.yxt.udpfacade.bean.demo.DemoInit4Mq;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * CopyOrgSecondListener
 *
 * <AUTHOR> harleyge
 * @Date 12/9/24 7:55 pm
 */
@Slf4j
@RequiredArgsConstructor
@Component
@RocketMQMessageListener(consumerGroup = TalentBkRocketMqConstant.GROUP_PREFIX + BkFacadeContants.TOPIC_UDP_DEMO_INIT,         topic = BkFacadeContants.TOPIC_UDP_DEMO_INIT, consumeThreadNumber = 2, consumeTimeout = 30)
public class CopyOrgSecondConsumer implements RocketMQListener<String> {
    private final DemoCopyService demoCopyService;
    @Override
    public void onMessage(String message) {
        try {
            DemoInit4Mq bean = BeanHelper.json2Bean(message, DemoInit4Mq.class);
            if (bean == null || StringUtils.isAnyEmpty(
                    bean.getSourceOrgId(), bean.getTargetOrgId(),
                    bean.getSourceDomain(), bean.getTargetDomain())) {
                log.warn("CopyOrgSecondListener empty param msg {}", message);
                return;
            }
            OrgInit4Mq init4Mq = demoCopyService.orgInitData(bean.getTargetOrgId());
            if (init4Mq == null || !bean.getSourceOrgId().equals(init4Mq.getSourceOrgId())) {
                log.warn("CopyOrgSecondListener invalid init4Mq msg {} init4Mq.sourceOrgId {}",
                        message, Optional.ofNullable(init4Mq).map(OrgInit4Mq::getSourceOrgId).orElse(null));
                return;
            }
            demoCopyService.demoCopyBizDone(init4Mq.getTargetOrgId(),
                    TalentBkConstants.DEMO_COPY_ORG_INITIALIZE, YesOrNo.YES.getValue());
        } catch (Exception ex) {
            log.error("CopyOrgSecondListener error MQ msg {}", message, ex);
        }
    }
}
