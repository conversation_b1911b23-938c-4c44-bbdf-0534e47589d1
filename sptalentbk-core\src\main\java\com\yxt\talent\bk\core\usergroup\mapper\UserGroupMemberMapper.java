package com.yxt.talent.bk.core.usergroup.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.bk.core.usergroup.bean.GroupMemberVO;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupMember;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

public interface UserGroupMemberMapper extends BaseMapper<UserGroupMember> {
    int updateBatch(List<UserGroupMember> list);

    int updateBatchSelective(List<UserGroupMember> list);

    int batchInsert(@Param("list") List<UserGroupMember> list);

    int insertOrUpdate(UserGroupMember record);

    int insertOrUpdateSelective(UserGroupMember record);

    int deleteByGroupId(@Param("orgId") String orgId, @Param("userId") String userId, @Param("groupId") Long groupId);

    int deleteByGroupIdAndUserIds(@Param("orgId") String orgId, @Param("userIds") List<String> userIds,
        @Param("groupId") Long groupId);

    /**
     * 根据群组ID分页获取成员信息
     *
     * @param iPage
     * @param groupId
     * @param orgId
     * @return
     */
    Page<GroupMemberVO> findPage(IPage<GroupMemberVO> iPage, @Param("groupId") Long groupId,
        @Param("orgId") String orgId);
}
