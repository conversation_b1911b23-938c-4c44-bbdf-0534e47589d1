package com.yxt.talent.bk.svc.tag;

import com.yxt.common.util.BeanCopierUtil;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.TagValueRepository;
import com.yxt.talent.bk.svc.tag.bean.TagValue4Create;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 标签值表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-09
 */
@AllArgsConstructor
@Service
public class TagValueService {

    private final TagValueRepository tagValueRepository;
    private final TagRepository tagRepository;

    public List<TagValue4Create> list2Vo(String orgId) {
        List<TagValueEntity> list = tagValueRepository.list(orgId);
        return BeanCopierUtil.convertList(list, TagValueEntity.class, TagValue4Create.class);
    }

    public List<TagValueEntity> list(String orgId) {
        return tagValueRepository.list(orgId);
    }

    public List<TagValueEntity> listByTagId(String orgId, String tagId) {
        return tagValueRepository.listByTagId(orgId, tagId);
    }

    public TagValue4Create getById2Vo(String orgId, String id) {
        TagValue4Create tagValue4Create = new TagValue4Create();
        BeanUtils.copyProperties(tagValueRepository.queryTagById(orgId, id), tagValue4Create, TagValue4Create.class);
        return tagValue4Create;
    }

    public TagValueEntity getById(String orgId, String id) {
        return tagValueRepository.queryTagById(orgId, id);
    }

    public boolean saveOrUpdate(TagValueEntity tagValue) {
        return tagValueRepository.saveOrUpdate(tagValue);
    }

    public Boolean saveOrUpdateBatch(Collection<TagValueEntity> tagValues) {
        return tagValueRepository.saveOrUpdateBatch(tagValues);
    }

    public Boolean removeById(String orgId, String id) {
        return tagValueRepository.removeById(orgId, id);
    }

    public Boolean removeByIds(String orgId, Collection<String> ids) {
        return tagValueRepository.removeByIds(orgId, ids);
    }

    public void saveBatch(List<TagValueEntity> saveTagValueList) {
        tagValueRepository.saveBatch(saveTagValueList);
    }
}
