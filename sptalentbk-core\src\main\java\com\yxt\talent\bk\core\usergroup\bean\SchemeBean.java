package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 人才列表
 *
 * <AUTHOR>
 * @Date 2023/8/16 14:17
 **/
@Data
public class SchemeBean implements UdpLangSupport {

    @Schema(description = "人员id")
    private String userId;

    @Schema(description = "姓名")
    private String fullname;

    @Schema(description = "账号")
    private String username;

    @JsonIgnore
    private String deptId;

    @Schema(description = "主部门全路径名称，例：研发中心->园区 ")
    private String deptName;

    @JsonIgnore
    private String positionId;

    @Schema(description = "主岗位名称")
    private String positionName;

    @Schema(description = "用户状态，0-禁用，1-启用")
    private int status;

    @Schema(description = "入职时间")
    private LocalDateTime hireDate;

    @Schema(description = "职级名称")
    private String gradeName;

    @Schema(description = "头像")
    private String imgUrl;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(userId, fullname, this::setFullname);
    }

    @Override
    public UdpLangUnitBean deptLangProperty() {
        return new UdpLangUnitBean(deptId, deptName, this::setDeptName);
    }

    @Override
    public UdpLangUnitBean positionLangProperty() {
        return new UdpLangUnitBean(positionId, positionName, this::setPositionName);
    }
}
