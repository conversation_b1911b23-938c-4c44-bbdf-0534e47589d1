package com.yxt.talent.bk.core.tag.bean;

import lombok.Data;

import java.util.List;

@Data
public class TagSearchBean {

    /**
     * 分类id 为空查询所有分类的标签
     */
    private String catalogId;

    /**
     * 更新类型 默认0  0-手动 1-自动 -1全部"
     */
    private int updateType = 0;

    /**
     * 标签名称 支持模糊搜索
     */
    private String keyWord;

    /**
     * 标签来源 0-内置,1-自建,2-固定 空：全部
     */
    private List<Integer> sourceType;

    /**
     * 启用类型 默认-1  0-禁用 1-启用 -1全部"
     */
    private int tagEnable = -1;

    /**
     * 是否开通人才盘点
     */
    private boolean rvOrNot;
}
