package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.util.DateUtil;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

/**
*  bk_package_data
* <AUTHOR>
* @since 2022/9/8 9:41
* @version 1.0
*/
@Data
@NoArgsConstructor
@TableName("bk_package_data")
public class PackageDataEntity {

    @Schema(description = "雪花id")
    private Long id;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "业务类型")
    private String bizType;

    @Schema(description = "业务数据id")
    private String masterId;

    @Schema(description = "业务数据包md5")
    private String bizDataMd5;

    @Schema(description = "业务数据包")
    private String bizData;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    public void init(String orgId) {
        setUpdateTime(DateUtil.currentTime());
        if (id == null) {
            this.id = TalentbkUtil.snowFLowId();
            this.setCreateTime(getUpdateTime());
            setOrgId(orgId);
        } else if (StringUtils.isNotBlank(orgId)) {
            setOrgId(orgId);
        }
    }
}
