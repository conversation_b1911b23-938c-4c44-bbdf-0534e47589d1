package com.yxt.talent.bk.svc.udp.rpc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Lists;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.pool.bean.SearchDeptInfo;
import com.yxt.udpfacade.bean.common.IdsBean;
import com.yxt.udpfacade.bean.dept.DeptBean;
import com.yxt.udpfacade.bean.dept.DeptManagerBean;
import com.yxt.udpfacade.bean.org.groupcorp.OrgDeptPosIdParamBean;
import com.yxt.udpfacade.bean.org.groupcorp.OrgDeptPosIdResultBean;
import com.yxt.udpfacade.service.UdpFacade;
import com.yxt.udpfacade.service.UdpGroupCorpFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class UdpOrgSearchRpc {

    private final UdpFacade udpFacade;
    private final UdpGroupCorpFacade udpGroupCorpFacade;

    /**
     * 查询父部门以及下面所有子部门
     * @param
     * @return
     * <AUTHOR>
     * @since 2022/9/23
     */
    public List<String> searchAllDeptIdListFromEsFacade(String orgId, List<SearchDeptInfo> deptInfoList) {
        if (CollectionUtils.isEmpty(deptInfoList)) {
            return new ArrayList<>(0);
        }
        List<String> result = Lists.newArrayList();
        deptInfoList = deptInfoList.stream().filter(e -> StringUtils.isNotBlank(e.getDeptId())).collect(Collectors.toList());
        // 每次批处理500条 超过则分批处理
        BatchOperationUtil.batchExecute(deptInfoList, 500,
            (List<SearchDeptInfo> list) -> this.callBackMethod(orgId, list, result));
        return result;
    }


    private void callBackMethod(String orgId, List<SearchDeptInfo> list, List<String> result) {
        list.forEach(deptInfo -> {
            String deptId = deptInfo.getDeptId();
            boolean includeAllOrNot = CommonUtils.isTrue(deptInfo.getIncludeAll());
            // 查询子部门
            if (includeAllOrNot) {
                // 查询deptId+子ids
                CommonList<String> deptBeanCommonList = udpFacade.getDeptTreeIds(orgId, deptId);
                if (Objects.nonNull(deptBeanCommonList) && CollectionUtils.isNotEmpty(deptBeanCommonList.getDatas())) {
                    // 添加子部门id
                    result.addAll(deptBeanCommonList.getDatas());
                }
            } else {
                // 添加父id
                result.add(deptId);
            }
        });
    }

    public List<DeptBean> findDepartmentNameList(String orgId, List<String> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>(0);
        }
        List<DeptBean> result = Lists.newArrayList();
        // 每次批处理500条 超过则分批处理
        BatchOperationUtil.batchExecute(ids, 500,
            (List<String> list) -> {
                List<DeptBean> deptBeans = udpFacade.findDepartmentNameList(orgId, list);
                if (CollectionUtils.isNotEmpty(deptBeans)){
                    result.addAll(deptBeans);
                }
            });
        return result;
    }

    public List<IdName> findPositionNameList(String orgId, List<String> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>(0);
        }
        List<IdName> result = Lists.newArrayList();
        // 每次批处理500条 超过则分批处理
        BatchOperationUtil.batchExecute(ids, 500,
            (List<String> list) -> {
                OrgDeptPosIdParamBean orgDeptPosIdParamBean = new OrgDeptPosIdParamBean();
                orgDeptPosIdParamBean.setOrgId(orgId);
                orgDeptPosIdParamBean.setPositionIds(new HashSet<>(list));
                OrgDeptPosIdResultBean orgDeptPosIdResultBean = udpGroupCorpFacade.listOrgDeptPosIdNames(orgDeptPosIdParamBean);
                if (orgDeptPosIdResultBean != null && CollectionUtils.isNotEmpty(orgDeptPosIdResultBean.getPositions())){
                    result.addAll(orgDeptPosIdResultBean.getPositions());
                }
            });
        return result;
    }

    public List<IdName> findGradeNameList(String orgId, List<String> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>(0);
        }
        List<IdName> result = Lists.newArrayList();
        // 每次批处理500条 超过则分批处理
        BatchOperationUtil.batchExecute(ids, 500,
            (List<String> list) -> {
                OrgDeptPosIdParamBean orgDeptPosIdParamBean = new OrgDeptPosIdParamBean();
                orgDeptPosIdParamBean.setOrgId(orgId);
                orgDeptPosIdParamBean.setGradeIds(new HashSet<>(list));
                OrgDeptPosIdResultBean orgDeptPosIdResultBean = udpGroupCorpFacade.listOrgDeptPosIdNames(orgDeptPosIdParamBean);
                if (orgDeptPosIdResultBean != null && CollectionUtils.isNotEmpty(orgDeptPosIdResultBean.getGrades())){
                    result.addAll(orgDeptPosIdResultBean.getGrades());
                }
            });
        return result;
    }

    /**
     * 获取所有部门经理是当前用户的部门ID
     *
     * @param orgId
     * @param userId
     * @return
     */
    public Set<String> findManageDeptIds(String orgId, String userId) {
        IdsBean bean = new IdsBean();
        bean.setIds(ListUtil.toList(userId));

        List<DeptManagerBean> beans = udpFacade.checkIsDeptManagers(orgId, bean);
        if (CollUtil.isEmpty(beans)) {
            return Collections.emptySet();
        }

        return beans.stream()
            //非空
            .filter(Objects::nonNull)
            //是部门经理的
            .filter(o -> o.getManager() == 1)
            .map(DeptManagerBean::getDeptId)
            .collect(Collectors.toSet());
    }
}
