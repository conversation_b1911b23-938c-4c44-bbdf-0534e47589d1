package com.yxt.talent.bk.svc.rpc;

import com.yxt.spmodel.facade.bean.demo.OrgDemoIdMappingVO;
import com.yxt.spmodel.facade.service.SpmodelDemoCopyIdMappingService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * SpmodelRpc
 *
 * <AUTHOR> harleyge
 * @Date 23/9/24 11:04 am
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SpmodelRpc {
    private final SpmodelDemoCopyIdMappingService spmodelDemoCopyIdMappingService;

    public OrgDemoIdMappingVO demoCopyIdMap(String orgId) {
        try {
            return spmodelDemoCopyIdMappingService.getOrgDemoIdMappingVO(orgId);
        } catch (FeignException.BadRequest e) {
            String respBody = e.contentUTF8();
            if (respBody != null && respBody.indexOf("apis.spm.org.info.not.exist") > -1) {
                log.warn("org spmodel not init {} resp {}", orgId, respBody);
                return new OrgDemoIdMappingVO();
            }
            throw e;
        }
    }
}
