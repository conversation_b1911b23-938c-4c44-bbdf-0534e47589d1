package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.SnowFlowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * heirRemindTodoEntity
 *
 * <AUTHOR> geyan
 * @Date 27/9/23 10:08 am
 */
@Data
@TableName("bk_heir_remind_todo")
public class HeirRemindTodoEntity extends SnowFlowIdEntity {
    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "岗位或者部门id")
    private String posId;

    @Schema(description = "提醒人")
    private String remindUserId;

    @Schema(description = "处理目标用户")
    private String targetUserId;

    @Schema(description = "处理目标数据id")
    private Long targetRecordId;

    @Schema(description = "待办id")
    private String todoId;
    @Schema(description = "待办状态，0:未处理，1:完成，2:已取消")
    private Integer todoStatus;
    @Schema(description = "提醒主要参数")
    private String remindParam;
}
