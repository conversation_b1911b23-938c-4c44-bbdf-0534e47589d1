package com.yxt.talent.bk.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * IArrayUtils
 *
 * <AUTHOR> geyan
 * @Date 2021/12/28 11:03 上午
 */
@Slf4j
public class IArrayUtils {
    private IArrayUtils(){}

    /**
     * 返回item 第一个
     * @param list
     * @param <T>
     * @return
     */
    public static <T> T getFirst(Collection<T> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().findFirst().orElse(null);
        }
        return null;
    }

    public static <T> String getListOrgId(Collection<T> list, Function<T, String> orgIdGetter) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (T item : list) {
                if (item != null) {
                    String orgId = orgIdGetter.apply(item);
                    if (StringUtils.isNotEmpty(orgId)) {
                        return orgId;
                    }
                }
            }
        }
        return StringPool.EMPTY;
    }

    /**
     * 查询首个匹配
     * @param list
     * @param predicate
     * @param <T>
     * @return
     */
    public static <T> T getFirstMatch(Collection<T> list, Predicate<T> predicate) {
        if (list == null) {
            return null;
        }
        return list.stream().filter(predicate).findFirst().orElse(null);
    }

    /**
     * 查询首个匹配
     * @param list
     * @param predicate
     * @param <T>
     * @return
     */
    public static <T> void firstMatch(Collection<T> list, Predicate<T> predicate, Consumer<T> consumer) {
        if (list == null) {
            return;
        }
        T match = list.stream().filter(predicate).findFirst().orElse(null);
        if (match == null) {
            return;
        }
        consumer.accept(match);
    }

    /**
     * any match
     * @param list
     * @param predicate
     * @param <T>
     * @return
     */
    public static <T> boolean anyMatch(Collection<T> list, Predicate<T> predicate) {
        if (list == null) {
            return false;
        }
        return list.stream().anyMatch(predicate);
    }

    /**
     * foreach
     * @param list
     * @param consumer
     * @param <T>
     */
    public static <T> void forEach(Collection<T> list, Consumer<T> consumer) {
        if (list != null) {
            list.forEach(consumer);
        }
    }

    /**
     * contains
     * @param list
     * @param item
     * @param <T>
     * @return
     */
    public static <T> boolean contains(Collection<T> list, T item) {
        if (list == null || item == null) {
            return false;
        }
        return list.contains(item);
    }

    /**
     * list2map
     * @param list
     * @param keyGetter
     * @param <K>
     * @param <I>
     * @return
     */
    public static <K, I> Map<K, List<I>> list2Map(Collection<I> list,
                                                  Function<I, K> keyGetter) {
        Map<K, List<I>> ret = new HashMap<>();
        if (list != null) {
            list.forEach(item -> {
                K key = keyGetter.apply(item);
                List<I> subList = ret.get(key);
                if (subList == null) {
                    subList = new ArrayList<>();
                    ret.put(key, subList);
                }
                subList.add(item);
            });
        }
        return ret;
    }

    public static <K, V, I> Map<K, List<V>> list2Map(Collection<I> list,
                                                     Function<I, K> keyGetter,
                                                     Function<I, V> valueGetter) {
        Map<K, List<V>> ret = new HashMap<>();
        if (list != null) {
            list.forEach(item -> {
                K key = keyGetter.apply(item);
                List<V> subList = ret.get(key);
                if (subList == null) {
                    subList = new ArrayList<>();
                    ret.put(key, subList);
                }
                subList.add(valueGetter.apply(item));
            });
        }
        return ret;
    }

    public static <K, V, I> Map<K, V> listAsMap(Collection<I> list,
                                                  Function<I, K> keyGetter,
                                                  Function<I, V> valueGetter) {
        Map<K,V> ret = new HashMap<>();
        if (list != null) {
            list.forEach(item -> {
                ret.put(keyGetter.apply(item), valueGetter.apply(item));
            });
        }
        return ret;
    }

    /**
     * array to list
     * @param arr
     * @param <T>
     * @return
     */
    public static <T> List<T> arr2List(T[] arr) {
        if (arr == null || arr.length < 1) {
            return Collections.emptyList();
        }
        List<T> ret = new ArrayList<>(arr.length);
        for (T item : arr) {
            ret.add(item);
        }
        return ret;
    }

    /**
     * list to array
     * @param list
     * @param createArr
     * @param <T>
     * @return
     */
    public static <T> T[] list2Arr(List<T> list, Function<Integer, T[]> createArr) {
        if (list == null || list.size() < 1) {
            return null;
        }
        T[] ret = createArr.apply(list.size());
        for (int i = 0;i < list.size();i++) {
            ret[i] = list.get(i);
        }
        return ret;
    }

    /**
     * 删除列表不符合条件的item
     * @param list
     * @param predicate
     * @param <T>
     */
    public static <T> void remove(List<T> list, Predicate<T> predicate) {
        remove(list, predicate, Boolean.FALSE);
    }

    /**
     * 删除列表不符合条件的item,
     * backRemoved为true时，返回被删除的itemList
     * 没有不符合条件的item时返回null
     * @param list
     * @param predicate
     * @param backRemoved
     * @param <T>
     * @return
     */
    public static <T> List<T> remove(List<T> list, Predicate<T> predicate, boolean backRemoved) {
        List<T> removeItems = backRemoved ? new ArrayList<>() : null;
        if (CollectionUtils.size(list) > 0) {
            if (list instanceof LinkedList) {
                return linkedListRemoveItem((LinkedList<T>)list, predicate);
            } else {
                int len = list.size();
                int removeCount = listRemoveItem(list, predicate, removeItems);
                while (true) {
                    if (removeCount < 1) {
                        break;
                    }
                    len--;
                    removeCount--;
                    list.remove(len);
                }
            }
        }
        return removeItems;
    }

    private static <T> List<T> linkedListRemoveItem(LinkedList<T> list, Predicate<T> predicate) {
        List<T> removeItems = null;
        for (T item : list) {
            if (predicate.test(item)) {
                if (removeItems == null) {
                    removeItems = new ArrayList<>();
                }
                removeItems.add(item);
            }
        }
        if (removeItems != null) {
            removeItems.forEach(list::remove);
        }
        return removeItems;
    }

    private static <T> int listRemoveItem(List<T> list, Predicate<T> predicate, List<T> removeItems) {
        int emptyStartIndex = -1;
        int removeCount = 0;
        for (int i = 0;i < list.size();i++) {
            T item = list.get(i);
            if (predicate.test(item)) {
                removeCount++;
                if (emptyStartIndex < 0) {
                    emptyStartIndex = i;
                }

                if (removeItems != null) {
                    removeItems.add(item);
                }
            } else if (emptyStartIndex > -1) {
                list.set(emptyStartIndex, item);
                emptyStartIndex++;
            }
        }
        return removeCount;
    }

    /**
     * 处理list 第idx个item，如果超出最大idx,则consumer不调用
     * @param list
     * @param idx
     * @param consumer
     * @param <T>
     */
    public static <T> void processListItem(List<T> list, int idx, Consumer<T> consumer) {
        if (idx < CollectionUtils.size(list)) {
            consumer.accept(list.get(idx));
        }
    }

    /**
     * 排序
     * @param list
     * @param sortFieldGetter
     * @param <T>
     */
    public static <T> void sortList(List<T> list, Function<T, Comparable> sortFieldGetter) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.sort((item1, item2) -> compareAsc(sortFieldGetter.apply(item1), sortFieldGetter.apply(item2)));
        }
    }

    /**
     * 排序
     * @param list
     * @param sortFieldGetters
     * @param <T>
     */
    public static <T> void sortList(List<T> list, Pair<Boolean, Function<T, Comparable>>... sortFieldGetters) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.sort((item1, item2) -> {
                int ret = 0;
                for (Pair<Boolean, Function<T, Comparable>> pair : sortFieldGetters) {
                    if (pair.getLeft()) {
                        ret = compareAsc(pair.getRight().apply(item1), pair.getRight().apply(item2));
                    } else {
                        ret = compareAsc(pair.getRight().apply(item2), pair.getRight().apply(item1));
                    }
                    if (ret != 0) {
                        return ret;
                    }
                }
                return ret;
            });
        }
    }

    /**
     * 排序
     * @param list
     * @param sortFieldGetter
     * @param <T>
     */
    public static <T> void sortListDesc(List<T> list, Function<T, Comparable> sortFieldGetter) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.sort((item1, item2) -> compareDesc(sortFieldGetter.apply(item1), sortFieldGetter.apply(item2)));
        }
    }

    /**
     * 正向比较结果 null最大
     * @param c1
     * @param c2
     * @return
     */
    public static int compareAsc(Comparable c1, Comparable c2) {
        if (c1 == null && c2 == null) {
            return 0;
        } else if (c1 == null) {
            return 1;
        } else if (c2 == null) {
            return -1;
        } else {
            return c1.compareTo(c2);
        }
    }

    /**
     * 反向比较结果，null最小
     * @param c1
     * @param c2
     * @return
     */
    public static int compareDesc(Comparable c1, Comparable c2) {
        if (c1 == null && c2 == null) {
            return 0;
        } else if (c1 == null) {
            return 1;
        } else if (c2 == null) {
            return -1;
        } else {
            return c2.compareTo(c1);
        }
    }

    public static <K, V> void addMapList(Map<K, List<V>> map, K key, V item) {
        List<V> list = map.computeIfAbsent(key, tmpKey -> new ArrayList<>());
        list.add(item);
    }

    /**
     * 数据分组
     * @param list
     * @param groupBy
     * @param <K>
     * @param <T>
     * @return
     */
    public static <K, T> Map<K, List<T>> group2Map(List<T> list, Function<T, K> groupBy) {
        Map<K, List<T>> retMap = new HashMap<>();
        if (list == null) {
            return retMap;
        }
        for (T item : list) {
            K groupKey = groupBy.apply(item);
            List<T> itemList = retMap.computeIfAbsent(groupKey, tmpKey -> new ArrayList<>());
            itemList.add(item);
        }
        return retMap;
    }

    public static <K, T> Map<K, T> listToMap(List<T> tList, Function<T, K> keyMethod) {
        if (tList == null || tList.isEmpty()) {
            return Collections.emptyMap();
        } else {
            Map<K, T> map = new LinkedHashMap<>(tList.size());
            for (T t : tList) {
                map.put(keyMethod.apply(t), t);
            }
            return map;
        }
    }

    public static <K, T> Map<K, List<T>> listToMultiMap(List<T> tList, Function<T, K> keyMethod) {
        if (tList == null || tList.isEmpty()) {
            return Collections.emptyMap();
        } else {
            Map<K, List<T>> map = new LinkedHashMap<>(tList.size());
            for (T t : tList) {
                map.computeIfAbsent(keyMethod.apply(t), k -> new ArrayList<>()).add(t);
            }
            return map;
        }
    }

    /**
     * 静默转换(异常和null值忽略)
     * @param tList
     * @param keyMethod
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> List<R> convertSilent(List<T> tList, Function<T, R> keyMethod) {
        if (tList == null || tList.isEmpty()) {
            return new ArrayList<>();
        } else {
            return tList.stream().map(item -> {
                try {
                    return keyMethod.apply(item);
                } catch (Exception e) {
                    return null;
                }
            }).filter(item -> item != null).collect(Collectors.toList());
        }
    }

    /**
     * 从map中获取缓存
     * @param cache
     * @param key
     * @param getValue
     * @param <K>
     * @param <V>
     * @return
     */
    public static <K, V> V getCacheByMap(Map<K, V> cache, K key, Function<K, V> getValue) {
        if (cache.containsKey(key)) {
            return cache.get(key);
        }
        V value = getValue.apply(key);
        cache.put(key, value);
        return value;
    }

    public static <A, B, C> void removeUnion(List<A> listA, Function<A, C> getAKey,
                                             List<B> listB, Function<B, C> getBKey) {
        if (CollectionUtils.isEmpty(listA) || CollectionUtils.isEmpty(listB)) {
            return;
        }
        Set<C> unionSet = new HashSet<>();
        if (listA.size() < listB.size()) {
            Set<C> listAKeys= listA.stream().map(getAKey).filter(Objects::nonNull).collect(Collectors.toSet());
            for (B item : listB) {
                C bKey = getBKey.apply(item);
                if (listAKeys.contains(bKey)) {
                    unionSet.add(bKey);
                }
            }
        } else {
            Set<C> listBKeys= listB.stream().map(getBKey).filter(Objects::nonNull).collect(Collectors.toSet());
            for (A item : listA) {
                C bKey = getAKey.apply(item);
                if (listBKeys.contains(bKey)) {
                    unionSet.add(bKey);
                }
            }
        }
        if (unionSet.isEmpty()) {
            return;
        }
        remove(listA, item -> unionSet.contains(getAKey.apply(item)));
        remove(listB, item -> unionSet.contains(getBKey.apply(item)));
    }

    public static <T> List<T> jsonToBeanOfList(List<JSONObject> list, Class<T> clazz) {
        return jsonToBeanOfList(list, clazz, null);
    }
    public static <T> List<T> jsonToBeanOfList(List<JSONObject> list, Class<T> clazz,
                                               Function<Stream<T>, Stream<T>> streamHandle) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        AtomicReference<JSONObject> failObjRef = new AtomicReference<>();
        try {
            Stream<T> stream = list.stream().filter(Objects::nonNull)
                    .map(item -> {
                        failObjRef.set(item);
                        return item.toJavaObject(clazz);
                    });
            if (streamHandle != null) {
                stream = streamHandle.apply(stream);
            }
            return stream.collect(Collectors.toList());
        } catch (Exception e) {
            log.error("jsonToBeanOfList error jsonArr {} targetClazz {}",
                    failObjRef.get() == null ? "null" : failObjRef.get().toJSONString(), clazz.getName(), e);
            throw e;
        }
    }

    public static <T> T listGet(List<T> list, int idx) {
        return list != null && idx < list.size() ? list.get(idx) : null;
    }

    public static <K, V, R> R mapGet(Map<K, V> map, K key, Function<V, R> convertor) {
        if (map == null) {
            return null;
        }
        return Optional.ofNullable(map.get(key)).map(convertor).orElse(null);
    }
}
