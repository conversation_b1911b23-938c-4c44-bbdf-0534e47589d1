package com.yxt.talent.bk.core.persona.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("bk_persona_theme_dimension")
public class PersonaThemeDimensionEntity extends CreatorEntity {
    /**
     * 画像主题维度主键
     */
    @TableId
    private String id;
    /**
     * 机构id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * 画像主题id
     */
    @TableField("theme_id")
    private String themeId;
    /**
     * 维度名称
     */
    @TableField("dimension_name")
    private String dimensionName;
    /**
     * '维度来源(0-内置,1-自建)
     */
    @TableField("dimension_source")
    private Integer dimensionSource;
}
