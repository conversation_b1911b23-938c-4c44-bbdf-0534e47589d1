package com.yxt.talent.bk.api.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import com.yxt.talent.bk.svc.usergroup.UserGroupService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: sptalentbkapi
 * @description: 规则计算器重新计算
 **/
@Component
@Slf4j
@AllArgsConstructor
public class UserGroupReCaculateHandler extends IJobHandler {

    private final UserGroupService userGroupService;

    @Override
    @XxlJob(value = "userGroupDelayCaculate")
    public ReturnT<String> execute(String param) throws Exception {
        try {
            List<String> needDelayOrgIds = userGroupService.getCacuDelayOrgs();
            if (CollectionUtils.isNotEmpty(needDelayOrgIds)){
                // 是否需要删除缓存
                boolean needDeleteCache = true;
                List<UserGroup> userGroupList = userGroupService.getGroupByJob(needDelayOrgIds);
                // 新的延迟计算机构规则
                List<String> newDelayOrgIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(userGroupList)){
                    Map<String,List<UserGroup>> usergroupMap = userGroupList.stream().collect(Collectors.groupingBy(UserGroup::getOrgId));

                    usergroupMap.forEach((orgId,list) -> {
                        if (userGroupService.canCanculete(orgId)){
                            list.forEach(userGroup -> userGroupService.caculateMembers(userGroup.getOrgId(), "job" ,userGroup.getId()));
                        }else {
                            newDelayOrgIds.add(orgId);
                        }
                    });

                    if (CollectionUtils.isNotEmpty(newDelayOrgIds)){
                        needDeleteCache = false;
                        log.info("重新计算机构ID：{}", BeanHelper.bean2Json(newDelayOrgIds, JsonInclude.Include.ALWAYS));
                        userGroupService.setCacuDelayOrgs(newDelayOrgIds);
                    }
                }

                if (needDeleteCache){
                    userGroupService.deleteCacuDelayOrgs();
                }

            }
        }catch (Exception e){
            log.error("userGroupCaculate error", e);
        }
        return ReturnT.SUCCESS;
    }

}
