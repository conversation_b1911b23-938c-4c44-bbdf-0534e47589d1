package com.yxt.talent.bk.svc.export;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.yxt.common.exception.ApiException;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.MapBuilder;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.I18nComponent;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.common.utils.DownInfoUtil;
import com.yxt.talent.bk.core.dashboard.bean.*;
import com.yxt.talent.bk.core.spmodel.entity.DwdDept;
import com.yxt.talent.bk.core.spmodel.mapper.DwdDeptMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserSkillRtStatisticsMapper;
import com.yxt.talent.bk.svc.dashboard.TalentBoardService;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.ubiz.export.bean.ExportFileInfo;
import com.yxt.ubiz.export.bean.ExportParam;
import com.yxt.ubiz.export.bean.SimpleTemplateParam;
import com.yxt.ubiz.export.common.enums.ExportFileTypeEnum;
import com.yxt.ubiz.export.core.AbstractExportWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.util.CellAddress;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/2
 */
@Service
@Slf4j
@AllArgsConstructor
public class DashBoardDeptDimExportService extends AbstractExportWrapper {

    private final ILock lockService;
    private final AuthService authService;
    private final I18nComponent i18nComponent;
    private final UserAuthService userAuthService;
    private final TalentBoardService talentBoardService;
    private final DwdUserSkillRtStatisticsMapper dwdUserSkillRtStatisticsMapper;
    private final DwdUserMapper dwdUserMapper;
    private final DwdDeptMapper dwdDeptMapper;

    private static final int MAX_LEASE_TIME = 100;

    private final Map<String, String> headData = MapBuilder.<String, String>newMapBuilder()
            .put("deptName", "apis.talentbk.dashboard.dept.dim.temp.deptName")
            .put("deptFullPathName", "apis.talentbk.dashboard.dept.dim.temp.deptFullPathName")
            .put("enableUserCount", "apis.talentbk.dashboard.dept.dim.temp.enableUserCount")
            .put("skillReachCount", "apis.talentbk.dashboard.dept.dim.temp.skillReachCount")
            .put("skillReachRate", "apis.talentbk.dashboard.dept.dim.temp.skillReachRate")
            .put("taskReachCount", "apis.talentbk.dashboard.dept.dim.temp.taskReachCount")
            .put("taskReachRate", "apis.talentbk.dashboard.dept.dim.temp.taskReachRate").immutableMap();

    @Override
    public Map<String, String> getExportHeader(Object o) {
        return headData;
    }

    @Override
    public void loadData(Object b, BiConsumer<List<?>, ExportParam> consumerList) {
        //查询所有的符合参数的部门数据
        CommonExportParam exportParam = JSON.parseObject(JSON.toJSONString(b), CommonExportParam.class);
        List<Long> groupIds = exportParam.getUserGroupIds();
        List<String> deptIds = exportParam.getDeptIds();
        String userId = exportParam.getOptUserId();
        String orgId = exportParam.getOrgId();
        List<DashBoardDeptDimExportVO> emptyDataList = new ArrayList<>();
        SimpleTemplateParam tempParam = new SimpleTemplateParam();
        tempParam.setHasRemarkRows(new boolean[]{false});
        tempParam.setHeadCells(Lists.newArrayList(CellAddress.A1));
        tempParam.setSheetNum(0);
        if (CollectionUtils.isEmpty(deptIds)) {
            //查询当前人员的权限范围
            UserDeptAuthDTO userDeptAuthDTO = talentBoardService.getUserAuthDept(orgId, userId, exportParam.getAdmin());
            if (!userDeptAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userDeptAuthDTO.getDeptIds())) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
            //如果是空，说明该用户拥有所有部门的权限
            deptIds = userDeptAuthDTO.getDeptIds();
        }
        //获取权限下的群组集合。如果没有传，则查询下权限下的群组数据
        if (CollectionUtils.isEmpty(groupIds)) {
            UserGroupAuthDTO userGroupAuthDTO = talentBoardService.getUserAuthGroupIds(orgId, userId,
                    exportParam.getAdmin());
            if (!userGroupAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userGroupAuthDTO.getGroupIds())) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
            //如果是空，说明该用户拥有所有群组的权限
            groupIds = userGroupAuthDTO.getGroupIds();
        }
        //        List<String> finalDeptIds = talentBoardService.transferDeptId(deptIds, orgId);
        List<String> finalDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
            finalDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
            //查询有部门ID，但是宽表不存在，则返回结果空数据，不然会被认为拥有所有权限
            if (CollectionUtils.isEmpty(finalDeptIds)) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
        }
        //查询权限部门下的人员和权限群组下的人 交集。根据交集的人员查询出能看到哪些部门ID集合
        List<String> deptIdsRes = talentBoardService.getDeptAndGroupByUserMix(orgId, groupIds, finalDeptIds);
        if (CollectionUtils.isEmpty(deptIdsRes)) {
            consumerList.accept(emptyDataList, tempParam);
            return;
        }
        //查询部门数据
        List<DwdDept> deptList = dwdDeptMapper.getByDeptIds(orgId, deptIdsRes);
        if (CollectionUtils.isEmpty(deptList)) {
            consumerList.accept(emptyDataList, tempParam);
            return;
        }
        List<List<DwdDept>> partDeptList = Lists.partition(deptList, 1000);
        List<Long> finalGroupIds = groupIds;
        partDeptList.forEach(deptItem -> {
            List<DashBoardDeptDimExportVO> exportData = new ArrayList<>();
            List<DashBoardDeptDimVO> resultData = new ArrayList<>();
            List<String> dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct()
                    .collect(Collectors.toList());
            //根据部门数据组装人员数据,查询部门已启用的人数
            List<DeptUserCountDTO> deptUserCountList = dwdUserMapper.getDeptUserCount(orgId, dwdDeptIds,
                    finalGroupIds);
            Map<String, Long> deptUserCountMap = StreamUtil.list2map(deptUserCountList,
                    DeptUserCountDTO::getDeptId, DeptUserCountDTO::getUserCount);
            List<DeptUserSkillRtReachDTO> deptUserSkillRtReachList = dwdUserSkillRtStatisticsMapper.getDeptUserSkillReachCount(
                    orgId, dwdDeptIds, finalGroupIds);
            Map<String, DeptUserSkillRtReachDTO> deptUserSkillRtReachMap = StreamUtil.list2map(deptUserSkillRtReachList,
                    DeptUserSkillRtReachDTO::getDeptId);
            talentBoardService.setDeptVO(deptList, resultData, deptUserCountMap, deptUserSkillRtReachMap);
            resultData.forEach(res -> {
                DashBoardDeptDimExportVO exportVO = new DashBoardDeptDimExportVO();
                BeanCopierUtil.copy(res, exportVO);
                exportVO.setDeptFullPathName(res.getDeptName());
                if (StringUtils.isBlank(res.getDeptName())) {
                    exportVO.setDeptName("-");
                } else {
                    String[] strArr = res.getDeptName().split("->");
                    exportVO.setDeptName(strArr[strArr.length - 1]);
                }
                exportVO.setSkillReachRate(exportVO.getSkillReachRate() + "%");
                exportVO.setTaskReachRate(exportVO.getTaskReachRate() + "%");
                exportData.add(exportVO);
            });
            consumerList.accept(exportData, tempParam);
            exportData.clear();
        });

    }

    public void exportDeptDimDashBoard(CommonExportParam searchParam, String orgId, String userId, String fullname) {
        //校验参数
        if (CollectionUtils.isEmpty(searchParam.getDeptIds()) && CollectionUtils.isEmpty(
                searchParam.getUserGroupIds())) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        }
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_DASHBOARD_DEPT_DIM_EXPORT, orgId, userId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                ExportFileInfo exportFileInfo = new ExportFileInfo();
                //设置业务的查询参数
                exportFileInfo.setQueryParams(searchParam);
                exportFileInfo.setTemplatePath("excel/dashboard_dept_dim.xlsx");
                //设置下载信息
                exportFileInfo.setDownInfo(DownInfoUtil.getDownInfo(ModuleConstants.FUNCTION_NAME));
                exportFileInfo.setLocale(authService.getLocale());
                exportFileInfo.setFileType(ExportFileTypeEnum.EXCEL);
                //不需要后缀.后缀由fileType的suffix决定,此文件名必须唯一
                String fileName = i18nComponent.getI18nValue(ExportConstants.DASHBOARD_DEPT_DIM_EXPORT_FILE_NAME);
                exportFileInfo.setName(fileName + "_" + System.currentTimeMillis());
                exportFileInfo.setFileName(fileName + System.nanoTime());
                exportFileInfo.setOrgId(orgId);
                exportFileInfo.setUserId(userId);
                exportFileInfo.setFullname(fullname);
                exportFileInfo.setExportTopic(TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_FILE);
                exportFileInfo.setUsemq(true);
                exportFileInfo.setDownloadI18n(true);
                export(exportFileInfo);
            } catch (Exception ex) {
                log.warn("exportDeptDimDashBoard  orgId={},userId={};err", orgId, userId, ex);
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_FILE_EXPORT_ING);
        }
    }
}
