package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@Schema(name = "我的团队-人才看板-部门组织人才库")
public class UserDeptUgroup4Get {

    @Schema(description = "组织人才库id")
    private String id;

    @Schema(description = "组织人才库id")
    private String name;

    @Schema(description = "组织人才库id")
    private long userCnt;

}
