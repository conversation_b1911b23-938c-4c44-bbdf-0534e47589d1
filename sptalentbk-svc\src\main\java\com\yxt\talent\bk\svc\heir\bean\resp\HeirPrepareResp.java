package com.yxt.talent.bk.svc.heir.bean.resp;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Locale;

@Data
public class HeirPrepareResp {

    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @Schema(description = "序号")
    private Integer orderIndex;
    @Schema(description = "自定义名称（简体）")
    private String levelName1;
    @Schema(description = "自定义名称（英文）")
    private String levelName2;
    @Schema(description = "自定义名称（繁体）")
    private String levelName3;
    @Schema(description = "颜色设置")
    private String colorCode;
    @Schema(description = "备注")
    private String remark;

    public String queryLevelName(Locale locale) {
        if (Locale.SIMPLIFIED_CHINESE.equals(locale)) {
            return levelName1;
        } else if (Locale.TRADITIONAL_CHINESE.equals(locale)) {
            return levelName3;
        } else if (Locale.ENGLISH.equals(locale)) {
            return levelName2;
        } else{
            return levelName1;
        }
    }
}
