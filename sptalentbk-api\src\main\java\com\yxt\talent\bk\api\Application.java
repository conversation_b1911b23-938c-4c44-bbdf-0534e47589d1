package com.yxt.talent.bk.api;

import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.udp.repo.UdpDeptRepository;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@Slf4j
@EnableAsync
@EnableDiscoveryClient
@ComponentScan("com.yxt")
@SpringBootApplication(exclude = com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration.class)
@ImportAutoConfiguration({FeignAutoConfiguration.class})
public class Application {

    public static void main(String[] args) {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        SpringApplication.run(Application.class, args);
        CommonUtils.stopLogFileAppenderOnNative();
    }

    @Bean
    @ConditionalOnProperty(name = "spring.main.lazy-initialization", havingValue = "true")
    public BeanFactoryPostProcessor lazyInitBeanFactoryPostProcessor() {
        return new BeanFactoryPostProcessor() {
            @Override
            public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
                for (String beanName : beanFactory.getBeanDefinitionNames()) {
                    BeanDefinition beanDefinition = beanFactory.getBeanDefinition(beanName);
                    String className = beanDefinition.getBeanClassName();
                    if (className == null) {
                        className = StringPool.EMPTY;
                    }
                    if ("springContextHolder".equals(beanName)
                            || "xxlJobExecutor".equals(beanName)
                            || className.startsWith("com.yxt.common.")
                            || className.startsWith("com.xxl.")
                            || className.startsWith("com.yxt.job.")
                            || className.equals(UdpDeptRepository.class.getName())) {
                        log.info("beanName {} lazyInitFalse className {}", beanName, className);
                        beanDefinition.setLazyInit(false);
                    }
                }
            }
        };
    }
}
