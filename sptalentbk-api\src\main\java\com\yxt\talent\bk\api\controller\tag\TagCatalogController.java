package com.yxt.talent.bk.api.controller.tag;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.svc.catalog.CatalogService;
import com.yxt.talent.bk.svc.tag.bean.TagCatalog4Create;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 分类控制层
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Deprecated
@Tag(name = "分类控制层")
@RestController
@RequestMapping("/mgr/tagcatalog")
public class TagCatalogController extends BaseController {

    @Resource
    private CatalogService catalogService;

    @Deprecated
    @Operation(summary = "查询分类表列表(查询默认+自建【不需要内置】)")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/list")
    public List<TagCatalog4Create> list() {
        UserCacheBasic userCache = getUserCacheBasic();
        return catalogService.list4Vo(userCache.getOrgId());
    }

    @Deprecated
    @Operation(summary = "根据id查询分类表")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @GetMapping("/getone/{id}")
    public TagCatalog4Create getOne(@PathVariable String id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return catalogService.getById(userCache.getOrgId(), id);
    }

    @Deprecated
    @Operation(summary = "更新分类表")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN)
    @PostMapping("/saveorupdate")
    public TagCatalog saveOrUpdate(@RequestBody TagCatalog4Create catalog) {
        UserCacheBasic userCache = getUserCacheBasic();
        return catalogService.saveOrUpdate(userCache.getOrgId(), userCache.getUserId(), catalog);
    }

    @Deprecated
    @Operation(summary = "根据id删除分类表")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN)
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable String id) {
        UserCacheBasic userCache = getUserCacheBasic();
        return catalogService.removeById(userCache.getOrgId(), id);
    }
}

