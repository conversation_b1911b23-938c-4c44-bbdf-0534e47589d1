package com.yxt.talent.bk.common.enums;

/**
 * 人才标签
 */
public enum TagSearchEnum {
    /**
     * 标签来源
     */
    ORDINARYTAG("ordinaryTag","普通标签"),
    OTHERTAG("otherTag", "其他标签"),
    TALENTRV("talentRv","人才盘点"),
    DEPARTMENT("departmentId", "部门"),
    POSITION("positionId","岗位"),
    GRAND_NAME("gradeName","职级"),
    AGE_GROUP("ageGroup","年龄段"),
    ENTRY_DATE("entryDate", "入职时间"),
    SEX("sex","性别") ,
    LEADER_SHIP_STYLE("leadershipStyle","领导风格"),
    CERTS("certs","获得证书"),
    O2OS("o2os", "完成培训"),
    EXAMS("exams", "通过考试"),
    PERFORMANCE_LEVEL("performanceLevel","绩效等级"),
    ABILITY_LEVEL("abilityLevel","能力等级"),
    POTENTIAL_LEVEL("potentialLevel","潜力等级"),
    REVIEW_RESULT("reviewResult", "盘点结果")
    ;

    private String key;

    private String name;

    TagSearchEnum(String key, String name){
        this.key = key;
        this.name = name;
    }
    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }

}
