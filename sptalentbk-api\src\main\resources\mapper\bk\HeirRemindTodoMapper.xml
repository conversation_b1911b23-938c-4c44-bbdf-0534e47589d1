<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirRemindTodoMapper">
    <sql id="all_columns">
        a.id,
        a.org_id,
        a.pos_id,
        a.remind_user_id,
        a.target_user_id,
        a.target_record_id,
        a.todo_id,
        a.todo_status,
        a.remind_param,
        a.create_user_id,
        a.create_time,
        a.update_user_id,
        a.update_time,
        a.deleted
    </sql>

    <select id="getRemindTodo" resultType="com.yxt.talent.bk.core.heir.entity.HeirRemindTodoEntity">
        select <include refid="all_columns"/> from bk_heir_remind_todo a where
        a.org_id = #{orgId} and a.pos_id = #{posId}
        and a.remind_user_id = #{remindUserId} and a.target_record_id = #{targetRecordId} and a.deleted = 0 limit 1
    </select>

    <select id="tgtRecordRemind" resultType="com.yxt.talent.bk.core.heir.bean.HeirRemindTodoIdDTO">
        select id,todo_id,remind_user_id from bk_heir_remind_todo where org_id = #{orgId} and pos_id = #{posId}
        and deleted = 0 and target_record_id in
        <foreach collection="targetRecordIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="batchEndTodo">
        update bk_heir_remind_todo set deleted = 1,todo_status = #{todoStatus},update_time = now() where org_id = #{orgId} and id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
