package com.yxt.talent.bk.api.component;

import com.yxt.common.exception.ApiException;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.persona.repo.PersonaThemeDimensionTagMapRepository;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.TagValueRepository;
import com.yxt.talent.bk.svc.tag.bean.Tag4Create;
import com.yxt.talent.bk.svc.tag.bean.TagValue4Create;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户标签component
 *
 * <AUTHOR>
 * @since 2022/8/15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TagComponent {

    private final TagRepository tagRepository;
    private final TagValueRepository tagValueRepository;
    private final UserTagComponent userTagComponent;
    private final PersonaThemeDimensionTagMapRepository tagMapRepository;

    /**
     * 编辑标签
     *
     * @param orgId
     * @param userId
     * @param tag4Create
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public Tag4Create editTag(String orgId, String userId, Tag4Create tag4Create) {
        //保存基本信息
        TagEntity tagEntity = tagRepository.queryTagById(orgId, tag4Create.getId());
        Validate.isNotNull(tagEntity, "apis.talentbk.tag.not.exist");
        BeanCopierUtil.copy(tag4Create, tagEntity);
        EntityUtil.setUpdatedInfo(userId, tagEntity);
        tagRepository.saveOrUpdate(tagEntity);
        //如果是分层标签，处理下分层标签值
        if (1 == tag4Create.getTagType()) {
            //读取分层标签列表
            if (CollectionUtils.isNotEmpty(tag4Create.getTagValueList())) {
                validateTagValueNames(tag4Create.getTagValueList());
                List<TagValueEntity> tagValueEntityList = new ArrayList<>();
                //查询当前db中的标签值数据
                List<TagValueEntity> dbList = tagValueRepository.getTagValuesByTagId(orgId, tagEntity.getId());
                List<String> existIds = new ArrayList<>();
                List<String> dbIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dbList)) {
                    dbIds = dbList.stream().map(TagValueEntity::getId).collect(Collectors.toList());
                }
                tag4Create.getTagValueList().forEach(e -> {
                    TagValueEntity tagValueEntity;
                    if (StringUtils.isNotEmpty(e.getId())) {
                        existIds.add(e.getId());
                        tagValueEntity = tagValueRepository.queryTagById(orgId, e.getId());
                        Validate.isNotNull(tagValueEntity, "apis.talentbk.tagvalue.not.exist");
                        EntityUtil.setUpdatedInfo(userId, tagValueEntity);
                    } else {
                        tagValueEntity = new TagValueEntity();
                        String valueId = ApiUtil.getUuid();
                        EntityUtil.setCreateInfo(userId, tagValueEntity);
                        tagValueEntity.setId(valueId);
                        e.setId(valueId);
                        tagValueEntity.setTagId(tagEntity.getId());
                        tagValueEntity.setOrgId(orgId);
                    }
                    tagValueEntity.setValueName(e.getValueName());
                    tagValueEntity.setOrderIndex(e.getOrderIndex());
                    tagValueEntityList.add(tagValueEntity);
                });
                if (CollectionUtils.isNotEmpty(dbList)) {
                    List<String> toDeleteIds = dbIds.stream().filter(e -> !existIds.contains(e))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(toDeleteIds)) {
                        tagValueRepository.removeByIds(orgId, toDeleteIds);
                        removeTagValueList(orgId, tag4Create.getId(), userId, toDeleteIds);
                    }
                }
                tagValueRepository.saveOrUpdateBatch(tagValueEntityList);
            } else {
                //至少一个分层标签
                throw new ApiException("apis.talentbk.tagvlue.list.contains.atleast.one.value");
            }
        } else {
            //普通标签的标签值value同步更新
            List<TagValueEntity> tagValueEntityList = tagValueRepository.getTagValuesByTagId(orgId, tag4Create.getId());
            if (CollectionUtils.isNotEmpty(tagValueEntityList)) {
                //理论上只存在一个
                TagValueEntity tagValueEntity = tagValueEntityList.get(0);
                EntityUtil.setUpdatedInfo(userId, tagValueEntity);
                tagValueEntity.setValueName(tag4Create.getTagName());
                tagValueRepository.saveOrUpdate(tagValueEntity);
                //返回给前端
                List<TagValue4Create> tagValueList = new ArrayList<>(1);
                TagValue4Create tagValue4Create = new TagValue4Create();
                tagValue4Create.setId(tagValueEntity.getId());
                tagValue4Create.setOrderIndex(tagValueEntity.getOrderIndex());
                tagValue4Create.setValueName(tagValueEntity.getValueName());
                tagValueList.add(tagValue4Create);
                tag4Create.setTagValueList(tagValueList);
            }
        }
        return tag4Create;
    }

    /**
     * 删除标签
     *
     * @param orgId
     * @param tagId
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void removeTag(String orgId, String tagId, String userId) {
        TagEntity tagEntity = tagRepository.queryTagById(orgId, tagId);
        Validate.isNotNull(tagEntity, "apis.talentbk.tag.not.exist");
        //只有禁用的才可以被删除
        Validate.isTrue(0 == tagEntity.getEnable(), "apis.talentbk.tag.to.delete.must.be.disabled");
        List<TagValueEntity> tagValueList = tagValueRepository.getTagValuesByTagId(orgId, tagId);
        if (CollectionUtils.isNotEmpty(tagValueList)) {
            removeTagValueList(orgId, tagId, userId,
                    tagValueList.stream().map(TagValueEntity::getId).collect(Collectors.toList()));
        }
        //删除标签
        tagRepository.removeById(orgId, tagId);
        //删除维度标签关系
        tagMapRepository.removeByTag(orgId, tagId);
    }

    /**
     * 删除分层标签下的信息包含人员
     *
     * @param orgId
     * @param tagValueIds
     */
    private void removeTagValueList(String orgId, String tagId, String userId, List<String> tagValueIds) {
        if (CollectionUtils.isNotEmpty(tagValueIds)) {
            //删除
            tagValueRepository.removeByIds(orgId, tagValueIds);
        }
        //删除标签下的人员
        userTagComponent.delTagValueId(orgId, userId, tagId, tagValueIds);
    }

    /**
     * 校验分层标签值是否有重名
     *
     * @param tagValueList
     */
    private void validateTagValueNames(List<TagValue4Create> tagValueList) {
        Set<String> set = tagValueList.stream().map(TagValue4Create::getValueName).collect(Collectors.toSet());
        Validate.isTrue(set.size() == tagValueList.size(), "apis.talentbk.tagvlue.name.conflict");
    }
}

