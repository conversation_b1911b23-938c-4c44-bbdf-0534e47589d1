package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class DashBoardDeptDimVO {

    @Schema(description = "部门ID")
    private String deptId;
    @Schema(description = "三方部门ID")
    private String thirdDeptId;
    @Schema(description = "部门名称")
    private String deptName;
    @Schema(description = "启用人数")
    private long enableUserCount;
    @Schema(description = "能力达标人数")
    private long skillReachCount;
    @Schema(description = "能力达标率")
    private String skillReachRate;
    @Schema(description = "任务达标人数")
    private long taskReachCount;
    @Schema(description = "任务达标率")
    private String taskReachRate;
}
