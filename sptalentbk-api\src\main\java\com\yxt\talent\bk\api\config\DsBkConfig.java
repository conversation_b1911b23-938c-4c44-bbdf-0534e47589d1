package com.yxt.talent.bk.api.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @since 2023/1/16
 */
@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
public class DsBkConfig {

    @Bean("bkDataSource")
    @ConfigurationProperties("spring.datasource.druid.bk")
    public DataSource bkDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(TalentBkConstants.DB_1_TRANSACTION_MANAGER)
    public PlatformTransactionManager db1TransactionManager(@Qualifier("bkDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
