package com.yxt.talent.bk.svc.heir.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

public enum CycleEnum {
    /**
     * 循环类型
     */
    EVERYDAY("everyday","每天", item -> "每天"),
    EVERYWEEK("everyweek","每周", weekNo -> "每周-周" + weekNo),
    EVERYMONTH("everymonth","每月", dayNo -> "每月-" + dayNo + "号");

    private final String code;
    private final String name;
    private final Function<Integer, String> descFunc;

    CycleEnum(String code, String name, Function<Integer, String> descFunc) {
        this.code = code;
        this.name = name;
        this.descFunc = descFunc;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Function<Integer, String> getDescFunc() {
        return descFunc;
    }

    public static CycleEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (CycleEnum value : CycleEnum.values()) {
                if (code.equals(value.code)) {
                    return value;
                }
            }
        }
        return null;
    }
}
