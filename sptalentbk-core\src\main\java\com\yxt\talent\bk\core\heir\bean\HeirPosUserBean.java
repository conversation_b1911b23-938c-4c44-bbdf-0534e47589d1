package com.yxt.talent.bk.core.heir.bean;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import lombok.Data;

/**
 * HeirRefUserIdBean
 *
 * <AUTHOR> geyan
 * @Date 17/8/23 9:26 am
 */
@Data
public class HeirPosUserBean implements UdpLangSupport {
    private Long id;
    private String posId;
    private String userId;
    private String username;
    private String fullname;
    private String ImgUrl;
    private Integer deleted;
    private int updateDeleted;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(userId, fullname, this::setFullname);
    }
}
