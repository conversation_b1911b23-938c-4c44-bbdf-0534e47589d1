package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.talent.bk.common.constants.PoolConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
@Schema(name = "人才池保存")
public class Pool4Save {
    @Schema(description = "主键【编辑必填】")
    private String id;
    @Schema(description = "人才池名称")
    @NotBlank(message = PoolConstants.VERIFY_SAVE_POOLNAME_SIZE)
    @Size(max = 50, min = 2, message =  PoolConstants.VERIFY_SAVE_POOLNAME_SIZE)
    private String poolName;
    @Schema(description = "分类Id")
    private String catalogId;
    @Schema(description = "预期人数")
    private Integer expectNum;
    @Schema(description = "备注")
    @Size(max = 500,  message =  PoolConstants.VERIFY_SAVE_REMARK_SIZE)
    private String remark;
    @Schema(description = "管理者Id列表")
    private List<String> mgrIdList;
}
