package com.yxt.talent.bk.core.heir.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirRefUserIdBean
 *
 * <AUTHOR> geyan
 * @Date 17/8/23 9:26 am
 */
@Data
public class PosUserBriefBean implements UdpLangSupport {
    private String userId;
    @Schema(description = "账号")
    private String username;
    @Schema(description = "姓名")
    private String fullName;
    @Schema(description = "用户头像")
    private String imgUrl;
    private String deptId;
    private String deptName;
    @Schema(description = "0:进行中，1:已退出")
    private Integer heirStatus;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "准备度规则等级id")
    private Long prepareLevelId;
    @Schema(description = "准备等级名称")
    private String levelName;
    @Schema(description = "准备等级颜色")
    private String levelColor;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(userId, fullName, this::setFullName);
    }

    @Override
    public UdpLangUnitBean deptLangProperty() {
        return new UdpLangUnitBean(deptId, deptName, this::setDeptName);
    }
}
