package com.yxt.talent.bk.svc.export;

import com.yxt.spsdk.common.annotation.SpExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */
@Data
public class ExportRtDetailDTO {
    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.rt.taskName", name = "任务名称", index = 0)
    private String taskName;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.rt.parentTaskName", name = "父级任务名称", index = 1)
    private String parentTaskName;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.rt.taskDesc", name = "任务描述", index = 2)
    private String taskDesc;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.rt.taskResult", name = "评估结果", index = 3)
    private String taskResult;

    @SpExcelProperty(nameKey = "apis.talentbk.dashboard.personal.detail.rt.taskReached", name = "是否达标", index = 4)
    private String taskReached;


}
