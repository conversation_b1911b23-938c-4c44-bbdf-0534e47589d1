package com.yxt.talent.bk.svc.mq.constant;

import com.yxt.udpfacade.constants.UdpRocketMqConstants;

public class HeirRocketMqConstants {
    private HeirRocketMqConstants() {
        //NO SONAR
    }

    public static final String GROUP_PREFIX = "sptalentbk-group-";

    public static final String TOPIC_HEIR_READINESS_COMPUTE = "heir-readiness-compute";

    public static final String TOPIC_HEIR_READINESS_REMIND = "heir-readiness-remind";

    /**
     * udp用户删除通知
     */
    public static final String TOPIC_C_UDP_USER_DELETE = UdpRocketMqConstants.TOPIC_UDP_USER_DELETE;
}
