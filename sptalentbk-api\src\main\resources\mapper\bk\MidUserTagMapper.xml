<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.test.mapper.MidUserTagMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.test.entity.MidUserTag">
    <!--@mbg.generated-->
    <!--@Table bk_mid_user_tag-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="user_id" jdbcType="CHAR" property="userId" />
    <result column="performance_trend" jdbcType="CHAR" property="performanceTrend" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="fullname" jdbcType="VARCHAR" property="fullname" />
    <result column="sex" jdbcType="TINYINT" property="sex" />
    <result column="birthday" jdbcType="TIMESTAMP" property="birthday" />
    <result column="hire_date" jdbcType="TIMESTAMP" property="hireDate" />
    <result column="user_status" jdbcType="TINYINT" property="userStatus" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="dept_id" jdbcType="CHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="position_id" jdbcType="CHAR" property="positionId" />
    <result column="position_name" jdbcType="VARCHAR" property="positionName" />
    <result column="grade_id" jdbcType="CHAR" property="gradeId" />
    <result column="grade_name" jdbcType="VARCHAR" property="gradeName" />
    <result column="exams" jdbcType="LONGVARCHAR" property="exams" />
    <result column="certs" jdbcType="LONGVARCHAR" property="certs" />
    <result column="o2os" jdbcType="LONGVARCHAR" property="o2os" />
    <result column="advantage_comm_abliity" jdbcType="LONGVARCHAR" property="advantageCommAbliity" />
    <result column="inferiority_comm_abliity" jdbcType="LONGVARCHAR" property="inferiorityCommAbliity" />
    <result column="other_comm_abliity" jdbcType="LONGVARCHAR" property="otherCommAbliity" />
    <result column="advantage_pro_abliity" jdbcType="LONGVARCHAR" property="advantageProAbliity" />
    <result column="inferiority_pro_abliity" jdbcType="LONGVARCHAR" property="inferiorityProAbliity" />
    <result column="other_pro_abliity" jdbcType="LONGVARCHAR" property="otherProAbliity" />
    <result column="profession_driver" jdbcType="LONGVARCHAR" property="professionDriver" />
    <result column="character_traits" jdbcType="LONGVARCHAR" property="characterTraits" />
    <result column="tenure_risk" jdbcType="LONGVARCHAR" property="tenureRisk" />
    <result column="leadership_style" jdbcType="LONGVARCHAR" property="leadershipStyle" />
    <result column="user_no" jdbcType="VARCHAR" property="userNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, user_id, performance_trend, username,
    fullname, sex, birthday, hire_date, user_status, deleted, dept_id, dept_name, position_id,
    position_name, grade_id, grade_name, exams, certs, o2os, advantage_comm_abliity,
    inferiority_comm_abliity, other_comm_abliity, advantage_pro_abliity, inferiority_pro_abliity,
    other_pro_abliity, profession_driver, character_traits, tenure_risk, leadership_style,
    user_no
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update bk_mid_user_tag
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgId != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.orgId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.userId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="performance_trend = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.performanceTrend != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.performanceTrend,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>


      <trim prefix="username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.username != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.username,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fullname = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fullname != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.fullname,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sex = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sex != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.sex,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="birthday = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.birthday != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.birthday,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="hire_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hireDate != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.hireDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userStatus != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.userStatus,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deleted != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.deleted,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deptId != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.deptId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="dept_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deptName != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.deptName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="position_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.positionId != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.positionId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="position_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.positionName != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.positionName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="grade_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gradeId != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.gradeId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="grade_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gradeName != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.gradeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="exams = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.exams != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.exams,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="certs = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.certs != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.certs,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="o2os = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.o2os != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.o2os,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="advantage_comm_abliity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.advantageCommAbliity != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.advantageCommAbliity,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="inferiority_comm_abliity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inferiorityCommAbliity != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.inferiorityCommAbliity,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="other_comm_abliity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.otherCommAbliity != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.otherCommAbliity,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="advantage_pro_abliity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.advantageProAbliity != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.advantageProAbliity,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="inferiority_pro_abliity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inferiorityProAbliity != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.inferiorityProAbliity,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="other_pro_abliity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.otherProAbliity != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.otherProAbliity,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="profession_driver = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.professionDriver != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.professionDriver,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="character_traits = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.characterTraits != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.characterTraits,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tenure_risk = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenureRisk != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.tenureRisk,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="leadership_style = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.leadershipStyle != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.leadershipStyle,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userNo != null">
            when id = #{item.id,jdbcType=CHAR} then #{item.userNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=CHAR}
    </foreach>
  </update>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="updated" index="index" separator=";">
            update bk_mid_user_tag
            <set>
                <if test="updated.id != null">
                    id = #{updated.id,jdbcType=CHAR},
                </if>
                <if test="updated.orgId != null">
                    org_id = #{updated.orgId,jdbcType=CHAR},
                </if>
                <if test="updated.userId != null">
                    user_id = #{updated.userId,jdbcType=CHAR},
                </if>
                <if test="updated.performanceTrend != null">
                    performance_trend = #{updated.performanceTrend,jdbcType=CHAR},
                </if>
                <if test="updated.username != null">
                    username = #{updated.username,jdbcType=VARCHAR},
                </if>
                <if test="updated.fullname != null">
                    fullname = #{updated.fullname,jdbcType=VARCHAR},
                </if>
                <if test="updated.sex != null">
                    sex = #{updated.sex,jdbcType=TINYINT},
                </if>
                <if test="updated.birthday != null">
                    birthday = #{updated.birthday,jdbcType=TIMESTAMP},
                </if>
                <if test="updated.hireDate != null">
                    hire_date = #{updated.hireDate,jdbcType=TIMESTAMP},
                </if>
                <if test="updated.userStatus != null">
                    user_status = #{updated.userStatus,jdbcType=TINYINT},
                </if>
                <if test="updated.deleted != null">
                    deleted = #{updated.deleted,jdbcType=TINYINT},
                </if>
                <if test="updated.deptId != null">
                    dept_id = #{updated.deptId,jdbcType=CHAR},
                </if>
                <if test="updated.deptName != null">
                    dept_name = #{updated.deptName,jdbcType=VARCHAR},
                </if>
                <if test="updated.positionId != null">
                    position_id = #{updated.positionId,jdbcType=CHAR},
                </if>
                <if test="updated.positionName != null">
                    position_name = #{updated.positionName,jdbcType=VARCHAR},
                </if>
                <if test="updated.gradeId != null">
                    grade_id = #{updated.gradeId,jdbcType=CHAR},
                </if>
                <if test="updated.gradeName != null">
                    grade_name = #{updated.gradeName,jdbcType=VARCHAR},
                </if>
                <if test="updated.exams != null">
                    exams = #{updated.exams,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.certs != null">
                    certs = #{updated.certs,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.o2os != null">
                    o2os = #{updated.o2os,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.advantageCommAbliity != null">
                    advantage_comm_abliity = #{updated.advantageCommAbliity,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.inferiorityCommAbliity != null">
                    inferiority_comm_abliity = #{updated.inferiorityCommAbliity,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.otherCommAbliity != null">
                    other_comm_abliity = #{updated.otherCommAbliity,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.advantageProAbliity != null">
                    advantage_pro_abliity = #{updated.advantageProAbliity,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.inferiorityProAbliity != null">
                    inferiority_pro_abliity = #{updated.inferiorityProAbliity,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.otherProAbliity != null">
                    other_pro_abliity = #{updated.otherProAbliity,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.professionDriver != null">
                    profession_driver = #{updated.professionDriver,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.characterTraits != null">
                    character_traits = #{updated.characterTraits,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.tenureRisk != null">
                    tenure_risk = #{updated.tenureRisk,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.leadershipStyle != null">
                    leadership_style = #{updated.leadershipStyle,jdbcType=LONGVARCHAR},
                </if>
                <if test="updated.userNo != null">
                    user_no = #{updated.userNo,jdbcType=VARCHAR},
                </if>
            </set>
            where id=#{updated.id,jdbcType=CHAR}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2023-06-12-->
  <insert id="insertList">
        INSERT INTO bk_mid_user_tag(
        id,
        org_id,
        user_id,
        performance_trend,
        username,
        fullname,
        sex,
        birthday,
        hire_date,
        user_status,
        deleted,
        dept_id,
        dept_name,
        position_id,
        position_name,
        grade_id,
        grade_name,
        exams,
        certs,
        o2os,
        advantage_comm_abliity,
        inferiority_comm_abliity,
        other_comm_abliity,
        advantage_pro_abliity,
        inferiority_pro_abliity,
        other_pro_abliity,
        profession_driver,
        character_traits,
        tenure_risk,
        leadership_style,
        user_no
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id,jdbcType=CHAR},
            #{element.orgId,jdbcType=CHAR},
            #{element.userId,jdbcType=CHAR},
            #{element.performanceTrend,jdbcType=CHAR},
            #{element.username,jdbcType=VARCHAR},
            #{element.fullname,jdbcType=VARCHAR},
            #{element.sex,jdbcType=TINYINT},
            #{element.birthday,jdbcType=TIMESTAMP},
            #{element.hireDate,jdbcType=TIMESTAMP},
            #{element.userStatus,jdbcType=TINYINT},
            #{element.deleted,jdbcType=TINYINT},
            #{element.deptId,jdbcType=CHAR},
            #{element.deptName,jdbcType=VARCHAR},
            #{element.positionId,jdbcType=CHAR},
            #{element.positionName,jdbcType=VARCHAR},
            #{element.gradeId,jdbcType=CHAR},
            #{element.gradeName,jdbcType=VARCHAR},
            #{element.exams,jdbcType=LONGVARCHAR},
            #{element.certs,jdbcType=LONGVARCHAR},
            #{element.o2os,jdbcType=LONGVARCHAR},
            #{element.advantageCommAbliity,jdbcType=LONGVARCHAR},
            #{element.inferiorityCommAbliity,jdbcType=LONGVARCHAR},
            #{element.otherCommAbliity,jdbcType=LONGVARCHAR},
            #{element.advantageProAbliity,jdbcType=LONGVARCHAR},
            #{element.inferiorityProAbliity,jdbcType=LONGVARCHAR},
            #{element.otherProAbliity,jdbcType=LONGVARCHAR},
            #{element.professionDriver,jdbcType=LONGVARCHAR},
            #{element.characterTraits,jdbcType=LONGVARCHAR},
            #{element.tenureRisk,jdbcType=LONGVARCHAR},
            #{element.leadershipStyle,jdbcType=LONGVARCHAR},
            #{element.userNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>
