spring.application.name=sptalentbkapi
server.port=9627
spring.cloud.nacos.config.shared-configs[0].data-id=application.properties
spring.cloud.nacos.config.shared-configs[0].refresh=true
spring.cloud.nacos.config.shared-configs[1].data-id=application-shared-endpoint.properties
spring.cloud.nacos.config.shared-configs[1].group=DEFAULT_GROUP
spring.cloud.nacos.config.shared-configs[1].refresh=true
spring.cloud.nacos.config.shared-configs[2].data-id=application-shared-security.properties
spring.cloud.nacos.config.shared-configs[2].group=DEFAULT_GROUP
spring.cloud.nacos.config.shared-configs[2].refresh=true
spring.cloud.nacos.config.shared-configs[3].data-id=${spring.application.name}-biz.properties
spring.cloud.nacos.config.shared-configs[3].group=DEFAULT_GROUP
spring.cloud.nacos.config.shared-configs[3].refresh=true

spring.cloud.nacos.discovery.enabled=${nacos.discover.enable:true}
spring.cloud.nacos.discovery.server-addr=${nacos.url:10.130.9.118:8848}
spring.cloud.nacos.discovery.namespace=${nacos.namespace:}
spring.cloud.nacos.config.enabled=${nacos.discover.enable:true}
spring.cloud.nacos.config.server-addr=${nacos.url:10.130.9.118:8848}
spring.cloud.nacos.config.namespace=${nacos.config.namespace:}

nacos.core.auth.caching.enabled=true
spring.cloud.nacos.config.username=${nacos.rw.username:}
spring.cloud.nacos.config.password=${nacos.rw.password:}
spring.cloud.nacos.discovery.username=${nacos.rw.username:}
spring.cloud.nacos.discovery.password=${nacos.rw.password:}

spring.messages.basename=sptalentbk-messages,talent-messages,common-messages
ikit.i18n.bundle.fallback-basename=message,i18n
#spring.cloud.sentinel.datasource.ds1.nacos.username=${nacos.rw.username:}
#spring.cloud.sentinel.datasource.ds1.nacos.password=${nacos.rw.password:}
