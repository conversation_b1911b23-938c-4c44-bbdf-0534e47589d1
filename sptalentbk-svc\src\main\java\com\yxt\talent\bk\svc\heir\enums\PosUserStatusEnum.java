package com.yxt.talent.bk.svc.heir.enums;

/**
 * 继任状态
* <AUTHOR>
*  @date 2023/8/16
**/
public enum PosUserStatusEnum {

    /** 进行中 */
    GOING(0, "apis.talentbk.heir.status_0"),
    /** 已退出 */
    EXIT(1, "apis.talentbk.heir.status_1");

    private final int value;
    private final String nameKey;

    PosUserStatusEnum(int value, String nameKey) {
        this.value = value;
        this.nameKey = nameKey;
    }

    public int getValue() {
        return value;
    }

    public static String getNameKeyByType(Integer value) {
        if (value != null) {
            for (PosUserStatusEnum posUserStatusEnum : PosUserStatusEnum.values()) {
                if (value.equals(posUserStatusEnum.value)) {
                    return posUserStatusEnum.nameKey;
                }
            }
        }
        return null;
    }
}
