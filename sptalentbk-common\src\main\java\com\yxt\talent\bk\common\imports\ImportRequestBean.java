package com.yxt.talent.bk.common.imports;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;


@Data
@AllArgsConstructor
public class ImportRequestBean {

	@Schema(description = "导入文件ID", example = "dc9de391-e4e9-4704-a881-ba7b74a37e57")
	private String fileId;

	@Schema(description = "主体目标ID", example = "dc9de391-e4e9-4704-a881-ba7b74a37e57")
	private String targetId;

}
