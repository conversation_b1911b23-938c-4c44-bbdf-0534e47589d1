package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.Data;

/**
 * PoolUserOut4Log
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 1:52 pm
 */
@Data
public class PoolUserOut4Log {
    private String userFullName;
    @AuditLogField(name = "出池人员", orderIndex = 0, fullEqual = true)
    private String userDesc;
    @AuditLogField(name = "出池类型", orderIndex = 1)
    private String outDesc;
    @AuditLogField(name = "出池去向", orderIndex = 2)
    private String remark;
}
