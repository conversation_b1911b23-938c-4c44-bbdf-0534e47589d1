package com.yxt.talent.bk.core.usergroup.bean;

import com.yxt.talent.bk.core.heir.bean.HeirUserPosBriefBean;
import com.yxt.talentrvfacade.bean.UserDimGrid4Info;
import com.yxt.talentrvfacade.bean.UserDimResult4Facade;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Data
public class UserCompareDetail {
    @Schema(description = "用户 ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;
    @Schema(description = "头像", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String imgUrl;
    @Schema(description = "姓名", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullname;
    @Schema(description = "部门", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deptName;
    @Schema(description = "岗位", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String positionName;
    @Schema(description = "性别(0-未知 1-男 2-女)", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer gender;
    @Schema(description = "性别", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String genderName;
    @Schema(description = "职级", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gradeName;
    @Schema(description = "政治面貌", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String political;
    @Schema(description = "学历", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String education;
    @Schema(description = "毕业院校", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String university;
    @Schema(description = "专业", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String major;
    @Schema(description = "工作年限", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer workYears;
    @Schema(description = "入职时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime entryTime;
    @Schema(description = "司龄", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal serviceYears;
    @Schema(description = "学习培训", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<UserCompareDetailTraining> trainings = new ArrayList<>();
    @Schema(description = "奖惩", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<UserCompareDetailReward> rewards = new ArrayList<>();
    @Schema(description = "绩效考核", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<UserComparePerf4Get> perfs = new ArrayList<>();

    @Schema(description = "继任岗位", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HeirUserPosBriefBean> positionList = new ArrayList<>();
    @Schema(description = "继任部门", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HeirUserPosBriefBean> deptList = new ArrayList<>();

    @Schema(description = "盘点结果", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private UserDimResult4Facade userDimResult4Get;
    @Schema(description = "盘点九宫格", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private UserDimGrid4Info userDimGrid4Info;


    public void setGender(Integer gender) {
        this.gender = gender;
        //0-未知 1-男 2-女
        switch (this.gender){
            case 0:
                this.genderName = "未知";
                break;
            case 1:
                this.genderName = "男";
                break;
            case 2:
                this.genderName = " 女";
                break;
            default:
                break;
        }
    }
}
