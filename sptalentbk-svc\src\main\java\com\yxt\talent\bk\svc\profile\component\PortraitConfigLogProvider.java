package com.yxt.talent.bk.svc.profile.component;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.util.BeanUtils;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.profile.UserProfileConfigService;
import com.yxt.talent.bk.svc.profile.bean.PortraitConfig4Update;
import com.yxt.talent.bk.svc.profile.bean.PortraitConfigLog;
import com.yxt.talent.bk.svc.profile.bean.PortraitShowCfgDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@AllArgsConstructor
@Component
public class PortraitConfigLogProvider implements AuditLogDataProvider<PortraitConfig4Update, PortraitConfigLog> {
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final UserProfileConfigService userProfileConfigService;
    @Override
    public PortraitConfigLog before(PortraitConfig4Update param, AuditLogBasicBean logBasic) {
        PortraitConfig4Update logParam = userProfileConfigService.getPortraitConfig4Log(logBasic.getOrgId(), param.getUpdateClient() == 1);
        logParam.setUpdateClient(param.getUpdateClient());
        return paramAsLog(logBasic.getOrgId(), logParam);
    }

    @Override
    public PortraitConfigLog after(PortraitConfig4Update param, AuditLogBasicBean logBasic) {
        return paramAsLog(logBasic.getOrgId(), param);
    }

    private PortraitConfigLog paramAsLog(String orgId, PortraitConfig4Update param) {
        PortraitConfigLog ret = new PortraitConfigLog();
        ret.setUpdateClient(param.getUpdateClient());
        if (param.getUpdateClient() == 1) {
            if (Objects.equals(2, param.getClientRangeComplex())) {
                ret.setClientRangeComplex("仅白名单员工才能查看自己的个人画像");
                ret.setWhiteUser(udpLiteUserRepository.userNames4Log(orgId, param.getUserIds()));
                ret.setShowCfg(showCfgDesc(param.getClientCfgDto()));
            } else if (Objects.equals(1, param.getClientRangeComplex())) {
                ret.setClientRangeComplex("所有员工可查看自己的个人画像");
                ret.setShowCfg(showCfgDesc(param.getClientCfgDto()));
            } else {
                ret.setClientRangeComplex("所有员工都不能查看自己的个人画像");
            }
        } else {
            if (CommonUtils.isTrue(param.getManagerRange())) {
                ret.setManagerRange("已开启");
                ret.setShowCfg(showCfgDesc(param.getManagerCfgDto()));
            } else {
                ret.setManagerRange("未开启");
            }
        }
        return ret;
    }

    @Override
    public Pair<String, String> entityInfo(PortraitConfig4Update param, PortraitConfigLog beforeObj, PortraitConfigLog afterObj, AuditLogBasicBean logBasic) {
        return Pair.of(param.getId(), param.getUpdateClient() == 1 ? "员工可见范围" : "团队管理者可见范围");
    }

    private String showCfgDesc(PortraitShowCfgDto cfgDto) {
        if (cfgDto == null) {
            return null;
        }
        List<String> enableModels = new ArrayList<>();
        BeanUtils.declaredFields(PortraitShowCfgDto.class, field -> {
            Schema schema = field.getAnnotation(Schema.class);
            if (StringUtils.isNotEmpty(schema.description())) {
                PropertyDescriptor propDesc = BeanUtil.getPropertyDescriptor(PortraitShowCfgDto.class, field.getName());
                try {
                    Object cfgValue = propDesc.getReadMethod().invoke(cfgDto);
                    if (cfgValue instanceof Integer enabled && CommonUtils.isTrue(enabled)) {
                        enableModels.add(schema.description());
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                } catch (InvocationTargetException e) {
                    throw new RuntimeException(e);
                }
            }
        });
        return String.join(",", enableModels);
    }
}
