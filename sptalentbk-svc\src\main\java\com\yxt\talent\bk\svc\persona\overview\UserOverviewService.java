package com.yxt.talent.bk.svc.persona.overview;

import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.persona.entity.UserOverview;
import com.yxt.talent.bk.core.persona.repo.UserOverviewRepository;
import com.yxt.talent.bk.svc.persona.bean.UserOverviewBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserOverviewService {
    private final UserOverviewRepository userOverviewRepository;

    /**
     * 查询用户特征综述
     *
     * @param orgId
     * @param userId
     * @return
     */
    public UserOverviewBean findUserOverview(String orgId, String userId) {
        UserOverviewBean userOverviewBean = new UserOverviewBean();
        UserOverview userOverview = userOverviewRepository.findUserOverview(orgId, userId);
        if (null != userOverview) {
            BeanCopierUtil.copy(userOverview, userOverviewBean);
        }
        return userOverviewBean;
    }

    /**
     * 新增个人特征综述
     *
     * @param orgId
     * @param opUserId
     * @param userOverviewBean
     */
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void addUserOverview(String orgId, String opUserId, UserOverviewBean userOverviewBean) {
        UserOverview userOverview = userOverviewRepository.findUserOverview(orgId, userOverviewBean.getUserId());
        if (null == userOverview) {
            userOverview = new UserOverview();
            userOverview.setId(ApiUtil.getUuid());
            userOverview.setOrgId(orgId);
            userOverview.setUserId(userOverviewBean.getUserId());
            EntityUtil.setCreateInfo(opUserId, userOverview);
        } else {
            EntityUtil.setUpdatedInfo(opUserId, userOverview);
        }
        userOverview.setFeatureOverview(userOverviewBean.getFeatureOverview());
        userOverviewRepository.saveOrUpdate(userOverview);
    }
}
