package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.Data;

/**
 * PoolUserPrepare4Log
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 1:52 pm
 */
@Data
public class PoolUserPrepare4Log {
    private String userFullName;
    @AuditLogField(name = "出池人员", orderIndex = 0, fullEqual = true)
    private String userDesc;
    @AuditLogField(name = "准备度", orderIndex = 1)
    private String prepareDesc;
}
