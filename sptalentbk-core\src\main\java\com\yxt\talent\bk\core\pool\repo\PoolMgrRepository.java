package com.yxt.talent.bk.core.pool.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.pool.bean.PoolMgr4List;
import com.yxt.talent.bk.core.pool.entity.PoolMgr;
import com.yxt.talent.bk.core.pool.mapper.PoolMgrMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class PoolMgrRepository extends ServiceImpl<PoolMgrMapper, PoolMgr> {

    public List<PoolMgr> findByPoolId(String orgId, String poolId) {
        LambdaQueryWrapper<PoolMgr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PoolMgr::getPoolId, poolId);
        queryWrapper.eq(PoolMgr::getOrgId, orgId);
        return list(queryWrapper);
    }

    public List<PoolMgr> findByPoolIds(String orgId, Collection<String> poolIds) {
        if (CollectionUtils.isEmpty(poolIds)) {
            log.warn("LOG10280:org_id={}", orgId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PoolMgr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PoolMgr::getPoolId, poolIds);
        queryWrapper.eq(PoolMgr::getOrgId, orgId);
        return list(queryWrapper);
    }

    public boolean removeByOrgId(String orgId,String poolId) {
        LambdaQueryWrapper<PoolMgr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PoolMgr::getPoolId, poolId);
        queryWrapper.eq(PoolMgr::getOrgId, orgId);
        return remove(queryWrapper);
    }

    public List<PoolMgr4List> findPoolMgrByPoolId(String orgId, String poolId) {
        return TalentbkUtil.bkUdpTranslate(orgId, baseMapper.findPoolMgrByPoolId(orgId, poolId));
    }
    public List<PoolMgr4List> findPoolMgrByPoolIds(String orgId, Collection<String> poolIds) {
        return TalentbkUtil.bkUdpTranslate(orgId, baseMapper.findPoolMgrByPoolIds(orgId, poolIds));
    }
}
