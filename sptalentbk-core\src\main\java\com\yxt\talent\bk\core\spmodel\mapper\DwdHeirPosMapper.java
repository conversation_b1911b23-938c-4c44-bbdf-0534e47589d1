package com.yxt.talent.bk.core.spmodel.mapper;

import com.yxt.talent.bk.core.spmodel.entity.DwdHeirPosEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DwdHeirPosEntityMapper
 *
 * <AUTHOR> harley<PERSON>
 * @Date 13/6/24 4:17 pm
 */
@Mapper
public interface DwdHeirPosMapper {

    List<DwdHeirPosEntity> listTop(@Param("orgId") String orgId,
                                   @Param("yearly") int yearly,
                                   @Param("deptId") String deptId,
                                   @Param("posType") int posType);
}
