package com.yxt.talent.bk.api.controller.client;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.usergroup.bean.UserCompareBean;
import com.yxt.talent.bk.core.usergroup.bean.UserCompareDetail;
import com.yxt.talent.bk.svc.profile.UserProfileConfigService;
import com.yxt.talent.bk.svc.profile.UserProfileService;
import com.yxt.talent.bk.svc.profile.bean.CareerHistory4Get;
import com.yxt.talent.bk.svc.profile.bean.PortraitConfigBean;
import com.yxt.talent.bk.svc.profile.bean.RewardPunishmentHistory4Get;
import com.yxt.talent.bk.svc.profile.bean.TrainingHistory4Get;
import com.yxt.talent.bk.svc.profile.bean.UserBasic4Get;
import com.yxt.talent.bk.svc.profile.bean.UserCourse4Get;
import com.yxt.talent.bk.svc.profile.bean.UserExam4Get;
import com.yxt.talent.bk.svc.profile.bean.UserLearnData4Get;
import com.yxt.talent.bk.svc.profile.bean.UserModel4Get;
import com.yxt.talent.bk.svc.profile.bean.UserPerfBean4Get;
import com.yxt.talent.bk.svc.profile.bean.UserPerfType4Get;
import com.yxt.talent.bk.svc.profile.bean.UserRvTask4Get;
import com.yxt.talent.bk.svc.profile.bean.UserSkill4Get;
import com.yxt.talent.bk.svc.profile.bean.UserTag4Get;
import com.yxt.talent.bk.svc.profile.bean.WorkHistory4Get;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/portrait/")
@Tag(name = "用户端人才画像")
public class ClientUserPortraitController extends BaseController {
    private final UserProfileService userProfileService;
    private final UserProfileConfigService userProfileConfigService;

    @Operation(summary = "人员基本信息")
    @ResponseStatus(HttpStatus.OK)
    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.QUERY)})
    @GetMapping(value = "/basic", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public UserBasic4Get getUserBasic(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getUserBasic(orgId, userId);
    }

    @Operation(summary = "用户标签")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/tag", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.QUERY)})
    public List<UserTag4Get> getUserTags(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getUserTags(orgId, userId);
    }

    @Operation(summary = "内部任职履历")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/career", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.QUERY)})
    public List<CareerHistory4Get> getCareerHistory(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getCareerHistory(orgId, userId);
    }

    @Operation(summary = "外部工作经历")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/work", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.QUERY)})
    public List<WorkHistory4Get> getWorkHistory(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getWorkHistory(orgId, userId);
    }

    @Operation(summary = "学习培训")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/training", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.QUERY)})
    public PagingList<TrainingHistory4Get> getTrainingHistory(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return userProfileService.getTrainingHistoryPage(orgId, userId, paging);
    }

    @Operation(summary = "奖惩信息历史")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/rp", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.QUERY)})
    public PagingList<RewardPunishmentHistory4Get> getRewardPunishmentHistory(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return userProfileService.getRewardPunishmentHistoryPage(orgId, userId, paging);
    }

    @Operation(summary = "绩效表现")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/perf", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.QUERY)})
    public UserPerfBean4Get getUserPerf(
            @RequestParam(value = "cycle", defaultValue = "2") Integer cycle,
            @RequestParam(value = "limit", defaultValue = "6") Integer limit,
            @RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        UserPerfBean4Get userPerfBean4Get = new UserPerfBean4Get();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        userPerfBean4Get.setList(userProfileService.getUserPerf(orgId, userId, cycle, limit));
        userPerfBean4Get.setPerfs(userProfileService.getAllPerf(orgId));
        return userPerfBean4Get;
    }

    @Operation(summary = "个人绩效类型")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/perftype", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.QUERY)})
    public List<UserPerfType4Get> getUserPerfType(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getUserPerfType(orgId, userId);
    }

    @Operation(summary = "人才对比的数据")
    @PostMapping(value = "/info", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public UserCompareDetail getCompareDetail(@RequestBody UserCompareBean userCompareBean) {
        TalentbkUtil.isUuid(userCompareBean.getUserId(), "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        return userProfileService.getCompareDetail(userCache.getOrgId(), userCompareBean);
    }

    @Operation(summary = "员工个人画像是否展示")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/user/config", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public PortraitConfigBean getUserPortraitConfig(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileConfigService.getClientPortraitConfig(orgId, userId);
    }

    @Operation(summary = "管理者个人画像是否展示")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/mgr/config", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public PortraitConfigBean getManagerPortraitConfig(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileConfigService.getManagerPortraitConfig(orgId, userId);
    }

    @Operation(summary = "个人能力")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/skillInfo", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public List<UserSkill4Get> getUserSkillInfo(@RequestParam(required = false) String userId ,
            @RequestParam(value = "modelId", defaultValue = "", required = false) String modelId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        TalentbkUtil.isUuid(modelId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getUserSkillInfo(orgId, userId, modelId);
    }


    @Operation(summary = "个人能力-模型列表")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/skillmodel", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public List<UserModel4Get> getUserModelList(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getUserModelList(orgId, userId);
    }

    @Operation(summary = "职责任务")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/rvtaskinfo", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public UserRvTask4Get getRvTaskInfo(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getRvTaskInfo(orgId, userId);
    }

    @Operation(summary = "职业特征评估")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/characteristic", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public List<UserTag4Get> getUserCharacteristic(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getUserCharacteristic(orgId, userId);
    }

    @Operation(summary = "学习数据概览")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/learndata", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public UserLearnData4Get getUserLearnData(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        return userProfileService.getUserLearnData(orgId, userId);
    }

    @Operation(summary = "课程学习记录")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/coursedata", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public PagingList<UserCourse4Get> getUserCourseData(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return userProfileService.getUserCourseData(orgId, userId, paging);
    }

    @Operation(summary = "参与考试记录")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/examdata", produces = Constants.MEDIATYPE)
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public PagingList<UserExam4Get> getUserExamData(@RequestParam(required = false) String userId) {
        TalentbkUtil.isUuid(userId, "apis.talentbk.param.error");
        UserCacheBasic userCache = getUserCacheBasic();
        String orgId = userCache.getOrgId();
        if (StringUtils.isBlank(userId)) {
            userId = userCache.getUserId();
        }
        Paging paging = SqlUtils.toPaging(ApiUtil.getPageRequest(getRequest()));
        return userProfileService.getUserExamData(orgId, userId, paging);
    }
}
