package com.yxt.talent.bk.svc.search.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Schema(name = "人才搜索-主页-列表-搜索入参-非标准标签")
@Data
public class UserSearchBean4OtherTag {
    @Schema(description = "入职日期【起始】",example = "2021-01-01")
    @JsonFormat(pattern = Constants.SDF_YEAR2DAY,timezone = Constants.STR_GMT8)
    private String entryDateStart;
    @Schema(description = "入职日期【结束】",example = "2022-12-10")
    @JsonFormat(pattern = Constants.SDF_YEAR2DAY,timezone = Constants.STR_GMT8)
    private String entryDateEnd;
    @Schema(description = "部门")
    private List<UserSearchBean4DeptTag> departments;
}
