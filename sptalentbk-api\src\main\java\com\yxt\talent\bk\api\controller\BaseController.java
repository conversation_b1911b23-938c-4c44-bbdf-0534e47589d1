package com.yxt.talent.bk.api.controller;

import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StringUtil;
import com.yxt.talent.bk.common.bean.PageParam;
import com.yxt.talent.bk.common.utils.CommonUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 * BaseController
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 11:11 am
 */
@Slf4j
public class BaseController {
    @Autowired
    private AuthService authService;

    @Value("${base.skmap.sptalentbkapi:}")
    private String bkSkVal;

    protected UserCacheDetail getUserCacheDetail() {
        return authService.getUserCacheDetail();
    }

    protected UserCacheBasic getUserCacheBasic() {
        UserCacheBasic userBasic = authService.getUserCacheBasic();
        if (StringUtils.isEmpty(userBasic.getOrgId())) {
            throw new ApiException(ExceptionKey.TOKEN_INVALID);
        }
        return userBasic;
    }

    protected HttpServletRequest getRequest() {
        return ApiUtil.getRequestByContext();
    }

    protected PageParam getPageParam() {
        HttpServletRequest request = ApiUtil.getRequestByContext();
        int limit = StringUtil.str2Int(request.getParameter("limit"), 20);
        if (limit > 500) {
            limit = 500;
        } else if (limit <= 0) {
            limit = 20;
        }
        int offset = StringUtil.str2Int(request.getParameter("offset"), 0);
        PageParam ret = new PageParam();
        ret.setLimit(limit);
        ret.setOffset(offset);
        return ret;
    }

    protected PageRequest getPage() {
        HttpServletRequest request = ApiUtil.getRequestByContext();
        return ApiUtil.getPageRequest(request);
    }

    protected void normalCheckApi() {
        if (CommonUtils.notProdEnv()) {
            return;
        }
        String sign = ApiUtil.getRequestByContext().getParameter("sign");
        String dateStr = CommonUtils.formatDate("yyyyMMdd", new Date());
        if (!checkSign(dateStr, sign)) {
            throw new ApiException(ExceptionKey.NO_PERMISSSION);
        }
    }

    protected boolean checkSign(String targetId, String sign) {
        return CommonUtils.md5Hex(bkSkVal + targetId).equals(sign);
    }

    protected String getOauthOrgId() {
        HttpServletRequest request = ApiUtil.getRequestByContext();
        String orgId = request.getParameter("orgId");
        if (StringUtils.isNotEmpty(orgId)) {
            return orgId;
        }
        return authService.getOrgIdByOauthToken(request);
    }
}
