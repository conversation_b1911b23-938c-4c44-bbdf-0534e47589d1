package com.yxt.talent.bk.svc.common.bean;

import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spmodel.facade.bean.rule.LabelRuleInfo;

import java.util.List;

/**
 * BkLabelRuleInfo
 *
 * <AUTHOR> harley<PERSON>
 * @Date 25/7/24 11:16 am
 */
public class BkLabelRuleInfo extends LabelRuleInfo {
    private List<String> userValues;
    private Boolean userMatchRule;
    public List<String> getUserValues() {
        return userValues;
    }

    public void setUserValues(List<String> userValues) {
        this.userValues = userValues;
    }

    public Boolean getUserMatchRule() {
        return userMatchRule;
    }

    public void setUserMatchRule(Boolean userMatchRule) {
        this.userMatchRule = userMatchRule;
    }

    public void copyOf(LabelRuleInfo ruleInfo) {
        BeanCopierUtil.copy(ruleInfo, this, false);
    }
}
