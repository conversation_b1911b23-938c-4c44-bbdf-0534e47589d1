package com.yxt.talent.bk.svc.heir;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.spmodel.facade.bean.label.LabelVO;
import com.yxt.spmodel.facade.bean.label.UserLabelVO;
import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.spsdk.common.utils.YxtExportUtils;
import com.yxt.talent.bk.common.bean.EntityDeletedBean;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.*;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.common.enums.HeirRiskRuleTypeEnum;
import com.yxt.talent.bk.common.utils.CallUtils;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.core.heir.bean.HeirPosPIdBean;
import com.yxt.talent.bk.core.heir.bean.*;
import com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosBean;
import com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosUserBean;
import com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosUserEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosBenchmarkEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosExt;
import com.yxt.talent.bk.core.heir.mapper.HeirPosBenchmarkMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPermissionMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import com.yxt.talent.bk.core.heir.repo.HeirPosBenchmarkRepository;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.spmodel.entity.DwdHeirPosEntity;
import com.yxt.talent.bk.core.spmodel.mapper.DwdHeirPosMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserMapper;
import com.yxt.talent.bk.core.udp.bean.QueryUserBean;
import com.yxt.talent.bk.core.udp.bean.UdpPositionBean;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.core.udp.bean.open.UserBasicEntity;
import com.yxt.talent.bk.core.udp.mapper.UdpDeptMapper;
import com.yxt.talent.bk.core.udp.repo.UdpDeptRepository;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.common.SpmodelSqlRpc;
import com.yxt.talent.bk.svc.export.HeirDeptExportStrategy;
import com.yxt.talent.bk.svc.export.HeirPositionExportStrategy;
import com.yxt.talent.bk.svc.heir.bean.*;
import com.yxt.talent.bk.svc.heir.bean.req.HeirPosEditBean;
import com.yxt.talent.bk.svc.heir.bean.req.HeirPosTreeReq;
import com.yxt.talent.bk.svc.heir.bean.resp.*;
import com.yxt.talent.bk.svc.profile.bean.TagValue4Get;
import com.yxt.talent.bk.svc.profile.bean.UserTag4Get;
import com.yxt.talent.bk.svc.udp.UdpQueryService;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean4Dept;
import com.yxt.talent.bk.svc.udp.rpc.UdpRpc;
import com.yxt.talent.bk.svc.usergroup.rpc.TagSearchRpc;
import com.yxt.udpfacade.bean.dept.DeptUserMapBean;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * HeirPosService
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 10:54 am
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HeirPosService {

    private final HeirPosMapper heirPosMapper;
    private final HeirPosRepository heirPosRepository;
    private final HeirPosUserMapper heirPosUserMapper;
    private final HeirPosPermissionMapper heirPosPermissionMapper;
    private final HeirPosBenchmarkMapper posBenchmarkMapper;
    private final HeirPosBenchmarkRepository heirPosBenchmarkRepository;
    private final HeirOrgLevelConfigService orgLevelConfigService;
    private final UdpDeptMapper udpDeptMapper;
    private final UdpDeptRepository udpDeptRepository;
    private final UdpQueryService udpQueryService;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final AuthService authService;
    private final SpmodelSqlRpc spmodelSqlRpc;
    private final TagSearchRpc tagSearchRpc;
    private final UdpRpc udpRpc;
    private final UserAuthService userAuthService;
    private final DwdHeirPosMapper dwdHeirPosMapper;
    private final DwdUserMapper dwdUserMapper;

    /**
     * 添加岗位
     * @param user
     * @param positionIds
     * @return
     */
    @DbHintMaster
    public List<String> addPos(UserBasicBean user, List<String> positionIds) {
        List<String> addIds = new ArrayList<>();
        List<EntityDeletedBean> existIds = heirPosMapper.existIds(user.getOrgId(), positionIds);
        if (CollectionUtils.isNotEmpty(positionIds)) {
            for (IdName idName : udpRpc.queryPositionNameByIds(user.getOrgId(), positionIds)) {
                String posId = idName.getId();
                HeirPosEntity posEntity = HeirPosEntity.createEntity(user.getOrgId(), user.getUserId());
                posEntity.setId(posId);
                posEntity.setPosName(idName.getName());
                posEntity.setPosType(HeirPosTypeEnum.POSITION.getType());
                EntityDeletedBean deletedBean = IArrayUtils.getFirstMatch(existIds, item -> posId.equals(item.getId()));
                if (deletedBean == null) {
                    heirPosMapper.insert(posEntity);
                    addIds.add(posId);
                } else if (deletedBean.getDeleted() == YesOrNo.YES.getValue()) {
                    //表数据id直接使用的岗位id，如果被删除了，再加进来则重置数据
                    heirPosMapper.updateById(posEntity);
                    addIds.add(posId);
                }
                HeirAddPos4Log addLog = new HeirAddPos4Log();
                addLog.setPositionId(posId);
                addLog.setPositionName(idName.getName());
                AuditLogHooker.addAfterDataLog(addLog);
            }
        }
        log.info("addedPos orgId {} posIds {}", user.getOrgId(), JSON.toJSONString(addIds));
        return addIds;
    }

    @DbHintMaster
    public void updatePos(UserBasicBean user, HeirPosEditBean editBean) {
        HeirPosEntity dbPosEntity = heirPosMapper.selectById(editBean.getId());
        CommonUtils.checkOrgData(user.getOrgId(), dbPosEntity, HeirPosEntity::getOrgId);
        HeirPosEntity updatePos = new HeirPosEntity();
        updatePos.setId(editBean.getId());
        if (dbPosEntity.getPosType() == HeirPosTypeEnum.POSITION.getType()) {
            posCheckAndSetPId(user, editBean, dbPosEntity, updatePos);
        }
        updatePos.setHeirTargetQty(editBean.getHeirTargetQty());
        updatePos.setRiskRuleType(editBean.getRiskRuleType());
        updatePos.setRiskLevelId(editBean.getRiskLevelId());
        updatePos.init(user.getUserId());
        heirPosMapper.updateById(updatePos);
        //保存标杆用户
        replaceBenchmarkUser(user, editBean.getId(), editBean.getBenchmarkUserIds());
    }

    private void posCheckAndSetPId(UserBasicBean user, HeirPosEditBean editBean,
                                   HeirPosEntity dbPosEntity, HeirPosEntity updatePos) {
        if (StringUtils.isBlank(editBean.getParentPosId())) {
            updatePos.setParentPosId(StringPool.EMPTY);
        } else if (!editBean.getParentPosId().equals(dbPosEntity.getParentPosId())) {
            if (editBean.getParentPosId().equals(editBean.getId())) {
                throw new ApiException(BkApiErrorKeys.HEIR_SET_PARENT_POS_ID_CYCLE);
            }
            //尝试添加父级
            if (addPos(user, Lists.newArrayList(editBean.getParentPosId())).size() < 1) {
                //父级存在的情况下，校验是否成环
                Map<String, HeirPosPIdBean> allIdMap = StreamUtil.list2map(
                        heirPosMapper.selectPIdList(user.getOrgId(), HeirPosTypeEnum.POSITION.getType()),
                        HeirPosPIdBean::getId);
                HeirPosPIdBean pIdBean = allIdMap.get(editBean.getId());
                if (pIdBean != null) {
                    HeirPosPIdBean tempPIdBean = allIdMap.get(editBean.getParentPosId());
                    while (true) {
                        if (tempPIdBean == null) {
                            break;
                        }
                        if (pIdBean.getId().equals(tempPIdBean.getId())) {
                            throw new ApiException(BkApiErrorKeys.HEIR_SET_PARENT_POS_ID_CYCLE);
                        }
                        tempPIdBean = allIdMap.get(tempPIdBean.getParentPosId());
                    }
                }
            }
            updatePos.setParentPosId(editBean.getParentPosId());
        }
    }

    @DbHintMaster
    public void updateDeptPos(UserBasicBean user, HeirPosEditBean editBean) {
        HeirPosEntity dbPosEntity = heirPosMapper.selectById(editBean.getId());
        if (dbPosEntity != null) {
            if (dbPosEntity.getPosType() == HeirPosTypeEnum.POSITION.getType()) {
                throw new ApiException(ExceptionKey.PARAM_TYPE_INVALID);
            }
            CommonUtils.checkOrgData(user.getOrgId(), dbPosEntity, HeirPosEntity::getOrgId);
        }
        boolean insertPos = dbPosEntity == null;
        HeirPosEntity posEntity;
        if (insertPos) {
            posEntity = HeirPosEntity.createEntity(user.getOrgId(), user.getUserId());
            posEntity.setPosType(HeirPosTypeEnum.DEPT.getType());
        } else {
            posEntity = new HeirPosEntity();
        }
        posEntity.setId(editBean.getId());
        posEntity.setHeirTargetQty(editBean.getHeirTargetQty());
        posEntity.setRiskRuleType(editBean.getRiskRuleType());
        posEntity.setRiskLevelId(editBean.getRiskLevelId());
        if (insertPos) {
            heirPosMapper.insert(posEntity);
        } else {
            posEntity.init(user.getUserId());
            heirPosMapper.updateById(posEntity);
        }
        //保存标杆用户
        replaceBenchmarkUser(user, editBean.getId(), editBean.getBenchmarkUserIds());
    }

    @DbHintMaster
    public void delPos(UserBasicBean user, String posId, boolean childrenAsRoot) {
        String orgId = user.getOrgId();
        HeirPosEntity dbPosEntity = heirPosMapper.selectById(posId);
        CommonUtils.checkOrgData(user.getOrgId(), dbPosEntity, HeirPosEntity::getOrgId);
        if (dbPosEntity.getPosType() != HeirPosTypeEnum.POSITION.getType()) {
            return;
        }
        if (childrenAsRoot) {
            heirPosMapper.replacePId(user.getOrgId(), HeirPosTypeEnum.POSITION.getType(),
                posId, dbPosEntity.getParentPosId());
        }
        heirPosMapper.removeByIds(user.getOrgId(), Lists.newArrayList(posId));
        heirPosMapper.removePosRelated(user.getOrgId(), posId);
        Set<String> delPosIdSet = new HashSet<>();
        delPosIdSet.add(posId);
        if (!childrenAsRoot) {
            //子继任岗位也要删除
            Set<String> pIds = new HashSet<>();
            pIds.add(posId);
            List<HeirPosPIdBean> allPosIds = heirPosMapper.selectPIdList(user.getOrgId(), HeirPosTypeEnum.POSITION.getType());
            for (int i = 0; i < allPosIds.size(); i++) {
                Set<String> tempPosIds = new HashSet<>();
                for (HeirPosPIdBean posPIdBean : allPosIds) {
                    if (pIds.contains(posPIdBean.getParentPosId())) {
                        tempPosIds.add(posPIdBean.getId());
                    }
                }
                if (tempPosIds.isEmpty()) {
                    break;
                }
                log.info("removeAllChildren orgId {} posId {} childrenIds {}", orgId, posId, JSON.toJSONString(tempPosIds));
                heirPosMapper.removeByIds(orgId, tempPosIds);
                delPosIdSet.addAll(tempPosIds);
                CallUtils.asyncCall(() -> {
                    tempPosIds.forEach(delPosId -> heirPosMapper.removePosRelated(orgId, delPosId));
                });
                pIds = tempPosIds;
            }
        }
        AuditLogHooker.setLogParam(delPosIdSet);
    }

    public void posExport(PosExportParam param) {
        int posType = param.getPosType();
        HeirPosTreeReq treeReq = new HeirPosTreeReq();
        BeanCopierUtil.copy(param, treeReq);
        HeirPosTreeResp treeResp = posBriefTree(UserBasicBean.createBy(param.getUserCache()), posType, treeReq);
        List<HeirPosNodeBean> nodeList = new ArrayList<>();
        posNodeTreeAsList(nodeList, treeResp.getNodeTree());
        if (posType == HeirPosTypeEnum.DEPT.getType()) {
            YxtExportUtils.exportData(param.getUserCache(), HeirDeptExportStrategy.class, nodeList);
        } else {
            YxtExportUtils.exportData(param.getUserCache(), HeirPositionExportStrategy.class, nodeList);
        }
    }

    private void posNodeTreeAsList(List<HeirPosNodeBean> allNodeList, List<HeirPosNodeBean> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        for (HeirPosNodeBean posNode : nodeList) {
            allNodeList.add(posNode);
            posNodeTreeAsList(allNodeList, posNode.getChildren());
        }
    }

    public HeirPosTreeResp posBriefTree(UserBasicBean user, int posType,
                                              HeirPosTreeReq treeReq) {
        String orgId = user.getOrgId();
        List<String> inPosIds = treeReq.getPosIds();
        List<String> userIds = treeReq.getUserIds();
        Long riskLevelId = treeReq.getRiskLevelId();
        List<HeirPosRawBean> list = allPos(user.getOrgId(), posType);
        Map<String, HeirPosRawBean> allPosMap = StreamUtil.list2map(list, HeirPosRawBean::getId);
        list.forEach(item -> {
            item.setDataState(YesOrNo.YES.getValue());
            item.setHasPerm(YesOrNo.YES.getValue());
        });
        //是否部门继任
        boolean deptHeir = posType == HeirPosTypeEnum.DEPT.getType();
        if (CollectionUtils.isNotEmpty(inPosIds)) {
            //指定部门或者岗位时
            posTreeMarkAsRemoved(deptHeir, list, item -> !inPosIds.contains(item.getId()));
        }
        List<RiskLevelValueDTO> riskLevels = riskLevel(orgId);
        list.forEach(item -> calcRiskLevelId(item, riskLevels));
        if (riskLevelId != null) {
            //继任风险条件过滤
            posTreeMarkAsRemoved(deptHeir, list, item -> !Objects.equals(riskLevelId, item.getRiskLevelId()));
        }
        if (!list.isEmpty() && !user.isAdminUser()) {
            if (deptHeir) {
                UserAuthBean4Dept deptAuthBean = userAuthService.getAuthDeptIds(orgId, user.getUserId(),
                        TalentBkAuthCodes.SP_SUCCESSION_MAP, TalentBkAuthCodes.SUCCESSION_DEPT);
                if (deptAuthBean.getIsAll() == YesOrNo.NO.getValue()) {
                    List<String> deptIds = Optional.ofNullable(deptAuthBean.getDeptIds()).orElse(Lists.emptyList());
                    posTreeMarkAsRemoved(deptHeir, list, item -> {
                        boolean noPerm = !deptIds.contains(item.getId());
                        if (noPerm) {
                            item.setHasPerm(YesOrNo.NO.getValue());
                        }
                        return noPerm;
                    });
                }
            } else {
                List<String> permissionPosIds = list.stream()
                        .filter(item -> item.getPermissionFlag() == YesOrNo.YES.getValue()
                                && !Objects.equals(user.getUserId(), item.getCreateUserId()))
                        .map(HeirPosRawBean::getId)
                        .collect(Collectors.toList());
                //权限过滤
                if (!permissionPosIds.isEmpty()) {
                    List<String> userPosIds = heirPosPermissionMapper.listUserPosIds(user.getUserId(), permissionPosIds);
                    posTreeMarkAsRemoved(deptHeir, list, item ->
                            item.getPermissionFlag() == YesOrNo.YES.getValue()
                                    && !userPosIds.contains(item.getId()));
                }
            }
        }
        if (!list.isEmpty() && CollectionUtils.isNotEmpty(userIds)) {
            //继任者
            List<String> userPosIds = heirPosUserMapper.queryUserPosIds(orgId, userIds);
            posTreeMarkAsRemoved(deptHeir, list, item -> !userPosIds.contains(item.getId()));
        }
        posTreeRemoveNotUsed(true, new HeirPosTreeContainer(list, allPosMap));
        //去掉无效数据
        IArrayUtils.remove(list, node -> node.getDataState() == -1);
        List<HeirPosNodeBean> finalList = convertToPosNode(list);
        Map<String, HeirPosNodeBean> finalPosMap = StreamUtil.list2map(finalList, HeirPosNodeBean::getId);
        List<HeirPosNodeBean> nodeTree = new ArrayList<>();
        for (HeirPosNodeBean treeResp : finalList) {
            String pId = treeResp.getParentPosId();
            while (true) {
                HeirPosNodeBean pTree = finalPosMap.get(pId);
                if (pTree != null) {
                    if (pTree.getChildren() == null) {
                        pTree.setChildren(new ArrayList<>());
                    }
                    pTree.getChildren().add(treeResp);
                    break;
                }
                pId = Optional.ofNullable(allPosMap.get(pId))
                        .map(HeirPosRawBean::getParentPosId).orElse(null);
                if (StringUtils.isEmpty(pId)) {
                    nodeTree.add(treeResp);
                    break;
                }
            }
        }
        if (posType == HeirPosTypeEnum.POSITION.getType()) {
            loadPosDetail(orgId, nodeTree, HeirPosNodeBean::getId, (nodeBean, pairVal) -> {
                fillPosDetail(nodeBean, pairVal);
            });
        } else {
            loadDeptDetail(orgId, nodeTree, HeirPosNodeBean::getId, (nodeBean, deptMgt) -> {
                fillDeptDetail(nodeBean, deptMgt);
            });
        }
        HeirPosTreeResp treeResp = new HeirPosTreeResp();
        treeResp.setPosQty(finalList.size());
        treeResp.setHeirUserQty(finalList.stream().map(HeirPosNodeBean::getHeirValidQty)
            .reduce((a, b) -> a + b).orElse(0));
        treeResp.setNodeTree(nodeTree);
        return treeResp;
    }

    private List<HeirPosNodeBean> convertToPosNode(List<HeirPosRawBean> list) {
        return BeanCopierUtil.convertList(list, item -> {
            HeirPosNodeBean treeResp = new HeirPosNodeBean();
            treeResp.setId(item.getId());
            treeResp.setName(item.getName());
            treeResp.setIdFullPath(item.getIdFullPath());
            treeResp.setSourceDeleted(item.getSourceDeleted());
            treeResp.setParentPosId(item.getParentPosId());
            treeResp.setHeirTargetQty(CommonUtils.zeroAsNull(item.getHeirTargetQty()));
            treeResp.setHeirValidQty(item.getHeirValidQty());
            treeResp.setRiskRuleType(item.getRiskRuleType());
            treeResp.setRiskLevelId(CommonUtils.zeroAsNull(item.getRiskLevelId()));
            treeResp.setDataState(item.getDataState());
            treeResp.setHasPerm(item.getHasPerm());
            return treeResp;
        });
    }

    private void posTreeMarkAsRemoved(boolean deptHeir,
                                      List<HeirPosRawBean> list,
                                      Predicate<HeirPosRawBean> removeCheck) {
        if (deptHeir) {
            list.forEach(item -> {
                if (removeCheck.test(item)) {
                    item.setDataState(-1);
                }
            });
        } else {
            IArrayUtils.remove(list, removeCheck);
        }
    }

    private void posTreeRemoveNotUsed(boolean first, HeirPosTreeContainer container) {
        if (first) {
            if (CollectionUtils.isEmpty(container.nodeList)) {
                return;
            }
            container.tmpNodeMap = new HashMap<>(container.nodeList.size() / 10);
            container.nodeList.forEach(node -> {
                if (node.getDataState() == YesOrNo.YES.getValue()) {
                    HeirPosRawBean posPNode = container.allNodeMap.get(node.getParentPosId());
                    if (posPNode != null) {
                        container.tmpNodeMap.put(node.getParentPosId(), posPNode);
                        if (posPNode.getDataState() == -1) {
                            posPNode.setDataState(YesOrNo.NO.getValue());
                        }
                    }
                }
            });
            posTreeRemoveNotUsed(false, container);
        } else {
            if (container.tmpNodeMap.isEmpty()) {
                return;
            }
            HeirPosRawBean[] nodeArr = new HeirPosRawBean[container.tmpNodeMap.size()];
            container.tmpNodeMap.values().toArray(nodeArr);
            container.tmpNodeMap.clear();
            for (HeirPosRawBean posTree : nodeArr) {
                HeirPosRawBean posPNode = container.allNodeMap.get(posTree.getParentPosId());
                if (posPNode != null) {
                    container.tmpNodeMap.put(posTree.getParentPosId(), posPNode);
                    if (posPNode.getDataState() == -1) {
                        posPNode.setDataState(YesOrNo.NO.getValue());
                    }
                }
            }
            posTreeRemoveNotUsed(false, container);
        }
    }

    private static class HeirPosTreeContainer {
        public HashMap<String, HeirPosRawBean> tmpNodeMap;
        public List<HeirPosRawBean> nodeList;
        public Map<String, HeirPosRawBean> allNodeMap;

        public HeirPosTreeContainer(List<HeirPosRawBean> nodeList, Map<String, HeirPosRawBean> allNodeMap) {
            this.nodeList = nodeList;
            this.allNodeMap = allNodeMap;
        }
    }

    public PagingList<HeirDeptInfoDTO> deptHeirList(UserBasicBean user,
                                                    PageRequest pageReq,
                                                    String deptId) {
        Page<DwdHeirPosBean> queryPage = new Page<>(pageReq.getCurrent(), pageReq.getSize(),
            pageReq.getCurrent() < 2 ? true : false);
        String orgId = user.getOrgId();
        String userId = user.getUserId();
        String deptRoutingPath = udpDeptMapper.getRoutingPathById(orgId, deptId);
        if (StringUtils.isEmpty(deptRoutingPath)) {
            return BeanCopierUtil.toPagingList(queryPage, DwdHeirPosBean.class, HeirDeptInfoDTO.class);
        }
        IPage<HeirPosRawBean> page = heirPosRepository.selectDeptHeir(queryPage, orgId, userId, deptId, deptRoutingPath);
        for (HeirPosRawBean posRaw : page.getRecords()) {
            initHeirPosRaw(posRaw);
        }
        PagingList<HeirDeptInfoDTO> retPage = BeanCopierUtil.toPagingList(page, HeirPosRawBean.class, HeirDeptInfoDTO.class);
        loadDeptDetail(orgId, retPage.getDatas(), HeirDeptInfoDTO::getId, (nodeBean, deptMgt) -> {
            nodeBean.setManageName(Optional.ofNullable(IArrayUtils.getFirst(deptMgt))
                .orElse(new UserBriefBean()).getFullname());
        });
        List<RiskLevelValueDTO> riskLevels = riskLevel(orgId);
        retPage.getDatas().forEach(item -> calcRiskLevelId(item, riskLevels));
        return retPage;
    }

    public List<HeirPosNodeBean> queryPosDetail(String orgId, int posType, List<String> posIds) {
        List<HeirPosNodeBean> list = new ArrayList<>(posIds.size());
        if (posType == HeirPosTypeEnum.POSITION.getType()) {
            loadPosDetail(orgId, posIds, item -> item, (posId, pairVal) -> {
                HeirPosNodeBean nodeBean = new HeirPosNodeBean();
                nodeBean.setId(posId);
                fillPosDetail(nodeBean, pairVal);
                list.add(nodeBean);
            });
        } else {
            loadDeptDetail(orgId, posIds, item -> item, (posId, deptMgt) -> {
                HeirPosNodeBean nodeBean = new HeirPosNodeBean();
                nodeBean.setId(posId);
                fillDeptDetail(nodeBean, deptMgt);
                list.add(nodeBean);
            });
        }
        return list;
    }

    public void loadPostDetail(int posType, String orgId, List<HeirPosNodeBean> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            if (posType == HeirPosTypeEnum.POSITION.getType()) {
                loadPosDetail(orgId, list, HeirPosNodeBean::getId, this::fillPosDetail);
            } else {
                loadDeptDetail(orgId, list, HeirPosNodeBean::getId, this::fillDeptDetail);
            }
        }
    }
    private void fillPosDetail(HeirPosNodeBean nodeBean, Pair<List<UserBriefBean>, UdpPositionBean> pairVal) {
        if (pairVal.getRight() == null) {
            nodeBean.setSourceDeleted(YesOrNo.YES.getValue());
        } else {
            nodeBean.setName(pairVal.getRight().getName());
            nodeBean.setPositionUserQty(pairVal.getRight().getUserCount());
        }
        nodeBean.setBenchmarkUsers(pairVal.getLeft());
    }

    private void fillDeptDetail(HeirPosNodeBean nodeBean, List<UserBriefBean> userList) {
        nodeBean.setBenchmarkUsers(userList);
    }
    private <T> void loadPosDetail(String orgId, List<T> list, Function<T, String> posIdGetter,
                                  BiConsumer<T, Pair<List<UserBriefBean>, UdpPositionBean>> consumerPos) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> posIds = list.stream().map(posIdGetter).collect(Collectors.toList());
        Map<String, UdpPositionBean> positionMap = StreamUtil.list2map(udpQueryService.listPositionStaByIds(orgId, posIds),
            UdpPositionBean::getId);
        List<UserBriefBean> posBenchmarkUsers = BeanCopierUtil.convertList(
                heirPosBenchmarkRepository.posTopUsers(orgId, posIds),
            posUser -> {
                UserBriefBean userBrief = new UserBriefBean();
                userBrief.setId(posUser.getUserId());
                userBrief.setPosId(posUser.getPosId());
                userBrief.setImgUrl(posUser.getImgUrl());
                userBrief.setFullname(posUser.getFullname());
                userBrief.setUsername(posUser.getUsername());
                return userBrief;
            });
        Map<String, List<UserBriefBean>> posBenchmarkMap = IArrayUtils.listToMultiMap(posBenchmarkUsers, UserBriefBean::getPosId);
        list.forEach(item -> {
            String posId = posIdGetter.apply(item);
            consumerPos.accept(item, Pair.of(posBenchmarkMap.get(posId), positionMap.get(posId)));
        });
    }

    private <T> void loadDeptDetail(String orgId, List<T> list, Function<T, String> deptIdGetter,
                                    BiConsumer<T, List<UserBriefBean>> consumerDept) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> deptIds = list.stream().map(deptIdGetter)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<UserBriefBean> deptMgtUsers = udpRpc.getDeptManagerId(orgId, deptIds).stream()
            .filter(item -> StringUtils.isNotEmpty(item.getManagerId()))
            .map(item -> {
                UserBriefBean userBrief = new UserBriefBean();
                userBrief.setId(item.getManagerId());
                userBrief.setPosId(item.getDeptId());
                return userBrief;
            }).collect(Collectors.toList());
        udpLiteUserRepository.fillSpUserInfo(orgId, deptMgtUsers, UserBriefBean::getId, (userBrief, udpUser) -> {
            if (udpUser.isSpUser() && YesOrNo.NO.getValue() == udpUser.getDeleted()) {
                userBrief.setImgUrl(udpUser.getImgUrl());
                userBrief.setFullname(udpUser.getFullname());
                userBrief.setUsername(udpUser.getUsername());
                }
        }, Lists.newArrayList("username", ConstantPool.F_FULLNAME,"imgUrl"));
        //用户名不存在表示未关联有效用户
        IArrayUtils.remove(deptMgtUsers, item -> StringUtils.isEmpty(item.getUsername()));
        Map<String, List<UserBriefBean>> deptMgtUserMap = IArrayUtils.listToMultiMap(deptMgtUsers, UserBriefBean::getPosId);
        list.forEach(item -> {
            String deptId = deptIdGetter.apply(item);
            consumerDept.accept(item, deptMgtUserMap.get(deptId));
        });
    }

    private void calcRiskLevelId(HeirPosRiskSupport posRaw, List<RiskLevelValueDTO> riskLevels) {
        if (posRaw.getRiskRuleType() == HeirRiskRuleTypeEnum.AUTO.getType()
            && posRaw.getHeirTargetQty() > 0) {
            BigDecimal posPtg = new BigDecimal(posRaw.getHeirValidQty()).multiply(TalentBkConstants.BIG_DECIMAL_100)
                .divide(new BigDecimal(posRaw.getHeirTargetQty()), 4, RoundingMode.FLOOR);
            BigDecimal minOver = null;
            RiskLevelValueDTO finalRisk = null;
            //取差值最小的
            for (RiskLevelValueDTO riskLevel : riskLevels) {
                BigDecimal posOver = posPtg.subtract(riskLevel.getValue());
                if (posOver.compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }
                if (minOver == null || posOver.compareTo(minOver) < 0) {
                    minOver = posOver;
                    finalRisk = riskLevel;
                }
            }
            if (finalRisk != null) {
                posRaw.setRiskLevelId(finalRisk.getId());
                posRaw.setRiskLevelName(finalRisk.getLevelName());
                posRaw.setRiskLevelColor(finalRisk.getColorCode());
            }
        } else if (posRaw.getRiskRuleType() == HeirRiskRuleTypeEnum.MANUAL.getType()) {
            Long riskLevelId = posRaw.getRiskLevelId();
            RiskLevelValueDTO finalRisk = null;
            if (riskLevelId != null) {
                finalRisk = IArrayUtils.getFirstMatch(riskLevels, item -> riskLevelId.equals(item.getId()));
            }
            if (finalRisk != null) {
                posRaw.setRiskLevelName(finalRisk.getLevelName());
                posRaw.setRiskLevelColor(finalRisk.getColorCode());
            }
        }
    }

    private List<RiskLevelValueDTO> riskLevel(String orgId) {
        List<HeirRiskResp> levelCfgList = orgLevelConfigService.getHeirRiskData(orgId);
        List<RiskLevelValueDTO> ret = new ArrayList<>();
        for (HeirRiskResp entity : levelCfgList) {
            CfgValueBean cfgVal = entity.getCfgValueBean();
            if (cfgVal != null && cfgVal.getValue() != null) {
                RiskLevelValueDTO valueDTO = new RiskLevelValueDTO();
                BeanCopierUtil.copy(cfgVal, valueDTO);
                valueDTO.setId(entity.getId());
                valueDTO.setLevelName(entity.getLevelName1());
                valueDTO.setColorCode(entity.getColorCode());
                ret.add(valueDTO);
            }
        }
        return ret;
    }

    /**
     *
     * @param user
     * @param posId
     * @return
     */
    public List<UdpUserBriefBean> allBenchmarkUserId(UserBasicBean user, String posId, int posType) {
        List<String> userIds;
        String orgId = user.getOrgId();
        if (posType == HeirPosTypeEnum.POSITION.getType()) {
            userIds =  posBenchmarkMapper.posUserIds(posId);
        } else {
            userIds = Lists.newArrayList();
            DeptUserMapBean deptUserMapBean = IArrayUtils.getFirst(
                udpRpc.getDeptManagerId(orgId, Lists.newArrayList(posId)));
            if (deptUserMapBean != null && StringUtils.isNotEmpty(deptUserMapBean.getManagerId())) {
                userIds.add(deptUserMapBean.getManagerId());
            }
        }
        List<UdpUserBriefBean> ret = new ArrayList<>(userIds.size());
        udpLiteUserRepository.fillUserInfo(orgId, userIds, item -> item,
            (userId, userBrief) -> ret.add(userBrief),
            Lists.newArrayList("username",ConstantPool.F_FULLNAME, "imgUrl","deptName","status"));
        return ret;
    }

    @DbHintMaster
    public void addBenchmarkUser(UserBasicBean user, String posId, String userId, boolean enabled) {
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_BENCHMARK_OPERATE, posId);
        CommonUtils.lockRun(lockKey,1, TimeUnit.MINUTES, () -> {
            if (enabled) {
                HeirPosUserBean existUser = IArrayUtils.getFirst(posBenchmarkMapper.posUser(posId, Lists.newArrayList(userId),null));
                if (existUser == null) {
                    HeirPosBenchmarkEntity benchmark = new HeirPosBenchmarkEntity();
                    benchmark.init(user.getOrgId(), user.getUserId());
                    benchmark.setPosId(posId);
                    benchmark.setUserId(userId);
                    posBenchmarkMapper.insert(benchmark);
                } else if (existUser.getDeleted() == YesOrNo.YES.getValue()) {
                    existUser.setUpdateDeleted(YesOrNo.NO.getValue());
                    posBenchmarkMapper.updateDeleted(Lists.newArrayList(existUser));
                }
            } else {
                posBenchmarkMapper.removeByUserIds(posId, Lists.newArrayList(userId));
            }
        });
    }

    private void replaceBenchmarkUser(UserBasicBean user, String posId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        userIds = udpLiteUserRepository.existUserIds(user.getOrgId(), userIds);
        if (CollectionUtils.isNotEmpty(userIds)) {
            String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_HEIR_POS_BENCHMARK_OPERATE, posId);
            List<String> finalUserIds = userIds;
            CommonUtils.lockRun(lockKey,1, TimeUnit.MINUTES, () -> {
                List<HeirPosUserBean> existUserIds = posBenchmarkMapper.posUser(posId, null,null);
                for (HeirPosUserBean existUserId : existUserIds) {
                    existUserId.setUpdateDeleted(CommonUtils.bool2Int(!finalUserIds.contains(existUserId.getUserId())));
                }
                BatchOperationUtil.batchSave(existUserIds.stream()
                        .filter(item -> !Objects.equals(item.getDeleted(), item.getUpdateDeleted()))
                        .collect(Collectors.toList()),
                    posBenchmarkMapper::updateDeleted);
                Set<String> existUserIdSet = existUserIds.stream()
                    .map(HeirPosUserBean::getUserId).collect(Collectors.toSet());
                IArrayUtils.remove(finalUserIds, existUserIdSet::contains);
                List<HeirPosBenchmarkEntity> list = BeanCopierUtil.convertList(finalUserIds, userId -> {
                    HeirPosBenchmarkEntity benchmark = new HeirPosBenchmarkEntity();
                    benchmark.init(user.getOrgId(), user.getUserId());
                    benchmark.setPosId(posId);
                    benchmark.setUserId(userId);
                    return benchmark;
                });
                BatchOperationUtil.batchSave(list, posBenchmarkMapper::insertBatchSomeColumn);
            });
        }
    }

    private List<HeirPosRawBean> allPos(String orgId, int posType) {
        List<HeirPosRawBean> list = heirPosRepository.allPos(orgId, posType);
        for (HeirPosRawBean posRaw : list) {
            initHeirPosRaw(posRaw);
        }
        return list;
    }

    private void initHeirPosRaw(HeirPosRawBean posRaw) {
        if (posRaw.getSourceDeleted() == null) {
            posRaw.setSourceDeleted(0);
        }
        if (posRaw.getHeirTargetQty() == null) {
            posRaw.setHeirTargetQty(0);
        }
        if (posRaw.getHeirValidQty() == null) {
            posRaw.setHeirValidQty(0);
        }
        if (posRaw.getRiskRuleType() == null) {
            posRaw.setRiskRuleType(HeirRiskRuleTypeEnum.AUTO.getType());
        }
        if (posRaw.getPermissionFlag() == null) {
            posRaw.setPermissionFlag(0);
        }
        if (posRaw.getRiskRuleType() == HeirRiskRuleTypeEnum.AUTO.getType()) {
            posRaw.setRiskLevelId(null);
        }
    }

    /**
     * 初始化继任
     * 主要针对部门类型的继任（部门同步延迟可能导致没有对应的部门继任）
     */
    public HeirPosExt init(UserCacheBasic currentUser, String posId, Integer posType) {
        if (posType != HeirPosTypeEnum.DEPT.getType()) {
            log.error("not support init. posType: {}", posType);
            throw new ApiException(BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE);
        }
        String deptName = udpDeptRepository.getNameById(currentUser.getOrgId(), posId);
        Validate.isNotNull(deptName, BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE);
        HeirPosEntity posEntity = HeirPosEntity.createEntity(currentUser.getOrgId(), currentUser.getUserId());
        posEntity.setId(posId);
        posEntity.setPosName(deptName);
        posEntity.setPosType(HeirPosTypeEnum.DEPT.getType());
        posEntity.init(currentUser.getUserId());
        heirPosMapper.insert(posEntity);
        log.info("init heirPos finish. orgId: {} posId: {} posName: {}", currentUser.getOrgId(), posId, posEntity.getPosName());
        HeirPosExt heirPosExt = new HeirPosExt();
        BeanCopierUtil.copy(posEntity, heirPosExt, false);
        return heirPosExt;
    }

    public PagingList<DwdHeirPosResp> listPage4Open(PageRequest pageReq, String orgId) {
        Page<DwdHeirPosBean> queryPage = new Page<>(pageReq.getCurrent(), pageReq.getSize(),
            pageReq.getCurrent() < 2 ? true : false);
        IPage<DwdHeirPosBean> pageRet = heirPosMapper.listPage4Open(queryPage, orgId);
        List<DwdHeirPosBean> list = pageRet.getRecords();
        if (CollectionUtils.isNotEmpty(list)) {
            List<RiskLevelValueDTO> riskLevels = riskLevel(orgId);
            for (DwdHeirPosBean heirPos : list) {
                calcRiskLevelId(heirPos, riskLevels);
            }
        }
        return BeanCopierUtil.toPagingList(pageRet, item -> {
            DwdHeirPosResp resp = new DwdHeirPosResp();
            BeanCopierUtil.copy(item, resp);
            resp.setPosId(item.getDeptOrPosId());
            return resp;
        });
    }

    public PagingList<DwdHeirPosUserResp> listUserPage4Open(PageRequest pageReq, String orgId, int userType) {
        //userType 0:继任用户，1:标杆用户
        Page<DwdHeirPosUserBean> queryPage = new Page<>(pageReq.getCurrent(), pageReq.getSize(),
            pageReq.getCurrent() < 2 ? true : false);
        IPage<DwdHeirPosUserBean> pageRet;
        if (userType == 0) {
            pageRet = heirPosUserMapper.listPage4Open(queryPage, orgId);
            Map<Long, HeirPrepareBean> prepareMap = queryPrepareMap(orgId, null);
            for (DwdHeirPosUserBean record : pageRet.getRecords()) {
                HeirPrepareBean prepareBean = prepareMap.get(record.getPrepareLevelId());
                if (prepareBean != null) {
                    record.setLevelName(prepareBean.getLevelName());
                    record.setLevelColor(prepareBean.getColorCode());
                }
            }
        } else {
            userType = 1;
            pageRet = posBenchmarkMapper.listPage4Open(queryPage, orgId);
        }
        Map<String, String> idPosIdMap = new HashMap<>(pageRet.getRecords().size());
        for (DwdHeirPosUserBean record : pageRet.getRecords()) {
            idPosIdMap.put(record.getPosId(), null);
            record.setUserType(userType);
        }
        if (!idPosIdMap.isEmpty()) {
            heirPosMapper.queryDeptOrPosIdByIds(idPosIdMap.keySet())
                    .forEach(item -> idPosIdMap.put(item.getId(), item.getDeptOrPosId()));
        }
        return BeanCopierUtil.toPagingList(pageRet, item -> {
            DwdHeirPosUserResp resp = new DwdHeirPosUserResp();
            BeanCopierUtil.copy(item, resp);
            resp.setPosId(idPosIdMap.get(item.getPosId()));
            return resp;
        });
    }

    public PagingList<PosBenchmarkUserResp> listPositionUser(PageRequest pageReq,
                                                             String orgId, String positionId,
                                                             String name) {
        Page<PosUserListResp> page = new Page<>(pageReq.getCurrent(), pageReq.getSize(), true);
        QueryUserBean queryUser = new QueryUserBean();
        queryUser.setPositionId(positionId);
        queryUser.setFullname(name);
        queryUser.setStatus(YesOrNo.YES.getValue());
        IPage<UdpUserBriefBean> userInfoPage = udpLiteUserRepository.briefPage(page, orgId, queryUser);
        PagingList<PosBenchmarkUserResp> ret = BeanCopierUtil.toPagingList(userInfoPage, item -> {
            PosBenchmarkUserResp posUser = new PosBenchmarkUserResp();
            posUser.setId(item.getId());
            posUser.setUsername(item.getUsername());
            posUser.setFullname(item.getFullname());
            posUser.setDeptName(item.getDeptName());
            posUser.setPositionName(item.getPositionName());
            return posUser;
        });
        if (!ret.getDatas().isEmpty()) {
            Set<String> existUserSet = posBenchmarkMapper.posUser(positionId, ret.getDatas().stream()
                .map(PosBenchmarkUserResp::getId).collect(Collectors.toList()),YesOrNo.NO.getValue())
                .stream().map(HeirPosUserBean::getUserId).collect(Collectors.toSet());
            ret.getDatas().forEach(item -> item.setBenchmarkUser(existUserSet.contains(item.getId())));
        }
        return ret;
    }

    public List<HeirUserPosBriefBean> userPosList(String orgId, String userId) {
        List<HeirUserPosBriefBean> list = heirPosRepository.userPosList(orgId, userId);
        List<String> positionIds = new ArrayList<>();
        List<String> pathDeptIds = new ArrayList<>();
        List<String> deptIds = new ArrayList<>();
        List<Long> prepareIds = new ArrayList<>();
        for (HeirUserPosBriefBean userPos : list) {
            if (userPos.getPosType() == HeirPosTypeEnum.POSITION.getType()) {
                positionIds.add(userPos.getPosId());
            } else if (StringUtils.isNotEmpty(userPos.getIdFullPath())) {
                deptIds.add(userPos.getPosId());
                for (String deptId : userPos.getIdFullPath().split(StringPool.SEMICOLON)) {
                    pathDeptIds.add(deptId);
                }
            }
            if (userPos.getPrepareLevelId() != null) {
                prepareIds.add(userPos.getPrepareLevelId());
            }
        }
        List<IdName> posNames = udpRpc.queryPositionNameByIds(orgId, positionIds);
        List<IdName> deptNames = udpRpc.queryDeptNameByIds(orgId, pathDeptIds);
        List<DeptUserMapBean> deptManageList = udpRpc.getDeptManagerId(orgId, deptIds);
        Map<String, String> nameMap = new HashMap<>();
        posNames.forEach(item -> nameMap.put(item.getId(), item.getName()));
        deptNames.forEach(item -> nameMap.put(item.getId(), item.getName()));
        userPosListFill(orgId, list, deptManageList, nameMap);
        return list;
    }

    private void userPosListFill(String orgId, List<HeirUserPosBriefBean> list,
                                 List<DeptUserMapBean> deptManageList,
                                 Map<String, String> nameMap) {
        Locale locale = authService.getLocale();
        Map<Long, HeirPrepareBean> prepareMap = queryPrepareMap(orgId, locale);
        for (HeirUserPosBriefBean userPos : list) {
            if (userPos.getPosType() == HeirPosTypeEnum.POSITION.getType()) {
                userPos.setName(Optional.ofNullable(nameMap.get(userPos.getPosId())).orElse(userPos.getName()));
            } else {
                IArrayUtils.getFirstMatch(deptManageList, item -> {
                    if (userPos.getPosId().equals(item.getDeptId())) {
                        userPos.setDeptManagerName(item.getManagerName());
                        return true;
                    }
                    return false;
                });
                if (StringUtils.isNotEmpty(userPos.getIdFullPath())) {
                    List<String> deptPath = new ArrayList<>();
                    for (String deptId : userPos.getIdFullPath().split(StringPool.SEMICOLON)) {
                        String deptName = nameMap.get(deptId);
                        if (StringUtils.isBlank(deptName)) {
                            continue;
                        }
                        deptPath.add(deptName);
                        //最后一个就是用户当前部门
                        userPos.setName(deptName);
                    }
                    userPos.setDeptPath(deptPath);
                }
            }
            HeirPrepareBean prepareBean = prepareMap.get(userPos.getPrepareLevelId());
            if (prepareBean != null) {
                userPos.setPrepareName(prepareBean.getLevelName());
                userPos.setPrepareColor(prepareBean.getColorCode());
            }
        }
    }

    public List<OpenHeirPosResp> allHeir4Model(String orgId, String deptId, int posType) {
        orgId = udpQueryService.demoCopyOrgId(orgId);
        int currentYear = Calendar.getInstance().get(Calendar.YEAR);
        return BeanCopierUtil.convertList(dwdHeirPosMapper.listTop(orgId, currentYear, deptId, posType),
                DwdHeirPosEntity.class, OpenHeirPosResp.class);
    }

    public List<OpenHeirPosUserResp> heirUser4Model(String orgId, String posId, int userType, String deptId) {
        orgId = udpQueryService.demoCopyOrgId(orgId);
        Map<String, Object> param = new HashMap<>();
        param.put("user_type", userType);
        param.put("org_id", orgId);
        param.put("pos_id", posId);
        List<OpenHeirPosUserResp> list = BeanCopierUtil.convertList(spmodelSqlRpc.query(orgId, DwdHeirPosUserEntity.class,
            Lists.newArrayList("userId","userType","levelName","levelColor"), param, "order by update_time desc"),
            DwdHeirPosUserEntity.class, OpenHeirPosUserResp.class);
        udpLiteUserRepository.fillUserInfo(orgId, list, OpenHeirPosUserResp::getUserId, (posUser, udpUser) -> {
            posUser.setUserId(udpUser.getId());
            posUser.setFullname(udpUser.getFullname());
            //posUser.setThirdUserId(udpUser.getThirdUserId());
        }, Lists.newArrayList(ConstantPool.F_FULLNAME, "thirdUserId", "userId"));
        List<String> userIds = list.stream().map(OpenHeirPosUserResp::getUserId)
            .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userIds)) {
            Map<String, Object> queryUserParam = new HashMap<>();
            queryUserParam.put("user_id", userIds);
            Map<String, UserBasicEntity> userMap = StreamUtil.list2map(spmodelSqlRpc.query(orgId, UserBasicEntity.class,
                Lists.newArrayList("userId","age","avatarUrl","gender","serviceYears","thirdJobgradeName"),
                queryUserParam), UserBasicEntity::getUserId);
            for (OpenHeirPosUserResp posUser : list) {
                UserBasicEntity basicEntity = userMap.get(posUser.getUserId());
                if (basicEntity != null) {
                    posUser.setThirdAge(basicEntity.getAge());
                    posUser.setThirdAvatarUrl(basicEntity.getAvatarUrl());
                    posUser.setThirdGender(basicEntity.getGender());
                    posUser.setThirdServiceYears(basicEntity.getServiceYears());
                    posUser.setThirdJobgradeName(basicEntity.getThirdJobgradeName());
                }
            }
            if (StringUtils.isNotEmpty(deptId)) {
                //设置用户是否是当前部门及子部门下的
                List<String> targetDeptIds = dwdUserMapper.userUnderDept(orgId, deptId, userIds);
                list.forEach(item -> item.setUserUnderDept(CommonUtils.bool2Int(targetDeptIds.contains(item.getUserId()))));
            }
        }
        Map<String, UserLabelVO> userLabelMap = StreamUtil.list2map(
            tagSearchRpc.userLabelList(orgId, list.stream().map(OpenHeirPosUserResp::getUserId)
                .collect(Collectors.toList())), UserLabelVO::getUserId);
        for (OpenHeirPosUserResp posUser : list) {
            UserLabelVO userLabel = userLabelMap.get(posUser.getUserId());
            if (userLabel == null) {
                continue;
            }
            posUser.setUserTagList(Lists.newArrayList());
            IArrayUtils.list2Map(userLabel.getLabelList(), LabelVO::getLabelCategorySystemCode)
                .forEach((sysCode, subList) -> {
                    //产品需求只返回【身份标签】和【荣誉标签】
                    if ("IDENTITY".equalsIgnoreCase(sysCode) || "HONOR".equalsIgnoreCase(sysCode)) {
                        UserTag4Get tag4Get = new UserTag4Get();
                        tag4Get.setLabelCategorySystemCode(sysCode);
                        tag4Get.setTagValues(Lists.newArrayList());
                        subList.forEach(labelVO -> IArrayUtils.forEach(labelVO.getLabelValueList(), labelValueVO -> {
                            TagValue4Get tagVal = new TagValue4Get();
                            tagVal.setLabelValue(labelValueVO.getLabelValue());
                            tagVal.setLabelValueId(String.valueOf(labelValueVO.getLabelId()));
                            tag4Get.getTagValues().add(tagVal);
                        }));
                        posUser.getUserTagList().add(tag4Get);
                    }
                });
        }
        return list;
    }

    public Map<Long, HeirPrepareBean> queryPrepareMap(String orgId, Locale locale) {
        Map<Long, HeirPrepareBean> prepareMap = new HashMap<>();
        orgLevelConfigService.getHeirPrepareData(orgId)
            .forEach(item -> {
                String name = item.getLevelName1();
                if (locale != null) {
                    if (Locale.SIMPLIFIED_CHINESE.equals(locale)) {
                        name = item.getLevelName1();
                    } else if (Locale.TRADITIONAL_CHINESE.equals(locale)) {
                        name = item.getLevelName3();
                    } else if (Locale.ENGLISH.equals(locale)) {
                        name = item.getLevelName2();
                    }
                }
                HeirPrepareBean itemBean = new HeirPrepareBean();
                BeanCopierUtil.copy(item, itemBean);
                itemBean.setLevelName(name);
                prepareMap.put(item.getId(), itemBean);
            });
        return prepareMap;
    }
}
