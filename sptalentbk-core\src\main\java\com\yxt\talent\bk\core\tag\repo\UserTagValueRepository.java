package com.yxt.talent.bk.core.tag.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.core.tag.bean.TagValueBean;
import com.yxt.talent.bk.core.tag.entity.UserTagValueEntity;
import com.yxt.talent.bk.core.tag.mapper.UserTagValueMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/15
 */
@Repository
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class UserTagValueRepository extends ServiceImpl<UserTagValueMapper, UserTagValueEntity> {

    private final UserTagValueMapper userTagValueMapper;

    public List<UserTagValueEntity> findByUserId(String orgId, String userId) {
        LambdaQueryWrapper<UserTagValueEntity> wrapper = getQueryWrapper();
        wrapper.eq(UserTagValueEntity::getOrgId, orgId);
        wrapper.eq(UserTagValueEntity::getUserId, userId);
        wrapper.eq(UserTagValueEntity::getDeleted, 0);
        return list(wrapper);
    }

    private LambdaQueryWrapper<UserTagValueEntity> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }


    public void deleteByTagIdsAndUserIds(String orgId,
            @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) Collection<String> tagIds,
            @NotEmpty(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) Collection<String> userIds,
            String operatorId) {
        userTagValueMapper.deleteByTagIdsAndUserIds(orgId, tagIds, userIds, operatorId,
                DateUtils.localDataTime2UData(LocalDateTime.now()));
    }

    public List<TagValueBean> findValueNameTagIdLike(String orgId, String keyword) {
        return userTagValueMapper.findValueNameTagIdLike(orgId, keyword);
    }
}
