package com.yxt.talent.bk.core.udp.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.bk.common.bean.udp.UdpLangKeywordBean;
import lombok.Data;

/**
 * QueryUserBean
 *
 * <AUTHOR> geyan
 * @Date 8/9/23 3:49 pm
 */
@Data
public class QueryUserBean {
    private String positionId;
    private String fullname;
    /**
     * (0-禁用,1-启用)'
     */
    private Integer status;

    @JsonIgnore
    private UdpLangKeywordBean keywordLangMatch;

    private String deptId;
}
