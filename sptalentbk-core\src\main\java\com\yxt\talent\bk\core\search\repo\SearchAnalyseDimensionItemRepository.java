package com.yxt.talent.bk.core.search.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.search.entity.SearchAnalyseDimensionItem;
import com.yxt.talent.bk.core.search.mapper.SearchAnalyseDimensionItemMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Deprecated
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class SearchAnalyseDimensionItemRepository  extends ServiceImpl<SearchAnalyseDimensionItemMapper, SearchAnalyseDimensionItem> {

    public List<SearchAnalyseDimensionItem> findByOrgId(String orgId){
        LambdaQueryWrapper<SearchAnalyseDimensionItem> query = new LambdaQueryWrapper<>();
        query.eq(SearchAnalyseDimensionItem::getOrgId, orgId);
        query.orderByAsc(SearchAnalyseDimensionItem::getOrderIndex);
        return list(query);
    }
}
