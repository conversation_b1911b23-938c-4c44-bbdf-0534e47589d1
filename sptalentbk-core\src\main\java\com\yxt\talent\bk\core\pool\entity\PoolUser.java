package com.yxt.talent.bk.core.pool.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.CreatorEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *  人才池与人员关系业务表
 * <AUTHOR>
 * @since 2022/9/8 9:41
 * @version 1.0
 */
@Data
@NoArgsConstructor
@TableName("bk_pool_user")
@EqualsAndHashCode(callSuper = true)
public class PoolUser extends CreatorEntity {

    @TableField("id")
    @Schema(description = "主键")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID)
    private String id;

    @TableField("org_id")
    @Schema(description = "机构id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ORG_ID)
    private String orgId;

    @TableField("pool_id")
    @Schema(description = "人才池id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_POOL_ID)
    private String poolId;

    @TableField("user_id")
    @Schema(description = "人员id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String userId;

    @TableField("readiness_id")
    @Schema(description = "准备度id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_POOL_READINESS_ID)
    private String readinessId;

    @TableField("event_pool_out")
    @Schema(description = "出池事件枚举(0：在池;1:合格出池且任用;2:合格出池;3:不合格出池;4:未完成中途退出;5:离职)")
    private Integer eventPoolOut;

    @TableField("remark")
    @Schema(description = "备注/去向")
    private String remark;

    /**
     * 出池时间
     */
    @TableField("out_time")
    private Date outTime;

    /**
     * 是否最近一次入池
     */
    @TableField("recently")
    private Integer recently;
}
