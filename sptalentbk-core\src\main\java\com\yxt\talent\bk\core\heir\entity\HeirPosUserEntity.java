package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.SnowFlowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * HeirPosUserEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:54 am
 */
@Data
@TableName("bk_heir_pos_user")
public class HeirPosUserEntity extends SnowFlowIdEntity {
    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "用户id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String userId;

    @Schema(description = "bk_heir_pos pkId")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_POS_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String posId;

    @Schema(description = "0:岗位，1:部门")
    private Integer posType;

    @Schema(description = "准备度规则等级id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_ORG_LEVEL_CFG_ID)
    private Long prepareLevelId;

    @Schema(description = "计算的准备度id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_ORG_LEVEL_CFG_ID)
    private Long calcLevelId;

    @Schema(description = "0:进行中，1:已退出")
    private Integer heirStatus;

    @Schema(description = "退出原因")
    private String quitReason;

    @Schema(description = "退出时间")
    private Date exitTime;

    @Schema(description = "处理状态：-1:重置,0:暂不处理,1:已处理")
    private Integer modifyStatus;
}
