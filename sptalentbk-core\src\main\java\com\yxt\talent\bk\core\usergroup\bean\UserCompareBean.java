package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Data
public class UserCompareBean {

    @Schema(description = " 绩效时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer performanceTime = 0;
    @Schema(description = "培训搜索时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer trainingTime;
    @Schema(description = "奖惩时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer rewardTime;
	@Schema(description = "用户 ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private String userId;
}
