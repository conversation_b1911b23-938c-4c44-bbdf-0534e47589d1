package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * @since 2020/8/26 13:55 13:55
 * <AUTHOR>
 */
@Data
@Schema(name = "能力等级")
public class SkillLevelMap4Create {

	@Schema(description = "等级")
	private int level;

	@Schema(description = "等级名称")
	private String name;

	@Schema(description = "等级描述")
	private String description;

	@Schema(description = "等级行为表现")
	private String behavior;
}
