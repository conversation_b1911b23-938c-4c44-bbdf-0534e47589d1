package com.yxt.talent.bk.core.heir.repo;

import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.heir.entity.PackageDataEntity;
import com.yxt.talent.bk.core.heir.mapper.PackageDataMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * PackageDataRepository
 *
 * <AUTHOR> harleyge
 * @Date 26/7/24 10:16 am
 */
@Slf4j
@Repository
@AllArgsConstructor
public class PackageDataRepository {
    private final PackageDataMapper packageDataMapper;

    public Long savePackage(String orgId, String bizType, String masterId, String bizData) {
        String bizDataMd5 = CommonUtils.md5Hex(bizData);
        Long dataId = packageDataMapper.getByBizDataMd5(orgId, bizType, bizDataMd5);
        if (dataId != null) {
            return dataId;
        }
        PackageDataEntity dataEntity = new PackageDataEntity();
        dataEntity.setBizType(bizType);
        dataEntity.setMasterId(masterId);
        dataEntity.setBizData(bizData);
        dataEntity.setBizDataMd5(bizDataMd5);
        dataEntity.init(orgId);
        packageDataMapper.insert(dataEntity);
        return dataEntity.getId();
    }
}
