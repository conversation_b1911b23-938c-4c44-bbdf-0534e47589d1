package com.yxt.talent.bk.svc.base;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Data
@Service
@RefreshScope
public class BkConfigService {

    @Value("#{'${elasticsearch.indexName:null}' ?: T(com.yxt.talent.bk.common.constants.TalentBkConstants).ES_INDEX}")
    private String indexName;

    @Value("${sptalentbk.demo.org.id:}")
    private String demoOrgId;

    @Value("${sptalentbk.audit.log.enabled:true}")
    private boolean auditLogEnabled;

    @Value("${sptalentbk.save.log.enabled:false}")
    private boolean saveLogEnabled;

    @Value("${sptalentbk.save.log.ding:}")
    private String saveLogDing;

    @Value("${sptalentbk.save.log.observer:}")
    private String saveLogObserver;
}
