package com.yxt.talent.bk.core.udp.bean.open;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.common.Constants;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * UserBasicEntity
 *
 * <AUTHOR> geyan
 * @Date 20/9/23 10:23 am
 */
@SqlTable(UserBasicEntity.TABLE)
@Data
public class UserBasicEntity {
    public static final String TABLE = "dws_user";

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "三方用户id")
    private String thirdUserId;

    @Schema(description = "udp用户id")
    private String userId;

    @Schema(description = "禁用/启用(0-禁用 1-启用)")
    private Integer enabled;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "照片地址")
    private String avatarUrl;

    @Schema(description = "大学")
    private String university;

    @Schema(description = "学历")
    private String education;

    @Schema(description = "性别(0-未知 1-男 2-女)")
    private Integer gender;

    @Schema(description = "政治面貌")
    private String political;

    @Schema(description = "婚姻面貌(0-未知 1-已婚 2-未婚)")
    private Integer marital;

    @Schema(description = "入职时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime entryTime;

    @Schema(description = "司龄")
    private Integer serviceYears;

    @JsonIgnore
    @Schema(description = "三方部门id")
    private String thirdDeptId;

    @Schema(description = "udp部门id")
    private String deptId;

    @Schema(description = "部门名称")
    private String thirdDeptName;

    @Schema(description = "三方部门名称全路径，销售部->广州大区->销售一组")
    private String thirdDeptNamePath;

    @JsonIgnore
    @Schema(description = "三方职级id")
    private String thirdJobgradeId;

    @Schema(description = "udp职级id")
    private String jobgradeId;

    @Schema(description = "职级名称")
    private String thirdJobgradeName;

    @JsonIgnore
    @Schema(description = "三方岗位id")
    private String thirdPositionId;

    @Schema(description = "udp岗位id")
    private String positionId;

    @Schema(description = "岗位名称")
    private String thirdPositionName;

    @Schema(description = "是否管理者(0-否 1-是)")
    private Integer manager;

    @Schema(description = "跨部门任职意愿")
    private String willingness;

    @Schema(description = "可接受工作调配地")
    private String workLocation;

    @Schema(description = "可接受调派时间")
    private String dispatchTime;

    @Schema(description = "职业资格证书，多个证书使用半角分号分割")
    private String profCerts;

    @Schema(description = "工作年限")
    private String workYears;

    @Schema(description = "专业")
    private String major;

    @Schema(description = "项目盘点结果-能力维度（0-默认，1-低，2-中，3-高）")
    private Integer ability;
    @Schema(description = "项目盘点结果-潜力维度（0-默认，1-低，2-中，3-高）")
    private Integer potential;
    @Schema(description = "项目盘点结果-绩效维度（0-默认，1-低，2-中，3-高）")
    private Integer performance;
}
