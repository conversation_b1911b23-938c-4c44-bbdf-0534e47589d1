package com.yxt.talent.bk.svc.scheme.component;

import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spmodel.facade.bean.label.LabelParam;
import com.yxt.spmodel.facade.bean.label.LabelVO;
import com.yxt.spmodel.facade.bean.label.LabelValueVO;
import com.yxt.talent.bk.common.bean.searchrule.SPTagSearchBean;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.talent.bk.svc.scheme.bean.SearchScheme4Log;
import com.yxt.talent.bk.svc.scheme.bean.SearchSchemeBean;
import com.yxt.talent.bk.svc.usergroup.rpc.TagSearchRpc;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SearchSchemeCreateLogProvider
 *
 * <AUTHOR> geyan
 * @Date 22/3/24 5:43 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class SearchSchemeCreateLogProvider implements AuditLogDataProvider<SearchSchemeBean, SearchScheme4Log> {
    private final TagSearchRpc tagSearchRpc;
    @Override
    public SearchScheme4Log before(SearchSchemeBean param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public SearchScheme4Log after(SearchSchemeBean param, AuditLogBasicBean logBasic) {
        SearchScheme4Log schemeLog = new SearchScheme4Log();
        schemeLog.setSchemeName(param.getSchemeName());
        StringBuilder conditionDesc = new StringBuilder("满足下列所有条件：").append("\r\n");
        LabelParam queryLabel = new LabelParam();
        queryLabel.setOrgId(logBasic.getOrgId());
        queryLabel.setLabelList(BeanCopierUtil.convertList(param.getTagSearch(), tagSearch -> {
            LabelParam.LabelAndValue labelAndValue = new LabelParam.LabelAndValue();
            labelAndValue.setLabelId(tagSearch.getTagId());
            List<Long> labelValueIds = tagSearch.getTagValues().stream().map(str -> CommonUtils.tryParseLong(str, null))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (labelValueIds.isEmpty()) {
                labelValueIds.add(0L);
            }
            labelAndValue.setLabelValueIds(labelValueIds);
            return labelAndValue;
        }));
        if (CollectionUtils.isNotEmpty(queryLabel.getLabelList())) {
            List<LabelVO> labelList = tagSearchRpc.getLabelList(queryLabel);
            for (SPTagSearchBean tagSearch : param.getTagSearch()) {
                LabelVO labelVO = IArrayUtils.getFirstMatch(labelList, item -> item.getLabelId().equals(tagSearch.getTagId()));
                if (labelVO != null) {
                    conditionDesc.append(labelVO.getName()).append("：");
                    if (CollectionUtils.isNotEmpty(labelVO.getLabelValueList())) {
                        for (String tagValueIdStr : tagSearch.getTagValues()) {
                            LabelValueVO labelValueVO = IArrayUtils.getFirstMatch(labelVO.getLabelValueList(),
                                    item -> String.valueOf(item.getLabelValueId()).equals(tagValueIdStr));
                            if (labelValueVO != null) {
                                conditionDesc.append(labelValueVO.getLabelValue()).append(",");
                            }
                        }
                    }
                    conditionDesc.setCharAt(conditionDesc.length() - 1, ';');
                    conditionDesc.append("\r\n");
                }
            }
        }
        schemeLog.setConditionDesc(conditionDesc.toString());
        return schemeLog;
    }

    @Override
    public Pair<String, String> entityInfo(SearchSchemeBean param, SearchScheme4Log beforeObj, SearchScheme4Log afterObj, AuditLogBasicBean logBasic) {
        return Pair.of(String.valueOf(param.getId()),
                String.format("人才列表-筛选器-%s", param.getSchemeName()));
    }
}
