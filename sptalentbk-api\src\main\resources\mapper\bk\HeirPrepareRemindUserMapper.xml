<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirPrepareRemindUserMapper">
    <insert id="batchInsert">
        insert into bk_heir_prepare_remind_user (id,org_id, pos_id, user_id,
                                                 create_user_id, create_time, update_user_id,
                                                 update_time, deleted)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id,jdbcType=BIGINT},#{item.orgId,jdbcType=VARCHAR}, #{item.posId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},
            #{item.createUserId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.deleted,jdbcType=TINYINT})
        </foreach>
    </insert>

    <select id="selectUserIdsByPrepareCfgId" resultType="com.yxt.talent.bk.core.heir.entity.HeirPrepareRemindUserEntity">
        select * from bk_heir_prepare_remind_user where pos_id=#{posId} and deleted=0
    </select>

    <delete id="deleteUsers">
        delete from bk_heir_prepare_remind_user where org_id=#{orgId} and pos_id =#{posId}
    </delete>

    <select id="posRemindUserIds" resultType="string">
        select user_id from bk_heir_prepare_remind_user where pos_id=#{posId} and deleted=0
    </select>
</mapper>
