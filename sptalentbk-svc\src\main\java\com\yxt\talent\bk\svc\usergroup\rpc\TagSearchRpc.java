package com.yxt.talent.bk.svc.usergroup.rpc;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageBean;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.spmodel.facade.bean.UserBaseSearch;
import com.yxt.spmodel.facade.bean.label.LabelParam;
import com.yxt.spmodel.facade.bean.label.LabelUserParam;
import com.yxt.spmodel.facade.bean.label.LabelUserVO;
import com.yxt.spmodel.facade.bean.label.LabelVO;
import com.yxt.spmodel.facade.bean.label.UserLabelParam;
import com.yxt.spmodel.facade.bean.label.UserLabelVO;
import com.yxt.spmodel.facade.service.SpmodelLabelService;
import com.yxt.talent.bk.common.bean.searchrule.BaseSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SPTagSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import com.yxt.talent.bk.common.utils.SqlUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @program: sptalentbkapi
 * @description:
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class TagSearchRpc {
    private final SpmodelLabelService spmodelLabelService;

    public PagingList<LabelUserVO> searchUserByTag(String orgId, SearchRuleBean searchRuleBean, BaseSearchBean baseSearchBean, PageBean pageBean) {
        try {
            LabelUserParam labelUserParam = new LabelUserParam();
            labelUserParam.setOrgId(orgId);
            UserBaseSearch userBaseSearch = BeanHelper.json2Bean(BeanHelper.bean2Json(baseSearchBean), UserBaseSearch.class);
            if (userBaseSearch.getSearchType() == null){
                // 默认交集
                userBaseSearch.setSearchType(1);
            }
            userBaseSearch.setKeyword(SqlUtils.escapeLike(userBaseSearch.getKeyword()));
            labelUserParam.setBaseSearch(userBaseSearch);
            labelUserParam.setLabelCombType(searchRuleBean.getTagSearchType());
            List<LabelUserParam.ConditionRule> rules = new ArrayList<>();
            searchRuleBean.getTagSearch().forEach(bean -> rules.add(convertTagSearch(bean)));
            labelUserParam.setRules(rules);
            labelUserParam.setPageBean(pageBean);
            log.info("request info:{}",BeanHelper.bean2Json(labelUserParam));
            return spmodelLabelService.getLabelUserList(labelUserParam);
        }catch (Exception e){
            log.error("search user error:",e);
            throw new ApiException("apis.talentbk.userGroup.getmembers.error");
        }
    }

    /**
     * @description: 此接口，人员过多，不会再查，注意应用场景, 目前用于群组，群组目前只支持 2000 人
     * @param
     * @return
     */
    public Set<String> searchAllUserByTagForUserGroup(String orgId, SearchRuleBean searchRuleBean, BaseSearchBean baseSearchBean) {
        int size = 1000;
        int offset = 0;
        Set<String> list = new HashSet<>();
        boolean flag = true;
        while (flag){
            PageBean pageBean = new PageBean();
            pageBean.setOffset(offset);
            pageBean.setLimit(size);
            PagingList<LabelUserVO> labelUserVOS = searchUserByTag(orgId, searchRuleBean, baseSearchBean, pageBean);
            if (labelUserVOS != null && CollectionUtils.isNotEmpty(labelUserVOS.getDatas())){
                list.addAll(BeanCopierUtil.convertList(labelUserVOS.getDatas(), LabelUserVO::getUserId));
            }
            if (labelUserVOS != null && CollectionUtils.isNotEmpty(labelUserVOS.getDatas()) && labelUserVOS.getDatas().size() >= size){
                offset = offset + size;
                continue;
            }
            flag = false;
        }
        return list;
    }

    public List<LabelVO> getTagNames(String orgId, List<SPTagSearchBean> tagSearchBeans) {
        LabelParam labelParam = new LabelParam();
        labelParam.setOrgId(orgId);
        List<LabelParam.LabelAndValue> labelAndValues = new ArrayList<>();
        for (SPTagSearchBean tagSearchBean : tagSearchBeans) {
            LabelParam.LabelAndValue labelAndValue = new LabelParam.LabelAndValue();
            labelAndValue.setLabelId(tagSearchBean.getTagId());
            List<Long> tagValues = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(tagSearchBean.getTagValues())){
                tagSearchBean.getTagValues().forEach((e)-> tagValues.add(Long.parseLong(e)));
            }
            labelAndValue.setLabelValueIds(tagValues);
            labelAndValues.add(labelAndValue);
        }
        labelParam.setLabelList(labelAndValues);
        List<LabelVO> lables =  spmodelLabelService.getLabelList(labelParam);
        if (lables != null){
            return lables;
        }
        return Lists.emptyList();
    }

    private LabelUserParam.ConditionRule convertTagSearch(SPTagSearchBean spTagSearchBean) {
        LabelUserParam.ConditionRule conditionRule = new LabelUserParam.ConditionRule();
        conditionRule.setLabelId(spTagSearchBean.getTagId());
        conditionRule.setSymbol(spTagSearchBean.getSymbol());
        List<Long> tagValues = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spTagSearchBean.getTagValues())){
            spTagSearchBean.getTagValues().forEach((e)-> tagValues.add(Long.parseLong(e)));
        }
        conditionRule.setLabelValueIds(tagValues);
        return conditionRule;
    }

    public List<LabelVO> getLabelList(LabelParam param) {
        List<LabelVO> ret = spmodelLabelService.getLabelList(param);
        return ret == null ? Lists.emptyList() : ret;
    }

    public List<UserLabelVO> getUserLabelList(UserLabelParam param) {
        List<UserLabelVO> ret = spmodelLabelService.getUserLabelList(param);
        return ret == null ? Lists.emptyList() : ret;
    }

    public List<UserLabelVO> userLabelList(String orgId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.emptyList();
        }
        UserLabelParam param = new UserLabelParam();
        param.setOrgId(orgId);
        param.setUserIds(userIds);
        return getUserLabelList(param);
    }

    public boolean labelExecuteFinish(String orgId){
         return spmodelLabelService.labelExecuteFinish(orgId);
    }

}
