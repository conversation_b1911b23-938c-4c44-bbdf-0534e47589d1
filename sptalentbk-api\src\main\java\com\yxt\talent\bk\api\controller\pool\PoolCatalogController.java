package com.yxt.talent.bk.api.controller.pool;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.catalog.PoolCatalogService;
import com.yxt.talent.bk.svc.catalog.bean.Catalog4List;
import com.yxt.talent.bk.svc.catalog.bean.Catalog4Save;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/mgr/pool/catalog")
@Slf4j
@AllArgsConstructor
@Tag(name = "人才池分类")
public class PoolCatalogController extends BaseController {
    private final PoolCatalogService poolCatalogService;

    @Operation(summary = "人才池分类-列表")
    @GetMapping(value = "/list", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<Catalog4List> findByOrgId() {
        UserCacheDetail userDetail = getUserCacheDetail();
        List<Catalog4List> catalogList = poolCatalogService.findByOrgId(userDetail.getOrgId(),null);
        return new CommonList<>(catalogList);
    }

    @Operation(summary = "人才池分类-创建")
    @PostMapping(value = "", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public Catalog4Save create(@Valid @RequestBody Catalog4Save catalog) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return poolCatalogService.createPoolCatalog(userDetail.getOrgId() ,userDetail.getUserId(), catalog);
    }

    @Operation(summary = "人才池分类-编辑")
    @PutMapping(value = "", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN)
    public void update(@Valid @RequestBody Catalog4Save catalog) {
        UserCacheDetail userDetail = getUserCacheDetail();
        poolCatalogService.updatePoolCatalog(userDetail.getOrgId() ,userDetail.getUserId(), catalog);
    }

    @Operation(summary = "人才池分类-删除")
    @DeleteMapping(value = "/{id}", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN)
    public void delete(@PathVariable String id) {
        UserCacheDetail userDetail = getUserCacheDetail();
        poolCatalogService.deletePoolCatalog(userDetail.getOrgId(), id);
    }

}
