<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>sptalentbk-parent</artifactId>
        <groupId>com.yxt</groupId>
        <version>6.5.1-jdk17</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>sptalentbk-common</artifactId>
    <version>6.5.1-jdk17</version>

    <dependencies>
        <dependency>
            <groupId>com.yxt.ubiz</groupId>
            <artifactId>ubiz-export</artifactId>
            <version>1.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentrv-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>spmodelapi-facade</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>






</project>
