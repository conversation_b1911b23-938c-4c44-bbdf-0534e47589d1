package com.yxt.talent.bk.core.persona.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.persona.entity.UserOverview;
import com.yxt.talent.bk.core.persona.mapper.UserOverviewMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class UserOverviewRepository extends ServiceImpl<UserOverviewMapper, UserOverview> {

    public UserOverview findUserOverview(String orgId, String userId) {
        LambdaQueryWrapper<UserOverview> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserOverview::getOrgId, orgId);
        queryWrapper.eq(UserOverview::getUserId, userId);
        return getOne(queryWrapper);
    }
}
