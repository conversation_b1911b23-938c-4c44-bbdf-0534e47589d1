package com.yxt.talent.bk.common.utils;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Maps;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.service.ILock;
import com.yxt.common.util.StreamUtil;
import jodd.util.StringPool;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.javers.common.collections.Lists;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * CommonUtils
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 2:07 pm
 */
@Slf4j
public class CommonUtils {
    private CommonUtils(){
        //NO SONAR
    }

    /**
     *
     * @param lockKey
     * @param waitTime
     * @param leaseTime
     * @param unit
     * @param run(是否锁成功)
     */
    public static boolean tryLockRun(String lockKey, int waitTime, int leaseTime, TimeUnit unit, Runnable run) {
        ILock lockService = SpringContextHolder.getBean(ILock.class);
        boolean locked = false;
        try {
            locked = lockService.tryLock(lockKey, waitTime, leaseTime, unit);
            if (locked) {
                run.run();
            }
        } finally {
            if (locked) {
                try {
                    lockService.unLock(lockKey);
                } catch (Exception e) {
                    log.error("tryLockRun {} unLock error! ", lockKey);
                }
            }
        }
        return locked;
    }

    /**
     *
     * @param lockKey
     * @param waitTime
     * @param leaseTime
     * @param unit
     * @param run(是否锁成功)
     */
    public static <T> T tryLockReturn(String lockKey, int waitTime, int leaseTime, TimeUnit unit, Supplier<T> run) {
        ILock lockService = SpringContextHolder.getBean(ILock.class);
        boolean locked = false;
        try {
            locked = lockService.tryLock(lockKey, waitTime, leaseTime, unit);
            if (locked) {
                return run.get();
            } else {
                throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
            }
        } finally {
            if (locked) {
                try {
                    lockService.unLock(lockKey);
                } catch (Exception e) {
                    log.error("tryLockReturn {} unLock error! ", lockKey);
                }
            }
        }
    }

    /**
     *
     * @param lockKey
     * @param leaseTime
     * @param unit
     * @param run(是否锁成功)
     */
    public static boolean lockRun(String lockKey, int leaseTime, TimeUnit unit, Runnable run) {
        ILock lockService = SpringContextHolder.getBean(ILock.class);
        boolean locked = false;
        try {
            locked = lockService.tryLock(lockKey, leaseTime, leaseTime, unit);
            run.run();
        } finally {
            if (locked) {
                try {
                    lockService.unLock(lockKey);
                } catch (Exception e) {
                    log.error("lockRun {} unLock error! ", lockKey);
                }
            }
        }
        return locked;
    }

    /**
     * 数量是否大于0
     * @param count
     * @return
     */
    public static boolean existCount(Integer count) {
        return count != null && count > 0;
    }

    public static <T> void checkOrgData(String orgId, T entity, Function<T, String> orgIdGetter) {
        if (entity == null) {
            throw new ApiException(ExceptionKey.PARAM_TYPE_INVALID);
        }
        if (!orgId.equals(orgIdGetter.apply(entity))) {
            throw new ApiException(ExceptionKey.NO_PERMISSSION);
        }
    }

    public static String field2Column(String fieldName) {
        return StringUtils.camelToUnderline(fieldName);
    }

    /**
     * bool 转int:null或者False为0，True为1
     *
     * @param bool
     * @return
     */
    public static int bool2Int(Boolean bool) {
        if (bool != null && bool) {
            return YesOrNo.YES.getValue();
        } else {
            return YesOrNo.NO.getValue();
        }
    }

    public static boolean isTrue(Integer intBool) {
        return intBool != null && intBool == 1;
    }

    public static boolean notProdEnv() {
        String profiles = concat(activeProfiles(), StringPool.COMMA);
        log.info("current profiles: {}", profiles);
        if (profiles == null) {
            return false;
        }
        if (profiles.indexOf("native") > -1 || profiles.indexOf("dev") > -1 || profiles.indexOf("di") > -1
            || profiles.indexOf("stable") > -1 || profiles.indexOf("stress") > -1 || profiles.indexOf("tf-") > -1) {
            return true;
        }
        return false;
    }

    public static String[] activeProfiles() {
        return SpringContextHolder.getApplicationContext().getEnvironment().getActiveProfiles();
    }

    /**
     * 字符串数组拼接
     * @param list
     * @param concat
     * @return
     */
    public static String concat(String[] list, String concat) {
        if (list == null || list.length < 1) {
            return null;
        }
        List<String> strings = Arrays.asList(list);
        return concat(strings,concat);
    }

    /**
     * 字符串list拼接
     * @param list
     * @param concat
     * @return
     */
    public static String concat(Collection<String> list, String concat) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        concat = nullAsEmpty(concat);
        StringBuilder ret = new StringBuilder();
        boolean isFirst = true;
        for (String str : list) {
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(str)) {
                if (isFirst) {
                    isFirst = false;
                } else {
                    ret.append(concat);
                }
                ret.append(str);
            }
        }
        return ret.toString();
    }

    public static String nullAsEmpty(String input) {
        if (input == null) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        return input;
    }

    public static BigDecimal tryParseDecimal(String input, BigDecimal defaultVal) {
        if (StringUtils.isBlank(input)) {
            return defaultVal;
        }
        try {
            return new BigDecimal(input.trim());
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public static Long tryParseLong(String input, Long defaultVal) {
        if (StringUtils.isBlank(input)) {
            return defaultVal;
        }
        try {
            return Long.parseLong(input);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * md5Hex
     * @param input
     * @return
     */
    public static String md5Hex(String input) {
        try {
            return MD5Utils.md5Hex(input.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            throw new ApiException(ExceptionKey.MICRO_SERVICE_INTERNAL_ERROR);
        }
    }

    public static <T> List<T> filterNull(List<T> coll) {
        if (CollectionUtils.isEmpty(coll)) {
            return coll;
        }
        coll.removeIf(Objects::isNull);
        return coll;
    }

    public static List<String> filterNullAndBlank(List<String> coll) {
        if (CollectionUtils.isEmpty(coll)) {
            return coll;
        }
        coll.removeIf(org.apache.commons.lang3.StringUtils::isBlank);
        return coll;
    }

    public static String formatDate(String pattern, Date date) {
        return FastDateFormat.getInstance(pattern).format(date);
    }

    public static <T extends Number> T zeroAsNull(T param) {
        if (param == null) {
            return null;
        }
        if (param.doubleValue() == 0) {
            return null;
        }
        return param;
    }

    public static Paging genPagingInfo(int totalCount, int offset, int limit) {
        Paging paging = new Paging();
        paging.setOffset(offset);
        paging.setLimit(limit);
        paging.setCount(totalCount);
        if (totalCount > 0) {
            int pages = (totalCount / limit) + (totalCount % limit == 0 ? 0 : 1);
            paging.setPages(pages);
        } else {
            paging.setPages(0);
        }
        return paging;
    }

    public static <T> Pair<Paging, List<T>> simulatePaging(List<T> list, int offset, int limit) {
        int totalCount = list.size();
        int start = offset;
        int end = offset + limit;
        end = end > totalCount ? totalCount : end;
        List<T> items = offset >= totalCount ? new ArrayList<>() : list.subList(start, end);
        return Pair.of(genPagingInfo(totalCount, offset, limit), items);
    }

    public static <T> boolean anyMatch(Predicate predicate, T... objects) {
        if (objects != null) {
            for (T object : objects) {
                if (predicate.test(object)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static <T> void notNullConsume(T obj, Consumer<T> consumer) {
        if (obj != null) {
            consumer.accept(obj);
        }
    }

    public static <T> List<T> parseArray(String jsonArr, Class<T> clazz) {
        if (StringUtils.isBlank(jsonArr)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(jsonArr, clazz);
    }

    public static <T> T parseObject(String jsonObj, Class<T> clazz, T defaultVal) {
        return Optional.ofNullable(JSON.parseObject(jsonObj, clazz)).orElse(defaultVal);
    }
    public static <T, R> Map<R, T> list2map(Collection<T> datas, Function<T, R> mapKey) {
        if (datas == null) {
            return Maps.newHashMap();
        }
        return StreamUtil.list2map(datas, mapKey);
    }

    public static boolean isAllEmpty(Collection... list) {
        if (list != null) {
            for (Collection collection : list) {
                if (CollectionUtils.isNotEmpty(collection)) {
                    return false;
                }
            }
        }
        return true;
    }
}
