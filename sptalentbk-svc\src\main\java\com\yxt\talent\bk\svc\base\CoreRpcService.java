package com.yxt.talent.bk.svc.base;

import com.yxt.common.Constants;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.coreapi.client.bean.dataauth.DataAuthPermissionBean;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermission;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermissionResponse;
import com.yxt.coreapi.client.bean.sale.OrgVerifyFactorBean;
import com.yxt.coreapi.client.service.CoreApiFacade;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class CoreRpcService {
    private final CoreApiFacade coreApiFacade;

    public List<OrgVerifyFactorBean> verifyOrgFactors(String orgId, List<String> factorCodes) {
        return coreApiFacade.verifyOrgFactors(orgId, factorCodes);
    }

    public DataAuthPermissionBean getUserDataPermission(String orgId, String userId, String navCode, String dataPermissionCode) {
        return coreApiFacade.getUserDataPermission(orgId, userId, navCode, dataPermissionCode, Constants.XXV2_PRODUCT_CODE);
    }

    public UserAuthBean verifyPermission(UserBasicBean userBasic, String navCode, String dataPermsCode, List<String> verifyUserIds) {
        if (verifyUserIds == null) {
            verifyUserIds = Lists.newArrayList();
        }
        UserAuthBean ret = new UserAuthBean();
        if (userBasic.isAdminUser()) {
            ret.setAdmin(YesOrNo.YES.getValue());
            ret.setUserIds(verifyUserIds);
            ret.setAllUserAllow(true);
        } else {
            ret.setAdmin(YesOrNo.NO.getValue());
            if (verifyUserIds.size() > 2000) {
                log.warn("verifyPermission verifyUserIds size {} overLimit", verifyUserIds.size());
            }
            UserDataPermission dataPermission = new UserDataPermission();
            dataPermission.setOrgId(userBasic.getOrgId());
            dataPermission.setUserId(userBasic.getUserId());
            dataPermission.setNavCode(navCode);
            dataPermission.setDataPermsCode(dataPermsCode);
            dataPermission.setProductCode(Constants.XXV2_PRODUCT_CODE);
            List<String> permUserIds = new ArrayList<>(verifyUserIds.size() / 5);
            BatchOperationUtil.batchExecute(verifyUserIds, 400, subUserIds -> {
                dataPermission.setVerifyUsers(subUserIds);
                UserDataPermissionResponse resp = coreApiFacade.verifyUserDataPermission(dataPermission);
                if (CollectionUtils.isNotEmpty(resp.getUserIds())) {
                    permUserIds.addAll(resp.getUserIds());
                }
            });
            ret.setUserIds(permUserIds);
            if (CollectionUtils.isNotEmpty(verifyUserIds) && permUserIds.size() >= verifyUserIds.size()) {
                ret.setAllUserAllow(true);
            }
        }
        return ret;
    }
}
