package com.yxt.talent.bk.common.utils;

import com.google.common.collect.Table;
import com.google.common.collect.TreeBasedTable;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.util.Validate;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlField;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlOrder;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@UtilityClass
public class SqlUtils {

    public static final String PARAM_COUNTS = "counts";

    public String buildSqlQuery(Class<?> clazz, Map<String, Object> queryParams) {
        StringBuilder sqlBuilder = new StringBuilder("SELECT ");

        // 获取表名
        String tableName = getTableName(clazz);

        // 构建 SELECT 子句
        buildSelectClause(sqlBuilder, clazz);

        // 构建 FROM 子句
        buildFromClause(sqlBuilder, tableName);

        // 构建 WHERE 子句
        buildWhereClause(sqlBuilder, queryParams);

        // 构建 ORDER BY 子句
        buildOrderByClause(sqlBuilder, clazz);

        return sqlBuilder.toString();
    }

    public String buildSqlQuery(Class<?> clazz, List<String> acceptFields,
                                Map<String, Object> queryParams, String sqlLast) {
        StringBuilder sqlBuilder = new StringBuilder("SELECT ");

        // 获取表名
        String tableName = getTableName(clazz);

        // 构建 SELECT 子句
        buildSelectClause(sqlBuilder, clazz, acceptFields);

        // 构建 FROM 子句
        buildFromClause(sqlBuilder, tableName);

        // 构建 WHERE 子句
        buildWhereClause(sqlBuilder, queryParams);

        if (StringUtils.isNotBlank(sqlLast)) {
            sqlBuilder.append(" ");
            sqlBuilder.append(sqlLast);
        }
        return sqlBuilder.toString();
    }

    public String buildSqlPageQuery(Class<?> clazz, Map<String, Object> queryParams, Paging page) {
        return buildSqlQuery(clazz, queryParams) + buildPageByClause(page);
    }

    private static StringBuilder buildPageByClause(Paging page) {
        Objects.requireNonNull(page, "分页参数不能为空");
        long limit = page.getLimit();
        long offset = page.getOffset();
        return new StringBuilder().append(" LIMIT ").append(offset).append(", ").append(limit);
    }

    public static Paging toPaging(PageRequest pageRequest) {
        long size = pageRequest.getSize();
        long current = pageRequest.getCurrent();
        Paging page = new Paging();
        page.setOffset((current - 1) * size);
        page.setLimit(size);
        return page;
    }


    private String getTableName(Class<?> clazz) {
        SqlTable sqlTable = clazz.getAnnotation(SqlTable.class);
        Validate.isNotNull(sqlTable, "未找到SqlTable注解");
        return sqlTable.value();
    }

    private void buildSelectClause(StringBuilder sqlBuilder, Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            Annotation ignoreField = field.getAnnotation(SqlIgnore.class);
            if (ignoreField == null) {
                String formattedFieldName = formatFieldName(field, true);
                sqlBuilder.append(formattedFieldName).append(", ");
            }
        }
        // 移除最后一个逗号和空格
        sqlBuilder.setLength(sqlBuilder.length() - 2);
    }

    private void buildSelectClause(StringBuilder sqlBuilder, Class<?> clazz, List<String> acceptFields) {
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if ((field.getModifiers() & Modifier.STATIC) != 0) {
                continue;
            }
            String fieldName = field.getName();
            Annotation ignoreField = field.getAnnotation(SqlIgnore.class);
            if (ignoreField == null && (CollectionUtils.isEmpty(acceptFields) || acceptFields.contains(fieldName))) {
                String formattedFieldName = formatFieldName(field, true);
                sqlBuilder.append(formattedFieldName).append(", ");
            }
        }
        // 移除最后一个逗号和空格
        sqlBuilder.setLength(sqlBuilder.length() - 2);
    }

    private String formatFieldName(Field field, boolean appendAs) {
        String fieldName = field.getName();
        String lowerCaseField = fieldName.replaceAll("([A-Z])", "_$1").toLowerCase();
        SqlField sqlField = field.getAnnotation(SqlField.class);
        if (sqlField != null && StringUtils.isNotBlank(sqlField.value())) {
            lowerCaseField = sqlField.value();
        }
        return appendAs ? lowerCaseField + " AS " + fieldName : lowerCaseField;
    }

    private void buildFromClause(StringBuilder sqlBuilder, String tableName) {
        sqlBuilder.append(" FROM ").append(tableName);
    }

    private void buildWhereClause(StringBuilder sqlBuilder, Map<String, Object> queryParams) {
        if (queryParams != null && !queryParams.isEmpty()) {
            sqlBuilder.append(" WHERE ");
            for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();
                appendQueryParam(sqlBuilder, paramName, paramValue);
                sqlBuilder.append(" AND ");
            }
            // 移除最后一个 AND
            sqlBuilder.setLength(sqlBuilder.length() - 5);
        }
    }

    private void appendQueryParam(StringBuilder sqlBuilder, String paramName, Object paramValue) {
        if (paramValue == null) {
            sqlBuilder.append(paramName).append(" IS NULL");
        } else {
            if (paramValue instanceof String) {
                sqlBuilder.append(paramName).append(" = '").append(paramValue).append("'");
            } else if (tryParamValueIn(sqlBuilder, paramName, paramValue)) {
                //NO SONAR
            } else {
                sqlBuilder.append(paramName).append(" = ").append(paramValue);
            }
        }
    }

    private boolean tryParamValueIn(StringBuilder sqlBuilder, String paramName, Object paramValue) {
        if (paramValue instanceof Object[]) {
            Object[] arrayValue = (Object[]) paramValue;
            sqlBuilder.append(paramName).append(" IN (");
            for (int i = 0; i < arrayValue.length; i++) {
                if (i > 0) {
                    sqlBuilder.append(", ");
                }
                Object item = arrayValue[i];
                if (item instanceof String) {
                    sqlBuilder.append("'").append(item).append("'");
                } else {
                    sqlBuilder.append(item);
                }
            }
            sqlBuilder.append(")");
            return true;
        } else if (paramValue instanceof Collection) {
            Collection arrayValue = (Collection) paramValue;
            sqlBuilder.append(paramName).append(" IN (");
            int i = 0;
            for (Object item : arrayValue) {
                if (i > 0) {
                    sqlBuilder.append(", ");
                }
                if (item instanceof String) {
                    sqlBuilder.append("'").append(item).append("'");
                } else {
                    sqlBuilder.append(item);
                }
                i++;
            }
            sqlBuilder.append(")");
            return true;
        }
        return false;
    }

    public static String appendCountSql(String query) {
        return "SELECT COUNT(*) as " + PARAM_COUNTS + " FROM (" + query + ") AS t";
    }

    private void buildOrderByClause(StringBuilder sqlBuilder, Class<?> clazz) {
        Table<Integer, String, String> orderTables = TreeBasedTable.create();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            SqlOrder orderField = field.getAnnotation(SqlOrder.class);
            if (orderField != null) {
                orderTables.put(orderField.order(), formatFieldName(field, false), orderField.direction());
            }
        }

        if (!orderTables.isEmpty()) {
            sqlBuilder.append(" ORDER BY ");
            for (Integer order : orderTables.rowKeySet()) {
                for (Map.Entry<String, String> orderInfo : orderTables.row(order).entrySet()) {
                    String fieldName = orderInfo.getKey();
                    boolean isAsc = Objects.equals(orderInfo.getValue(), SqlOrder.ASC);
                    sqlBuilder.append(fieldName);
                    if (!isAsc) {
                        sqlBuilder.append(" DESC");
                    }
                    sqlBuilder.append(", ");
                }
            }
            // 移除最后一个逗号和空格
            sqlBuilder.setLength(sqlBuilder.length() - 2);
        }
    }

    public static String escapeLike(String keyword) {
        if (StringUtils.isBlank(keyword) || keyword.indexOf("%") < 0) {
            return keyword;
        }
        char[] chs = keyword.toCharArray();
        StringBuilder ret = new StringBuilder(chs.length);
        for (char ch : chs) {
            if (ch == '%') {
                ret.append("\\");
            }
            ret.append(ch);
        }
        return ret.toString();
    }
}
