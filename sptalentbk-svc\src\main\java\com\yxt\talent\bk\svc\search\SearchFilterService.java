package com.yxt.talent.bk.svc.search;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.exception.ApiException;
import com.yxt.common.service.ILock;
import com.yxt.common.util.*;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.enums.TagSearchEnum;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeDimensionTagMapEntity;
import com.yxt.talent.bk.core.persona.repo.PersonaThemeDimensionTagMapRepository;
import com.yxt.talent.bk.core.search.bean.UserSearchAnalyseDimensionBean;
import com.yxt.talent.bk.core.search.entity.SearchAnalyseDimension;
import com.yxt.talent.bk.core.search.entity.SearchAnalyseDimensionItem;
import com.yxt.talent.bk.core.search.entity.SearchFilterGroup;
import com.yxt.talent.bk.core.search.entity.SearchFilterItem;
import com.yxt.talent.bk.core.search.repo.SearchAnalyseDimensionItemRepository;
import com.yxt.talent.bk.core.search.repo.SearchAnalyseDimensionRepository;
import com.yxt.talent.bk.core.search.repo.SearchFilterGroupRepository;
import com.yxt.talent.bk.core.search.repo.SearchFilterItemRepository;
import com.yxt.talent.bk.core.tag.bean.TagNameBean;
import com.yxt.talent.bk.core.tag.bean.TagSimpleBean;
import com.yxt.talent.bk.core.tag.bean.TagValueSimpleBean;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.entity.UserTagValueEntity;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.UserTagValueRepository;
import com.yxt.talent.bk.svc.base.CoreFactorService;
import com.yxt.talent.bk.svc.search.bean.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SearchFilterService {
    private static final String F_ORDINARY_TAG = "ordinaryTag";
    private static final String F_DEPARTMENT_ID = "departmentId";
    private static final String F_POSITION_ID = "positionId";
    private static final String F_GRADE_NAME = "gradeName";
    private static final String F_AGE_GROUP = "ageGroup";
    private static final String F_CERTS = "certs";
    private static final String F_EXAMS = "exams";
    private static final String F_PERFORMANCE_LEVEL = "performanceLevel";
    private static final String F_ABILITY_LEVEL = "abilityLevel";
    private static final String F_POTENTIAL_LEVEL = "potentialLevel";
    private static final String F_REVIEW_RESULT = "reviewResult";

    private final SearchFilterItemRepository searchFilterItemRepository;
    private final SearchAnalyseDimensionItemRepository searchDimensionItemRepository;
    private final TagRepository tagRepository;
    private final SearchFilterGroupRepository searchFilterGroupRepository;
    private final SearchAnalyseDimensionRepository searchAnalyseDimensionRepository;
    private final SearchAnalyseDimensionItemRepository searchAnalyseDimensionItemRepository;
    private final UserTagValueRepository userTagValueRepository;
    private final CoreFactorService coreFactorService;
    private final PersonaThemeDimensionTagMapRepository personaThemeDimensionTagMapRepository;
    private final ILock lockService;
    private final SearchAnalyseDimensionService searchAnalyseDimensionService;
    /**
     * 获取，筛选，维度的勾选标签
     *
     * @param flag 0-筛选条件标签，1-透视条件标签
     * @return 标签合集
     */
    public List<TagBean4Select> findFilterSelectTag(String orgId, int flag, String userId) {
        List<TagBean4Select> tagBean4SelectList = new ArrayList<>();
        // 先获取内置标签
        if (flag == 0) {
            // 筛选标签
            dealFilterItem(orgId, tagBean4SelectList);
        } else {
            // 透视标签
            dealDimensionItem(orgId, tagBean4SelectList, userId);
        }

        // 获取普通标签
        TagBean4Select tag4Ordinary = new TagBean4Select();
        tag4Ordinary.setFlag(0);
        tag4Ordinary.setTagId(F_ORDINARY_TAG);
        tag4Ordinary.setTagName("普通标签");
        tag4Ordinary.setCatalogName("其他标签");
        tagBean4SelectList.add(tag4Ordinary);

        // 获取分层标签
        List<TagEntity> tab4Select = tagRepository.findTab4Select(orgId);
        for (TagEntity tagEntity : tab4Select) {
            TagBean4Select tag = new TagBean4Select();
            tag.setTagId(tagEntity.getTagKey());
            tag.setTagName(tagEntity.getTagName());
            tag.setCatalogName("其他标签");
            tag.setTagType(1);
            tagBean4SelectList.add(tag);
        }
        return tagBean4SelectList;
    }

    @DbHintMaster
    private void dealDimensionItem(String orgId, List<TagBean4Select> tagBean4SelectList, String userId) {
        List<SearchAnalyseDimensionItem> searchAnalyseDimensionItems = searchDimensionItemRepository.findByOrgId(orgId);
        if (CollectionUtils.isEmpty(searchAnalyseDimensionItems)) {
            searchAnalyseDimensionItems = initAnalyseItemLock(orgId, userId);
        }
        // 获取内置标签中，禁用的标签
        List<TagEntity> forbiddenTag = tagRepository.findForbiddenTag(orgId);
        List<String> forbiddenTagKeys = forbiddenTag.stream().map(TagEntity::getTagKey).collect(Collectors.toList());
        Boolean openRv = coreFactorService.isOpenRv(orgId);
        Map<String, TagNameBean> tagNameMap = getLeastSelfTagName(orgId);
        for (SearchAnalyseDimensionItem item : searchAnalyseDimensionItems) {
            // 判断是否开通了人才盘点
            TagBean4Select tag = new TagBean4Select();
            if ("人才盘点".equals(item.getItemCatalogName())) {
                if (Boolean.TRUE.equals(openRv)) {
                    tag.setFlag(1);
                } else {
                    continue;
                }
            }
            if (forbiddenTagKeys.contains(item.getItemKey())){
                continue;
            }
            tag.setTagId(item.getItemKey());
            TagNameBean tagNameBean = tagNameMap.get(item.getItemKey());
            if (tagNameBean != null) {
                tag.setTagName(tagNameBean.getTagName());
            } else {
                tag.setTagName(item.getItemName());
            }
            tag.setCatalogName(item.getItemCatalogName());
            tagBean4SelectList.add(tag);
        }
    }

    private List<SearchAnalyseDimensionItem> initAnalyseItemLock(String orgId, String userId) {
        List<SearchAnalyseDimensionItem> resList = new ArrayList<>();
        String lockKey = String.format(TalentBkRedisKeys.TALENTBK_INIT_DIMENSION_ITEM_LOCK_KEY, orgId);
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                resList = initAnalyseItem(orgId, userId);
            } catch (Exception e) {
                log.error("initAnalyseItemLock error orgId={}， errormsg={}",orgId , e.getMessage());
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            return resList;
        }
        return resList;
    }

    @DbHintMaster
    private void dealFilterItem(String orgId, List<TagBean4Select> tagBean4SelectList) {
        List<SearchFilterItem> searchItems = searchFilterItemRepository.findFilterByOrgId(orgId);
        if (CollectionUtils.isEmpty(searchItems)) {
            searchItems = initFilterItemLock(orgId);
        }
        // 获取内置标签中，禁用的标签
        List<TagEntity> forbiddenTag = tagRepository.findForbiddenTag(orgId);
        List<String> forbiddenTagKeys = forbiddenTag.stream().map(TagEntity::getTagKey).collect(Collectors.toList());
        // 获取内置标签的名称
        Map<String, TagNameBean> tagMap = getLeastSelfTagName(orgId);
        Boolean openRv = coreFactorService.isOpenRv(orgId);
        for (SearchFilterItem searchFilterItem : searchItems) {
            TagBean4Select tag = new TagBean4Select();
            if ("人才盘点".equals(searchFilterItem.getItemCatalogName())) {
                if (Boolean.TRUE.equals(openRv)) {
                    tag.setFlag(1);
                } else {
                    continue;
                }
            }
            if (forbiddenTagKeys.contains(searchFilterItem.getItemKey())) {
                continue;
            }
            tag.setTagId(searchFilterItem.getItemKey());
            // 获取最新的内置标签名称
            TagNameBean tagNameBean = tagMap.get(searchFilterItem.getItemKey());
            if (tagNameBean != null) {
                tag.setTagName(tagNameBean.getTagName());
            } else {
                tag.setTagName(searchFilterItem.getItemName());
            }

            if (searchFilterItem.getItemKey().equals(TagSearchEnum.ENTRY_DATE.getKey())) {
                tag.setTagName(TagSearchEnum.ENTRY_DATE.getName());
            }
            tag.setCatalogName(searchFilterItem.getItemCatalogName());
            tagBean4SelectList.add(tag);
        }
    }

    private Map<String, TagNameBean> getLeastSelfTagName(String orgId) {
        List<TagNameBean> selfTags = tagRepository.findSelfTagByOrgId(orgId);
        return StreamUtil.list2map(selfTags, TagNameBean::getTagKey);
    }


    private List<SearchFilterItem> initFilterItemLock(String orgId) {
        List<SearchFilterItem> resList = new ArrayList<>();
        String lockKey = String.format(TalentBkRedisKeys.TALENTBK_INIT_FILTER_ITEM_LOCK_KEY, orgId);
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                resList = initFilterItem(orgId);
            } catch (Exception e) {
                log.error("initFilterItemLock error orgId={}, errorMsg={}", orgId, e.getMessage());
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            return resList;
        }
        return resList;
    }

    /**
     * 获取筛选组名称列表
     *
     * @param orgId 机构id
     * @return 筛选组名称合集
     */
    public List<FilterGroupNameBean> findGroupNameList(String orgId){
        List<SearchFilterGroup> groupNames = searchFilterGroupRepository.findGroupNamesByOrgId(orgId);
        List<FilterGroupNameBean> groupNameBeans = new ArrayList<>();
        // 如果为空，则新建一条全部数据
        if (CollectionUtils.isEmpty(groupNames)) {

            return dealTagGroupInit(orgId, groupNameBeans);
        }
        for (SearchFilterGroup group : groupNames) {
            FilterGroupNameBean filterGroup = new FilterGroupNameBean();
            filterGroup.setId(group.getId());
            filterGroup.setGroupName(group.getGroupName());
            filterGroup.setGroupType(group.getGroupType());
            groupNameBeans.add(filterGroup);
        }
        return groupNameBeans;
   }


    private List<FilterGroupNameBean> dealTagGroupInit(String orgId, List<FilterGroupNameBean> groupNameBeans) {
        String lockKey = String
                .format(TalentBkRedisKeys.TALENTBK_TAG_INIT_GROUP_CACHE_KEY, orgId);
        SearchFilterGroup search;
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                search = initGroupData(orgId);
            } catch (Exception e) {
                log.error("dealTagGroupInit error orgId:{}", orgId, e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            return groupNameBeans;
        }
        FilterGroupNameBean filterGroup = new FilterGroupNameBean();
        filterGroup.setId(search.getId());
        filterGroup.setGroupName(search.getGroupName());
        filterGroup.setGroupType(search.getGroupType());
        groupNameBeans.add(filterGroup);
        return groupNameBeans;
    }

    @NotNull
    private SearchFilterGroup initGroupData(String orgId) {
        SearchFilterGroup search = new SearchFilterGroup();
        search.setId(ApiUtil.getUuid());
        search.setGroupName("全部人才");
        search.setGroupType(1);
        search.setOrgId(orgId);
        EntityUtil.setCreateInfo("init", search);
        List<TagGroupBean> resList = new ArrayList<>();
        // 添加部门
        TagGroupBean deptBean = new TagGroupBean();
        deptBean.setTagId(F_DEPARTMENT_ID);
        deptBean.setTagName("部门");
        resList.add(deptBean);
        // 加岗位
        TagGroupBean posBean = new TagGroupBean();
        posBean.setTagId(F_POSITION_ID);
        posBean.setTagName("岗位");
        resList.add(posBean);
        // 职级
        TagGroupBean gradeBean = new TagGroupBean();
        gradeBean.setTagId(F_GRADE_NAME);
        gradeBean.setTagName("职级");
        resList.add(gradeBean);
        // 年龄段
        TagGroupBean ageGroupBean = new TagGroupBean();
        ageGroupBean.setTagId(F_AGE_GROUP);
        ageGroupBean.setTagName("年龄段");
        ageGroupBean.setType(1);
        ageGroupBean.setValueChooseModel(0);
        List<TagValueBean> tagValueBeanList = new ArrayList<>();
        // 获取年龄段分层标签
        List<TagValueSimpleBean> ageGroup = tagRepository.findTagValueList(orgId, F_AGE_GROUP);
        for (TagValueSimpleBean tag : ageGroup) {
            TagValueBean tagValueBean = new TagValueBean();
            tagValueBean.setTagValueId(tag.getId());
            tagValueBean.setTagValueName(tag.getValueName());
            tagValueBeanList.add(tagValueBean);
        }
        ageGroupBean.setTagValueBeanList(tagValueBeanList);
        resList.add(ageGroupBean);
        // 普通标签
        TagGroupBean ordinaryTagBean = new TagGroupBean();
        ordinaryTagBean.setTagId(F_ORDINARY_TAG);
        ordinaryTagBean.setTagName("普通标签");
        ordinaryTagBean.setValueChooseModel(1);
        ordinaryTagBean.setLogic(1);
        ordinaryTagBean.setType(0);
        List<TagValueBean> ordinaryList = new ArrayList<>();
        List<TagEntity> ordinaryTag = tagRepository.findOrdinaryTag(orgId);
        for (TagEntity tagEntity : ordinaryTag) {
            TagValueBean tagValueBean = new TagValueBean();
            tagValueBean.setTagValueId(tagEntity.getTagKey());
            tagValueBean.setTagValueName(tagEntity.getTagName());
            ordinaryList.add(tagValueBean);
        }
        ordinaryTagBean.setTagValueBeanList(ordinaryList);
        resList.add(ordinaryTagBean);
        search.setFilterValue(JSON.toJSONString(resList));
        searchFilterGroupRepository.save(search);
        return search;
    }

    /**
     * 获取分层标签
     *
     * @param orgId 机构id
     * @param tagIds 标签id
     * @return 集合
     */
   public List<FilterTagBean> findFilterTagBean(String orgId, List<String> tagIds) {
       //内置的标签列表中查询
       List<FilterTagBean> list = new ArrayList<>();
       List<TagSimpleBean> tagSimpleBeans = tagRepository.fingTagMsg(orgId, tagIds);
       Set<String> existTagIds = tagSimpleBeans.stream().map(TagSimpleBean::getId).collect(Collectors.toSet());

       // 获取没有查到的标签key
       List<String> noSearchIds = new ArrayList<>();
       tagIds.forEach(tag ->{
           if (!existTagIds.contains(tag)) {
               noSearchIds.add(tag);
           }
       });
       List<SearchFilterItem> itemKeys = searchFilterItemRepository.findTagsByItemKeys(orgId, noSearchIds);
       itemKeys.forEach(item ->{
           TagSimpleBean tagSimpleBean = new TagSimpleBean();
           // 证书，考试，培训，支持分层标签， 支持且或选择
           if (F_CERTS.equals(item.getItemKey())
                   ||"o2os".equals(item.getItemKey())
                   || F_EXAMS.equals(item.getItemKey())) {
               tagSimpleBean.setTagType(1);
               tagSimpleBean.setValueChooseModel(1);
           }
           tagSimpleBean.setId(item.getItemKey());
           tagSimpleBean.setTagName(item.getItemName());
           if (TagSearchEnum.ENTRY_DATE.getKey().equals(item.getItemKey())) {
               tagSimpleBean.setTagName(TagSearchEnum.ENTRY_DATE.getName());
           }
           tagSimpleBeans.add(tagSimpleBean);
       });

       Map<String, FilterTagBean> map = new HashMap<>();
       for (TagSimpleBean tag : tagSimpleBeans) {
           dealTagMsg(map, tag);
       }
       // 自建普通标签合集
       dealOrdinaryTag(orgId, tagIds, list);

       // 合并数据
       for (Map.Entry<String, FilterTagBean> next : map.entrySet()) {
           list.add(next.getValue());
       }

       // 排序，恢复前端传入时的标签顺序
       List<FilterTagBean> resList = new ArrayList<>();
       Map<String, FilterTagBean> tagBeanMap = StreamUtil.list2map(list, FilterTagBean::getTagId);
       for (String tagId : tagIds) {
           if (!tagBeanMap.containsKey(tagId)) {
               continue;
           }
           FilterTagBean filterTagBean = tagBeanMap.get(tagId);
           resList.add(filterTagBean);
       }

       return resList;
   }

    private void dealOrdinaryTag(String orgId, List<String> tagIds, List<FilterTagBean> list) {
        if (tagIds.contains(F_ORDINARY_TAG)) {
            FilterTagBean filterTagBean = new FilterTagBean();
            filterTagBean.setTagId(F_ORDINARY_TAG);
            filterTagBean.setTagName("普通标签");
            filterTagBean.setValueChooseModel(1);
            filterTagBean.setLogic(1);
            filterTagBean.setType(0);
            List<TagValueBean> tagValueBeanList = new ArrayList<>();
            List<TagEntity> ordinaryTag = tagRepository.findOrdinaryTag(orgId);
            for (TagEntity tagEntity : ordinaryTag) {
                TagValueBean tagValueBean = new TagValueBean();
                tagValueBean.setTagValueId(tagEntity.getTagKey());
                tagValueBean.setTagValueName(tagEntity.getTagName());
                tagValueBeanList.add(tagValueBean);
            }
            if (CollectionUtils.isNotEmpty(tagValueBeanList)) {
                filterTagBean.setTagValueBeanList(tagValueBeanList);
            }
            list.add(filterTagBean);
        }
    }

    private void dealTagMsg(Map<String, FilterTagBean> map, TagSimpleBean tag) {
        if (map.containsKey(tag.getId())) {
            FilterTagBean filterTagBean = map.get(tag.getId());
            List<TagValueBean> tagValueBeanList = filterTagBean.getTagValueBeanList();
            if (StringUtils.isNotEmpty(tag.getValueId())) {
                TagValueBean tagValueBean = new TagValueBean();
                tagValueBean.setTagValueId(tag.getValueId());
                tagValueBean.setTagValueName(tag.getValueName());
                tagValueBeanList.add(tagValueBean);
            }
        } else {
            FilterTagBean filterTagBean = new FilterTagBean();
            filterTagBean.setTagId(tag.getId());
            filterTagBean.setTagName(tag.getTagName());
            if (tag.getValueChooseModel() == 1) {
                filterTagBean.setLogic(1);
            }
            filterTagBean.setType(tag.getTagType());
            filterTagBean.setValueChooseModel(tag.getValueChooseModel());
            List<TagValueBean> tagValueBeanList = new ArrayList<>();
            if (StringUtils.isNotEmpty(tag.getValueId())) {
                TagValueBean tagValueBean = new TagValueBean();
                tagValueBean.setTagValueId(tag.getValueId());
                tagValueBean.setTagValueName(tag.getValueName());
                tagValueBeanList.add(tagValueBean);
            }
            filterTagBean.setTagValueBeanList(tagValueBeanList);
            map.put(tag.getId(), filterTagBean);
        }
    }

    /**
     * 分组数据
     *
     * @param orgId 机构id
     * @param groupId 分组id
     * @return 机构
     */
    public FilterGroup4Create findGroupMsg(String orgId, String groupId) {
        SearchFilterGroup group = searchFilterGroupRepository.findGroupById(orgId, groupId);
        if (group == null) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_GROUP_NOT_FOUND);
        }
        // 筛选信息中的 标签值更新为最新的标签，排除已经删除  隐藏的标签
        List<TagGroupBean> resList = dealFilterValue(orgId, group);

        FilterGroup4Create filterGroup4Create = new FilterGroup4Create();
        filterGroup4Create.setId(group.getId());
        filterGroup4Create.setGroupName(group.getGroupName());
        filterGroup4Create.setText(JSON.toJSONString(resList));
        return filterGroup4Create;
    }


    private List<TagGroupBean> dealFilterValue(String orgId, SearchFilterGroup group) {
        TagGroupBean[] tagGroupBeans = BeanHelper.json2Bean(group.getFilterValue(), TagGroupBean[].class);
        List<TagGroupBean> resList = new ArrayList<>();
        List<String> tagIds = new ArrayList<>();
        for (TagGroupBean tagGroupBean : tagGroupBeans) {
            tagIds.add(tagGroupBean.getTagId());
        }

        List<TagSimpleBean> tagSimpleBeans = tagRepository.fingTagMsg(orgId, tagIds);
        Map<String, FilterTagBean> map = new HashMap<>();
        for (TagSimpleBean simpleBean : tagSimpleBeans) {
            dealTagMsg(map, simpleBean);
        }
        // 自建普通标签合集
        List<FilterTagBean> list = new ArrayList<>();
        if (tagIds.contains(F_ORDINARY_TAG)) {
            dealOrdinaryTag(orgId, tagIds, list);
        }

        Boolean openRv = coreFactorService.isOpenRv(orgId);
        for (TagGroupBean tagGroupBean : tagGroupBeans) {
            TagGroupBean resTag = new TagGroupBean();

            // 处理tag表中没有的标签，岗位, 部门, 职级，证书，培训，考试
            if (F_DEPARTMENT_ID.equals(tagGroupBean.getTagId())
                    ||F_POSITION_ID.equals(tagGroupBean.getTagId())
                    || F_CERTS.equals(tagGroupBean.getTagId())
                    || "o2os".equals(tagGroupBean.getTagId())
                    || F_EXAMS.equals(tagGroupBean.getTagId())
                    || F_GRADE_NAME.equals(tagGroupBean.getTagId())) {
                BeanCopierUtil.copy(tagGroupBean, resTag);
                resList.add(resTag);
                continue;
            }
            // 如果该机构判断未开通盘点，则不显示盘点标签
            if (Boolean.FALSE.equals(openRv)) {
                if (F_PERFORMANCE_LEVEL.equals(tagGroupBean.getTagId())
                        || F_ABILITY_LEVEL.equals(tagGroupBean.getTagId())
                        || F_POTENTIAL_LEVEL.equals(tagGroupBean.getTagId())
                        || F_REVIEW_RESULT.equals(tagGroupBean.getTagId())) {
                    continue;
                }
            }
            // 处理普通标签
            if (F_ORDINARY_TAG.equals(tagGroupBean.getTagId())
                    && CollectionUtils.isNotEmpty(tagGroupBean.getTagValueBeanList())) {
                BeanCopierUtil.copy(tagGroupBean, resTag);
                FilterTagBean filterTagBean = list.get(0);
                resTag.setTagValueBeanList(filterTagBean.getTagValueBeanList());
                resList.add(resTag);
                continue;
            }
            // 排除掉已经删除或不可见的标签
            if (!map.containsKey(tagGroupBean.getTagId())) {
                continue;
            }
            // 处理 自建分层标签
            BeanCopierUtil.copy(tagGroupBean, resTag);
            FilterTagBean filterTagBean = map.get(tagGroupBean.getTagId());
            if (filterTagBean != null) {
                resTag.setTagName(filterTagBean.getTagName());
                resTag.setTagValueBeanList(filterTagBean.getTagValueBeanList());
            }
            // 如果key值是entryDate ,标签名称改为  入职时间
            if (TagSearchEnum.ENTRY_DATE.getKey().equals(tagGroupBean.getTagId())) {
                resTag.setTagName(TagSearchEnum.ENTRY_DATE.getName());
            }
            resList.add(resTag);
        }
        return resList;
    }

    /**
     * 保存数据分组数据
     *
     * @param orgId
     * @param bean
     * @param userId
     * @return
     */
    public String saveGroup(String orgId, FilterGroup4Create bean, String userId) {
        SearchFilterGroup group = new SearchFilterGroup();
        chkGroupName(orgId, bean.getGroupName(), StringUtils.EMPTY);
        String id = ApiUtil.getUuid();
        group.setId(id);
        group.setOrgId(orgId);
        group.setGroupName(bean.getGroupName());
        group.setFilterValue(bean.getText());
        EntityUtil.setCreateInfo(userId, group);
        searchFilterGroupRepository.save(group);
        return id;
    }

    @DbHintMaster
    public void updateGroup(String orgId, FilterGroup4Create bean, String userId) {
        // 校验是否存在
        SearchFilterGroup group = searchFilterGroupRepository.getById(bean.getId());
        if (group == null) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_GROUP_NOT_FOUND);
        }
        // 校验名称是否重复
        chkGroupName(orgId, bean.getGroupName(), bean.getId());
        // 默认筛选组不能修改
        if (group.getGroupType() == 1) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_GROUP_NO_EDIT);
        }
        group.setGroupName(bean.getGroupName());
        group.setFilterValue(bean.getText());
        EntityUtil.setUpdatedInfo(userId, group);
        searchFilterGroupRepository.updateById(group);
    }

    private void chkGroupName(String orgId, String name, String groupId) {
        // 名称长度校验
        if (StringUtils.isEmpty(name.trim())) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_GROUP_NAME_FORM_ERROR);
        }
        if (name.length() == 0 || name.length() > 20) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_GROUP_NAME_FORM_ERROR);
        }
        // 重复校验
        long num = searchFilterGroupRepository.chkGroupName(orgId, name, groupId);
        if (num > 0) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_GROUP_NAME_REPEAT);
        }
    }

    public void deleteGroup(String orgId, String groupId) {
        SearchFilterGroup group = searchFilterGroupRepository.findGroupById(orgId, groupId);
        if (group == null) {
            return;
        }
        if (group.getGroupType() == 1) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_GROUP_DELETE_ERROR);
        }
        searchFilterGroupRepository.removeById(groupId);
    }

    @DbHintMaster
    public void updateGroupName(String orgId, FilterGroup4Create bean, String userId) {
        SearchFilterGroup group = searchFilterGroupRepository.getById(bean.getId());
        if (group == null) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_GROUP_NOT_FOUND);
        }
        chkGroupName(orgId, bean.getGroupName(), bean.getId());
        group.setGroupName(bean.getGroupName());
        EntityUtil.setUpdatedInfo(userId, group);
        searchFilterGroupRepository.updateGroupName(orgId, group);
    }

    public List<DimensionBean> dimensionList(String orgId, String userId) {
        List<DimensionBean> list = new ArrayList<>();
        List<UserSearchAnalyseDimensionBean> beans = searchAnalyseDimensionRepository.getBaseMapper()
                .findByOrgId(orgId, userId);
        List<UserSearchAnalyseDimensionBean> resList = new ArrayList<>();
        // 如果是空初始化
        if (CollectionUtils.isEmpty(beans)) {
            resList = searchAnalyseDimensionService.initAnalyseDimensionLock(orgId, userId);
        }
        if (CollectionUtils.isNotEmpty(resList)) {
            for (UserSearchAnalyseDimensionBean res : resList) {
                DimensionBean bean = new DimensionBean();
                bean.setTagId(res.getItemKey());
                bean.setTagName(res.getItemName());
                bean.setOrderIndex(res.getOrderIndex());
                list.add(bean);
            }
            return list;
        }
        for (UserSearchAnalyseDimensionBean dimension : beans) {
            DimensionBean bean = new DimensionBean();
            bean.setTagId(dimension.getItemKey());
            bean.setTagName(dimension.getItemName());
            bean.setOrderIndex(dimension.getOrderIndex());
            list.add(bean);
        }
        return list;
    }

    /**
     * 跟新，保存维度列表
     *
     * @param orgId
     * @param userId
     * @param bean
     */
    public void saveDimension(String orgId, String userId, DimensionBeanList bean) {
        saveOrUpdateDimension(orgId, userId, bean);
    }

    private void saveOrUpdateDimension(String orgId, String userId, DimensionBeanList bean) {
        if (bean.getDimensionBeans().isEmpty() || bean.getDimensionBeans().size() > 5) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_DIMENSION_SIZE_ERROR);
        }
        // 维度key重复校验
        List<DimensionBean> dimensionBeans = bean.getDimensionBeans();
        List<String> tagIds = dimensionBeans.stream().map(DimensionBean::getTagId).distinct().collect(Collectors.toList());
        if (tagIds.size() != dimensionBeans.size()) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_DIMENSION_REPEAT);
        }
        List<SearchAnalyseDimension> list = new ArrayList<>();
        // 保存新值
        for (DimensionBean dimensionBean : bean.getDimensionBeans()) {
            SearchAnalyseDimension dimension = new SearchAnalyseDimension();
            dimension.setId(ApiUtil.getUuid());
            dimension.setOrgId(orgId);
            dimension.setItemKey(dimensionBean.getTagId());
            dimension.setItemValue(dimensionBean.getTagName());
            dimension.setOrderIndex(dimensionBean.getOrderIndex());
            dimension.setUserId(userId);
            EntityUtil.setCreateInfo(userId, dimension);
            list.add(dimension);
        }
        // 删除旧值
        searchAnalyseDimensionRepository.deleteByOrgId(orgId, userId);
        searchAnalyseDimensionRepository.saveBatch(list);
    }

    public List<FilterTagBean> searchAllTag(String orgId, TagBean4Search bean) {
        bean.setKeyWord(TalentbkUtil.escapeStr(bean.getKeyWord()));
        List<FilterTagBean> list = new ArrayList<>();
        List<PersonaThemeDimensionTagMapEntity> personaList = new ArrayList<>();
        if (StringUtils.isNotEmpty(bean.getDimensionId())) {
            personaList = personaThemeDimensionTagMapRepository
                    .queryByDimensionId(orgId, bean.getDimensionId());
        }
        if (StringUtils.isNotEmpty(bean.getDimensionId()) && CollectionUtils.isEmpty(personaList)) {
            return list;
        }
        List<String> tagIds = personaList.stream().map(PersonaThemeDimensionTagMapEntity::getTagId)
                .collect(Collectors.toList());

        Map<String, List<TagSimpleBean>> tagValueMap = getTagValueMap(orgId, bean, tagIds);
        tagValueMap.forEach((tagId1, value) -> {
            TagSimpleBean tagSimpleBean = value.get(0);
            FilterTagBean filterTagBean = new FilterTagBean();
            filterTagBean.setTagId(tagSimpleBean.getId());
            filterTagBean.setTagName(tagSimpleBean.getTagName());
            filterTagBean.setType(tagSimpleBean.getTagType());
            filterTagBean.setValueChooseModel(tagSimpleBean.getValueChooseModel());
            filterTagBean.setCreateTime(tagSimpleBean.getCreateTime());
            List<TagValueBean> tagValueBeanList = Lists.newArrayListWithCapacity(value.size());
            value.forEach(tagSimpleBean1 -> {
                TagValueBean tagValueBean = TagValueBean.builder().tagValueId(tagSimpleBean1.getValueId())
                        .tagValueName(tagSimpleBean1.getValueName()).build();
                tagValueBeanList.add(tagValueBean);
            });
            filterTagBean.setTagValueBeanList(tagValueBeanList);
            list.add(filterTagBean);
        });

        // 按时间排序
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .sorted(Comparator.comparing(FilterTagBean::getCreateTime).reversed()).collect(Collectors.toList());
        }
        return list;
    }

    private Map<String, List<TagSimpleBean>> getTagValueMap(String orgId, TagBean4Search bean, List<String> tagIds) {
        List<String> nameLikeList = Lists.newArrayList();
        Set<String> valueNameLikeSet = Sets.newConcurrentHashSet();
        List<com.yxt.talent.bk.core.tag.bean.TagValueBean> valueNameTagIdLikeList;
        if (StringUtils.isNotBlank(bean.getKeyWord())) {
            // 判断keyword是按标签名还是标签值名搜索
            nameLikeList = tagRepository.findTagNameLike(orgId, bean.getKeyWord());
            valueNameTagIdLikeList = userTagValueRepository.findValueNameTagIdLike(orgId, bean.getKeyWord());
            // 添加标签值对应的标签名
            if (CollectionUtils.isNotEmpty(valueNameTagIdLikeList)) {
                valueNameLikeSet = valueNameTagIdLikeList.stream().map(com.yxt.talent.bk.core.tag.bean.TagValueBean::getValueName)
                        .collect(Collectors.toSet());
                Set<String> tagIdSet = valueNameTagIdLikeList.stream().map(
                        com.yxt.talent.bk.core.tag.bean.TagValueBean::getTagId)
                        .collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(tagIdSet)) {
                    List<String> tagNames = tagRepository.findTagNameByIds(orgId, tagIdSet);
                    nameLikeList.addAll(tagNames);
                }
            }
            if (CollectionUtils.isEmpty(nameLikeList) && CollectionUtils.isEmpty(valueNameLikeSet)) {
                return Maps.newHashMap();
            }
        }
        List<TagSimpleBean> tagSimpleBeans = tagRepository.findTag4Search(orgId, bean.getCatalogId(),
                tagIds, nameLikeList, valueNameLikeSet);
        if (CollectionUtils.isNotEmpty(tagSimpleBeans)) {
            return tagSimpleBeans.stream().collect(Collectors.groupingBy(TagSimpleBean::getId));
        }
        return Maps.newHashMap();
    }

    /**
     * 透视项初始化
     */
    public List<SearchAnalyseDimensionItem> initAnalyseItem(String orgId, String userId) {
        List<SearchAnalyseDimensionItem> list = new ArrayList<>();
        int index = 0;
        // 基本信息
        insertAnalyseItem(orgId, F_DEPARTMENT_ID, "部门", "基本信息", list, index++, userId);
        insertAnalyseItem(orgId, F_POSITION_ID, "岗位", "基本信息", list, index++, userId);
        insertAnalyseItem(orgId, F_GRADE_NAME, "职级", "基本信息", list, index++, userId);
        insertAnalyseItem(orgId, "sex", "性别", "基本信息", list, index++, userId);
        insertAnalyseItem(orgId, F_AGE_GROUP, "年龄段", "基本信息", list, index++, userId);
        insertAnalyseItem(orgId, "leadershipStyle", "领导风格", "基本信息", list, index++, userId);

        // 人才盘点
        insertAnalyseItem(orgId, F_PERFORMANCE_LEVEL, "绩效等级", "人才盘点", list, index++, userId);
        insertAnalyseItem(orgId, F_ABILITY_LEVEL, "能力等级", "人才盘点", list, index++, userId);
        insertAnalyseItem(orgId, F_POTENTIAL_LEVEL, "潜力等级", "人才盘点", list, index++, userId);
        insertAnalyseItem(orgId, F_REVIEW_RESULT, "盘点结果", "人才盘点", list, index, userId);

        searchAnalyseDimensionItemRepository.saveBatch(list);
        return list;
    }

    /**
     * 标签勾选初始化，筛选组
     *
     * @param orgId 机构id
     */
    public List<SearchFilterItem> initFilterItem(String orgId) {
        List<SearchFilterItem> list = new ArrayList<>();
        int index = 0;
        // 部门
        insertFilterItem(orgId, F_DEPARTMENT_ID, "部门", "基本信息", list, index++);
        insertFilterItem(orgId, F_GRADE_NAME, "职级", "基本信息", list, index++);
        insertFilterItem(orgId, "entryDate", "入职时间", "基本信息", list, index++);
        insertFilterItem(orgId, "sex", "性别", "基本信息", list, index++);
        insertFilterItem(orgId, F_AGE_GROUP, "年龄段", "基本信息", list, index++);

        insertFilterItem(orgId, F_POSITION_ID, "岗位", "基本信息", list, index++);
        insertFilterItem(orgId, "leadershipStyle", "领导风格", "基本信息", list, index++);


        // 学习信息
        insertFilterItem(orgId, F_CERTS, "获得证书", "学习信息", list, index++);
        insertFilterItem(orgId, "o2os", "完成培训", "学习信息", list, index++);
        insertFilterItem(orgId, F_EXAMS, "通过考试", "学习信息", list, index++);

        // 人才盘点
        insertFilterItem(orgId, F_PERFORMANCE_LEVEL, "绩效等级", "人才盘点", list, index++);
        insertFilterItem(orgId, F_ABILITY_LEVEL, "能力等级", "人才盘点", list, index++);
        insertFilterItem(orgId, F_POTENTIAL_LEVEL, "潜力等级", "人才盘点", list, index++);
        insertFilterItem(orgId, F_REVIEW_RESULT, "盘点结果", "人才盘点", list, index);
        searchFilterItemRepository.saveBatch(list);
        return list;
    }

    private void insertAnalyseItem(String orgId, String itemKey, String itemName, String catalogName,
            List<SearchAnalyseDimensionItem> list, int index, String userId) {
        SearchAnalyseDimensionItem search = new SearchAnalyseDimensionItem();
        search.setOrgId(orgId);
        search.setId(ApiUtil.getUuid());
        search.setItemName(itemName);
        search.setItemKey(itemKey);
        search.setItemCatalogName(catalogName);
        search.setOrderIndex(index);
        EntityUtil.setCreateInfo(userId, search);
        list.add(search);
    }

    private void insertFilterItem(String orgId, String itemKey, String itemName, String catalogName,
            List<SearchFilterItem> list, int index) {
        SearchFilterItem search = new SearchFilterItem();
        search.setOrgId(orgId);
        search.setId(ApiUtil.getUuid());
        search.setItemName(itemName);
        search.setItemKey(itemKey);
        search.setItemCatalogName(catalogName);
        search.setOrderIndex(index);
        EntityUtil.setCreateInfo("init", search);
        list.add(search);
    }

    /**
     * 根据用户id查标签
     *
     * @param orgId 机构id
     * @param userId 用户id
     * @return
     */
    public List<TagAndValueBean> searchUserTag(String orgId, String userId) {
        List<TagAndValueBean> tagAndValueBeans = new ArrayList<>();
        List<UserTagValueEntity> userList = userTagValueRepository.findByUserId(orgId, userId);
        Map<String, List<UserTagValueEntity>> userMap = userList.stream()
                .collect(Collectors.groupingBy(UserTagValueEntity::getTagId));
        for(Map.Entry<String, List<UserTagValueEntity>> entry : userMap.entrySet()){
            String key = entry.getKey();
            List<UserTagValueEntity> userTagValueEntities = entry.getValue();
            TagAndValueBean tag = new TagAndValueBean();
            tag.setTagId(key);
            List<TagValueBean> tagValueBeans = new ArrayList<>();
            userTagValueEntities.forEach(user ->{
                TagValueBean tagValueBean = new TagValueBean();
                tagValueBean.setTagValueId(user.getTagValueId());
                tagValueBeans.add(tagValueBean);
            });
            tag.setTagValueBeans(tagValueBeans);
            tagAndValueBeans.add(tag);
        }

        return tagAndValueBeans;
    }

}
