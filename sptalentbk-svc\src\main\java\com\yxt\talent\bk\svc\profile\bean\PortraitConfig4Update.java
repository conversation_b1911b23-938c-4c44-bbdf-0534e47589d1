package com.yxt.talent.bk.svc.profile.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PortraitConfig4Update {
    @Schema(description = "更新类型，0:团队管理者可见范围,1:员工可见范围")
    private int updateClient;

    @Schema(description = "主键Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(description = "机构Id", hidden = true)
    private String orgId;

    @Schema(description = "0:所有员工不可见，1：所有员工可见，2：仅白名单可见")
    private Integer clientRangeComplex;

    @Schema(description = "管理者是否可见（0-否，1-是）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer managerRange;

    @Schema(description = "白名单人员id")
    private List<String> userIds;

    private PortraitShowCfgDto clientCfgDto;
    private PortraitShowCfgDto managerCfgDto;
}
