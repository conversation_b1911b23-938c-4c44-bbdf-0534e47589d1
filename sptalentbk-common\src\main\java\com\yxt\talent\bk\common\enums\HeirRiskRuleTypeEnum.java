package com.yxt.talent.bk.common.enums;

import jodd.util.StringPool;

/**
 * HeirRiskRuleTypeEnum
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 11:45 am
 */
public enum HeirRiskRuleTypeEnum {
    /**
     * 0:自动规则，1:手动规则
     */
    AUTO(0, "自动规则"),
    MANUAL(1, "手动规则");
    private int type;
    private String name;

    HeirRiskRuleTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(int type) {
        for (HeirRiskRuleTypeEnum value : HeirRiskRuleTypeEnum.values()) {
            if (value.type == type) {
                return value.name;
            }
        }
        return StringPool.EMPTY;
    }
}
