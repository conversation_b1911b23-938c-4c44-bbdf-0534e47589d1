package com.yxt.talent.bk.svc.export;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.spsdk.common.utils.YxtExportUtils;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.core.dashboard.bean.DashboardPersonalDetailExportParam;
import com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO;
import com.yxt.talent.bk.core.dashboard.bean.PersonalDetailSearchParam;
import com.yxt.talent.bk.core.dashboard.bean.UserDeptAuthDTO;
import com.yxt.talent.bk.core.dashboard.bean.UserGroupAuthDTO;
import com.yxt.talent.bk.core.mq.RocketMqProducerRepository;
import com.yxt.talent.bk.core.spmodel.entity.DwdDept;
import com.yxt.talent.bk.core.spmodel.mapper.DwdDeptMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserSkillRtStatisticsMapper;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.dashboard.TalentBoardService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */
@Service
@Slf4j
@AllArgsConstructor
public class DashBoardPersonalDetailExportService {

    private final RocketMqProducerRepository rocketMqProducerRepository;

    private final TalentBoardService talentBoardService;
    private final DwdUserSkillRtStatisticsMapper dwdUserSkillRtStatisticsMapper;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final DwdDeptMapper dwdDeptMapper;

    public void exportPersonalDetailDashBoard(UserCacheDetail userDetail, PersonalDetailSearchParam searchParam) {
        if (CollectionUtils.isEmpty(searchParam.getDeptIds()) && CollectionUtils.isEmpty(searchParam.getUserGroupIds())
                && CollectionUtils.isEmpty(searchParam.getUserIds())) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        }
        DashboardPersonalDetailExportParam dashboardPersonalDetailExportParam = new DashboardPersonalDetailExportParam();
        dashboardPersonalDetailExportParam.setUserIds(searchParam.getUserIds());
        dashboardPersonalDetailExportParam.setAdmin(userDetail.getAdmin());
        dashboardPersonalDetailExportParam.setDeptIds(searchParam.getDeptIds());
        dashboardPersonalDetailExportParam.setUserGroupIds(searchParam.getUserGroupIds());
        dashboardPersonalDetailExportParam.setLocale(userDetail.getLocale());
        dashboardPersonalDetailExportParam.setOptUserId(userDetail.getUserId());
        dashboardPersonalDetailExportParam.setOrgId(userDetail.getOrgId());
        //校验没有数据直接异常
        checkHaveData(userDetail, searchParam);
        YxtExportUtils.prepareSyncExport(userDetail, null, SyncExportEnum.DASHBOARD_PERSONAL_DETAIL,
                dashboardPersonalDetailExportParam);
        rocketMqProducerRepository.send(TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_SYNC,
                BeanHelper.bean2Json(dashboardPersonalDetailExportParam, JsonInclude.Include.NON_NULL));

    }

    private void checkHaveData(UserCacheDetail userDetail, PersonalDetailSearchParam searchParam) {
        String orgId = userDetail.getOrgId();
        String userId = userDetail.getUserId();
        List<String> userIds = searchParam.getUserIds();
        List<String> deptIds = searchParam.getDeptIds();
        List<Long> groupIds = searchParam.getUserGroupIds();
        if (CollectionUtils.isNotEmpty(userIds)) {
            //转换三方用户id
            checkUserIdsParam(orgId, userIds);
        } else {
            checkDeptIdsAndGroupIdsParam(userDetail, orgId, userId, deptIds, groupIds);
        }
    }

    private void checkDeptIdsAndGroupIdsParam(UserCacheDetail userDetail, String orgId, String userId,
            List<String> deptIds, List<Long> groupIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            //查询当前人员的权限范围
            UserDeptAuthDTO userDeptAuthDTO = talentBoardService.getUserAuthDept(orgId, userId, userDetail.getAdmin());
            if (!userDeptAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userDeptAuthDTO.getDeptIds())) {
                throw new ApiException(BkApiErrorKeys.EXPORT_NO_DATA);
            }
            //如果是空，说明该用户拥有所有部门的权限
            deptIds = userDeptAuthDTO.getDeptIds();
        }
        //获取权限下的群组集合。如果没有传，则查询下权限下的群组数据
        if (CollectionUtils.isEmpty(groupIds)) {
            UserGroupAuthDTO userGroupAuthDTO = talentBoardService.getUserAuthGroupIds(orgId, userId,
                    userDetail.getAdmin());
            if (!userGroupAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userGroupAuthDTO.getGroupIds())) {
                throw new ApiException(BkApiErrorKeys.EXPORT_NO_DATA);
            }
            //如果是空，说明该用户拥有所有群组的权限
            groupIds = userGroupAuthDTO.getGroupIds();
        }
        List<String> dwdDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
            dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dwdDeptIds)) {
                throw new ApiException(BkApiErrorKeys.EXPORT_NO_DATA);
            }
        }
        DwsUserSkillRtDTO userSkillRtDTO = dwdUserSkillRtStatisticsMapper.getAllByDeptGroupLimitOne(orgId, dwdDeptIds,
                groupIds);
        if (Objects.isNull(userSkillRtDTO)) {
            throw new ApiException(BkApiErrorKeys.EXPORT_NO_DATA);
        }
    }

    private void checkUserIdsParam(String orgId, List<String> userIds) {
        List<UdpLiteUser> udpLiteUsers = udpLiteUserRepository.getUdpUserInfo(orgId, userIds);
        List<String> udpUserIds = udpLiteUsers.stream().map(UdpLiteUser::getId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(udpUserIds)) {
            throw new ApiException(BkApiErrorKeys.EXPORT_NO_DATA);
        }
        DwsUserSkillRtDTO userRes = dwdUserSkillRtStatisticsMapper.getUserSkillRtByUserIdsOne(orgId, udpUserIds);
        if (Objects.isNull(userRes)) {
            throw new ApiException(BkApiErrorKeys.EXPORT_NO_DATA);
        }
    }

    public void doExportPersonalDetailDashBoard(DashboardPersonalDetailExportParam exportUserDetail) {
        log.info("doExportPersonalDetailDashBoard exportUserDetail={}", JSON.toJSONString(exportUserDetail));
        String orgId = exportUserDetail.getUserCache().getOrgId();
        String userId = exportUserDetail.getOptUserId();
        List<String> userIds = exportUserDetail.getUserIds();
        List<String> deptIds = exportUserDetail.getDeptIds();
        List<Long> groupIds = exportUserDetail.getUserGroupIds();
        List<DwsUserSkillRtDTO> userList;
        if (CollectionUtils.isNotEmpty(userIds)) {
            //转换三方用户id
            List<UdpLiteUser> udpLiteUsers = udpLiteUserRepository.getUdpUserInfo(orgId, userIds);
            List<String> udpUserIds = udpLiteUsers.stream().map(UdpLiteUser::getId)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(udpUserIds)) {
                return;
            }
            userList = dwdUserSkillRtStatisticsMapper.getUserSkillRtByUserIds(orgId, udpUserIds);
        } else {
            if (CollectionUtils.isEmpty(deptIds)) {
                //查询当前人员的权限范围
                UserDeptAuthDTO userDeptAuthDTO = talentBoardService.getUserAuthDept(orgId, userId,
                        exportUserDetail.getAdmin());
                if (!userDeptAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userDeptAuthDTO.getDeptIds())) {
                    return;
                }
                //如果是空，说明该用户拥有所有部门的权限
                deptIds = userDeptAuthDTO.getDeptIds();
            }
            //获取权限下的群组集合。如果没有传，则查询下权限下的群组数据
            if (CollectionUtils.isEmpty(groupIds)) {
                UserGroupAuthDTO userGroupAuthDTO = talentBoardService.getUserAuthGroupIds(orgId, userId,
                        exportUserDetail.getAdmin());
                if (!userGroupAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userGroupAuthDTO.getGroupIds())) {
                    return;
                }
                //如果是空，说明该用户拥有所有群组的权限
                groupIds = userGroupAuthDTO.getGroupIds();
            }
            List<String> dwdDeptIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(deptIds)) {
                List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
                dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(dwdDeptIds)) {
                    return;
                }
            }
            userList = dwdUserSkillRtStatisticsMapper.getAllByDeptGroup(orgId, dwdDeptIds, groupIds);
        }
        //        log.debug("doExportPersonalDetailDashBoard userList={}", JSON.toJSONString(userList));
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        BatchOperationUtil.batchExecute(userList, 500,
                subUserList -> YxtExportUtils.exportData(exportUserDetail.getUserCache(),
                        PersonalDetailExportStrategy.class, subUserList));
    }
}
