package com.yxt.talent.bk.svc.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.yxt.common.service.ILock;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.spsdk.common.bean.ResourceTransferMq;
import com.yxt.spsdk.common.component.SpResTransferService;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.core.mq.RocketMqProducerRepository;
import com.yxt.talent.bk.svc.common.enums.ResourceTransferEnum;
import com.yxt.talentbkfacade.constant.BkFacadeContants;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * ResourceTransferConsumer
 *
 * <AUTHOR> harleyge
 * @Date 14/9/24 10:41 am
 */
@Slf4j
@RequiredArgsConstructor
@Component
@RocketMQMessageListener(consumerGroup = TalentBkRocketMqConstant.GROUP_PREFIX + TalentBkRocketMqConstant.TOPIC_SHARE_TRANSFER_USER_RESOURCE,         topic = TalentBkRocketMqConstant.TOPIC_SHARE_TRANSFER_USER_RESOURCE, consumeThreadNumber = 1,         selectorExpression = TalentBkConstants.RES_TRANSFER_GROUP                 + StringPool.PIPE + StringPool.PIPE + TalentBkConstants.RES_TRANSFER_POOL                 + StringPool.PIPE + StringPool.PIPE + TalentBkConstants.RES_TRANSFER_SUCCESSION, consumeTimeout = 30)
public class ResourceTransferConsumer implements RocketMQListener<MessageExt> {
    private final ILock lockService;
    private final SpResTransferService spResTransferService;
    private final RocketMqProducerRepository rocketMqProducerRepository;

    @Override
    public void onMessage(MessageExt message) {
        String resourceCode = null;
        String msg = null;
        try {
            resourceCode = message.getTags();
            ResourceTransferEnum transferEnum = ResourceTransferEnum.getByCode(resourceCode);
            if (transferEnum == null) {
                log.debug("ResourceTransferListener skip resourceCode {}", resourceCode);
                return;
            }
            msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.debug("ResourceTransferListener msg {}", msg);
            ResourceTransferMq transfer = JSON.parseObject(msg, ResourceTransferMq.class);
            transfer.setResourceCode(resourceCode);
            String lockKey = String.format(TalentBkRedisKeys.CACHE_LOCK_RESOURCE_TRANSFER, transfer.getTo(), resourceCode);
            spResTransferService.resourceTransfer(transfer, lockService, lockKey,
                    transferEnum.getTransferFunc(), rocketMqProducerRepository::send);
        } catch (Exception e) {
            log.error("ResourceTransferListener error resourceCode {} msg {}", resourceCode, msg, e);
        }
    }
}
