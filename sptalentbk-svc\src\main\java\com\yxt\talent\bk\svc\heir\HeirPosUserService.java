package com.yxt.talent.bk.svc.heir;

import cn.hutool.core.lang.Pair;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserIdDTO;
import com.yxt.talent.bk.core.heir.bean.PosUserBriefBean;
import com.yxt.talent.bk.svc.heir.bean.HeirPos4Notify;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareDumpParam;
import com.yxt.talent.bk.svc.heir.bean.RemindTodoOperateDTO;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserListReq;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserUpdateReq;
import com.yxt.talent.bk.svc.heir.bean.resp.PosUserListResp;

import java.util.List;

public interface HeirPosUserService {

    void createPosUsers(UserCacheBasic currentUser, String posId, Integer posType, List<String> userIds);

    void updatePosUsers(UserCacheBasic currentUser, PosUserUpdateReq req);

    PagingList<PosUserListResp> list(PageRequest pageReq, UserCacheBasic currentUser, PosUserListReq req);

    PagingList<PosUserBriefBean> validBriefPage(UserCacheBasic currentUser, PageRequest pageReq, String posId);

    List<PosUserBriefBean> listUserBrief(UserCacheBasic currentUser, String posId);

    void delete(UserCacheBasic currentUser, String posId, List<String> userIds);

    void handleUserDel(String orgId, List<String> userIds);

    void asyncBatchCreateTodo(String orgId, HeirPos4Notify pos4Notify, List<HeirPosUserIdDTO> targetUserIds);

    void prepareUrgeHandle(UserBasicBean userBasic, String posId, String userId, Long prepareLevelId,
                           HeirPrepareDumpParam dumpParam);
}
