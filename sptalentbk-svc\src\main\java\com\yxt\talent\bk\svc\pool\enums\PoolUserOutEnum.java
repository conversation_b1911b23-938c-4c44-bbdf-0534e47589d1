package com.yxt.talent.bk.svc.pool.enums;

import jodd.util.StringPool;

/**
 * PoolUserOutEnum
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 2:08 pm
 */
public enum PoolUserOutEnum {
    /**
     * 人才池用户出池状态
     */
    IN_POOL(0, "在池"),
    OUT_USE(1, "合格出池且任用"),
    OUT_PASS(2, "合格出池"),
    NOT_PASS(3, "不合格"),
    PAUSE(4, "未完成中途退出"),
    LEAVE(5, "离职"),
    ;
    /**
     * 0：在池;1:合格出池且任用;2:合格出池;3:不合格;4:未完成中途退出;5:离职
     */

    private int code;
    private String name;

    PoolUserOutEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code != null) {
            for (PoolUserOutEnum poolUserOutEnum : PoolUserOutEnum.values()) {
                if (poolUserOutEnum.getCode() == code) {
                    return poolUserOutEnum.getName();
                }
            }
        }
        return StringPool.EMPTY;
    }
}
