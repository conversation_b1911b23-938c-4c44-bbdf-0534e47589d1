package com.yxt.talent.bk.common.enums;

public enum RuleJobTypeEnum {
    /**
     * job周期
     */
    DAY("每日", 1),
    WEEK("每周", 2),
    MONTH("每月", 3);

    private String name;

    private Integer type;

    RuleJobTypeEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }
}
