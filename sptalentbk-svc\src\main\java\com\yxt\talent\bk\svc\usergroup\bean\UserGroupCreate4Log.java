package com.yxt.talent.bk.svc.usergroup.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.Data;

/**
 * UserGroupCreate4Log
 *
 * <AUTHOR> geyan
 * @Date 22/3/24 4:43 pm
 */
@Data
public class UserGroupCreate4Log {
    @AuditLogField(name = "群组名称", orderIndex = 0)
    private String groupName;
    @AuditLogField(name = "负责人", orderIndex = 1)
    private String mgtUserNames;
    @AuditLogField(name = "群组类型", orderIndex = 2)
    private String groupType;
    @AuditLogField(name = "群组状态", orderIndex = 3)
    private String groupStatus;
    @AuditLogField(name = "标签描述", orderIndex = 4)
    private String groupDesc;
    @AuditLogField(name = "人才范围", orderIndex = 5)
    private String udpCondition;
    @AuditLogField(name = "标签逻辑", orderIndex = 6)
    private String ruleType;
    @AuditLogField(name = "计算规则", orderIndex = 7)
    private String ruleCondition;
}
