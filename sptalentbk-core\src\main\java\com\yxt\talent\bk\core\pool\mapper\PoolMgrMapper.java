package com.yxt.talent.bk.core.pool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.pool.bean.PoolMgr4List;
import com.yxt.talent.bk.core.pool.bean.PoolMgtBean;
import com.yxt.talent.bk.core.pool.entity.PoolMgr;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;


@Mapper
public interface PoolMgrMapper extends BaseMapper<PoolMgr> {

    List<PoolMgr4List> findPoolMgrByPoolId(@Param("orgId") String orgId, @Param("poolId") String poolId);
    List<PoolMgr4List> findPoolMgrByPoolIds(@Param("orgId") String orgId, @Param("poolIds") Collection<String> poolIds);
    List<String> findAuthPoolIdsByMgrIds(@Param("orgId") String orgId, @Param("mgrIds") Collection<String> mgrIds);

    @Select("select distinct mgr_id from bk_pool_mgr where org_id = #{orgId}")
    List<String> findAllMgrIdByOrgId(@Param("orgId") String orgId);

    @Select("select mgr_id from bk_pool_mgr where org_id = #{orgId} and pool_id = #{poolId}")
    List<String> poolMgtUserIds(@Param("orgId") String orgId, @Param("poolId") String poolId);

    List<PoolMgtBean> resTransferList(@Param("orgId") String orgId,
                                      @Param("userIds") List<String> userIds,
                                      @Param("poolIds") List<String> poolIds);
    void execResTransfer(@Param("orgId") String orgId, @Param("list") List<PoolMgtBean> list);
}
