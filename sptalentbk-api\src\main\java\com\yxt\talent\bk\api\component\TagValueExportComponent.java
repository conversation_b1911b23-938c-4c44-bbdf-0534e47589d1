package com.yxt.talent.bk.api.component;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.common.Constants;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.service.ILock;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.DlcComponent;
import com.yxt.export.ExcelUtil;
import com.yxt.export.I18nComponent;
import com.yxt.export.ImportResult;
import com.yxt.talent.bk.common.constants.*;
import com.yxt.talent.bk.common.imports.ImportRequestBean;
import com.yxt.talent.bk.common.utils.ExcelListener;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.TagValueRepository;
import com.yxt.talent.bk.svc.common.CommonService;
import com.yxt.talent.bk.svc.common.UserTagAndValueBusinessService;
import com.yxt.talent.bk.svc.tag.bean.TagBean;
import com.yxt.talent.bk.svc.tag.bean.UserTag4ImportBean;
import com.yxt.talent.bk.svc.tag.bean.export.*;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean;
import com.yxt.talent.bk.svc.udp.rpc.V2UdpEsUserSearchRpc;
import com.yxt.udpfacade.bean.user.es.search.EsUserFields;
import com.yxt.udpfacade.bean.user.es.search.EsUserInfoVo;
import com.yxt.udpfacade.bean.user.es.search.EsUserSearchParam;
import com.yxt.udpfacade.enums.SourceFromEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.assertj.core.util.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 标签表格操作 实现层
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Slf4j
@Service
@AllArgsConstructor
public class TagValueExportComponent {

    private static final String NAVCODE = "sp_gwnl_file_tag";
    private static final String PERMISSION_CODE = "sp_file_tag_traineesAdd_extent";
    private final ILock lockService;
    private final I18nComponent i18nComponent;
    private final DlcComponent dlcComponent;
    private final TagValueExportErrorStrategy tagValueExportErrorStrategy;
    private final TagValueExportErrorStrategy4Mul tagValueExportErrorStrategy4Mul;
    private final TagRepository tagRepository;
    private final TagValueRepository tagValueRepository;
    private final CommonService commonService;
    private final UserAuthService userAuthService;
    private final V2UdpEsUserSearchRpc v2UdpEsUserSearchRpc;
    private final UserTagAndValueBusinessService userTagAndValueBusinessService;

    @DbHintMaster
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public ImportResult tagValueUserImport(String orgId, String userId, String tagId, ImportRequestBean bean,
            MultipartFile file) {
        if (Objects.isNull(bean) || StringUtils.isBlank(tagId)) {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        TagEntity tagEntity = tagRepository.queryTagById(orgId, tagId);
        if (Objects.isNull(tagEntity)) {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        Integer tagType = tagEntity.getTagType();
        Integer valueChooseModel = tagEntity.getValueChooseModel();
        String lockKey = String
                .format(TalentBkRedisKeys.TALENTBK_TAG_VALUE_USER_IMPORT_CACHE_KEY, "importExcel", orgId);
        log.info("标签值人员批量导入锁：" + lockKey);
        TagValue4Export failed = new TagValue4Export();
        failed.setDataList4Mul(Lists.newArrayListWithCapacity(0));
        failed.setValueChooseModel(valueChooseModel);
        String failedFilePath;

        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                // 获取文件流
                InputStream stream = getBatchImportInputStream(bean, file);
                TagValue4Export importData = this.readTagValueExcel(tagType, stream);
                // 导入数据处理
                this.dealWithImportData(importData);
                int totalCount = 0;
                if (Objects.nonNull(importData) && CollectionUtils.isNotEmpty(importData.getDataList4Mul())) {
                    totalCount = importData.getDataList4Mul().size();
                    importData.setValueChooseModel(tagEntity.getValueChooseModel());
                }
                // 验证数据 数据处理下single 2 mul
                Map<String, TagValue4Export.TagExportData4Mul> idObjectMap = this
                        .importDataValid(orgId, userId, importData, tagId, failed, totalCount);
                // 格式转换
                List<UserTag4ImportBean> saves = this
                        .formatTagValueExport2UserTag4ImportBean(orgId, tagId, idObjectMap);
                // 保存数据
                if (idObjectMap.keySet().size() > 0) {
                    userTagAndValueBusinessService.saveUserTagAndValue4Import(orgId, userId, saves);
                }
                // 失败的数据转换
                List<TagValue4ErrorExport4Mul> exportList = Lists.newLinkedList();
                if (failed.getDataList4Mul().size() > 0) {
                    failed.getDataList4Mul().forEach(data4Mul -> {
                        TagValue4ErrorExport4Mul export = new TagValue4ErrorExport4Mul();
                        BeanUtils.copyProperties(data4Mul, export, TagValue4ErrorExport4Mul.class);
                        exportList.add(export);
                    });
                }
                // 导出验证失败的数据
                failedFilePath = errorMsg4Excel(orgId, userId, tagType == 0, exportList);
                int failedCount = failed.getDataList4Mul().size();
                int successCount = saves.isEmpty() ? 0 : totalCount - failedCount;
                // 返回信息
                return ImportResult.builder().totalCount(totalCount).failedCount(failedCount).successCount(successCount)
                        .filePath(failedFilePath).build();
            } catch (ApiException e1) {
                throw e1;
            } catch (Exception e2) {
                log.error("Excel导入异常", e2);
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        return ImportResult.builder().build();
    }

    private void dealWithImportData(TagValue4Export importData) {
        List<TagValue4Export.TagExportData4Single> dataList4Single = importData.getDataList4Single();
        if (CollectionUtils.isNotEmpty(dataList4Single)) {
            List<TagValue4Export.TagExportData4Mul> dataList4Mul = BeanCopierUtil
                    .convertList(dataList4Single, TagValue4Export.TagExportData4Single.class,
                            TagValue4Export.TagExportData4Mul.class);
            importData.setDataList4Mul(dataList4Mul);
        }
    }

    /**
     * errorMsg4Excel
     * @param orgId
     * @param userId
     * @param singleOrNot 是否普通标签
     * @param data
     * @return
     */
    private String errorMsg4Excel(String orgId, String userId, boolean singleOrNot,
            List<TagValue4ErrorExport4Mul> data) {
        String filePath = StringUtils.EMPTY;
        String lockKey = String.format(TalentBkRedisKeys.TALENTBK_TAG_VALUE_USER_EXPORT_CACHE_KEY, orgId, userId);

        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                String fileName =
                    i18nComponent.getI18nValue(ExportConstants.TAG_VALUE_USER_EXPORT_ERROR_FILE_NAME) + System
                                .currentTimeMillis() + ExportConstants.FILE_SUFFIX_XLSX;

                if (singleOrNot) {
                    if (CollectionUtils.isNotEmpty(data)) {
                        List<TagValue4ErrorExport> export4Singles = Lists.newArrayListWithCapacity(data.size());
                        data.forEach(exportData -> {
                            TagValue4ErrorExport export4Single = new TagValue4ErrorExport();
                            BeanUtils.copyProperties(exportData, export4Single, TagValue4ErrorExport.class);
                            export4Singles.add(export4Single);
                        });
                        filePath = dlcComponent
                                .upload2TemporaryDisk(fileName, export4Singles, tagValueExportErrorStrategy);
                    }
                } else {
                    filePath = dlcComponent.upload2TemporaryDisk(fileName, data, tagValueExportErrorStrategy4Mul);
                }
                log.debug("userTemplateExport4TagValue-path={}", filePath);
            } catch (Exception e) {
                log.error("标签人员模板导出异常", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        return filePath;
    }

    private Map<String, TagValue4Export.TagExportData4Mul> importDataValid(String orgId, String userId,
            TagValue4Export importData, String tagId, TagValue4Export failed, int size) {
        Validate.isTrue(importData != null, ExceptionKey.PARAM_TYPE_INVALID);
        Map<String, TagValue4Export.TagExportData4Mul> idObjectMap;
        List<TagValue4Export.TagExportData4Mul> dataList4Mul = importData.getDataList4Mul();
        if (CollectionUtils.isEmpty(dataList4Mul)) {
            return Maps.newHashMap();
        }
        int tagType = importData.getTagType();
        failed.setTagType(tagType);
        int valueChooseModel = importData.getValueChooseModel();
        Set<String> userNameSet = Sets.newLinkedHashSet();
        Map<String, TagValue4Export.TagExportData4Mul> userNameMap = Maps.newConcurrentMap();
        dataList4Mul.forEach(data4Mul -> {
            if (StringUtils.isNotBlank(data4Mul.getUsername())) {
                userNameSet.add(data4Mul.getUsername());
                userNameMap.put(data4Mul.getUsername(), data4Mul);
            }
        });
        long dbDataNum = tagValueRepository.countByTagIds(orgId, Lists.newArrayList(tagId));
        if (tagType == 1) {
            importMultiValueTagCheck(orgId, tagId, valueChooseModel, dbDataNum, failed, dataList4Mul);
        } else {
            importNormalTagCheck(orgId, tagId, failed, dataList4Mul);
        }
        // 如果数据全部不符合要求就直接返回 null
        if (failed.getDataList4Mul().size() == size) {
            return Maps.newHashMap();
        }
        EsUserSearchParam esUserSearchParamExcel = new EsUserSearchParam();
        esUserSearchParamExcel.setOrgId(orgId);
        esUserSearchParamExcel.setUsernames(userNameSet);
        esUserSearchParamExcel.setSourceFrom(SourceFromEnum.TALENT.getValue());
        String[] includeFields = new String[]{EsUserFields.FIELD_ID, EsUserFields.FIELD_USERNAME,
                EsUserFields.FIELD_FULLNAME};
        esUserSearchParamExcel.setIncludeFields(includeFields);
        esUserSearchParamExcel.setProductCode(Constants.XXV2_PRODUCT_CODE);
        log.debug("v2UdpEsUserSearchRpc 连接问题");
        List<String> userIdsExcel = ListUtils.emptyIfNull(v2UdpEsUserSearchRpc.search4List(esUserSearchParamExcel))
                .stream().map(EsUserInfoVo::getId).collect(Collectors.toList());
        Set<String> noExistUsernames = Sets.newHashSet();
        // 账号验证
        idObjectMap = this.userCountValid(userNameMap, esUserSearchParamExcel);
        // 用户是否都存在
        if (userNameSet.size() != userIdsExcel.size()) {
            noExistUsernames = userNameMap.keySet();
        }
        // 获取管辖范围外用户username
        List<String> authOutUsernames = this.authPermissionValid(orgId, userId, userIdsExcel);
        // 失败数据返回
        this.failDataFormat(Lists.newArrayList(noExistUsernames), authOutUsernames, dataList4Mul, failed);
        // 成功数据返回
        this.prepareDataFormat(Lists.newArrayList(noExistUsernames), authOutUsernames, idObjectMap);
        return idObjectMap;
    }

    private void importNormalTagCheck(String orgId, String tagId,
                                      TagValue4Export failed,
                                      List<TagValue4Export.TagExportData4Mul> dataList4Mul) {
        dataList4Mul.forEach(data -> {
            List<TagValueEntity> tagValuesByTagId = tagValueRepository.getTagValuesByTagId(orgId, tagId);
            if (CollectionUtils.isEmpty(tagValuesByTagId)) {
                throw new ApiException(BkApiErrorKeys.ERROR_KEY_TAG_IS_NULL);
            }
            if (tagValuesByTagId.size() > 1) {
                throw new ApiException(BkApiErrorKeys.TAG_EXIST_MUL_VALUE);
            }
            // 存普通标签值 方便调用保存人员标签方法
            data.setTagValue(tagValuesByTagId.get(0).getValueName());
            if (StringUtils.isBlank(data.getFullName()) && StringUtils.isBlank(data.getErrorMsg())) {
                data.setErrorMsg("用户名未填写");
            }
            if (StringUtils.isBlank(data.getUsername()) && StringUtils.isBlank(data.getErrorMsg())) {
                data.setErrorMsg("账号未填写");
            }
            if (StringUtils.isNotBlank(data.getErrorMsg())) {
                failed.getDataList4Mul().add(data);
            }
        });
    }

    private void importMultiValueTagCheck(String orgId, String tagId,
                                          int valueChooseModel, long dbDataNum,
                                          TagValue4Export failed,
                                          List<TagValue4Export.TagExportData4Mul> dataList4Mul) {
        dataList4Mul.forEach(data -> {
            if (StringUtils.isBlank(data.getFullName()) && StringUtils.isBlank(data.getErrorMsg())) {
                data.setErrorMsg("用户名未填写");
            }
            if (StringUtils.isBlank(data.getUsername()) && StringUtils.isBlank(data.getErrorMsg())) {
                data.setErrorMsg("账号未填写");
            }
            if (StringUtils.isBlank(data.getTagValue())) {
                data.setErrorMsg("标签值未填写");
            } else {
                List<String> valueNames = Lists.newArrayList(data.getTagValue().split("；"));
                if (valueChooseModel == 0 && valueNames.size() > 1 && StringUtils.isBlank(data.getErrorMsg())) {
                    data.setErrorMsg("存在多个标签值");
                }
                if (dbDataNum < valueNames.size() && StringUtils.isBlank(data.getErrorMsg())) {
                    data.setErrorMsg("标签值不存在");
                }
                Long dbDataNum2 = tagValueRepository.countByTagValueNamesAndTagId(orgId, valueNames, tagId);
                if (dbDataNum2 < valueNames.size() && StringUtils.isBlank(data.getErrorMsg())) {
                    data.setErrorMsg("标签值不存在");
                }
            }
            if (StringUtils.isNotBlank(data.getErrorMsg())) {
                failed.getDataList4Mul().add(data);
            }
        });
    }

    private void prepareDataFormat(ArrayList<String> noExistUsernames, List<String> authOutUsernames,
            Map<String, TagValue4Export.TagExportData4Mul> idObjectMap) {
        Set<TagValue4Export.TagExportData4Mul> errorData = Sets.newHashSet();
        Set<String> removeData = Sets.newHashSet();
        Set<Map.Entry<String, TagValue4Export.TagExportData4Mul>> entrySet = idObjectMap.entrySet();
        entrySet.forEach(data4Mul -> {
            TagValue4Export.TagExportData4Mul value = data4Mul.getValue();
            if (StringUtils.isNotBlank(value.getErrorMsg())) {
                errorData.add(value);
                removeData.add(data4Mul.getKey());
            }
            if (noExistUsernames.remove(value.getUsername()) && StringUtils.isBlank(value.getErrorMsg())) {
                errorData.add(value);
                removeData.add(data4Mul.getKey());
            }
            if (authOutUsernames.remove(value.getUsername()) && StringUtils.isBlank(value.getErrorMsg())) {
                errorData.add(value);
                removeData.add(data4Mul.getKey());
            }
        });
        idObjectMap.keySet().removeAll(removeData);
    }

    /**
     * failDataFormat
     * @param noExistUsernames 用户是否都存在
     * @param authOutUsernames 获取管辖范围外用户ids
     * @param dataList4Mul
     * @param failed
     */
    private void failDataFormat(List<String> noExistUsernames, List<String> authOutUsernames,
            List<TagValue4Export.TagExportData4Mul> dataList4Mul, TagValue4Export failed) {
        Set<TagValue4Export.TagExportData4Mul> resulSet = Sets.newHashSet();
        // 添加已经存在错误数据 统一处理8
        resulSet.addAll(failed.getDataList4Mul());
        dataList4Mul.forEach(data4Mul -> {
            if (noExistUsernames.remove(data4Mul.getUsername()) && StringUtils.isBlank(data4Mul.getErrorMsg())) {
                data4Mul.setErrorMsg("用户不存在");
                resulSet.add(data4Mul);
            }
            if (authOutUsernames.remove(data4Mul.getUsername()) && StringUtils.isBlank(data4Mul.getErrorMsg())) {
                data4Mul.setErrorMsg("用户不在管辖范围");
                resulSet.add(data4Mul);
            }
        });
        // 同步数据
        Set<TagValue4Export.TagExportData4Single> failData4SingleSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(resulSet)) {
            resulSet.forEach(data4Mul -> {
                TagValue4Export.TagExportData4Single data4Single = new TagValue4Export.TagExportData4Single();
                BeanUtils.copyProperties(data4Mul, data4Single, TagValue4Export.TagExportData4Single.class);
                failData4SingleSet.add(data4Single);
            });
        }
        failed.setDataList4Mul(Lists.newArrayList(resulSet));
        failed.setDataList4Single(Lists.newArrayList(failData4SingleSet));
    }

    private List<UserTag4ImportBean> formatTagValueExport2UserTag4ImportBean(String orgId, String tagId,
            Map<String, TagValue4Export.TagExportData4Mul> idObjectMap) {
        if (idObjectMap.isEmpty()) {
            log.error("标签人员导入： formatTagValueExport2UserTag4ImportBean 方法参数异常：{}, {}, {}", orgId, tagId, idObjectMap);
        }
        List<UserTag4ImportBean> result = Lists.newArrayListWithCapacity(idObjectMap.size());
        idObjectMap.forEach((key, value) -> {
            UserTag4ImportBean sava = new UserTag4ImportBean();
            sava.setUserId(key);
            sava.setUsername(value.getUsername());
            TagBean tagBean = new TagBean();
            List<String> tagValueNames = Arrays.asList(value.getTagValue().split("；"));
            List<TagValueEntity> entities = tagValueRepository.listByTagValueNamesAndTagId(orgId, tagValueNames, tagId);
            if (CollectionUtils.isNotEmpty(entities)) {
                tagBean.setTagValueIdList(
                        entities.parallelStream().map(TagValueEntity::getId).collect(Collectors.toList()));
            }
            tagBean.setTagId(tagId);
            sava.setTagBean(tagBean);
            result.add(sava);
        });
        return result;
    }

    /**
     * 账号存在验证
     * @param userNameMap
     * @return void
     * <AUTHOR>
     * @since 2022/8/15
     */
    private Map<String, TagValue4Export.TagExportData4Mul> userCountValid(
            Map<String, TagValue4Export.TagExportData4Mul> userNameMap, EsUserSearchParam esUserSearchParamEs) {
        if (userNameMap.isEmpty()) {
            return Maps.newHashMap();
        }
        String[] includeFields = new String[]{EsUserFields.FIELD_ID, EsUserFields.FIELD_USERNAME};
        esUserSearchParamEs.setIncludeFields(includeFields);
        // 管理员 则 查询用户是否存在就好  之前验证过
        esUserSearchParamEs.setProductCode(Constants.XXV2_PRODUCT_CODE);
        List<EsUserInfoVo> esUserInfoVos = v2UdpEsUserSearchRpc.search4List(esUserSearchParamEs);
        List<String> usernamesEs = ListUtils.emptyIfNull(esUserInfoVos).stream().map(EsUserInfoVo::getUsername)
                .collect(Collectors.toList());
        Map<String, TagValue4Export.TagExportData4Mul> idObjectMap = Maps.newConcurrentMap();
        if (CollectionUtils.isNotEmpty(usernamesEs)) {
            // 存储可以保存对象
            esUserInfoVos.forEach(
                    esUserInfoVo -> idObjectMap.put(esUserInfoVo.getId(), userNameMap.get(esUserInfoVo.getUsername())));
            // 返回不存在用户
            usernamesEs.forEach(userNameMap::remove);
        }
        return idObjectMap;
    }

    /**
     * 返回不存在用户
     * @param orgId
     * @param userId
     * @param userIdsExcel excel对应的人员ids
     * @return
     */
    private List<String> authPermissionValid(String orgId, String userId, List<String> userIdsExcel) {
        // 用账号去查用户id
        // 非管理员权限范围内的
        UserAuthBean userAuthBean = userAuthService.getAuthUserIds(orgId, userId, NAVCODE, PERMISSION_CODE);
        List<String> userIdsEs = userAuthBean.getUserIds();
        // 如果为管理员 则无非权限人员，之前代码校验了 【this.userCountValid(orgId, userNameSet, esUserSearchParamExcel);】
        if (CollectionUtils.isEmpty(userIdsEs)) {
            userIdsExcel.clear();
        } else {
            userIdsExcel.removeAll(userIdsEs);
            EsUserSearchParam esUserSearchParamEs = new EsUserSearchParam();
            esUserSearchParamEs.setOrgId(orgId);
            esUserSearchParamEs.setUserIds(userIdsExcel);
            esUserSearchParamEs.setProductCode(Constants.XXV2_PRODUCT_CODE);
            String[] includeFields = new String[]{EsUserFields.FIELD_ID, EsUserFields.FIELD_USERNAME};
            esUserSearchParamEs.setIncludeFields(includeFields);
            //  非管理权限下 非管辖范围人员
            return ListUtils.emptyIfNull(v2UdpEsUserSearchRpc.search4List(esUserSearchParamEs)).stream()
                    .map(EsUserInfoVo::getUsername).collect(Collectors.toList());
        }
        return Lists.newArrayListWithExpectedSize(0);
    }

    private TagValue4Export readTagValueExcel(int temp, InputStream inputStream) {
        TagValue4Export importData = new TagValue4Export();
        ExcelReader excelReader = EasyExcelFactory.read(inputStream).build();
        try {
            List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
            Map<String, ExcelListener> listenerMap = new HashMap<>(sheets.size());
            // 验证sheet名
            if (ImportConstants.IMPORT_USER.equals(sheets.get(0).getSheetName())) {
                return new TagValue4Export();
            }
            // 读取sheet
            for (ReadSheet sheet : sheets) {
                String sheetName = sheet.getSheetName();
                int rowNum = 2;
                ExcelListener listener = new ExcelListener();
                sheet.setCustomReadListenerList(Collections.singletonList(listener));
                sheet.setHeadRowNumber(rowNum);
                listenerMap.put(sheetName, listener);
            }
            excelReader.read(sheets);
            // 提取Excel数据
            listenerMap.forEach((sheetName, listener) -> {
                // 适配 0：普通标签， 1：分层标签
                if (temp == 0) {
                    importData.setTagType(0);
                    importData.setDataList4Single(formatExcelData2List4Single(listener));
                } else {
                    importData.setTagType(1);
                    importData.setDataList4Mul(formatExcelData2List4Mul(listener));
                }
            });
        } finally {
            excelReader.finish();
        }
        return importData;
    }

    private InputStream getBatchImportInputStream(ImportRequestBean bean, MultipartFile file) throws IOException {
        if (Objects.nonNull(file)) {
            return file.getInputStream();
        } else {
            String downloadUrl = commonService
                    .getDownloadUrl(bean.getFileId(), "apis.talentbk.studyconfig.import.fileId.invalid");
            return ExcelUtil.getRemoteInputStream(downloadUrl);
        }
    }


    private List<TagValue4Export.TagExportData4Mul> formatExcelData2List4Mul(ExcelListener listener) {
        List<Map<Integer, String>> list = listener.getData();
        List<TagValue4Export.TagExportData4Mul> importList = new ArrayList<>(list.size());
        for (Map<Integer, String> map : list) {
            int i = 0;
            TagValue4Export.TagExportData4Mul temp = new TagValue4Export.TagExportData4Mul();
            temp.setUsername(map.get(i));
            temp.setFullName(map.get(i + 1));
            temp.setTagValue(map.get(i + 2));
            importList.add(temp);
        }
        return importList;
    }

    private List<TagValue4Export.TagExportData4Single> formatExcelData2List4Single(ExcelListener listener) {
        List<Map<Integer, String>> list = listener.getData();
        List<TagValue4Export.TagExportData4Single> importList = new ArrayList<>(list.size());
        for (Map<Integer, String> map : list) {
            int i = 0;
            TagValue4Export.TagExportData4Single temp = new TagValue4Export.TagExportData4Single();
            temp.setUsername(map.get(i));
            temp.setFullName(map.get(i + 1));
            importList.add(temp);
        }
        return importList;
    }
}
