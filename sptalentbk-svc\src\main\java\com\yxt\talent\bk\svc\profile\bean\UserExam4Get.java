package com.yxt.talent.bk.svc.profile.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(name = "人才画像-参与考试记录")
public class UserExam4Get {

    @Schema(description = "考试名称")
    private String arrangeName;

    @Schema(description = "提交考试时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private LocalDateTime updateTime;

    @Schema(description = "得分")
    private BigDecimal highestScore;

    @Schema(description = "0-正考通过，1-补考通过，2-未通过，3-未提交，4-未批阅")
    int uemFinalPassed;

    @Schema(description = "最好成绩所属考试ID")
    @JsonProperty("ueId")
    private String bestUeId;

    @Schema(description = "普通考试不传，循环考试下的批次传 arrangeId")
    private String batchId;

    @Schema(description = "普通考试传宽表的arrangeId，循环考试下的批次传btach_arrange_id")
    private String arrangeId;

}
