package com.yxt.talent.bk.svc.catalog.bean;

import com.yxt.talent.bk.common.constants.PoolConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Data
public class Catalog4Save {

    @Schema(description = "分类主键【编辑时必填】")
    private String id;

    @Schema(description = "分类名称")
    @NotBlank(message = PoolConstants.VERIFY_POOL_CATALOG_NAME_SIZE)
    @Size(max = 20, min = 1, message = PoolConstants.VERIFY_POOL_CATALOG_NAME_SIZE)
    private String catalogName;

}
