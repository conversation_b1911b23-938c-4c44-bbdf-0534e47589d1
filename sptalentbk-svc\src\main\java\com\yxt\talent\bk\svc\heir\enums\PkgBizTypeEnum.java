package com.yxt.talent.bk.svc.heir.enums;

import com.yxt.talent.bk.svc.heir.bean.HeirPrepare4Dump;

import java.util.List;

/**
 * PkgBizTypeEnum
 *
 * <AUTHOR> harleyge
 * @Date 26/7/24 10:09 am
 */
public enum PkgBizTypeEnum {
    /**
     * 业务code
     * List<HeirPrepare4Dump>
     */
    HEIR_RULE_CFG("heir_rule_cfg","继任准备度规则，masterId:bk_heir_pos.id"),
    /**
     * HeirUserPrepareMatch4Dump
     */
    HEIR_RULE_MATCH("heir_rule_match", "继任准备度匹配结果，masterId:bk_heir_pos_user.id")
    ;
    private String code;
    private String desc;

    PkgBizTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
