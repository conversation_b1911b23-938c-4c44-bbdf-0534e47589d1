package com.yxt.talent.bk.svc.usergroup.component;

import com.yxt.common.pojo.IdName;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.udpbase.QueryUdpUtils;
import com.yxt.talent.bk.common.bean.searchrule.BaseSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupBean;
import com.yxt.talent.bk.core.usergroup.entity.SearchScheme;
import com.yxt.talent.bk.core.usergroup.repo.SearchSchemeRepository;
import com.yxt.talent.bk.svc.udp.rpc.UdpOrgSearchRpc;
import com.yxt.talent.bk.svc.usergroup.UserGroupService;
import com.yxt.talent.bk.svc.usergroup.bean.UserGroupCreate4Log;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * UserGroupCreateLogProvider
 *
 * <AUTHOR> geyan
 * @Date 22/3/24 4:42 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class UserGroupCreateLogProvider implements AuditLogDataProvider<UserGroupBean, UserGroupCreate4Log> {
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final UdpOrgSearchRpc udpOrgSearchRpc;
    private final SearchSchemeRepository searchSchemeRepository;
    private final UserGroupService userGroupService;
    @Override
    public UserGroupCreate4Log before(UserGroupBean param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public UserGroupCreate4Log after(UserGroupBean param, AuditLogBasicBean logBasic) {
        String orgId = logBasic.getOrgId();
        UserGroupCreate4Log createLog = new UserGroupCreate4Log();
        createLog.setGroupName(param.getGroupName());
        createLog.setMgtUserNames(udpLiteUserRepository.userNames4Log(logBasic.getOrgId(), param.getUserManagers()));
        createLog.setGroupDesc(param.getGroupDesc());
        createLog.setGroupType(Objects.equals(param.getGroupType(), 1) ? "静态" : "动态");
        createLog.setGroupStatus(CommonUtils.isTrue(param.getEnabled()) ? "启用" : "禁用");
        BaseSearchBean baseSearch = param.getBaseSearch();
        if (baseSearch != null && !baseSearch.emptyCondition()) {
            List<String> gradeNames = BatchOperationUtil.batchQuery(baseSearch.getGradeIds(),
                    subGradeIds -> BeanCopierUtil.convertList(udpOrgSearchRpc.findGradeNameList(orgId, subGradeIds),
                            IdName::getName));
            List<String> names = BatchOperationUtil.batchQuery(baseSearch.getUserIds(),
                    subUserIds -> udpLiteUserRepository.findNamesById(orgId, subUserIds));
            List<String> positionNames = QueryUdpUtils.getPositionNameByIds(baseSearch.getPositionIds());
            List<String> deptNames = QueryUdpUtils.getDeptNameByIds(BeanCopierUtil.convertList(baseSearch.getDepts(), BaseSearchBean.Dept::getDeptId));
            StringBuilder udpCondition = new StringBuilder();
            udpCondition.append(searchTypeDesc(baseSearch.getSearchType()));
            udpCondition.append("岗位：").append(String.join(StringPool.COMMA, positionNames)).append(";");
            udpCondition.append("部门：").append(String.join(StringPool.COMMA,deptNames)).append(";");
            udpCondition.append("职级：").append(String.join(StringPool.COMMA,gradeNames)).append(";");
            udpCondition.append("人员：").append(String.join(StringPool.COMMA, names)).append(";");
            createLog.setUdpCondition(udpCondition.toString());
        }
        if (param.getSchemeId() != null) {
            createLog.setRuleType("常用筛选器");
            createLog.setRuleCondition(Optional.ofNullable(searchSchemeRepository.findSchemeById(orgId, param.getSchemeId()))
                    .map(SearchScheme::getSchemeName).orElse(null));
        } else {
            createLog.setRuleType("自定义筛选");
            SearchRuleBean ruleBean = param.getSearchRuleBean();
            if (ruleBean != null) {
                StringBuilder ruleCondition = new StringBuilder();
                ruleCondition.append(searchTypeDesc(ruleBean.getTagSearchType()));
                userGroupService.queryRuleDetail(orgId, ruleBean.getTagSearch(), ruleDetail -> {
                    ruleCondition.append(ruleDetail.getTagName()).append(StringPool.COLON)
                            .append(Optional.ofNullable(ruleDetail.getTagValueNames())
                                    .map(tagValues -> String.join(StringPool.COMMA, tagValues)).orElse(StringPool.EMPTY))
                            .append(StringPool.SEMICOLON);
                });
                createLog.setRuleCondition(ruleCondition.toString());
            }
        }
        return createLog;
    }

    @Override
    public Pair<String, String> entityInfo(UserGroupBean param, UserGroupCreate4Log beforeObj, UserGroupCreate4Log afterObj, AuditLogBasicBean logBasic) {
        return Pair.of(String.valueOf(param.getId()), String.format("人才群组-%s", param.getGroupName()));
    }

    private String searchTypeDesc(Integer searchType) {
        return Objects.equals(searchType, 2) ? "满足下列任一条件：" : "满足下列所有条件：";
    }
}
