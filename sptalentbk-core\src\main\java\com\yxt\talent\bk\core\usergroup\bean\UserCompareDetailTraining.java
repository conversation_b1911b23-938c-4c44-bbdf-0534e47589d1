package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: sptalentbkapi
 * @description: 学习培训
 **/
@Data
public class UserCompareDetailTraining {

    @Schema(description = "项目名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private String projectName;
    @Schema(description = "开始时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime startTime;
    @Schema(description = "结束时间", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime endTime;
    @Schema(description = "完成状态（0-未完成 1-进行中, 2-已完成） ", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer completionStatus;

}
