package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class UserGroupModulePageVO {
    @Schema(description = "更新时使用，唯一 ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @Schema(description = "群组名称")
    private String groupName;
    @Schema(description = "群组人数")
    private int userCount;
    @Schema(description = "群组类型（1:静态，2：动态）")
    private Integer groupType;
}
