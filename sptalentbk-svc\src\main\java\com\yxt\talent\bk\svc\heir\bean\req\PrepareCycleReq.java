package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.svc.heir.bean.CalcCycleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "准备度规则")
public class PrepareCycleReq {

    @Schema(description = "岗位id")
    private String posId;

    @Schema(description = "执行周期配置")
    private CalcCycleBean calcCycleBean;

    @Schema(description = "提醒周期配置")
    private CalcCycleBean remindCycleBean;

    @Schema(description = "提醒人员id")
    @Size(message = BkApiErrorKeys.PARAM_SIZE_INVALID_MESSAGE, max = 99)
    private List<String> userIds;
}
