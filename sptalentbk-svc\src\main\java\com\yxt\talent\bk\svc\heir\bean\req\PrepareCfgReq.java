package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "准备度规则")
public class PrepareCfgReq {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "岗位id")
    private String posId;

    @Schema(description = "准备度规则等级id")
    private Long prepareLevelId;

    @Schema(description = "准备度规则配置")
    private LabelConditionJsonBean labelRuleGroupBean;

}
