
package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserRvTaskDimension {

    @Schema(description = "维度名称")
    private String dimName;

    @Schema(description = "0达标 1未达标")
    Integer jqTaskResult;

    @Schema(description = "项目创建时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime creatTime;


}
