package com.yxt.talent.bk.svc.usergroup;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageBean;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.EntityUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.DlcComponent;
import com.yxt.export.ExcelUtil;
import com.yxt.export.OutputStrategy;
import com.yxt.idworker.YxtIdWorker;
import com.yxt.spmodel.facade.bean.label.LabelParam;
import com.yxt.spmodel.facade.bean.label.LabelUserVO;
import com.yxt.spmodel.facade.bean.label.LabelVO;
import com.yxt.spmodel.facade.bean.label.LabelValueVO;
import com.yxt.spmodel.facade.bean.label.UserLabelParam;
import com.yxt.spmodel.facade.bean.label.UserLabelVO;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.bean.searchrule.BaseSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SPTagSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.enums.CommonEnableEnum;
import com.yxt.talent.bk.common.enums.RuleJobTypeEnum;
import com.yxt.talent.bk.common.enums.UserGroupTypeEnum;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.common.validator.PermissionValidator;
import com.yxt.talent.bk.core.pool.bean.SearchDeptInfo;
import com.yxt.talent.bk.core.tag.bean.Tag4Page;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.core.usergroup.bean.*;
import com.yxt.talent.bk.core.usergroup.entity.SearchRule;
import com.yxt.talent.bk.core.usergroup.entity.SearchScheme;
import com.yxt.talent.bk.core.usergroup.entity.UserDeptUgroup;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupManager;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupMember;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupTag;
import com.yxt.talent.bk.core.usergroup.mapper.UserDeptUgroupMapper;
import com.yxt.talent.bk.core.usergroup.mapper.UserGroupManagerMapper;
import com.yxt.talent.bk.core.usergroup.repo.SearchRuleRepository;
import com.yxt.talent.bk.core.usergroup.repo.SearchSchemeRepository;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupManagerRepository;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupMemberRepository;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupRepository;
import com.yxt.talent.bk.core.usergroup.repo.UserGroupTagRepository;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean;
import com.yxt.talent.bk.svc.udp.rpc.UdpOrgSearchRpc;
import com.yxt.talent.bk.svc.usergroup.rpc.TagSearchRpc;
import com.yxt.udpfacade.bean.dept.DeptBean;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class UserGroupService {

    private static final int MAX_LEASE_TIME = 120;
    private static final String NAVCODE = "sp_gwnl_file_group";
    /**
     * 员工人员管辖范围
     */
    private static final String PERMISSION_CODE = "sp_file_talentgroup_get_extent";
    private final UserGroupManagerMapper userGroupManagerMapper;
    private final UserGroupManagerRepository userGroupManagerRepository;
    private final DlcComponent dlcComponent;
    private final Executor wafTaskExecutor;
    private final TagSearchRpc tagSearchRpc;
    private final ILock lockService;
    private final UdpOrgSearchRpc udpOrgSearchRpc;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final UserGroupRepository userGroupRepository;
    private final UserGroupMemberRepository userGroupMemberRepository;
    private final SearchRuleRepository searchRuleRepository;
    private final UserGroupTagRepository userGroupTagRepository;
    private final SearchSchemeRepository searchSchemeRepository;
    private final RedisRepository redisRepository;
    private final PermissionValidator permissionValidator;
    private final UserDeptUgroupMapper userDeptUgroupMapper;
    private final UserAuthService userAuthService;

    private static BaseSearchBean getBaseSearchBean(String baseSearchString) {
        BaseSearchBean baseSearchBean = null;
        try {
            if (StringUtils.isNotBlank(baseSearchString)) {
                baseSearchBean = BeanHelper.json2Bean(baseSearchString, BaseSearchBean.class);
            }
        } catch (Exception e) {
            log.error("转化失败:{}", baseSearchString, e);
        }

        return baseSearchBean;
    }

    /**
     * 列表分页查询
     *
     * @param pageRequest
     * @param orgId
     * @return
     */
    public PagingList<UserGroup4Get> find4Page(PageRequest pageRequest, String orgId, String keyword,
            UserCacheDetail userDetail) {
        IPage<Tag4Page> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        if (StringUtils.isNotEmpty(keyword)) {
            keyword = ApiUtil.getFiltedLikeString(keyword);
        }
        // 权限管理
        List<String> deptIds = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        // 如果不是管理员
        if (!StringPool.ONE.equalsIgnoreCase(userDetail.getAdmin())) {
            Map<String, List<String>> permissionMsg = permissionValidator.getPermissionMsg(orgId,
                    userDetail.getUserId(), Lists.newArrayList(), NAVCODE, PERMISSION_CODE, userDetail);
            if (!StringPool.ONE.equalsIgnoreCase(userDetail.getAdmin())) {
                userIds = permissionMsg.get("userIds");
                deptIds = permissionMsg.get("deptIds");
            }
        }
        IPage<UserGroup4Get> pageList = userGroupRepository.find4Page(page, orgId, keyword, deptIds, userIds, 0);
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<Long> groupIds = StreamUtil.mapList(pageList.getRecords(), UserGroup4Get::getId);
            List<UserGroupManagerBean> userGroupManagerBeans = userGroupManagerRepository.findList(orgId, groupIds);
            Map<Long, List<UserGroupManagerBean>> userGMMap = userGroupManagerBeans.stream()
                    .collect(Collectors.groupingBy(UserGroupManagerBean::getGroupId));
            pageList.getRecords().forEach(userGroup4Get -> {
                List<UserGroupManagerBean> managers = userGMMap.get(userGroup4Get.getId());
                if (CollectionUtils.isNotEmpty(managers)) {
                    userGroup4Get.setUserManagers(StreamUtil.mapList(managers, UserGroupManagerBean::getFullname));
                } else {
                    userGroup4Get.setUserManagers(Lists.newArrayList());
                }
            });
        }
        return BeanCopierUtil.toPagingList(pageList);
    }

    /**
     * @param
     * @return
     * @description: 创建用户组
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public UserGroup createUserGroup(String orgId, String userId, UserGroupBean userGroupBean) {
        // 群组默认是查启用人员
        if (userGroupBean.getBaseSearch() == null) {
            userGroupBean.setBaseSearch(new BaseSearchBean());
        }
        userGroupBean.getBaseSearch().setStatus(1);

        // 创建群组基础信息
        Long groupId = YxtIdWorker.getId();
        SearchRuleBean srb = userGroupBean.getSearchRuleBean();
        Long schemeId = userGroupBean.getSchemeId();
        if (schemeId != null) {
            SearchScheme searchScheme = searchSchemeRepository.findSchemeById(orgId, schemeId);
            if (searchScheme == null) {
                throw new ApiException("apis.talentbk.userGroup.scheme.notexite");
            }
            Long sechemeSearchRuleId = searchScheme.getSearchRuleId();
            if (sechemeSearchRuleId != null) {
                srb = getSearchBean(orgId, searchScheme.getSearchRuleId());
            }
        }

        if (srb == null) {
            srb = new SearchRuleBean();
        }
        srb.initSearch();

        // 人员超过 2000，提示人才群组人员过多，不利于后续使用
        Set<String> userIds = searchAllUserByTagForUserGroup(orgId, srb, userGroupBean.getBaseSearch());
        if (UserGroupTypeEnum.STATIC.getType().equals(userGroupBean.getGroupType()) && CollectionUtils.isEmpty(
                userIds)) {
            throw new ApiException("apis.talentbk.userGroup.user.empty");
        }
        int userCount = createUserGroupMember(groupId, orgId, userId, userIds);

        Long searchRuleId = YxtIdWorker.getId();
        // 创建规则
        SearchRule searchRule = new SearchRule();
        searchRule.setTagSearchType(srb.getTagSearchType());
        searchRule.setId(searchRuleId);
        searchRule.setTagSearch(BeanHelper.bean2Json(srb.getTagSearch()));
        searchRule.setOrgId(orgId);
        TalentbkUtil.setCreateInfo(userId, searchRule);
        searchRuleRepository.save(searchRule);

        UserGroup userGroup = new UserGroup();
        BeanHelper.copyProperties(userGroupBean, userGroup);
        userGroup.setId(groupId);
        userGroup.setOrgId(orgId);
        userGroup.setUserCount(userCount);

        userGroup.setBaseSearch(BeanHelper.bean2Json(userGroupBean.getBaseSearch()));
        userGroup.setSearchRuleId(searchRuleId);
        userGroup.setSchemeId(schemeId);
        userGroup.setCaculateTime(LocalDateTime.now());
        TalentbkUtil.setCreateInfo(userId, userGroup);
        userGroupRepository.save(userGroup);

        // 创建管理员
        if (CollectionUtils.isNotEmpty(userGroupBean.getUserManagers())) {
            Set<String> uids = new HashSet<>(userGroupBean.getUserManagers());
            List<UserGroupManager> userGroupManagerList = new ArrayList<>();
            uids.forEach(uid -> {
                UserGroupManager userGroupManager = new UserGroupManager();
                userGroupManager.setId(YxtIdWorker.getId());
                userGroupManager.setOrgId(orgId);
                userGroupManager.setUserId(uid);
                userGroupManager.setGroupId(groupId);
                TalentbkUtil.setCreateInfo(userId, userGroupManager);
                userGroupManagerList.add(userGroupManager);
            });
            userGroupManagerMapper.batchInsert(userGroupManagerList);
        }


        // 创建群组标签关系
        List<UserGroupTag> userGroupTags = new ArrayList<>();
        srb.getTagSearch().forEach(spTagSearchBean -> {
            UserGroupTag userGroupTag = new UserGroupTag();
            userGroupTag.setId(YxtIdWorker.getId());
            userGroupTag.setGroupId(groupId);
            userGroupTag.setOrgId(orgId);
            userGroupTag.setGroupName(userGroup.getGroupName());
            userGroupTag.setTagId(spTagSearchBean.getTagId());
            TalentbkUtil.setCreateInfo(userId, userGroupTag);
            userGroupTags.add(userGroupTag);
        });
        if (CollectionUtils.isNotEmpty(userGroupTags)) {
            userGroupTagRepository.batchInsert(userGroupTags);
        }
        return userGroup;
    }

    private Set<String> searchAllUserByTagForUserGroup(String orgId, SearchRuleBean srb, BaseSearchBean baseSearch) {
        if (CollectionUtils.isNotEmpty(srb.getTagSearch())) {
            return tagSearchRpc.searchAllUserByTagForUserGroup(orgId, srb, baseSearch);
        } else if (!baseSearch.emptyCondition()) {
            return udpLiteUserRepository.listUserIds4Group(orgId, baseSearch.getStatus(),
                    BeanCopierUtil.convertList(baseSearch.getDepts(),
                            dept -> Pair.of(dept.getDeptId(), CommonUtils.isTrue(dept.getIncludeAll()))),
                    baseSearch.getPositionIds(), baseSearch.getGradeIds(), baseSearch.getUserIds());
        } else {
            return Sets.newHashSet();
        }
    }

    private boolean checkGroupNameExists(String orgId, String userId, String deptId, String groupName) {
        Long id = userGroupRepository.getBaseMapper().checkGroupNameExists(orgId, userId, deptId, groupName);
        if (id != null) {
            return true;
        }
        return false;
    }

    private int createUserGroupMember(Long groupId, String orgId, String opUid, Set<String> userIdSet) {
        if (CollectionUtils.isEmpty(userIdSet)) {
            return 0;
        }
        BatchOperationUtil.batchSave(userIdSet.stream().collect(Collectors.toList()), userIds -> {
            List<UserGroupMember> userGroupMembers = new ArrayList<>(userIds.size());
            userIds.forEach(uid -> {
                UserGroupMember userGroupMember = new UserGroupMember();
                userGroupMember.setId(YxtIdWorker.getId());
                userGroupMember.setGroupId(groupId);
                userGroupMember.setUserId(uid);
                userGroupMember.setOrgId(orgId);
                TalentbkUtil.setCreateInfo(opUid, userGroupMember);
                userGroupMembers.add(userGroupMember);
            });
            userGroupMemberRepository.batchInsert(userGroupMembers);
        });
        return userIdSet.size();
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void deleteUserGroup(String orgId, String userId, Long id) {
        UserGroup userGroup = checkAndGetById(orgId, id);
        userGroupRepository.deleteLogic(userId, id);
        userGroupManagerMapper.deleteByGroupId(orgId, userId, id);
        searchRuleRepository.deleteById(orgId, userId, userGroup.getSearchRuleId());
        userGroupMemberRepository.deleteByGroupId(orgId, userId, id);
        userGroupTagRepository.deleteByGroupId(orgId, id);
    }

    public UserGroup checkAndGetById(String orgId, Long userGroupId) {
        Validate.isNotNull(userGroupId, "apis.talentbk.param.miss.error", "userGroupId");
        UserGroup userGroup = userGroupRepository.getInfoById(orgId, userGroupId);
        Validate.isNotNull(userGroup, "apis.talentbk.usergroup.notExist");
        return userGroup;
    }

    public Map<String, String> exportGroupUserInfo(UserGroup userGroup, UserCacheDetail userCache) {
        log.info("exportGroupUserInfo groupId={},userId={},orgId={}", userGroup.getId(), userCache.getUserId(),
                userCache.getOrgId());

        String fileName = userGroup.getGroupName()
                + TalentbkUtil.getMessage("apis.talentbk.group.user.export.name", userCache.getLocale())
                + TalentBkConstants.FILE_TYPE_EXCEL;
        String orgId = userCache.getOrgId();
        Long groupId = userGroup.getId();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_USER_GROUP_EXPORT, orgId, groupId);
        if (!lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            throw new ApiException("apis.talentbk.file.export.ing");
        }
        Map<String, String> res = new HashMap<>(1);
        res.put(ExportConstants.EXPORT_URL_KEY, "");
        try {
            //获取标签规则信息
            Long ruleId = userGroup.getSearchRuleId();
            //判断是否有
            Validate.isNotNull(ruleId, "apis.talentbk.param.miss.error", "ruleId");
            SearchRule searchRule = getBySearchRuleId(orgId, ruleId);
            Validate.isNotNull(searchRule, "apis.talentbk.param.miss.error", "searchRule");
            String tagSearch = searchRule.getTagSearch();
            Validate.isNotBlank(tagSearch, "apis.talentbk.param.miss.error", "tagSearch");
            //从tagSearch中拉取数据
            List<SPTagSearchBean> tagSearchBeanList = JSON.parseArray(tagSearch, SPTagSearchBean.class);
            List<Long> tagIdList = tagSearchBeanList.stream().map(SPTagSearchBean::getTagId).distinct()
                    .collect(Collectors.toList());
            //获取标签对应的信息
            LabelParam labelParam = new LabelParam();
            labelParam.setOrgId(orgId);
            List<LabelParam.LabelAndValue> labels = new ArrayList<>();
            tagIdList.forEach(el -> {
                LabelParam.LabelAndValue labelIdParam = new LabelParam.LabelAndValue();
                labelIdParam.setLabelId(el);
                labels.add(labelIdParam);
            });
            labelParam.setLabelList(labels);
            List<LabelVO> labelVOList = tagSearchRpc.getLabelList(labelParam);
            Map<Long, String> labelIdNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelVOList)) {
                labelIdNameMap = StreamUtil.list2map(labelVOList, LabelVO::getLabelId, LabelVO::getName);
            }
            //从数据模型拉去标签名称信息
            List<String> tagNameList = new ArrayList<>();
            Map<Long, String> finalLabelIdNameMap = labelIdNameMap;
            tagIdList.forEach(el -> {
                String tagName = finalLabelIdNameMap.get(el);
                if (StringUtils.isBlank(tagName)) {
                    return;
                }
                tagNameList.add(tagName);
            });
            //设置表头信息
            List<List<String>> sheetHeaders = initHeaders(userCache.getLocale());
            tagNameList.forEach(dynamicsTagName -> {
                if (StringUtils.isBlank(dynamicsTagName)) {
                    return;
                }
                List<String> headerColDynamics = new ArrayList<>();
                headerColDynamics.add(dynamicsTagName);
                sheetHeaders.add(headerColDynamics);
            });
            String sheetName = "sheet1";
            //设置表格内容信息
            List<UserGroupMember> groupMemberList = getGroupMemberListByGroupId(orgId, groupId);
            Map<String, LocalDateTime> joinTimeMap = StreamUtil.list2map(groupMemberList, UserGroupMember::getUserId,
                    UserGroupMember::getCreateTime);
            List<String> userIdList = groupMemberList.stream().map(UserGroupMember::getUserId).distinct()
                    .collect(Collectors.toList());
            List<Object> sheetData = generateSheetData(orgId, userIdList, joinTimeMap, tagIdList);
            String titleName = fileName.replace(TalentBkConstants.FILE_ORID, "");
            OutputStrategy outputStrategy = new OutputStrategy() {
                @Override
                public String write(String path, String fileName, Object data) throws IOException {
                    String defaultKey = "key1";
                    List<IdName> sheets = new ArrayList<>();
                    IdName defaultSheet = new IdName(defaultKey, sheetName);
                    sheets.add(defaultSheet);
                    Map<String, List<List<String>>> headers = new HashMap<>(1);
                    headers.put(defaultKey, sheetHeaders);
                    Map<String, List<Object>> bodyData = new HashMap<>(1);
                    bodyData.put(defaultKey, sheetData);
                    ExcelUtil.exportWithDynamicHeader(headers, sheets, bodyData, path + fileName);
                    return fileName;
                }

                @Override
                public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
                    return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                            .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE)
                            .appCode(ModuleConstants.APP_CODE).moduleCode(ModuleConstants.MODULE_CODE)
                            .fileName(fileName).name(titleName).build();
                }
            };
            long taskId = dlcComponent.prepareExport(fileName, outputStrategy);
            String filePath = dlcComponent.upload2Disk(fileName, sheetData, outputStrategy, taskId);
            res.put(ExportConstants.EXPORT_URL_KEY, filePath);
            return res;
        } catch (ApiException ex) {
            log.warn("====>exportGroupUserInfo error:", ex);
        } catch (Exception e) {
            log.error("====>exportGroupUserInfo error:", e);
        } finally {
            lockService.unLock(lockKey);
        }
        return res;
    }

    private List<Object> generateSheetData(String orgId, List<String> userIdList,
            Map<String, LocalDateTime> joinTimeMap, List<Long> tagIdList) {
        List<Object> sheetData = new ArrayList<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            List<String> rowData = new ArrayList<>();
            rowData.add("");
            sheetData.add(rowData);
        }
        //调用UDP获取人员基础信息
        CompletableFuture<List<UdpLiteUser>> r1 = CompletableFuture.supplyAsync(() -> getUdpInfoList(orgId, userIdList),
                wafTaskExecutor);
        //调用基础数据获取标签信息
        CompletableFuture<List<UserLabelVO>> r2 = CompletableFuture.supplyAsync(
                () -> getLabelInfoList(orgId, userIdList, tagIdList), wafTaskExecutor);
        CompletableFuture.allOf(r1, r2).join();
        List<UdpLiteUser> udpRes = r1.join();
        List<UserLabelVO> userLabelVOList = r2.join();
        Map<String, List<LabelVO>> userLabelMap = StreamUtil.list2map(userLabelVOList, UserLabelVO::getUserId,
                UserLabelVO::getLabelList);
        Map<String, UdpLiteUser> udpResUserIdMap = StreamUtil.list2map(udpRes, UdpLiteUser::getId);
        userIdList.forEach(userId -> {
            List<String> rowData = new ArrayList<>();
            UdpLiteUser udpLiteUser = udpResUserIdMap.get(userId);
            if (Objects.nonNull(udpLiteUser)) {
                rowData.add(udpLiteUser.getFullname());
                rowData.add(udpLiteUser.getUsername());
                rowData.add(udpLiteUser.getDeptName());
                rowData.add(udpLiteUser.getPositionName());
                rowData.add(udpLiteUser.getGradeName());
            }
            LocalDateTime joinTime = joinTimeMap.get(userId);
            rowData.add(Objects.isNull(joinTime) ? "" : DateUtils.localDateTimeToStr(joinTime));
            //拼接标签内容
            List<LabelVO> labelValueList = userLabelMap.get(userId);
            if (CollectionUtils.isNotEmpty(labelValueList)) {
                for (Long tagId : tagIdList) {
                    Optional<LabelVO> labelItem = labelValueList.stream().filter(el -> tagId.equals(el.getLabelId()))
                            .findFirst();
                    if (labelItem.isPresent() && CollectionUtils.isNotEmpty(labelItem.get().getLabelValueList())) {
                        String tagValue = labelItem.get().getLabelValueList().stream().map(LabelValueVO::getLabelValue)
                                .collect(Collectors.joining("，"));
                        rowData.add(tagValue);
                    } else {
                        rowData.add("");
                    }
                }
            }
            sheetData.add(rowData);
        });
        return sheetData;
    }

    private List<UserLabelVO> getLabelInfoList(String orgId, List<String> userIdList, List<Long> tagIdList) {
        List<List<String>> partitionList = Lists.partition(userIdList, 1000);
        List<UserLabelVO> resultList = new ArrayList<>();
        List<CompletableFuture<List<UserLabelVO>>> futureList = new ArrayList<>();
        partitionList.forEach(list -> {
            CompletableFuture<List<UserLabelVO>> future = CompletableFuture.supplyAsync(
                    () -> getUserLabelInfo(orgId, userIdList, tagIdList), wafTaskExecutor);
            futureList.add(future);
        });
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        allFutures.join();
        for (CompletableFuture<List<UserLabelVO>> future : futureList) {
            resultList.addAll(future.join());
        }
        return resultList;
    }

    private List<UserLabelVO> getUserLabelInfo(String orgId, List<String> userIds, List<Long> tagIdList) {
        UserLabelParam userLabelParam = new UserLabelParam();
        userLabelParam.setOrgId(orgId);
        userLabelParam.setUserIds(userIds);
        userLabelParam.setLabelIds(tagIdList);
        return tagSearchRpc.getUserLabelList(userLabelParam);
    }

    private List<UdpLiteUser> getUdpInfoList(String orgId, List<String> userIdList) {
        List<List<String>> partitionList = Lists.partition(userIdList, 1000);
        List<UdpLiteUser> resultList = new ArrayList<>();
        List<CompletableFuture<List<UdpLiteUser>>> futureList = new ArrayList<>();
        partitionList.forEach(list -> {
            //查询udp_lite_user_sp表
            CompletableFuture<List<UdpLiteUser>> future = CompletableFuture.supplyAsync(
                    () -> getUdpUserInfo(orgId, list), wafTaskExecutor);
            futureList.add(future);
        });
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        allFutures.join();
        for (CompletableFuture<List<UdpLiteUser>> future : futureList) {
            resultList.addAll(future.join());
        }
        return resultList;
    }

    private List<UdpLiteUser> getUdpUserInfo(String orgId, List<String> userIds) {
        return udpLiteUserRepository.getUdpUserInfo(orgId, userIds);
    }

    /**
     * 返回组人员信息
     *
     * @param orgId
     * @param groupId
     * @return
     */
    private List<UserGroupMember> getGroupMemberListByGroupId(String orgId, Long groupId) {
        return userGroupMemberRepository.getGroupMemberListByGroupId(orgId, groupId);
    }

    /**
     * 初始化固定表头信息
     *
     * @return
     */
    private List<List<String>> initHeaders(String locateStr) {
        List<List<String>> header = new ArrayList<>();
        List<String> headerCol1 = new ArrayList<>();
        headerCol1.add(TalentbkUtil.getMessage("apis.talentbk.scheme.user.export.header.fullname", locateStr));
        header.add(headerCol1);
        List<String> headerCol2 = new ArrayList<>();
        headerCol2.add(TalentbkUtil.getMessage("apis.talentbk.scheme.user.export.header.username", locateStr));
        header.add(headerCol2);
        List<String> headerColDept = new ArrayList<>();
        headerColDept.add(TalentbkUtil.getMessage("apis.talentbk.scheme.user.export.header.deptName", locateStr));
        header.add(headerColDept);
        List<String> headerColPos = new ArrayList<>();
        headerColPos.add(TalentbkUtil.getMessage("apis.talentbk.scheme.user.export.header.positionName", locateStr));
        header.add(headerColPos);
        List<String> headerColGrade = new ArrayList<>();
        headerColGrade.add(TalentbkUtil.getMessage("apis.talentbk.scheme.user.export.header.gradeName", locateStr));
        header.add(headerColGrade);
        List<String> headerColJoinTime = new ArrayList<>();
        headerColJoinTime.add(TalentbkUtil.getMessage("apis.talentbk.group.user.export.header.hireDate", locateStr));
        header.add(headerColJoinTime);
        return header;
    }

    /**
     * 根据搜索规则ID，查询搜索规则
     *
     * @param orgId
     * @param searchRuleId
     * @return
     */
    private SearchRule getBySearchRuleId(String orgId, Long searchRuleId) {
        return searchRuleRepository.getBySearchRuleId(orgId, searchRuleId);
    }

    public void setUserGroupEnable(String orgId, String userId, long id, Integer enabled) {
        if (enabled.equals(CommonEnableEnum.DISABLED.getType())) {
            // 后续禁用的时候要做判断，是否有依赖项
        }
        UserGroup userGroup = checkAndGetById(orgId, id);
        userGroup.setEnabled(enabled);
        TalentbkUtil.setUpdatedInfo(userId, userGroup);
        userGroupRepository.updateById(userGroup);
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void caculateMembers(String orgId, String userId, long groupId) {
        String lockKey = orgId + groupId;
        UserGroup userGroup = checkAndGetById(orgId, groupId);
        if (userGroup.getGroupType() != null && UserGroupTypeEnum.STATIC.getType().equals(userGroup.getGroupType())) {
            throw new ApiException("apis.talentbk.userGroup.static.cannot.caculate");
        }
        if (userGroup.getEnabled() != null && YesOrNo.NO.getValue() == userGroup.getEnabled()) {
            throw new ApiException("apis.talentbk.userGroup.disable.cannot.caculate");
        }
        try {
            boolean lock = lockService.tryLock(lockKey, 10, TimeUnit.SECONDS);
            if (lock) {
                List<UserGroupMember> members = getGroupMemberListByGroupId(orgId, groupId);
                Set<String> oldUids = StreamUtil.map2set(members, UserGroupMember::getUserId);

                // 群组肯定会配置规则，不管是直接搜索，还是方案搜索，这个搜索规则 ID 都会有
                SearchRuleBean searchRuleBean = getSearchBean(userGroup.getOrgId(), userGroup.getSearchRuleId());
                // 基础搜索
                String baseSearchString = userGroup.getBaseSearch();
                BaseSearchBean baseSearchBean = getBaseSearchBean(baseSearchString);

                // 按照规则重新计算的用户
                Set<String> newUids = searchAllUserByTagForUserGroup(orgId, searchRuleBean, baseSearchBean);

                // 在oldUids 存在，newUids 不存在的用户 , 删除
                List<String> deleteUids = TalentbkUtil.calculateDifferenceUserIds(oldUids, newUids);
                // newUids 存在，在oldUids 不存在的用户 , 新增
                List<String> addUids = TalentbkUtil.calculateDifferenceUserIds(newUids, oldUids);

                oldUids.removeAll(deleteUids);
                oldUids.addAll(addUids);


                userGroup.setUserCount(oldUids.size());
                TalentbkUtil.setUpdatedInfo(userId, userGroup);
                userGroup.setCaculateTime(LocalDateTime.now());
                userGroupRepository.updateById(userGroup);

                if (CollectionUtils.isNotEmpty(deleteUids)) {
                    log.info("删除人员:{}", BeanHelper.bean2Json(deleteUids));
                    userGroupMemberRepository.deleteByGroupIdAndUserIds(orgId, deleteUids, groupId);
                }
                if (CollectionUtils.isNotEmpty(addUids)) {
                    createUserGroupMember(groupId, orgId, userId, new HashSet<>(addUids));
                }
            } else {
                throw new ApiException("apis.talentbk.userGroup.caclulating");
            }
        } finally {
            lockService.unLock(lockKey);
        }
    }

    private SearchRuleBean getSearchBean(String orgId, Long searchRuleId) {
        SearchRuleBean searchRuleBean = new SearchRuleBean();
        SearchRule searchRule = getBySearchRuleId(orgId, searchRuleId);
        if (searchRule == null) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_USERSEARCHRULE_NULL);
        }
        String tagSearchString = searchRule.getTagSearch();
        try {
            if (StringUtils.isNotBlank(tagSearchString)) {
                List<SPTagSearchBean> tagSearchBeans = BeanHelper.json2Bean(tagSearchString, List.class,
                        SPTagSearchBean.class);
                if (CollectionUtils.isNotEmpty(tagSearchBeans)) {
                    searchRuleBean.setTagSearch(tagSearchBeans);
                }
            }
        } catch (Exception e) {
            log.info("转化失败：{}", tagSearchString);
        }
        searchRuleBean.setTagSearchType(searchRule.getTagSearchType());
        return searchRuleBean;
    }

    public UserGroupDetail4Get getDetail(String orgId, long groupId) {
        UserGroup userGroup = checkAndGetById(orgId, groupId);
        UserGroupDetail4Get userGroupDetail4Get = new UserGroupDetail4Get();
        BeanHelper.copyProperties(userGroup, userGroupDetail4Get);

        List<UserGroupManagerBean> userGroupManagerBeans = userGroupManagerRepository.findList(orgId,
                Lists.newArrayList(groupId));
        if (CollectionUtils.isNotEmpty(userGroupManagerBeans)) {
            userGroupDetail4Get.setUserManagers(
                    StreamUtil.mapList(userGroupManagerBeans, UserGroupManagerBean::getFullname));
        }
        if (StringUtils.isNotBlank(userGroup.getCreateUserId())) {
            List<UdpLiteUser> udpLiteUsers = getUdpUserInfo(orgId, Lists.newArrayList(userGroup.getCreateUserId()));
            if (CollectionUtils.isNotEmpty(udpLiteUsers)) {
                userGroupDetail4Get.setCreateName(udpLiteUsers.get(0).getFullname());
            }
        }
        userGroupDetail4Get.setUserRuleDetail4Get(getRuleDetail(orgId, userGroup));
        if (userGroup.getSchemeId() != null) {
            userGroupDetail4Get.setSchemeId(userGroup.getSchemeId());
            SearchScheme searchScheme = searchSchemeRepository.findSchemeById(orgId, userGroup.getSchemeId());
            if (searchScheme != null) {
                userGroupDetail4Get.setSchemeName(searchScheme.getSchemeName());
            }
        }
        return userGroupDetail4Get;
    }

    /**
     * @param
     * @return
     * @description: 查询规则详情
     */
    public UserRuleDetail4Get getRuleDetail(String orgId, UserGroup userGroup) {
        UserRuleDetail4Get userRuleDetail4Get = new UserRuleDetail4Get();
        SearchRule searchRule = getBySearchRuleId(userGroup.getOrgId(), userGroup.getSearchRuleId());
        if (searchRule == null) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_USERSEARCHRULE_NULL);
        }
        String baseSearchString = userGroup.getBaseSearch();
        try {
            BaseSearchBean baseSearchBean = getBaseSearchBean(baseSearchString);
            if (baseSearchBean != null) {
                userRuleDetail4Get.setBaseSearchType(baseSearchBean.getSearchType());
                List<BaseSearchBean.Dept> depts = baseSearchBean.getDepts();
                if (CollectionUtils.isNotEmpty(depts)) {
                    List<SearchDeptInfo> deptInfoList = BeanCopierUtil.convertList(depts, BaseSearchBean.Dept.class,
                            SearchDeptInfo.class);
                    List<String> deptIds = StreamUtil.mapList(deptInfoList, SearchDeptInfo::getDeptId);
                    List<DeptBean> deptBeans = udpOrgSearchRpc.findDepartmentNameList(orgId, deptIds);
                    Map<String, DeptBean> deptBeanMap = StreamUtil.list2map(deptBeans, DeptBean::getId);
                    List<String> deptNames = new ArrayList<>(deptInfoList.size());
                    deptInfoList.forEach(e -> {
                        if (deptBeanMap.containsKey(e.getDeptId())) {
                            DeptBean deptBean = deptBeanMap.get(e.getDeptId());
                            if (deptBean != null && StringUtils.isNotBlank(deptBean.getName())) {
                                String name = deptBean.getName();
                                if (e.getIncludeAll() != null && e.getIncludeAll() == 1) {
                                    deptNames.add(name + "（后续全部）");
                                } else {
                                    deptNames.add(name);
                                }
                            }
                        }
                    });
                    userRuleDetail4Get.setDeptNames(deptNames);
                }

                List<String> positionIds = baseSearchBean.getPositionIds();
                List<String> positionNames = BatchOperationUtil.batchQuery(positionIds,
                        subPositionIds -> BeanCopierUtil.convertList(
                                udpOrgSearchRpc.findPositionNameList(orgId, subPositionIds), IdName::getName));
                userRuleDetail4Get.setPositionNames(positionNames);

                List<String> gradeIds = baseSearchBean.getGradeIds();
                List<String> gradeNames = BatchOperationUtil.batchQuery(gradeIds,
                        subGradeIds -> BeanCopierUtil.convertList(udpOrgSearchRpc.findGradeNameList(orgId, subGradeIds),
                                IdName::getName));
                userRuleDetail4Get.setGradeNames(gradeNames);

                List<String> userIds = baseSearchBean.getUserIds();
                List<String> names = BatchOperationUtil.batchQuery(userIds,
                        subUserIds -> udpLiteUserRepository.findNamesById(orgId, subUserIds));
                userRuleDetail4Get.setUserNames(names);
            }
        } catch (Exception e) {
            log.info("获取基础信息失败：{}", baseSearchString, e);
        }

        userRuleDetail4Get.setTagSearchType(searchRule.getTagSearchType());
        userRuleDetail4Get.setTags(getSearchTags(orgId, searchRule.getTagSearch()));

        return userRuleDetail4Get;
    }

    /**
     * 获取搜索的 tag 名称
     */
    public List<UserRuleDetailTag4Get> getSearchTags(String orgId, String tagSearchString) {
        List<UserRuleDetailTag4Get> tags = new ArrayList<>();
        if (StringUtils.isNotBlank(tagSearchString)) {
            List<SPTagSearchBean> tagSearchBeans = BeanHelper.json2Bean(tagSearchString, List.class,
                    SPTagSearchBean.class);
            queryRuleDetail(orgId, tagSearchBeans, tags::add);
        }
        return tags;
    }

    public void queryRuleDetail(String orgId, List<SPTagSearchBean> tagSearchBeans,
            Consumer<UserRuleDetailTag4Get> consumer) {
        if (CollectionUtils.isNotEmpty(tagSearchBeans)) {
            try {
                List<LabelVO> labelVOS = tagSearchRpc.getTagNames(orgId, tagSearchBeans);
                if (CollectionUtils.isNotEmpty(labelVOS)) {
                    for (LabelVO labelVO : labelVOS) {
                        UserRuleDetailTag4Get tag = new UserRuleDetailTag4Get();
                        tag.setTagName(labelVO.getName());
                        tag.setTagId(labelVO.getLabelId());
                        tag.setTagValueNames(
                                StreamUtil.mapList(labelVO.getLabelValueList(), LabelValueVO::getLabelValue));
                        consumer.accept(tag);
                    }
                }
            } catch (Exception e) {
                log.error("转化失败：{}", JSON.toJSONString(tagSearchBeans));
            }
        }
    }

    public List<UserGroup> getGroupByJob(List<String> orgIds) {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取今天是星期几
        DayOfWeek dayOfWeek = today.getDayOfWeek();
        int weekdate = dayOfWeek.getValue();
        return userGroupRepository.selectAllList(weekdate, today.getDayOfMonth(), orgIds);
    }

    /**
     * 获取群组数据列表
     *
     * @param userGroup
     * @param userCache
     * @param pageRequest
     * @return
     */
    public PagingList<GroupMemberVO> groupMemberList(UserGroup userGroup, UserCacheBasic userCache,
            PageRequest pageRequest) {
        Long groupId = userGroup.getId();
        String orgId = userCache.getOrgId();
        IPage<GroupMemberVO> iPage = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        //分页查询获取人数
        Page<GroupMemberVO> pageList = userGroupMemberRepository.findPage(iPage, groupId, orgId);
        List<GroupMemberVO> resDataList = pageList.getRecords();
        if (CollectionUtils.isEmpty(resDataList)) {
            return BeanCopierUtil.toPagingList(pageList);
        }
        List<String> userIds = resDataList.stream().map(GroupMemberVO::getUserId).distinct()
                .collect(Collectors.toList());
        //获取UDP用户基本信息
        List<UdpLiteUser> udpUserInfoList = getUdpUserInfo(orgId, userIds);
        Map<String, UdpLiteUser> userIdInfoMap = StreamUtil.list2map(udpUserInfoList, UdpLiteUser::getId);
        //获取用户标签信息
        Long ruleId = userGroup.getSearchRuleId();
        //判断是否有
        Validate.isNotNull(ruleId, "apis.talentbk.param.miss.error", "ruleId");
        SearchRule searchRule = getBySearchRuleId(orgId, ruleId);
        Validate.isNotNull(searchRule, "apis.talentbk.param.miss.error", "searchRule");
        String tagSearch = searchRule.getTagSearch();
        Validate.isNotBlank(tagSearch, "apis.talentbk.param.miss.error", "tagSearch");
        //从tagSearch中拉取数据
        List<SPTagSearchBean> tagSearchBeanList = JSON.parseArray(tagSearch, SPTagSearchBean.class);
        List<Long> tagIdList = tagSearchBeanList.stream().map(SPTagSearchBean::getTagId).distinct()
                .collect(Collectors.toList());
        UserLabelParam userLabelParam = new UserLabelParam();
        userLabelParam.setOrgId(orgId);
        userLabelParam.setUserIds(userIds);
        userLabelParam.setLabelIds(tagIdList);
        List<UserLabelVO> userLabelData = tagSearchRpc.getUserLabelList(userLabelParam);
        //获取用户和标签信息MAP
        Map<String, List<LabelVO>> userLabelMap = StreamUtil.list2map(userLabelData, UserLabelVO::getUserId,
                UserLabelVO::getLabelList);
        //数据组装
        resDataList.forEach(groupMember -> {
            String userId = groupMember.getUserId();
            if (StringUtils.isBlank(userId)) {
                return;
            }
            UdpLiteUser udpLiteUser = userIdInfoMap.get(userId);
            if (Objects.nonNull(udpLiteUser)) {
                groupMember.setFullname(udpLiteUser.getFullname());
                groupMember.setUsername(udpLiteUser.getUsername());
                groupMember.setPositionName(udpLiteUser.getPositionName());
                groupMember.setDeptName(udpLiteUser.getDeptName());
                groupMember.setGradeName(udpLiteUser.getGradeName());
            }
            List<LabelVO> userLabelInfo = userLabelMap.get(userId);
            if (CollectionUtils.isNotEmpty(userLabelInfo)) {
                List<DynamicsLabelVO> dynamicsLabelVOList = new ArrayList<>();
                userLabelInfo.forEach(el -> {
                    DynamicsLabelVO dynamicsLabelVO = new DynamicsLabelVO();
                    dynamicsLabelVO.setLabelId(el.getLabelId());
                    dynamicsLabelVO.setLabelName(el.getName());
                    List<LabelValueVO> labelValueVOList = el.getLabelValueList();
                    if (CollectionUtils.isNotEmpty(labelValueVOList)) {
                        dynamicsLabelVO.setLabelList(
                                BeanCopierUtil.convertList(labelValueVOList, LabelValueVO.class, LabelBizVO.class));
                    }
                    dynamicsLabelVOList.add(dynamicsLabelVO);
                });
                groupMember.setDynamicsLabelList(dynamicsLabelVOList);
            }
        });
        return BeanCopierUtil.toPagingList(pageList);
    }

    public List<UserRuleDetailTag4Get> getDynamicTags(String orgId, Long id) {
        UserGroup userGroup = checkAndGetById(orgId, id);
        SearchRule searchRule = getBySearchRuleId(userGroup.getOrgId(), userGroup.getSearchRuleId());
        if (searchRule == null) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_USERSEARCHRULE_NULL);
        }
        return getSearchTags(orgId, searchRule.getTagSearch());
    }

    public PagingList<SchemeBean> previewUserGroup(String orgId, int offset, int limit,
            UserGroupPreviewBean userGroupPreviewBean) {
        Long searchRuleId = null;
        SearchRuleBean srb = userGroupPreviewBean.getSearchRuleBean();
        Long sechemeId = userGroupPreviewBean.getSchemeId();
        if (sechemeId != null) {
            SearchScheme searchScheme = searchSchemeRepository.findSchemeById(orgId, sechemeId);
            if (searchScheme == null) {
                throw new ApiException("apis.talentbk.userGroup.scheme.notexite");
            }
            searchRuleId = searchScheme.getSearchRuleId();
            if (searchRuleId != null) {
                srb = getSearchBean(orgId, searchScheme.getSearchRuleId());
            }
        }

        if (srb == null) {
            srb = new SearchRuleBean();
        }
        srb.initSearch();
        if (userGroupPreviewBean.getBaseSearch() == null) {
            userGroupPreviewBean.setBaseSearch(new BaseSearchBean());
        }
        userGroupPreviewBean.getBaseSearch().setStatus(1);
        if (CollectionUtils.isNotEmpty(srb.getTagSearch())) {
            PageBean pageBean = new PageBean();
            pageBean.setOffset(offset);
            pageBean.setLimit(limit);
            PagingList<LabelUserVO> userVOPagingList = tagSearchRpc.searchUserByTag(orgId, srb,
                    userGroupPreviewBean.getBaseSearch(), pageBean);
            PagingList<SchemeBean> resPageList = new PagingList<>();
            resPageList.setPaging(userVOPagingList.getPaging());
            List<SchemeBean> tempList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(userVOPagingList.getDatas())) {
                userVOPagingList.getDatas().forEach(e -> {
                    SchemeBean schemeBean = new SchemeBean();
                    BeanCopierUtil.copy(e, schemeBean);
                    tempList.add(schemeBean);
                });
            }
            resPageList.setDatas(tempList);
            return resPageList;
        } else if (!userGroupPreviewBean.getBaseSearch().emptyCondition()) {
            return BeanCopierUtil.toPagingList(
                    udpLiteUserRepository.listUser4Group(TalentbkUtil.toPage(offset, limit), orgId,
                            userGroupPreviewBean.getBaseSearch().getStatus(),
                            BeanCopierUtil.convertList(userGroupPreviewBean.getBaseSearch().getDepts(),
                                    dept -> Pair.of(dept.getDeptId(), CommonUtils.isTrue(dept.getIncludeAll()))),
                            userGroupPreviewBean.getBaseSearch().getPositionIds(),
                            userGroupPreviewBean.getBaseSearch().getGradeIds(),
                            userGroupPreviewBean.getBaseSearch().getUserIds()), liteUser -> {
                        SchemeBean schemeBean = new SchemeBean();
                        BeanCopierUtil.copy(liteUser, schemeBean);
                        schemeBean.setUserId(liteUser.getId());
                        return schemeBean;
                    });
        } else {
            return TalentbkUtil.emptyPage(limit);
        }
    }

    /**
     * @param
     * @return
     * @description: 是否可以进行群组规则计算，如果标签服务正在计算规则，则不进行计算
     */
    public boolean canCanculete(String orgId) {
        return tagSearchRpc.labelExecuteFinish(orgId);
    }

    /**
     * @param
     * @return
     * @description: 删除延迟计算的机构ID
     */
    public void deleteCacuDelayOrgs() {
        redisRepository.delete(TalentBkRedisKeys.CACHE_KEY_USER_GROUP_CACULATE_DELAY_ORG);
    }

    /**
     * @param
     * @return
     * @description: 获取延迟计算的机构
     */
    public List<String> getCacuDelayOrgs() {
        String orgs = redisRepository.opsForValue().get(TalentBkRedisKeys.CACHE_KEY_USER_GROUP_CACULATE_DELAY_ORG);
        if (StringUtils.isNotBlank(orgs)) {
            return BeanHelper.json2Bean(orgs, List.class, String.class);
        }
        return Lists.newArrayList();
    }

    /**
     * @param
     * @return
     * @description: 设置延迟计算的机构ID
     */
    public void setCacuDelayOrgs(List<String> orgs) {
        if (CollectionUtils.isNotEmpty(orgs)) {
            redisRepository.opsForValue().set(TalentBkRedisKeys.CACHE_KEY_USER_GROUP_CACULATE_DELAY_ORG,
                    BeanHelper.bean2Json(orgs, JsonInclude.Include.ALWAYS), 2, TimeUnit.HOURS);
        }
    }

    /**
     * 校验群组添加规则
     */
    public void validateRule(UserGroupBean userGroupBean) {
        SearchRuleBean searchRuleBean = userGroupBean.getSearchRuleBean();

        if (userGroupBean.getGroupType().equals(UserGroupTypeEnum.DYNAMIC.getType())) {
            // 动态的群组，需要判断是否设置周期
            if (userGroupBean.getCaculateJob() == null) {
                throw new ApiException("apis.talentbk.userGroup.rule.job.null");
            }
            if ((RuleJobTypeEnum.WEEK.getType().equals(userGroupBean.getCaculateJob())
                    || RuleJobTypeEnum.MONTH.getType().equals(userGroupBean.getCaculateJob()))
                    && userGroupBean.getCaculateDate() == null) {
                throw new ApiException("apis.talentbk.userGroup.rule.job.dateerror");
            }
        }

        if (userGroupBean.getSchemeId() != null) {
            // 如果配置了方案，可以不用传规则
            return;
        }
        if (Optional.ofNullable(userGroupBean.getBaseSearch()).map(BaseSearchBean::emptyCondition).orElse(true)
                && Optional.ofNullable(searchRuleBean).map(ruleBean -> CollectionUtils.isEmpty(ruleBean.getTagSearch()))
                .orElse(true)) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_USERGROUP_RANGE_BLANK);
        }
    }

    public UserDeptUGroupVO getUserDeptUgroup(String orgId, Long id) {
        UserDeptUgroup userDeptUgroup = userDeptUgroupMapper.selectByOrgIdAndId(orgId, id);
        Validate.isNotNull(userDeptUgroup, BkApiErrorKeys.USER_DEPT_UGROUP_NOT_EXIST);

        String deptId = userDeptUgroup.getDeptId();
        Long ugroupId = userDeptUgroup.getUgroupId();
        UserGroup userGroup = userGroupRepository.getInfoById(orgId, ugroupId);
        Validate.isNotNull(userGroup, BkApiErrorKeys.USER_GROUP_NOT_EXIST);
        SearchRule searchRule = searchRuleRepository.queryRuleById(orgId, userGroup.getSearchRuleId());
        Validate.isNotNull(searchRule, BkApiErrorKeys.USER_GROUP_SEARCH_RULE_NOT_EXIST);
        return buildUserDeptUGroupVO(deptId, userGroup, searchRule);
    }

    private UserDeptUGroupVO buildUserDeptUGroupVO(String deptId, UserGroup userGroup, SearchRule searchRule) {
        UserDeptUGroupVO userDeptUGroupVO = new UserDeptUGroupVO();
        userDeptUGroupVO.setDeptId(deptId);
        userDeptUGroupVO.setSchemeId(userGroup.getSchemeId());
        userDeptUGroupVO.setGroupName(userGroup.getGroupName());
        String baseSearchString = userGroup.getBaseSearch();
        BaseSearchBean baseSearchBean = getBaseSearchBean(baseSearchString);
        userDeptUGroupVO.setBaseSearch(baseSearchBean);

        SearchRuleBean searchRuleBean = new SearchRuleBean();
        List<SPTagSearchBean> spTagSearchBeans = BeanHelper.json2Bean(searchRule.getTagSearch(), List.class,
                SPTagSearchBean.class);
        searchRuleBean.setTagSearch(spTagSearchBeans);
        searchRuleBean.setTagSearchType(searchRule.getTagSearchType());
        searchRuleBean.setRuleId(searchRule.getId());
        userDeptUGroupVO.setSearchRuleBean(searchRuleBean);
        return userDeptUGroupVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserDeptUgroup(String orgId, String userId, Long id, TeamUserUgroupCreateCmd updateCmd) {
        UserDeptUgroup userDeptUgroup = userDeptUgroupMapper.selectByOrgIdAndId(orgId, id);
        Validate.isNotNull(userDeptUgroup, BkApiErrorKeys.USER_DEPT_UGROUP_NOT_EXIST);
        // 先删后增
        this.deleteUserGroup(orgId, userId, userDeptUgroup.getUgroupId());
        // 检查部门下是否有重名人才库
        if (checkGroupNameExists(orgId, userId, updateCmd.getDeptId(), updateCmd.getGroupName())) {
            throw new ApiException("apis.talentbk.userGroup.name.exists");
        }
        fillDeptBaseSearch(updateCmd);
        UserGroupBean userGroupBean = toUserGroupBean(updateCmd);
        UserGroup userGroup = this.createUserGroup(orgId, userId, userGroupBean);
        userDeptUgroup.setUgroupId(userGroup.getId());
        // 将新的群组id绑定到当前的部门人才库中
        userDeptUgroupMapper.updateByPrimaryKey(userDeptUgroup);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteUserDeptUgroup(String orgId, String userId, Long id) {
        UserDeptUgroup userDeptUgroup = userDeptUgroupMapper.selectByOrgIdAndId(orgId, id);
        Validate.isNotNull(userDeptUgroup, BkApiErrorKeys.USER_DEPT_UGROUP_NOT_EXIST);
        this.deleteUserGroup(orgId, userId, userDeptUgroup.getUgroupId());
        userDeptUgroup.setDeleted(YesOrNo.YES.getValue());
        EntityUtil.setUpdatedInfo(userId, userDeptUgroup);
        userDeptUgroupMapper.updateByPrimaryKey(userDeptUgroup);
    }

    /**
     * 从其他部门复制人才看板-人才库
     *
     * @param orgId
     * @param userId
     */
    @Transactional(rollbackFor = Exception.class)
    public void copyUserDeptUgroup(String orgId, String userId, TeamUserUgroupCreateCmd cmd) {
        String deptId = cmd.getDeptId();
        Validate.isTrue(!Objects.equals(deptId, cmd.getFromDeptId()), "apis.talentbk.userGroup.dept.not.same");
        List<UserDeptUgroup> sourceUserDeptUgroupList = userDeptUgroupMapper.selectByUserIdAndDeptId(orgId, userId,
                cmd.getFromDeptId());
        Validate.isNotEmpty(sourceUserDeptUgroupList, "apis.talentbk.userGroup.dept.ugroup.not.exist");
        // 先删除当前员工在当前部门创建的人才库
        List<UserDeptUgroup> userDeptUgroups = userDeptUgroupMapper.selectByUserIdAndDeptId(orgId, userId, deptId);
        userDeptUgroups.forEach(userDeptUgroup -> this.deleteUserDeptUgroup(orgId, userId, userDeptUgroup.getId()));
        sourceUserDeptUgroupList.forEach(
                sourceUserDeptUgroup -> copyUserDeptUgroup(orgId, userId, cmd, sourceUserDeptUgroup));
    }

    private void copyUserDeptUgroup(String orgId, String userId, TeamUserUgroupCreateCmd cmd,
            UserDeptUgroup sourceUserDeptUgroup) {
        UserGroup sourceUserGroup = userGroupRepository.getInfoById(orgId, sourceUserDeptUgroup.getUgroupId());
        if (sourceUserGroup == null) {
            log.debug("LOG50120:{}", sourceUserDeptUgroup.getUgroupId());
            return;
        }

        TeamUserUgroupCreateCmd copyCmd = createTeamUserUgroupCreateCmd(cmd.getDeptId(), sourceUserGroup);
        this.createUserDeptUgroup(orgId, userId, copyCmd); // NOSONAR
    }

    private TeamUserUgroupCreateCmd createTeamUserUgroupCreateCmd(String deptId, UserGroup sourceUserGroup) {
        TeamUserUgroupCreateCmd cmdCopy = new TeamUserUgroupCreateCmd();
        cmdCopy.setDeptId(deptId);
        cmdCopy.setTeamGroup(1);
        cmdCopy.setGroupName(sourceUserGroup.getGroupName());
        cmdCopy.setGroupDesc(sourceUserGroup.getGroupDesc());
        cmdCopy.setSchemeId(sourceUserGroup.getSchemeId());

        SearchRule searchRule = searchRuleRepository.getBySearchRuleId(sourceUserGroup.getOrgId(),
                sourceUserGroup.getSearchRuleId());
        Validate.isNotNull(searchRule, BkApiErrorKeys.USER_GROUP_SEARCH_RULE_NOT_EXIST);
        cmdCopy.setSearchRuleBean(createSearchRuleBean(searchRule));

        BaseSearchBean baseSearchBean = createBaseSearchBean(deptId, sourceUserGroup.getBaseSearch());
        cmdCopy.setBaseSearch(baseSearchBean);

        return cmdCopy;
    }

    private SearchRuleBean createSearchRuleBean(SearchRule searchRule) {
        SearchRuleBean searchRuleBean = new SearchRuleBean();
        searchRuleBean.setTagSearchType(searchRule.getTagSearchType());

        List<SPTagSearchBean> tagSearchBeanList = JSON.parseArray(searchRule.getTagSearch(), SPTagSearchBean.class);
        searchRuleBean.setTagSearch(tagSearchBeanList);

        return searchRuleBean;
    }

    private BaseSearchBean createBaseSearchBean(String deptId, String baseSearchString) {
        BaseSearchBean baseSearchBean = getBaseSearchBean(baseSearchString);
        if (baseSearchBean == null) {
            return null;
        }
        BaseSearchBean.Dept dept = new BaseSearchBean.Dept();
        dept.setDeptId(deptId);
        dept.setIncludeAll(YesOrNo.YES.getValue());
        List<BaseSearchBean.Dept> depts = new ArrayList<>();
        depts.add(dept);
        baseSearchBean.setDepts(depts);

        return baseSearchBean;
    }

    /**
     * 直接创建人才看板-人才库
     *
     * @param orgId
     * @param userId
     * @param createCmd
     */
    @Transactional(rollbackFor = Exception.class)
    public void createUserDeptUgroup(String orgId, String userId, TeamUserUgroupCreateCmd createCmd) {
        // 一个部门一个团队管理员最多只能创建4个人才库
        int cnt = userDeptUgroupMapper.countByUserIdAndDeptId(orgId, userId, createCmd.getDeptId());
        Validate.isTrue(cnt < 4, "apis.talentbk.userGroup.dept.ugroup.max");

        // 检查部门下是否有重名人才库
        if (checkGroupNameExists(orgId, userId, createCmd.getDeptId(), createCmd.getGroupName())) {
            throw new ApiException("apis.talentbk.userGroup.name.exists");
        }

        // 我的团队-创建人才看板时,创建的人才群组默认都是查询这个部门下的人才,所以隐含一个部门id的基础条件
        fillDeptBaseSearch(createCmd);
        UserGroupBean userGroupBean = toUserGroupBean(createCmd);
        UserGroup userGroup = this.createUserGroup(orgId, userId, userGroupBean);

        UserDeptUgroup userDeptUgroup = new UserDeptUgroup();
        userDeptUgroup.setId(YxtIdWorker.getId());
        userDeptUgroup.setOrgId(orgId);
        userDeptUgroup.setUserId(userId);
        userDeptUgroup.setDeptId(createCmd.getDeptId());
        userDeptUgroup.setUgroupId(userGroup.getId());
        userDeptUgroup.setDeleted(YesOrNo.NO.getValue());
        TalentbkUtil.setCreateInfo(userId, userDeptUgroup);
        userDeptUgroupMapper.insert(userDeptUgroup);
    }

    private static void fillDeptBaseSearch(TeamUserUgroupCreateCmd teamUserUgroupCreateCmd) {
        BaseSearchBean baseSearch = teamUserUgroupCreateCmd.getBaseSearch();
        if (baseSearch == null) {
            baseSearch = new BaseSearchBean();
            baseSearch.setSearchType(2);
            teamUserUgroupCreateCmd.setBaseSearch(baseSearch);
            if (baseSearch.getDepts() == null) {
                baseSearch.setDepts(new ArrayList<>());
            }
        }
        BaseSearchBean.Dept dept = new BaseSearchBean.Dept();
        dept.setDeptId(teamUserUgroupCreateCmd.getDeptId());
        dept.setIncludeAll(1);
        if (baseSearch.getDepts() == null) {
            baseSearch.setDepts(new ArrayList<>());
        }
        if (!baseSearch.getDepts().contains(dept)) {
            baseSearch.getDepts().add(dept);
        }
    }

    @NotNull
    private static UserGroupBean toUserGroupBean(TeamUserUgroupCreateCmd teamUserUgroupCreateCmd) {
        UserGroupBean userGroupBean = new UserGroupBean();
        userGroupBean.setGroupType(teamUserUgroupCreateCmd.getGroupType());
        userGroupBean.setGroupName(teamUserUgroupCreateCmd.getGroupName());
        userGroupBean.setEnabled(teamUserUgroupCreateCmd.getEnabled());
        userGroupBean.setGroupDesc(teamUserUgroupCreateCmd.getGroupDesc());
        userGroupBean.setCaculateJob(teamUserUgroupCreateCmd.getCaculateJob());
        userGroupBean.setCaculateDate(teamUserUgroupCreateCmd.getCaculateDate());
        userGroupBean.setSchemeId(teamUserUgroupCreateCmd.getSchemeId());
        userGroupBean.setSearchRuleBean(teamUserUgroupCreateCmd.getSearchRuleBean());
        userGroupBean.setBaseSearch(teamUserUgroupCreateCmd.getBaseSearch());
        userGroupBean.setTeamGroup(teamUserUgroupCreateCmd.getTeamGroup());
        return userGroupBean;
    }

    public PagingList<GroupMemberVO> getUserDeptUgroupMembers(long userDeptUgroupId, UserCacheBasic userCache) {
        UserDeptUgroup userDeptUgroup = userDeptUgroupMapper.selectByPrimaryKey(userDeptUgroupId);
        Validate.isNotNull(userDeptUgroup, BkApiErrorKeys.USER_DEPT_UGROUP_NOT_EXIST);
        UserGroup userGroup = this.checkAndGetById(userCache.getOrgId(), userDeptUgroup.getUgroupId());
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        return this.groupMemberList(userGroup, userCache, pageRequest);
    }

    /**
     * 获取用户的指定部门的人才池列表
     *
     * @param orgId
     * @param userId
     * @param deptId
     * @return
     */
    public List<UserDeptUgroup4Get> findUserDeptUgroupPage(String orgId, String userId, String deptId) {
        return userDeptUgroupMapper.selectStatisticByUserIdAndDeptId(orgId, userId, deptId);
    }

    /**
     * 分页查询我管辖范围内的人才群组列表数据
     *
     * @param userBasic
     * @param groupName
     * @return
     */
    public PagingList<UserGroupModulePageVO> pageUserGroupForModule(UserBasicBean userBasic, String groupName,
            String navCode, String dataPermissionCode) {
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        String orgId = userBasic.getOrgId();
        if (StringUtils.isBlank(navCode) || StringUtils.isBlank(dataPermissionCode)) {
            return TalentbkUtil.emptyPage((int) pageRequest.getSize());
        }
        //获取群组的负责人和创建人
        List<String> verifyUserIds = getAllDataAuthUserIds(orgId);
        if (CollectionUtils.isEmpty(verifyUserIds)) {
            return TalentbkUtil.emptyPage((int) pageRequest.getSize());
        }
        //获取用户权限范围的数据
        UserAuthBean userAuthBean = userAuthService.verifyPermission(userBasic, navCode, dataPermissionCode,
                verifyUserIds);
        //没有所有的用户权限并且管辖范围内的人员ID为空，则返回空
        if (!userAuthBean.isAllUserAllow() && CollectionUtils.isEmpty(userAuthBean.getUserIds())) {
            return TalentbkUtil.emptyPage((int) pageRequest.getSize());
        }
        List<String> rangeUserIds = new ArrayList<>();
        //判断如果用户拥有所有权限则查询不需要根据创建人和负责人去查询
        if (!userAuthBean.isAllUserAllow()) {
            rangeUserIds = userAuthBean.getUserIds();
        }
        IPage<UserGroupModulePageVO> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        Page<UserGroupModulePageVO> pageResult = userGroupRepository.findAuthData4PageModule(pageable, orgId,
                rangeUserIds, groupName);
        return BeanCopierUtil.toPagingList(pageResult);

    }

    public List<String> getAllDataAuthUserIds(String orgId) {
        List<String> res = new ArrayList<>();
        //查询所有用户群组的创建人和负责人ID集合
        List<String> creatorIds = userGroupRepository.findAllCreateUserIds(orgId);
        if (CollectionUtils.isNotEmpty(creatorIds)) {
            res.addAll(creatorIds);
        }
        List<String> mgrUserIds = userGroupManagerRepository.findAllMgrUserIds(orgId);
        if (CollectionUtils.isNotEmpty(mgrUserIds)) {
            res.addAll(mgrUserIds);
        }
        return res;
    }
}
