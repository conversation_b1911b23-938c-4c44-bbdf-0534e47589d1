package com.yxt.talent.bk.core.mq;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.util.BeanHelper;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * 消息发送服务类
 */
@AllArgsConstructor
@Repository
@Slf4j
public class RocketMqProducerRepository {

    private final RocketMQTemplate rocketMQTemplate;

    /**
     * 发送普通消息
     */
    public void send(String topic, String body) {
        log.info("LOG60780:mq sending topic: {}, body: {}", topic, body);
        rocketMQTemplate.syncSend(topic, body);
    }

    /**
     * 发送普通延迟消息
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     */
    public void syncDelaySend(String topic, String body, int delayLevel) {
        log.info("LOG60760:mq sending topic: {}, body: {},delayLevel={}", topic, body, delayLevel);
        rocketMQTemplate.syncDelaySend(topic, body, delayLevel);
    }

    /**
     * 发送普通消息带Tag
     */
    public void sendWithTag(String topic, String tag, String body) {
        // 指定topic的同时，设置tag值，以便消费端可以根据tag值进行选择性消费
        log.info("LOG60770:mq sending topic {} with tag: {}, body: {}", topic, tag, body);
        rocketMQTemplate.syncSend(topic, body, tag);
    }


    /**
     * 同步发送顺序消息(推荐使用)
     *
     * @return
     */
    public SendResult sendOrderly(String topic, String jsonBody, String hashKey) {
        log.info("LOG60740:topic: {}, body: {}", topic, jsonBody);
        SendResult sendResult = rocketMQTemplate.syncSendOrderly(topic, jsonBody, (mqs, msg, arg) -> {
            int id = Objects.hashCode(hashKey);
            long index = Math.abs(id) % mqs.size();
            return mqs.get((int) index);
        }, hashKey);
        if (log.isDebugEnabled()) {
            log.debug("LOG60750:{}", BeanHelper.bean2Json(sendResult, JsonInclude.Include.ALWAYS));
        }
        return sendResult;
    }

}
