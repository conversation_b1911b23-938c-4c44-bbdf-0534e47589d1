package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.bean.HeirRemindTodoIdDTO;
import com.yxt.talent.bk.core.heir.entity.HeirRemindTodoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * HeirPrepareRemindUserMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 6:22 pm
 */
@Mapper
public interface HeirRemindTodoMapper extends BkBaseMapper<HeirRemindTodoEntity> {
    HeirRemindTodoEntity getRemindTodo(@Param("orgId") String orgId,
                                       @Param("posId") String posId,
                                       @Param("remindUserId") String remindUserId,
                                       @Param("targetRecordId") Long targetRecordId);

    List<HeirRemindTodoIdDTO> tgtRecordRemind(@Param("orgId") String orgId,
                                              @Param("posId") String posId,
                                              @Param("targetRecordIds") List<Long> targetRecordIds);

    int batchEndTodo(@Param("orgId") String orgId,
                     @Param("ids")List<Long> ids,
                     @Param("todoStatus")Integer todoStatus);
}
