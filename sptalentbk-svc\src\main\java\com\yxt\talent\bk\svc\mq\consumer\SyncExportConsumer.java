package com.yxt.talent.bk.svc.mq.consumer;

import cn.hutool.core.lang.Pair;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.spsdk.common.utils.YxtExportUtils;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.core.component.RedisComponent;
import com.yxt.talent.bk.svc.export.SyncExportEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2021/2/1 15:57 15:57
 */

@Component
@AllArgsConstructor
@Slf4j
@RocketMQMessageListener(consumerGroup = TalentBkRocketMqConstant.GROUP_PREFIX         + TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_SYNC, topic = TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_SYNC, consumeThreadNumber = 5, consumeTimeout = 30)
public class SyncExportConsumer implements RocketMQListener<String> {

    private final RedisComponent redisComponent;


    @Override
    public void onMessage(String message) {
        try {
            YxtExportUtils.exportData(message, SyncExportEnum.values(), (exportMsg, exportEnum) -> {
                String msgKey = exportMsg.getUserCache().getUserId() + exportMsg.getRequestId();
                return !redisComponent.setNx(String.format(TalentBkRedisKeys.CACHE_MQ_DUPLICATE_MESSAGE, msgKey),
                        Pair.of(35, TimeUnit.MINUTES));
            });
        } catch (Exception e) {
            log.error("exportSync fail msg {}", message, e);
        }
    }
}
