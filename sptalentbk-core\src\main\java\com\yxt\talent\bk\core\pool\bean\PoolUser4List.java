package com.yxt.talent.bk.core.pool.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import com.yxt.talent.bk.core.UdpBaseColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

/**
 *  人才池人员管理
 * <AUTHOR>
 * @since 2022/9/13 15:55
 * @version 1.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PoolUser4List extends UdpBaseColumn {
    @Schema(description = "主键")
    private String id;

    @Schema(description = "人才池id")
    private String poolId;

    @Schema(description = "准备度id")
    private String readinessId;

    @Schema(description = "准备度名称")
    private String readinessName;

    @Schema(description = "准备度(饱和度)")
    private Integer readiness;

    @Schema(description = "准备度(当前值)")
    private Integer orderIndex;

    @Schema(description = "准备度页面显示值")
    private Integer readinessValue;

    @Schema(description = "准备度页面显示值Str")
    private String readinessValueStr;

    @Schema(description = "人员id")
    private String userId;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+0800")
    private Date createTime;

    @Schema(description = "出池事件枚举(1:合格出池且任用;2:合格出池;3:不合格出池;4:未完成中途退出;5:离职)")
    private Integer eventPoolOut;

    @Schema(description = "备注/去向")
    private String remark;

    /**
     * 标签字段
     */
    @Schema(description="司龄")
    private String entryDate;
    @Schema(description="绩效等级（高、中、低）")
    private String performanceLevel;
    @Schema(description="能力等级（高、中、低）")
    private String abilityLevel;
    @Schema(description="潜力等级（高、中、低）")
    private String potentialLevel;

    @Schema(description = "是否存在测评 0:不存在 1:存在")
    private int existEval = 0;

    /**
     * 计算准备度
     */
    public Integer getReadinessValue() {
        if (Objects.nonNull(orderIndex) && Objects.nonNull(readiness)) {
            // 准备度计算方式 准备度(饱和度) - 准备度(当前值)
            return readiness - orderIndex;
        }
        return null;
    }

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(userId, getFullName(), this::setFullName);
    }
}
