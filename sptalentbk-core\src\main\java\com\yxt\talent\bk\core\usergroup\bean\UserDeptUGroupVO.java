package com.yxt.talent.bk.core.usergroup.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.bk.common.bean.searchrule.BaseSearchBean;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@Schema(name = "我的团队-人才看板-组织人才库-查看人才库详情")
public class UserDeptUGroupVO {

    @Schema(description = "部门id")
    private String deptId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "常用筛选器")
    private Long schemeId;

    @Schema(description = "标签搜索规则")
    private String groupName;

    @Schema(description = "标签搜索规则")
    private SearchRuleBean searchRuleBean;

    @Schema(description = "基础过滤规则")
    private BaseSearchBean baseSearch;
}
