package com.yxt.talent.bk.svc.common.enums;

import com.yxt.spsdk.common.bean.ResourceTransferMq;
import com.yxt.spsdk.common.bean.ResourceTransferQty;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.common.ResourceTransferService;
import org.apache.commons.lang3.StringUtils;

import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * ResourceTransferEnum
 *
 * <AUTHOR> harleyge
 * @Date 14/9/24 11:30 am
 */
public enum ResourceTransferEnum {
    /**
     * 资源转移处理方法
     */
    USER_GROUP(TalentBkConstants.RES_TRANSFER_GROUP,
            ResourceTransferService.self()::userGroupQty,
            ResourceTransferService.self()::transferUserGroup),
    POOL(TalentBkConstants.RES_TRANSFER_POOL,
            ResourceTransferService.self()::poolQty,
            ResourceTransferService.self()::transferPool),
    HEIR_SUCCESSION(TalentBkConstants.RES_TRANSFER_SUCCESSION,
            ResourceTransferService.self()::heirSuccessionQty,
            ResourceTransferService.self()::transferHeirSuccession);

    private String code;
    private BiFunction<String, String, Integer> countFunc;
    private Function<ResourceTransferMq, ResourceTransferQty> transferFunc;
    ResourceTransferEnum(String code, BiFunction<String, String, Integer> countFunc, Function<ResourceTransferMq, ResourceTransferQty> transferFunc) {
        this.code = code;
        this.countFunc = countFunc;
        this.transferFunc = transferFunc;
    }

    public static ResourceTransferEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (ResourceTransferEnum value : ResourceTransferEnum.values()) {
                if (code.equals(value.code)) {
                    return value;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public BiFunction<String, String, Integer> getCountFunc() {
        return countFunc;
    }

    public Function<ResourceTransferMq, ResourceTransferQty> getTransferFunc() {
        return transferFunc;
    }
}
