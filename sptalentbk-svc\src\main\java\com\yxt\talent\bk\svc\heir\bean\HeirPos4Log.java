package com.yxt.talent.bk.svc.heir.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * HeirPos4Log
 *
 * <AUTHOR> geyan
 * @Date 19/3/24 10:00 am
 */
@Data
public class HeirPos4Log {
    private String id;
    private String posName;
    @Schema(description = "空表示没有上级")
    private String parentPosId;
    @Schema(description = "0:自动规则，1:手动规则")
    private Integer riskRuleType;
    @Schema(description = "风险规则id")
    private Long riskLevelId;
    @Schema(description = "岗位标杆userIds")
    private List<String> benchmarkUserIds;

    @AuditLogField(name = "上级岗位", orderIndex = 0)
    private String parentPosName;
    @Schema(description = "目标继任数量,0:未设置")
    @AuditLogField(name = "继任目标人数", orderIndex = 1)
    private Integer heirTargetQty;
    @AuditLogField(name = "风险判断规则", orderIndex = 2)
    private String riskRuleTypeName;
    @AuditLogField(name = "继任风险", orderIndex = 3)
    private String riskLevelName;
}
