package com.yxt.talent.bk.svc.heir.component;

import com.alibaba.fastjson.JSON;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.heir.HeirOrgLevelConfigService;
import com.yxt.talent.bk.svc.heir.bean.HeirBmUserEdit4Log;
import com.yxt.talent.bk.svc.heir.bean.HeirBmUserEditParam4Log;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareRuleEdit4Log;
import com.yxt.talent.bk.svc.heir.bean.req.PrepareCfgReq;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * HeirBmUserEditLogProvider
 *
 * <AUTHOR> geyan
 * @Date 21/3/24 10:09 am
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirPrepareRuleEditLogProvider implements AuditLogDataProvider<PrepareCfgReq, HeirPrepareRuleEdit4Log> {
    private final HeirPosTypeSelector heirPosTypeSelector;
    private final HeirOrgLevelConfigService heirOrgLevelConfigService;
    @Override
    public HeirPrepareRuleEdit4Log before(PrepareCfgReq param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public HeirPrepareRuleEdit4Log after(PrepareCfgReq param, AuditLogBasicBean logBasic) {
        String orgId = logBasic.getOrgId();
        HeirPrepareRuleEdit4Log ruleEdit4Log = new HeirPrepareRuleEdit4Log();
        ruleEdit4Log.setPrepareName(Optional.ofNullable(heirOrgLevelConfigService.getHeirPrepareData(orgId, param.getPrepareLevelId()))
                .map(item -> item.queryLevelName(null)).orElse(null));
        ruleEdit4Log.setRuleJson(JSON.toJSONString(param.getLabelRuleGroupBean()));
        return ruleEdit4Log;
    }

    @Override
    public Pair<String, String> entityInfo(PrepareCfgReq param,
                                           HeirPrepareRuleEdit4Log beforeObj,
                                           HeirPrepareRuleEdit4Log afterObj,
                                           AuditLogBasicBean logBasic) {
        String posName = heirPosTypeSelector.getPosNameById(logBasic.getOrgId(), param.getPosId()).getValue();
        String prepareName = CommonUtils.firstNotEmpty(afterObj, beforeObj, HeirPrepareRuleEdit4Log::getPrepareName);
        return Pair.of(param.getPosId(), String.format(logBasic.getLogPoint().getPointName(), posName, prepareName));
    }
}
