package com.yxt.talent.bk.core.pool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.bk.core.pool.bean.Pool4AuditLog;
import com.yxt.talent.bk.core.pool.entity.Pool;
import com.yxt.talentbkfacade.bean.StrIdNameBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/09/08
 */
@Mapper
public interface PoolMapper extends BaseMapper<Pool> {

    IPage<Pool> findPageBy(IPage<Pool> page, @Param("catalogId") String catalogId,
                                         @Param("orgId") String orgId, @Param("keyword") String keyword,
                                         @Param("orderBy") String orderBy, @Param("direction") String direction ,
                                         @Param("poolIds") Collection<String> poolIds );

    List<Pool> listByIds(@Param("orgId") String orgId, @Param("poolIds") Collection<String> poolIds);

    /**
     * 更新饱和度
     * @param orgId
     * @param id
     */
    void updateSaturabilityById(@Param("orgId") String orgId, @Param("id") String id);

    Pool4AuditLog getById4AuditLog(@Param("id") String id);

    @Select("select pool_name from bk_pool where id = #{id}")
    String getNameById(@Param("id") String id);

    List<String> resTransferPoolIds(@Param("orgId") String orgId, @Param("userId") String userId);

    int execResTransfer(@Param("orgId") String orgId,
                        @Param("createUserId") String createUserId,
                        @Param("newCreateUserId") String newCreateUserId,
                        @Param("optUserId") String optUserId);

    List<StrIdNameBean> queryPoolNameByIds(@Param("ids") Collection<String> ids);
}
