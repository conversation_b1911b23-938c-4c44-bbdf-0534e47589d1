package com.yxt.talent.bk.svc.pool;

import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.*;
import com.yxt.o2ofacade.bean.request.talent.SpProject4Req;
import com.yxt.o2ofacade.bean.response.ProjectBaseInfoResp;
import com.yxt.o2ofacade.bean.response.talent.SpProjectInfoResp;
import com.yxt.o2ofacade.bean.talent.GroupMember4Create;
import com.yxt.o2ofacade.service.ProjectFacade;
import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.common.utils.StringConcatBuilder;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.mq.RocketMqProducerRepository;
import com.yxt.talent.bk.core.pool.bean.BindTraining4Get;
import com.yxt.talent.bk.core.pool.bean.PoolTraining4Search;
import com.yxt.talent.bk.core.pool.bean.Training4Bind;
import com.yxt.talent.bk.core.pool.bean.TrainingBindResult;
import com.yxt.talent.bk.core.pool.entity.ProjectTargetMap;
import com.yxt.talent.bk.core.pool.mapper.ProjectTargetMapMapper;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.pool.bean.PoolBindProject4Log;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@AllArgsConstructor
public class PoolTrainingService {

    private final ProjectTargetMapMapper projectTargetMapMapper;
    private final ProjectFacade projectFacade;
    private final RocketMqProducerRepository rocketMqProducerRepository;
    private final PoolRepository poolRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;

    /**
     * 用于sptalent删除学员时，检查是否在盘点中使用了该学员所在的该培训项目
     *
     * @param orgId
     * @param trainingId
     * @return
     */
    public boolean hasTrainingUsedInPool(String orgId, String trainingId) {
        return projectTargetMapMapper.countByOrgIdAndTrainingId(orgId, trainingId) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public TrainingBindResult bindTraining(String poolId, Training4Bind training4Bind, UserCacheDetail userDetail) {
        List<Long> trainingIds = TalentbkUtil.str2Long(training4Bind.getTrainingIds());
        List<SpProjectInfoResp> spProjectInfoResps = projectFacade.listSpProjects(userDetail.getOrgId(), trainingIds);

        TrainingBindResult bindResult = new TrainingBindResult();

        // 由于删除的项目o2o不会返回，这里将这些项目直接加入到返回结果中
        if (CollectionUtils.isEmpty(spProjectInfoResps)) {
            bindResult.getDeletedProjectIds().addAll(training4Bind.getTrainingIds());
            return bindResult;
        }

        for (Long trainingId : trainingIds) {
            if (spProjectInfoResps.stream().noneMatch(p -> p.getId().equals(trainingId))) {
                bindResult.getDeletedProjectIds().add(String.valueOf(trainingId));
            }
        }

        List<Long> validTrainingIds = getValidTrainingIds(training4Bind, spProjectInfoResps, bindResult);

        List<Long> nonExistingTrainingIds = checkTrainingBinding(trainingIds, spProjectInfoResps);

        List<ProjectTargetMap> bindedList =
            createProjectTargetMaps(poolId, validTrainingIds, nonExistingTrainingIds, userDetail);

        List<Long> finalExecTrainingIds = BeanCopierUtil.convertList(bindedList,
                bindMap -> NumberUtil.parseLong(bindMap.getTargetId()));
        batchInsertProjectTargetMaps(bindedList);

        executeAfterCommit(userDetail, validTrainingIds, nonExistingTrainingIds, training4Bind.getUserIds());

        StringConcatBuilder pjNames = new StringConcatBuilder(StringPool.COMMA);
        for (SpProjectInfoResp spPjInfo : spProjectInfoResps) {
            if (finalExecTrainingIds.contains(spPjInfo.getId())) {
                pjNames.append(spPjInfo.getName()).appendConcat();
            }
        }
        PoolBindProject4Log auditLog = new PoolBindProject4Log();
        auditLog.setPoolId(poolId);
        auditLog.setProjectNames(pjNames.output());
        if (StringUtils.isNotEmpty(auditLog.getProjectNames())) {
            auditLog.setPoolName(poolRepository.getNameById(poolId));
            auditLog.setUserNames(udpLiteUserRepository.userNames4Log(userDetail.getOrgId(), training4Bind.getUserIds()));
            AuditLogHooker.setLogAfterData(auditLog);
        }
        return bindResult;
    }

    /**
     * 检查训练项目是否存在
     */
    private void validateTrainingExistence(List<SpProjectInfoResp> spProjectInfoResps) {
        Validate.isNotEmpty(spProjectInfoResps, BkApiErrorKeys.TALENT_BK_PROJECT_BIND_TRAINING_NOT_EXIST);
    }

    /**
     * 获取有效的训练项目列表并进行分类
     */
    private List<Long> getValidTrainingIds(
            Training4Bind training4Bind, List<SpProjectInfoResp> spProjectInfoResps, TrainingBindResult bindResult) {
        List<Long> validTrainingIds = new ArrayList<>();
        for (SpProjectInfoResp spProjectInfoResp : spProjectInfoResps) {
            String trainingId = String.valueOf(spProjectInfoResp.getId());

            if (isTrainingIdSelected(training4Bind, trainingId)) {
                int projectStatus = spProjectInfoResp.getStatus();

                // 根据项目状态进行绑定结果的分类
                categorizeProjectStatus(bindResult, trainingId, projectStatus);

                // 将有效的训练项目加入列表
                if (isValidProjectStatus(projectStatus)) {
                    validTrainingIds.add(spProjectInfoResp.getId());
                }
            }
        }

        return validTrainingIds;
    }

    /**
     * 检查训练项目是否已经绑定，并返回不存在的训练项目列表
     */
    private List<Long> checkTrainingBinding(List<Long> trainingIds, List<SpProjectInfoResp> spProjectInfoResps) {
        List<Long> nonExistingTrainingIds = new ArrayList<>(trainingIds);

        for (SpProjectInfoResp spProjectInfoResp : spProjectInfoResps) {
            nonExistingTrainingIds.remove(spProjectInfoResp.getId());
        }

        if (!nonExistingTrainingIds.isEmpty()) {
            log.info("LOG64970:trainingIds:{} is not exist", nonExistingTrainingIds);
        }

        return nonExistingTrainingIds;
    }

    /**
     * 创建项目与训练项目的映射关系列表
     */
    private List<ProjectTargetMap> createProjectTargetMaps(String poolId, List<Long> validTrainingIds,
            List<Long> nonExistingTrainingIds, UserCacheDetail userDetail) {
        List<String> existTrainingIds =
            projectTargetMapMapper.selectTargetIdByOrgIdAndProjectId(userDetail.getOrgId(), poolId);
        List<ProjectTargetMap> bindedList = new ArrayList<>();

        for (Long trainingId : validTrainingIds) {
            if (existTrainingIds.contains(String.valueOf(trainingId))) {
                continue;
            }

            if (nonExistingTrainingIds.contains(trainingId)) {
                continue;
            }

            ProjectTargetMap projectTraining =
                createProjectTargetMap(userDetail.getOrgId(), poolId, trainingId, userDetail);
            bindedList.add(projectTraining);
        }

        return bindedList;
    }

    /**
     * 批量插入项目与训练项目的映射关系
     */
    private void batchInsertProjectTargetMaps(List<ProjectTargetMap> bindedList) {
        if (!bindedList.isEmpty()) {
            projectTargetMapMapper.batchInsert(bindedList);
        }
    }

    /**
     * 在事务提交后执行操作
     */
    private void executeAfterCommit(UserCacheDetail userDetail, List<Long> validTrainingIds,
            List<Long> nonExistingTrainingIds, List<String> userIds) {
        TalentbkUtil.execAfterCommitIfHas(() -> {
            for (Long trainingId : validTrainingIds) {
                if (nonExistingTrainingIds.contains(trainingId)) {
                    log.debug("LOG50050:");
                    continue;
                }

                ProjectBaseInfoResp projectBaseInfo =
                    projectFacade.getProjectBaseInfo(userDetail.getOrgId(), trainingId);
                if (projectBaseInfo == null) {
                    log.warn("LOG64950:projectBaseInfo is null, trainingId:{}", trainingId);
                    continue;
                }

                boolean isCycle = Objects.equals(projectBaseInfo.getTimeType(), 1);
                List<List<String>> userLists = TalentbkUtil.splitList(userIds, 50);
                for (List<String> userList : userLists) {
                    addGroupMember(userDetail.getOrgId(), trainingId, isCycle, userList, userDetail.getUserId(),
                        userDetail.getSource());
                }
            }
        });
    }

    /**
     * 创建项目与训练项目的映射关系
     */
    private ProjectTargetMap createProjectTargetMap(String orgId, String poolId, Long trainingId,
        UserCacheDetail userDetail) {
        ProjectTargetMap projectTraining = new ProjectTargetMap();
        projectTraining.setId(ApiUtil.getUuid());
        projectTraining.setOrgId(orgId);
        projectTraining.setProjectId(poolId);
        // 目前之后人才池
        projectTraining.setProjectType(0);
        projectTraining.setTargetId(String.valueOf(trainingId));
        // 培训项目
        projectTraining.setTargetType(3);
        TalentbkUtil.setCreateInfo(userDetail.getUserId(), projectTraining);
        return projectTraining;
    }

    /**
     * 判断训练项目是否被选择
     */
    private boolean isTrainingIdSelected(Training4Bind training4Bind, String trainingId) {
        return training4Bind.getTrainingIds().contains(trainingId);
    }

    /**
     * 根据项目状态进行分类
     */
    private void categorizeProjectStatus(TrainingBindResult bindResult, String trainingId, int projectStatus) {
        // 3：已结束；4：已归档；5：已删除
        if (projectStatus == 3) {
            bindResult.getEndProjectIds().add(trainingId);
        } else if (projectStatus == 4) {
            bindResult.getFiledProjectIds().add(trainingId);
        } else if (projectStatus == 5) {
            bindResult.getDeletedProjectIds().add(trainingId);
        }
    }

    /**
     * 判断项目状态是否有效
     */
    private boolean isValidProjectStatus(int projectStatus) {
        return projectStatus != 3 && projectStatus != 4 && projectStatus != 5;
    }

    public void addGroupMember(String orgId, Long trainingId, boolean isCycle, List<String> userIds, String optUserId,
        String source) {
        if (trainingId == null) {
            return;
        }
        // 人员加人到培训项目
        GroupMember4Create groupMember4Create = new GroupMember4Create();
        groupMember4Create.setOrgId(orgId);
        groupMember4Create.setProjectId(trainingId);
        Set<String> set = new HashSet<>(userIds);
        groupMember4Create.setUserIds(set);
        groupMember4Create.setOptUserId(optUserId);
        groupMember4Create.setSource(source);
        groupMember4Create.setFormal(1);
        groupMember4Create.setRole(1);
        if (isCycle) {
            groupMember4Create.setStartTime(LocalDateTime.now());
        }
        try {
            rocketMqProducerRepository.send(TalentBkRocketMqConstant.TOPIC_S_TALENT_ADD_GROUP_MEMBER,
                BeanHelper.bean2Json(groupMember4Create, JsonInclude.Include.ALWAYS));
        } catch (Exception e) {
            log.error("LOG64930:", e);
        }
    }

    public PagingList<BindTraining4Get> listBindTraining(String poolId, PoolTraining4Search criteria, String orgId) {
        List<ProjectTargetMap> bindedList = projectTargetMapMapper.selectByOrgIdAndPoolId(orgId, poolId);
        if (CollectionUtils.isEmpty(bindedList)) {
            return new PagingList<>();
        }
        List<String> trainingIds = StreamUtil.mapList(bindedList, ProjectTargetMap::getTargetId);
        List<Long> longTrainingIds = TalentbkUtil.str2Long(trainingIds);
        SpProject4Req spProject4Req = new SpProject4Req();
        spProject4Req.setOrgId(orgId);
        spProject4Req.setProjectIds(longTrainingIds);
        spProject4Req.setKeyword(SqlUtils.escapeLike(criteria.getTrainingName()));
        spProject4Req.setCurrent(Math.toIntExact(criteria.getCurPage()));
        spProject4Req.setLimit(Math.toIntExact(criteria.getLimit()));
        PagingList<SpProjectInfoResp> spProjectInfoResps = projectFacade.pageSpProjects(spProject4Req);
        return new PagingList<>(
            BeanCopierUtil.convertList(spProjectInfoResps.getDatas(), SpProjectInfoResp.class, BindTraining4Get.class),
            spProjectInfoResps.getPaging());
    }

}
