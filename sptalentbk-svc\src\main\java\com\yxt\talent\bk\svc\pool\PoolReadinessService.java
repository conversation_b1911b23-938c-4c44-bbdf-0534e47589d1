package com.yxt.talent.bk.svc.pool;

import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.service.ILock;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.talent.bk.common.constants.PoolConstants;
import com.yxt.talent.bk.common.constants.PoolReadinessConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.core.pool.entity.PoolReadiness;
import com.yxt.talent.bk.core.pool.repo.PoolReadinessRepository;
import com.yxt.talent.bk.core.pool.repo.PoolUserRepository;
import com.yxt.talent.bk.svc.pool.bean.readiness.PoolReadiness4List;
import com.yxt.talent.bk.svc.pool.bean.readiness.PoolReadiness4SaveBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class PoolReadinessService {
    private final PoolReadinessRepository poolReadinessRepository;
    private final PoolUserRepository poolUserRepository;
    private final ILock lockService;

    private static final int MAX_LEASE_TIME = 100;

    /**
     * 查询指定机构数据
     * @param orgId
     * @return
     */
    public List<PoolReadiness4List> findByOrgId(String orgId,String userId){
        List<PoolReadiness> list = poolReadinessRepository.list(orgId);
        if(CollectionUtils.isNotEmpty(list)){
            return BeanCopierUtil.convertList(list,PoolReadiness.class,PoolReadiness4List.class);
        }

        // 不存在 ，初始化数据
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_POOL_READINESS_INIT, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)){
            try{
                list = initData(orgId,userId);
            }catch (Exception e) {
                log.error(this.getClass().getSimpleName()+".list Exception", e);
                throw e;
            }  finally {
                lockService.unLock(lockKey);
            }
        }else{
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }

        return BeanCopierUtil.convertList(list,PoolReadiness.class,PoolReadiness4List.class);
    }

    /**
     * 初始化数据
     * @param orgId
     * @param userId
     * @return
     */
    private List<PoolReadiness> initData(String orgId,String userId){

        List<PoolReadiness> list = new ArrayList<>();
        int i = 0;
        for(String name : PoolReadinessConstants.DEF_READINESS){
            PoolReadiness bean = new PoolReadiness();
            bean.setOrgId(orgId);
            bean.setReadinessName(name);
            bean.setOrderIndex(i++);
            EntityUtil.setCreateInfo(userId, bean);
            list.add(bean);
        }
        poolReadinessRepository.saveBatch(list);

        return list;
    }


    /**
     * 创建
     * @param orgId
     * @param userId
     */
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void create(String orgId, String userId, PoolReadiness4SaveBean data){

        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_POOL_READINESS_CREATE, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)){
            try{
                // order_index 重排
                List<PoolReadiness> list = poolReadinessRepository.list(orgId);
                // 重名校验
                for(PoolReadiness pr: list){
                    if(pr.getReadinessName().equals(data.getReadinessName())){
                        throw new ApiException(PoolConstants.VERIFY_READINESS_SAVE_NAME_EXIST);
                    }
                }

                if(list.size() >= 5){
                    throw new ApiException(PoolConstants.VERIFY_READINESS_SAVE_ROW_OVERFLOW);
                }

                int i = 0;
                for(PoolReadiness pr : list){
                    pr.setOrderIndex(i++);
                }
                // 创建新数据
                PoolReadiness newBean = new PoolReadiness();
                newBean.setOrgId(orgId);
                newBean.setReadinessName(data.getReadinessName());
                newBean.setOrderIndex(i);
                EntityUtil.setCreateInfo(userId, newBean);
                list.add(newBean);
                poolReadinessRepository.saveOrUpdateBatch(list);
            }catch (Exception e) {
                log.error(this.getClass().getSimpleName()+ ".create Exception", e);
                throw e;
            }  finally {
                lockService.unLock(lockKey);
            }
        }else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
    }

    /**
     * 编辑
     * @param orgId
     * @param userId
     * @param data
     */
    public void update(String orgId, String userId, PoolReadiness4SaveBean data){
        if(data == null || StringUtils.isBlank(data.getId())){
            throw new ApiException("apis.talentbk.readiness.id.notBank");
        }

        // 重名校验
        List<PoolReadiness> poolReadinessList = poolReadinessRepository.findByReadinessName(orgId, data.getReadinessName());
        if (CollectionUtils.isNotEmpty(poolReadinessList)
                && !poolReadinessList.get(0).getId().equals(data.getId()) ) {
            throw new ApiException(PoolConstants.VERIFY_READINESS_SAVE_NAME_EXIST);
        }

        PoolReadiness poolReadiness = poolReadinessRepository.getById(orgId, data.getId());
        poolReadiness.setReadinessName(data.getReadinessName());
        EntityUtil.setUpdatedInfo(userId,poolReadiness);
        poolReadinessRepository.updateById(poolReadiness);
    }

    /**
     * 删除
     * @param orgId
     * @param id
     */
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void delete(String orgId,String id){

        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_POOL_READINESS_DELETE, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)){
            try {
                // 校验是否多于1条
                long count = poolReadinessRepository.countByOrgId(orgId);
                if(count <= 1){
                    throw new ApiException(PoolConstants.VERIFY_READINESS_SAVE_ROW_OVERFLOW);
                }
                // 校验是否已被使用
                long readinessCount = poolUserRepository.countByReadinessId(orgId, id);
                if(readinessCount >= 1){
                    throw new ApiException(PoolConstants.VERIFY_READINESS_DELETE_REFERENCE);
                }

                // 1. 删除指定id数据
                poolReadinessRepository.removeById(orgId,id);
                // 2.order_index 重排
                List<PoolReadiness> list = poolReadinessRepository.list(orgId);
                int i = 0;
                for(PoolReadiness pr : list){
                    pr.setOrderIndex(i++);
                }
                poolReadinessRepository.updateBatchById(list);
            }catch (Exception e) {
                log.error(this.getClass().getSimpleName()+ ".delete Exception", e);
                throw e;
            }  finally {
                lockService.unLock(lockKey);
            }
        }else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
    }

}
