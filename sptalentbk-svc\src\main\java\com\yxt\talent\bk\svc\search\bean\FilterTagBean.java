package com.yxt.talent.bk.svc.search.bean;

import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 标签名称
 */
@Data
@Schema(name = "主标签名称")
public class FilterTagBean {

    @Schema(description = "标签id")
    private String tagId;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "标签可以使用且或情况，0 只能使用 或，1 且 或 都能使用")
    private Integer logic = 0;

    @Schema(description = "标签类型, 0-普通标签,1-分层标签")
    private Integer type = 0;

    /**
     * 0-单选，1-多选
     */
    @Schema(description = "选择模式，0-单选，1-多选")
    private Integer valueChooseModel;

    @Schema(description = "分层标签信息")
    private List<TagValueBean> tagValueBeanList;

    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;
}
