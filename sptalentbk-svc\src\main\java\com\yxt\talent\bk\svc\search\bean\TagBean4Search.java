package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "标签搜索")
public class TagBean4Search {

    @Schema(description = "标签名称/标签值 支持模糊搜索")
    private String keyWord;

    @Schema(description = "分类id 为空查询所有分类的标签")
    private String catalogId;

    @Schema(description = "更新类型 默认-1  0-手动 1-自动 -1全部")
    private int updateType = -1;

    @Schema(description = "标签来源 0-内置,1-自建,2-固定 空：全部")
    private List<Integer> sourceType;

    @Schema(description = "是否查询标签绑定人员数量 0-是,1-否")
    private int tagBindUser;

    @Schema(description = "启用类型 默认-1  0-禁用 1-启用 -1全部")
    private int tagEnable = -1;

    @Schema(description = "维度id")
    private String dimensionId;
}
