package com.yxt.talent.bk.svc.profile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.I18nComponent;
import com.yxt.idworker.YxtIdWorker;
import com.yxt.spmodel.facade.bean.label.LabelVO;
import com.yxt.spmodel.facade.bean.label.LabelValueVO;
import com.yxt.spmodel.facade.bean.label.UserLabelParam;
import com.yxt.spmodel.facade.bean.label.UserLabelVO;
import com.yxt.spmodel.facade.bean.sql.SqlParam;
import com.yxt.spmodel.facade.service.SpmodelSqlService;
import com.yxt.spsdfacade.bean.spsd.ModelDto;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.bk.common.constants.ConstantPool;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.heir.bean.HeirUserPosBriefBean;
import com.yxt.talent.bk.core.spmodel.entity.*;
import com.yxt.talent.bk.core.spmodel.mapper.*;
import com.yxt.talent.bk.core.udp.bean.QueryUserBean;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.core.usergroup.bean.*;
import com.yxt.talent.bk.core.usergroup.entity.SearchSetting;
import com.yxt.talent.bk.core.usergroup.repo.SearchSettingRepository;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.profile.bean.*;
import com.yxt.talent.bk.svc.profile.enums.PerfType;
import com.yxt.talent.bk.svc.rpc.SdRpc;
import com.yxt.talent.bk.svc.usergroup.rpc.TagSearchRpc;
import com.yxt.talentbkfacade.bean.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.jetbrains.annotations.NotNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class UserProfileService {
    private final SearchSettingRepository searchSettingRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final SpmodelSqlService spmodelSqlService;
    private final TagSearchRpc tagSearchRpc;
    private final HeirPosService heirPosService;
    private final Executor wafTaskExecutor;
    private final DwdExamHistoryMapper dwdExamHistoryMapper;
    private final DwdTrainingHistoryMapper dwdTrainingHistoryMapper;
    private final DwdKngStudyHistoryMapper dwdKngStudyHistoryMapper;
    private final DwdSspAccSummaryMapper dwdSspAccSummaryMapper;
    private final DwdCerIssueHistoryMapper dwdCerIssueHistoryMapper;
    private final DwdUserJqTaskRvMapper dwdUserJqTaskRvMapper;
    private final DwdUserEvalSkillRtMapper dwdUserEvalSkillRtMapper;
    private final SdRpc sdRpc;
    private final I18nComponent i18nComponent;
    private static final  List<String> CHARACTERISTIC_TAG = List.of("zyqdl", "xgtd", "rzfx");

    /**
     * @param
     * @return
     * @description: 创建配置
     */
    public void createUserSetting(String orgId, String userId, String setting) {
        SearchSetting searchSetting = searchSettingRepository.findByOrgId(orgId);
        if (searchSetting == null) {
            searchSetting = new SearchSetting();
            searchSetting.setId(YxtIdWorker.getId());
            searchSetting.setOrgId(orgId);
            searchSetting.setSetting(setting);
            TalentbkUtil.setCreateInfo(userId, searchSetting);
            searchSettingRepository.save(searchSetting);
        } else {
            searchSetting.setSetting(setting);
            TalentbkUtil.setUpdatedInfo(userId, searchSetting);
            searchSettingRepository.updateById(searchSetting);
        }
    }

    /**
     * @param
     * @return
     * @description: 创建配置
     */
    public UserCompareSettingBean getUserSetting(String orgId) {
        UserCompareSettingBean userCompareSettingBean = new UserCompareSettingBean();
        SearchSetting setting = searchSettingRepository.findByOrgId(orgId);
        if (setting != null){
            userCompareSettingBean.setSetting(setting.getSetting());
        }
        return userCompareSettingBean;
    }

    public UserCompareDetail getCompareDetail(String orgId, UserCompareBean userCompareBean) {
        String userId = userCompareBean.getUserId();

        UdpLiteUser udpLiteUser = udpLiteUserRepository.findByOrgIdAndUserId(orgId, userId);
        UserCompareDetail userCompareDetail = new UserCompareDetail();
        userCompareDetail.setId(userId);
        Validate.isNotNull(udpLiteUser, "apis.talentbk.user.not.exist");
        userCompareDetail.setFullname(udpLiteUser.getFullname());

        // 异步请求
        CompletableFuture<List<HeirUserPosBriefBean>> heirFuture = CompletableFuture.supplyAsync(
                () -> heirPosService.userPosList(orgId, userCompareBean.getUserId()),
                    wafTaskExecutor);

        try {
            // 人才继任
            List<HeirUserPosBriefBean> list = heirFuture.get();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)){
                userCompareDetail.setDeptList(list.stream().filter(item -> item.getPosType() != HeirPosTypeEnum.POSITION.getType())
                    .collect(Collectors.toList()));
                userCompareDetail.setPositionList(list.stream().filter(item -> item.getPosType() == HeirPosTypeEnum.POSITION.getType())
                    .collect(Collectors.toList()));
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e){
            log.error("请求盘点，继任错误，", e);
        }

        if (StringUtils.isBlank(udpLiteUser.getId())){
            return userCompareDetail;
        }

        UserBasic4Get userBasic4Get = getUserBasic(orgId, userId);
        if (userBasic4Get == null){
            return userCompareDetail;
        }

        BeanHelper.copyProperties(userBasic4Get, userCompareDetail);
        userCompareDetail.setDeptName(userBasic4Get.getThirdDeptName());
        userCompareDetail.setPositionName(userBasic4Get.getThirdPositionName());
        userCompareDetail.setGradeName(userBasic4Get.getThirdJobgradeName());
        userCompareDetail.setImgUrl(userBasic4Get.getAvatarUrl());

        List<TrainingHistory4Get> trainingHistory4Gets = new ArrayList<>();
        if (StringUtils.isNotEmpty(udpLiteUser.getId())){
            if (userCompareBean.getTrainingTime() == null) {
                trainingHistory4Gets = getTrainingHistory(orgId, userId);
            }else {
                trainingHistory4Gets = getTrainingHistoryByYear(orgId, udpLiteUser.getId(), userCompareBean.getTrainingTime());
            }
        }
        List<UserCompareDetailTraining> trainings =
            BeanCopierUtil.convertList(trainingHistory4Gets, TrainingHistory4Get.class,
                UserCompareDetailTraining.class);
        userCompareDetail.setTrainings(trainings);

        List<RewardPunishmentHistory4Get> rewardPunishmentHistory4Gets = new ArrayList<>();
        if (udpLiteUser.getId() != null) {
            if (userCompareBean.getRewardTime() == null) {
                rewardPunishmentHistory4Gets = getRewardPunishmentHistory(orgId, userId);
            }else {
                rewardPunishmentHistory4Gets = getRewardPunishmentHistory(orgId, udpLiteUser.getId(), userCompareBean.getRewardTime());
            }
        }

        List<UserCompareDetailReward> rewards =
            BeanCopierUtil.convertList(rewardPunishmentHistory4Gets, RewardPunishmentHistory4Get.class,
                UserCompareDetailReward.class);
        userCompareDetail.setRewards(rewards);

        // 前 3 年
        int lastThreeYear = LocalDate.now().minusYears(userCompareBean.getPerformanceTime()).getYear();
        List<UserPerf4Get> userPerfs = getUserPerfByYear(orgId, udpLiteUser.getId(), lastThreeYear);
        if (CollectionUtils.isNotEmpty(userPerfs)){
            userCompareDetail.setPerfs(BeanCopierUtil.convertList(userPerfs, UserPerf4Get.class,
                UserComparePerf4Get.class));
        }



        return userCompareDetail;
    }

    /**
     * 获取用户培训经历, 按照时间搜索
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<TrainingHistory4Get> getTrainingHistoryByYear(String orgId, String userId, int year) {
        String sql = """
                SELECT * FROM dwd_training_history
                WHERE org_id = '%s'
                and user_id = '%s'
                and start_time >= DATE_SUB(CURDATE(), INTERVAL %d YEAR)
                order by start_time desc
                """.formatted(orgId, userId, year);

        List<JSONObject> results = new ArrayList<>();
        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(sql);
        try {
            results = spmodelSqlService.sql(param);
        } catch (Exception e) {
            log.error("LOG50010:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        return IArrayUtils.jsonToBeanOfList(results, TrainingHistory4Get.class);
    }

    /**
     * 获取用户奖惩信息历史, 通过时间查询
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<RewardPunishmentHistory4Get> getRewardPunishmentHistory(String orgId, String userId, Integer year) {
        String sql = "SELECT * FROM dwd_reward_punishment_history WHERE org_id = '"+ orgId +"'"
                     + " and user_id = '"+ userId +"'"
                     + " and acq_time >= DATE_SUB(CURDATE(), INTERVAL "+ year +" YEAR) order by acq_time desc;";

        List<JSONObject> results = new ArrayList<>();
        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(sql);
        try {
            results = spmodelSqlService.sql(param);
        } catch (Exception e) {
            log.error("LOG50000:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        return IArrayUtils.jsonToBeanOfList(results, RewardPunishmentHistory4Get.class);
    }

    /**
     * 获取用户基本信息
     *
     * @param orgId
     * @param userId
     * @return
     */
    @Nullable
    public UserBasic4Get getUserBasic(String orgId, String userId) {
        UdpLiteUser udpLiteUser = getUdpUser(orgId, userId);

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ConstantPool.C_USER_ID, userId);
        queryParams.put(ConstantPool.C_ORG_ID, orgId);
        List<UserBasic4Get> results = doQueryListFromModel(orgId, UserBasic4Get.class, queryParams);
        if (CollectionUtils.isEmpty(results)) {
            results.add(new UserBasic4Get());
        }

        UserBasic4Get user = results.get(0);
        user.setUserId(userId);
        user.setUsername(udpLiteUser.getUsername());
        user.setFullname(udpLiteUser.getFullname());
        user.setAvatarUrl(udpLiteUser.getImgUrl());
        user.setThirdPositionName(udpLiteUser.getPositionName());
        user.setThirdDeptNamePath(udpLiteUser.getDeptName());
        user.setEducation(user.getEducation());
        //if (StringUtils.isNotBlank(user.getEducation())) {
        //    List<DwdEducation> dwdEducations = doQueryListFromModel(orgId, DwdEducation.class,
        //            Maps.of(ConstantPool.C_ORG_ID, orgId, "third_edu_id", user.getEducation()));
        //    Map<String, String> eduIdMap = dwdEducations.stream()
        //            .filter(e -> StringUtils.isNotBlank(e.getThirdEduId()) && StringUtils.isNotBlank(e.getThirdEduName()))
        //            .collect(Collectors.toMap(DwdEducation::getThirdEduId, DwdEducation::getThirdEduName, (k1, k2) -> k1));
        //    user.setEducation(eduIdMap.get(user.getEducation()));
        //}
        return user;
    }

    /**
     * 获取用户任职履历
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<CareerHistory4Get> getCareerHistory(String orgId, String userId) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ConstantPool.C_USER_ID, userId);
        queryParams.put(ConstantPool.C_ORG_ID, orgId);
        queryParams.put(ConstantPool.F_DELETED, 0);
        List<CareerHistory4Get> results = doQueryListFromModel(orgId, CareerHistory4Get.class, queryParams);
        results.forEach(e -> e.setUserId(userId));
        return results;
    }

    /**
     * 获取用户外部工作经历
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<WorkHistory4Get> getWorkHistory(String orgId, String userId) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ConstantPool.C_USER_ID, userId);
        queryParams.put(ConstantPool.C_ORG_ID, orgId);
        List<WorkHistory4Get> results = doQueryListFromModel(orgId, WorkHistory4Get.class, queryParams);
        results.forEach(e -> e.setUserId(userId));
        return results;
    }

    /**
     * 获取用户培训经历
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<TrainingHistory4Get> getTrainingHistory(String orgId, String userId) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ConstantPool.C_USER_ID, userId);
        queryParams.put(ConstantPool.C_ORG_ID, orgId);
        List<TrainingHistory4Get> results = doQueryListFromModel(orgId, TrainingHistory4Get.class, queryParams);
        results.forEach(e -> e.setUserId(userId));
        return results;
    }

    /**
     * 分页查询获取用户培训经历
     *
     * @param orgId
     * @param userId
     * @param page
     * @return
     */
    public PagingList<TrainingHistory4Get> getTrainingHistoryPage(String orgId, String userId, Paging page) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ConstantPool.C_USER_ID, userId);
        queryParams.put(ConstantPool.C_ORG_ID, orgId);
        PagingList<TrainingHistory4Get> results =
                doQueryPageFromModel(orgId, TrainingHistory4Get.class, queryParams, page);
        if (CollectionUtils.isNotEmpty(results.getDatas())) {
            results.getDatas().forEach(e -> e.setUserId(userId));
        }
        return results;
    }

    /**
     * 获取用户奖惩信息历史
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<RewardPunishmentHistory4Get> getRewardPunishmentHistory(String orgId, String userId) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ConstantPool.C_USER_ID, userId);
        queryParams.put(ConstantPool.C_ORG_ID, orgId);
        List<RewardPunishmentHistory4Get> results =
                doQueryListFromModel(orgId, RewardPunishmentHistory4Get.class, queryParams);
        results.forEach(e -> e.setUserId(userId));
        return results;
    }

    /**
     * 获取用户奖惩信息历史分页
     *
     * @param orgId
     * @param userId
     * @return
     */
    public PagingList<RewardPunishmentHistory4Get> getRewardPunishmentHistoryPage(String orgId, String userId, Paging page) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ConstantPool.C_USER_ID, userId);
        queryParams.put(ConstantPool.C_ORG_ID, orgId);
        queryParams.put(ConstantPool.F_DELETED, 0);
        PagingList<RewardPunishmentHistory4Get> results =
            doQueryPageFromModel(orgId, RewardPunishmentHistory4Get.class, queryParams, page);
        if (CollectionUtils.isNotEmpty(results.getDatas())) {
            results.getDatas().forEach(e -> e.setUserId(userId));
        }
        return results;
    }

    public List<UserSkill4Get> getUserSkillInfo(String orgId, String userId, String modelId) {
        try {
            List<DwdUserEvalSkillRt> dwdUserEvalSkillRts = dwdUserEvalSkillRtMapper.queryByUserIdAndModelId(orgId, userId, modelId);
            log.info("getUserSkillInfo orgId={}, userId={}, modelId={} ,dwdUserEvalSkillRts={}", orgId, userId, modelId, BeanHelper.bean2Json(dwdUserEvalSkillRts, JsonInclude.Include.NON_NULL));
            List<UserSkill4Get> userSkills = BeanCopierUtil.convertList(dwdUserEvalSkillRts, DwdUserEvalSkillRt.class, UserSkill4Get.class);
            if (CollectionUtils.isNotEmpty(userSkills) && StringUtils.isNotEmpty(modelId)) {
                //按设置能力模型设置顺序返回
                for (int i = 0; i < userSkills.size(); i++) {
                    userSkills.get(i).setOrderIndex(Short.MAX_VALUE + i);
                }
                Map<String, UserSkill4Get> userSkillMap = StreamUtil.list2map(userSkills, UserSkill4Get::getSkillId);
                AtomicInteger orderIndex = new AtomicInteger();
                IArrayUtils.forEach(sdRpc.getIndicatorByModelId(orgId, modelId), indicatorDto -> {
                    Optional.ofNullable(userSkillMap.get(indicatorDto.getItemId()))
                            .ifPresent(userSkill -> userSkill.setOrderIndex(orderIndex.addAndGet(1)));
                });
                IArrayUtils.sortList(userSkills, UserSkill4Get::getOrderIndex);
            }
            return userSkills;
        }catch (Exception e){
            log.info("getUserSkillInfo error", e);
        }
        return new ArrayList<>();
    }

    public List<UserModel4Get> getUserModelList(String orgId, String userId) {
        try {
            List<String> modelIds = dwdUserEvalSkillRtMapper.queryModelIdByUserId(orgId, userId);
            log.info("getUserModelList orgId={}, userId={}, userId={}, modelIds={}", orgId, userId, userId, modelIds);
            if (CollectionUtils.isNotEmpty(modelIds)) {
                List<ModelDto> modelDetailList = sdRpc.getModelDetailList(orgId, modelIds);
                Map<String, ModelDto> modelMap = StreamUtil.list2map(modelDetailList,
                        ModelDto::getId);
                List<UserModel4Get> userModel4Gets = new ArrayList<>();
                modelIds.forEach(modelId -> {
                    UserModel4Get userModel4Get = new UserModel4Get();
                    if(null != modelMap.get(modelId)){
                        userModel4Get.setModelId(modelId);
                        ModelDto modelDto = modelMap.get(modelId);
                        userModel4Get.setModelName(modelDto.getTitle() + "(" + sdRpc.showModelVersion(modelDto.getVersionNumber()) + ")");
                    }
                    userModel4Gets.add(userModel4Get);
                });
                return userModel4Gets;
            }
        }catch (Exception e){
            log.info("getUserModelList error", e);
        }
        return new ArrayList<>();
    }

    public UserRvTask4Get getRvTaskInfo(String orgId, String userId){
        try {
            UserRvTask4Get userRvTask4Get = new UserRvTask4Get();
            UdpLiteUser udpLiteUser = getUdpUser(orgId, userId);
            List<DwdUserJqTaskRv> dwdUserJqTaskRvs = dwdUserJqTaskRvMapper.listByUserId(orgId,
                    udpLiteUser.getId(), udpLiteUser.getPositionId());
            log.info(
                    "getRvTaskInfo orgId={}, userId={}, userId={}, positionId={} ,dwdUserJqTaskRvs={}", orgId, userId,
                    udpLiteUser.getId(), udpLiteUser.getPositionId(),
                    BeanHelper.bean2Json(dwdUserJqTaskRvs, JsonInclude.Include.NON_NULL));
            List<UserRvTaskDimension> matchedDims = new ArrayList<>();
            List<UserRvTaskDimension> unmatchedDims = new ArrayList<>();
            dwdUserJqTaskRvs.forEach(dwdUserJqTaskRv -> {
                UserRvTaskDimension userRvTaskDimension = new UserRvTaskDimension();
                userRvTaskDimension.setDimName(dwdUserJqTaskRv.getJqTaskName());
                userRvTaskDimension.setJqTaskResult(dwdUserJqTaskRv.getJqTaskResult());
                userRvTaskDimension.setCreatTime(dwdUserJqTaskRv.getCreateTime());
                if(dwdUserJqTaskRv.getJqTaskResult() == 0){
                    unmatchedDims.add(userRvTaskDimension);
                }
                if(dwdUserJqTaskRv.getJqTaskResult() == 1){
                    matchedDims.add(userRvTaskDimension);
                }
            });
            userRvTask4Get.setMatchedDims(matchedDims);
            userRvTask4Get.setUnmatchedDims(unmatchedDims);
            return userRvTask4Get;
        }catch (Exception e){
            log.info("getRvTaskInfo error", e);
        }
        return new UserRvTask4Get();
    }

    public List<UserTag4Get> getUserCharacteristic(String orgId, String userId){
        List<UserTag4Get> userTag4Gets = new ArrayList<>();
        UserLabelParam userLabelParam = new UserLabelParam();
        userLabelParam.setOrgId(orgId);
        userLabelParam.setUserIds(Collections.singletonList(userId));
        List<UserLabelVO> userLabels;
        try {
            userLabels = tagSearchRpc.getUserLabelList(userLabelParam);
            log.info("getUserCharacteristic={}", BeanHelper.bean2Json(userLabels, JsonInclude.Include.ALWAYS));
        } catch (Exception e) {
            log.error("LOG50020:", e);
            return Collections.emptyList();
        }


        if(CollectionUtils.isNotEmpty(userLabels)) {
            Map<String, List<LabelVO>> groupedLabels = userLabels.stream().filter(e -> e.getUserId().equals(userId))
                    .flatMap(e -> e.getLabelList().stream().filter(labelVO -> StringUtils.isNotBlank(labelVO.getSystemCode()) && CHARACTERISTIC_TAG.contains(labelVO.getSystemCode())))
                    .collect(Collectors.groupingBy(LabelVO::getSystemCode));
            if (MapUtils.isNotEmpty(groupedLabels)) {
                groupedLabels.forEach((k, v) -> {
                    UserTag4Get userTag4Get = new UserTag4Get();
                    userTag4Get.setLabelCategorySystemCode(k);
                    List<TagValue4Get> tagValues = v.stream()
                            .flatMap(this::getTagValue4Gets)
                            .toList();
                    userTag4Get.setTagValues(tagValues);
                    userTag4Gets.add(userTag4Get);
                });
            }
        }
        return userTag4Gets;
    }

    public UserLearnData4Get getUserLearnData(String orgId, String userId){
        try {
            UserLearnData4Get userLearnData4Get = new UserLearnData4Get();
            DwdSspAccSummary dwdSspAccSummary = dwdSspAccSummaryMapper.queryByUserId(orgId, userId);
            log.info("getUserLearnData:orgId={}, userId={}, userId={}, dwdSspAccSummary={}", orgId, userId, userId, BeanHelper.bean2Json(dwdSspAccSummary, JsonInclude.Include.NON_NULL));
            if(null != dwdSspAccSummary){
                if(null != dwdSspAccSummary.getActualPoint()){
                    userLearnData4Get.setPoint(BigDecimal.valueOf(dwdSspAccSummary.getActualPoint()));
                }
                if(null != dwdSspAccSummary.getRealAcqScore()){
                   userLearnData4Get.setScore(dwdSspAccSummary.getRealAcqScore());
                }
                Long studyHour = dwdSspAccSummary.getStudyHour();
                if(null != studyHour){
                    userLearnData4Get.setLearningTime(DateUtils.convertSecondsToMinutes(studyHour));

                }
            }
            userLearnData4Get.setExams(dwdExamHistoryMapper.countByUserId(orgId, userId));
            userLearnData4Get.setCourses(dwdKngStudyHistoryMapper.countByUserId(orgId, userId));
            userLearnData4Get.setProjects(dwdTrainingHistoryMapper.countByUserId(orgId, userId));
            userLearnData4Get.setCertificates(dwdCerIssueHistoryMapper.countByUserId(orgId, userId));
            return userLearnData4Get;
        }catch (Exception e){
            log.info("getUserLearnData error", e);
        }

        return new UserLearnData4Get();
    }

    public PagingList<UserCourse4Get> getUserCourseData (String orgId, String userId, Paging page){
        try {
            int totalCount = dwdKngStudyHistoryMapper.countByUserId(orgId, userId);
            List<UserCourse4Get> userCourse4Gets = new ArrayList<>();
            List<DwdKngStudyHistory> dwdKngStudyHistories = dwdKngStudyHistoryMapper.listByUserId(orgId, userId, page.getOffset(), page.getLimit());
            if(CollectionUtils.isNotEmpty(dwdKngStudyHistories)){
                dwdKngStudyHistories.forEach( dwdKngStudyHistory -> {
                    UserCourse4Get userCourse4Get = new UserCourse4Get();
                    BeanHelper.copyProperties(dwdKngStudyHistory, userCourse4Get);
                    userCourse4Get.setLearningTime(DateUtils.convertSecondsToMinutes(dwdKngStudyHistory.getSumAccHours()));
                    userCourse4Gets.add(userCourse4Get);
                });
            }
            long totalPages = totalCount % page.getLimit() == 0 ? totalCount / page.getLimit() : totalCount / page.getLimit() + 1;
            return new PagingList<>(userCourse4Gets,
                    new Paging(page.getLimit(), page.getOffset(), totalPages, totalCount));
        }catch (Exception e){
            log.info("getUserCourseData error", e);
        }
        return new PagingList<>(Collections.emptyList(), new Paging(page.getLimit(), page.getOffset(), 0, 0));
    }

    public PagingList<UserExam4Get> getUserExamData(String orgId, String userId, Paging page){
        try {
            List<UserExam4Get> userExam4Gets;
            int totalCount = dwdExamHistoryMapper.countByUserId(orgId, userId);
            List<DwdExamHistory> dwdExamHistories = dwdExamHistoryMapper.listByUserId(orgId, userId, page.getOffset(), page.getLimit());
            long totalPages = totalCount % page.getLimit() == 0 ? totalCount / page.getLimit() : totalCount / page.getLimit() + 1;
            userExam4Gets = BeanCopierUtil.convertList(dwdExamHistories, DwdExamHistory.class, UserExam4Get.class);

            if (CollectionUtils.isNotEmpty(dwdExamHistories) && CollectionUtils.isNotEmpty(userExam4Gets)) {
                for (int i = 0; i < dwdExamHistories.size(); i++) {
                    UserExam4Get userExamDto = userExam4Gets.get(i);
                    DwdExamHistory dwdExamHistory = dwdExamHistories.get(i);

                    // bestUeId 已经由 BeanCopierUtil 从 dwdExamHistory.getBestUeId() 复制

                    // 维度类型：0：普通考试；1：循环考试；2：考试下的批次。 档案导入默认为0
                    Integer dimensionType = dwdExamHistory.getDimensionType();
                    if (dimensionType != null && dimensionType == 2) {
                        // 维度类型为2：考试下的批次
                        // UserExam4Get.batchId 应为批次本身的 arrangeId
                        userExamDto.setBatchId(dwdExamHistory.getArrangeId());
                        // UserExam4Get.arrangeId 应为父循环考试的 ID (即 dwdExamHistory.batchArrangeId)
                        userExamDto.setArrangeId(dwdExamHistory.getBatchArrangeId());
                    } else {
                        // 维度类型为0 (普通考试), 1 (循环考试), 或其他情况
                        // UserExam4Get.batchId 不传递
                        userExamDto.setBatchId(null);
                        // UserExam4Get.arrangeId 是考试本身的 arrangeId
                        // BeanCopierUtil 已从 dwdExamHistory.getArrangeId() 复制此值
                        // 为明确起见，可以再次设置，确保逻辑正确性
                        userExamDto.setArrangeId(dwdExamHistory.getArrangeId());
                    }
                }
            }

            return new PagingList<>(userExam4Gets,
                    new Paging(page.getLimit(), page.getOffset(), totalPages, totalCount));
        }catch (Exception e){
            log.info("getUserExamData error", e);
        }
        return new PagingList<>(Collections.emptyList(), new Paging(page.getLimit(), page.getOffset(), 0, 0));
    }

    /**
     * @return
     **/
    private <T> PagingList<T> doQueryPageFromModel(String orgId, Class<T> clazz, Map<String, Object> queryParams,
            Paging page) {
        Objects.requireNonNull(page, "分页参数不能为空");

        // 查询总数
        String countSql = SqlUtils.appendCountSql(SqlUtils.buildSqlQuery(clazz, queryParams));
        List<JSONObject> counts = doExecuteSql(orgId, JSONObject.class, countSql);
        JSONObject countJson = CollectionUtils.isEmpty(counts) ? null : counts.get(0);
        long count = countJson != null ? countJson.getLongValue(SqlUtils.PARAM_COUNTS) : 0;

        if (count == 0) {
            return new PagingList<>(Collections.emptyList(), new Paging(page.getLimit(), page.getOffset(), 0, 0));
        }

        String query = SqlUtils.buildSqlPageQuery(clazz, queryParams, page);
        List<T> datas = doExecuteSql(orgId, clazz, query);
        long totalPages = count % page.getLimit() == 0 ? count / page.getLimit() : count / page.getLimit() + 1;

        return new PagingList<>(datas, new Paging(page.getLimit(), page.getOffset(), totalPages, count));
    }

    /**
     * 按年进行查绩效
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<UserPerf4Get> getUserPerfByYear(String orgId, String userId, int year) {
        String sql = """
                SELECT du.id, du.yearly, du.period, du.perf,
                du.third_perf_id, dp.third_perf_name ,du.cycle
                FROM dwd_user_perf du
                INNER join dwd_perf dp on du.third_perf_id = dp.third_perf_id and dp.org_id = du.org_id
                where du.org_id = '%s' and du.user_id = '%s' and du.cycle = %d and du.yearly >= %d
                """;

        sql = String.format(sql, orgId, userId, PerfType.YEAR.getType(), year);
        sql = sql + " order by du.yearly desc, du.period desc";

        List<JSONObject> results = new ArrayList<>();
        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(sql);
        try {
            results = spmodelSqlService.sql(param);
        } catch (Exception e) {
            log.error("LOG50011:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        return IArrayUtils.jsonToBeanOfList(results, UserPerf4Get.class);
    }

    /**
     * 获取绩效表现
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<UserPerf4Get> getUserPerf(String orgId, String userId, int cycle, int limit) {
        String sql = """
                SELECT du.id, du.yearly, du.period, du.perf,
                du.third_perf_id, dp.third_perf_name ,du.cycle,du.perf_point, du.perf_score
                FROM dwd_user_perf du
                LEFT join dwd_perf dp on du.third_perf_id = dp.third_perf_id and dp.org_id = du.org_id
                where du.org_id = '%s' and du.user_id = '%s' and du.yearly is not null and du.yearly <> ''
                """;
        if (cycle >= 0) {
            sql = sql + " and du.cycle = " + cycle;
        }
        sql = String.format(sql, orgId, userId, cycle);
        sql = sql + " order by du.yearly desc, du.period desc limit "+ limit;

        List<JSONObject> results = new ArrayList<>();
        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(sql);
        log.info("getUserPerf sql={}", JSON.toJSONString(param));
        try {
            results = spmodelSqlService.sql(param);
        } catch (Exception e) {
            log.error("LOG50002:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        log.info("getUserPerf result={}", JSON.toJSONString(results));
        return IArrayUtils.jsonToBeanOfList(results, UserPerf4Get.class);
    }

    /**
     * 获取所有用户的绩效类型
     *
     * @param orgId
     * @param userId
     * @return
     */
    public List<UserPerfType4Get> getUserPerfType(String orgId, String userId) {
        String sql = """
                SELECT DISTINCT du.cycle
                FROM dwd_user_perf du
                where du.org_id = '%s' and du.user_id = '%s'
                """;

        sql = String.format(sql, orgId, userId);
        List<JSONObject> results = new ArrayList<>();
        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(sql);
        try {
            results = spmodelSqlService.sql(param);
        } catch (Exception e) {
            log.error("LOG50003:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        List<UserPerfType4Get> list = IArrayUtils.jsonToBeanOfList(results, UserPerfType4Get.class, stream -> stream
                .filter(e -> StringUtils.isNotBlank(PerfType.getNameByType(e.getCycle())))
                .sorted(Comparator.comparingInt(UserPerfType4Get::getCycle).reversed()));
        list.forEach(e -> {
            e.setCycleName(PerfType.getNameKeyByType(e.getCycle()));
            if (StringUtils.isNotEmpty(e.getCycleName())) {
                e.setCycleName(i18nComponent.getI18nValue(e.getCycleName(), YxtBasicUtils.requestLocale()));
            }
        });
        return list;
    }

    /**
     * 获取绩效表现
     *
     * @param orgId
     * @return
     */
    public List<Perf4Get> getAllPerf(String orgId) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ConstantPool.C_ORG_ID, orgId);
        return doQueryListFromModel(orgId, Perf4Get.class, queryParams);
    }

    @NotNull
    private <T> List<T> doQueryListFromModel(String orgId, Class<T> clazz, Map<String, Object> queryParams) {
        String query = SqlUtils.buildSqlQuery(clazz, queryParams);
        return doExecuteSql(orgId, clazz, query);
    }

    @NotNull
    private <T> List<T> doExecuteSql(String orgId, Class<T> clazz, String query) {
        log.debug("LOG50000:{}", query);
        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(query);
        List<JSONObject> results = new ArrayList<>();
        try {
            results = spmodelSqlService.sql(param);
        } catch (Exception e) {
            log.error("LOG50001:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            return new ArrayList<>();
        }
        return IArrayUtils.jsonToBeanOfList(results, clazz);
    }

    /**
     * @return
     **/
    private UdpLiteUser getUdpUser(String orgId, String userId) {
        UdpLiteUser udpLiteUser = udpLiteUserRepository.findByOrgIdAndUserId(orgId, userId);
        Validate.isNotNull(udpLiteUser, "apis.talentbk.user.not.exist");
        return udpLiteUser;
    }

    public List<UserTag4Get> getUserTags(String orgId, String userId) {
        UserLabelParam userLabelParam = new UserLabelParam();
        userLabelParam.setOrgId(orgId);
        userLabelParam.setUserIds(Collections.singletonList(userId));
        List<UserLabelVO> userLabels;
        try {
            userLabels = tagSearchRpc.getUserLabelList(userLabelParam);
        } catch (Exception e) {
            log.error("LOG50020:", e);
            return Collections.emptyList();
        }
        Map<String, List<LabelVO>> groupedLabels = userLabels.stream()
            .filter(e -> e.getUserId().equals(userId))
            .flatMap(e -> e.getLabelList().stream()).filter( label -> !CHARACTERISTIC_TAG.contains(StringUtils.isNotBlank(label.getSystemCode()) ? label.getSystemCode() :""))
            .map(e -> {
                if (StringUtils.isBlank(e.getLabelCategorySystemCode())) {
                    e.setLabelCategorySystemCode("OTHER");
                }
                return e;
            })
            .collect(Collectors.groupingBy(LabelVO::getLabelCategorySystemCode));

        return groupedLabels.entrySet().stream()
            .map(this::createUserTag4Get)
            .collect(Collectors.toList());
    }

    private UserTag4Get createUserTag4Get(Map.Entry<String, List<LabelVO>> entry) {
        UserTag4Get userTag4Get = new UserTag4Get();
        userTag4Get.setLabelCategorySystemCode(entry.getKey());
        List<TagValue4Get> tagValues = entry.getValue().stream()
            .flatMap(this::getTagValue4Gets)
            .collect(Collectors.toList());
        userTag4Get.setTagValues(tagValues);
        return userTag4Get;
    }

    private Stream<TagValue4Get> getTagValue4Gets(LabelVO labelVO) {
        return labelVO.getLabelValueList().stream()
            .map(this::createTagValue4Get);
    }

    private TagValue4Get createTagValue4Get(LabelValueVO labelValue) {
        TagValue4Get tagValue4Get = new TagValue4Get();
        tagValue4Get.setLabelValue(labelValue.getLabelValue());
        tagValue4Get.setLabelValueId(String.valueOf(labelValue.getLabelValueId()));
        return tagValue4Get;
    }

    /**
     * 查询同岗位用户画像
     * 根据用户ID查找其所在部门下指定岗位的其他用户，返回限制数量的用户画像数据
     *
     * @param req 用户画像请求参数
     * @return 用户画像列表
     */
    public List<UserProfileResp> querySamePositionUsers(UserProfileReq req) {
        try {
            // 1. 获取请求用户的基本信息，确定其所在部门
            UdpLiteUser currentUser = udpLiteUserRepository.findByOrgIdAndUserId(req.getOrgId(), req.getUserId());
            if (currentUser == null) {
                log.warn("未找到用户信息, orgId: {}, userId: {}", req.getOrgId(), req.getUserId());
                return new ArrayList<>();
            }

            // 2. 构建查询条件，查找同部门下指定岗位的用户
            QueryUserBean queryUser = new QueryUserBean();
            queryUser.setDeptId(currentUser.getDeptId());
            queryUser.setPositionId(req.getPositionId());
            queryUser.setStatus(1); // 只查询启用状态的用户

            // 使用分页查询获取同岗位用户
            Page<UdpUserBriefBean> page = new Page<>(1, req.getLimit() + 10); // 多查一些，以便排除当前用户后还有足够数量
            IPage<UdpUserBriefBean> userPage = udpLiteUserRepository.briefPage(page, req.getOrgId(), queryUser);

            // 3. 过滤出同部门的用户，排除当前用户，并限制数量
            List<UdpUserBriefBean> targetUsers = userPage.getRecords().stream()
                .filter(user -> !user.getId().equals(req.getUserId())) // 排除当前用户
                .limit(req.getLimit()) // 限制数量
                .toList();

            // 4. 为每个用户构建完整的画像信息
            List<UserProfileResp> result = new ArrayList<>();
            for (UdpUserBriefBean user : targetUsers) {
                UserProfileResp profile = buildUserProfileResp(req.getOrgId(), user.getId());
                if (profile != null) {
                    result.add(profile);
                }
            }

            log.info("查询同岗位用户画像成功, orgId: {}, userId: {}, positionId: {}, 找到用户数: {}",
                req.getOrgId(), req.getUserId(), req.getPositionId(), result.size());
            return result;
        } catch (Exception e) {
            log.error("查询同岗位用户画像失败, orgId: {}, userId: {}, positionId: {}",
                req.getOrgId(), req.getUserId(), req.getPositionId(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建用户画像响应信息（并发版）
     *
     * @param orgId  机构ID
     * @param userId 用户ID
     * @return 用户画像响应对象
     */
    public UserProfileResp buildUserProfileResp(String orgId, String userId) {
        try {
            UserProfileResp profile = new UserProfileResp();

            // 1. 使用CompletableFuture并发执行所有查询
            CompletableFuture<UserBasic4Get> basicFuture = CompletableFuture.supplyAsync(
                    () -> getUserBasic(orgId, userId),
                    wafTaskExecutor
            );

            CompletableFuture<List<UserTag4Get>> tagsFuture = CompletableFuture.supplyAsync(
                    () -> getUserTags(orgId, userId),
                    wafTaskExecutor
            );

            CompletableFuture<List<UserSkill4Get>> skillsFuture = CompletableFuture.supplyAsync(
                    () -> getUserSkillInfo(orgId, userId, ""),
                    wafTaskExecutor
            );

            CompletableFuture<UserRvTask4Get> rvTaskFuture = CompletableFuture.supplyAsync(
                    () -> getRvTaskInfo(orgId, userId),
                    wafTaskExecutor
            );

            CompletableFuture<List<UserPerf4Get>> perfsFuture = CompletableFuture.supplyAsync(
                    () -> getUserPerf(orgId, userId, -1, 50),
                    wafTaskExecutor
            );

            CompletableFuture<List<CareerHistory4Get>> careerFuture = CompletableFuture.supplyAsync(
                    () -> getCareerHistory(orgId, userId),
                    wafTaskExecutor
            );

            CompletableFuture<List<WorkHistory4Get>> workFuture = CompletableFuture.supplyAsync(
                    () -> getWorkHistory(orgId, userId),
                    wafTaskExecutor
            );

            // 2. 等待所有任务完成并处理结果
            CompletableFuture.allOf(
                    basicFuture, tagsFuture, skillsFuture, rvTaskFuture,
                    perfsFuture, careerFuture, workFuture
            ).join(); // 阻塞等待所有任务完成

            // 3. 处理每个任务的结果
            // 3.1 处理用户基本信息
            basicFuture.thenAccept(basic -> {
                if (basic != null) {
                    profile.setUserInfo(convertToUserInfo(basic));
                }
            }).exceptionally(ex -> {
                log.error("获取用户基本信息失败, orgId: {}, userId: {}", orgId, userId, ex);
                return null;
            });

            // 3.2 处理用户标签
            tagsFuture.thenAccept(tags -> {
                if (CollectionUtils.isNotEmpty(tags)) {
                    profile.setUserTags(convertToUserTags(tags));
                }
            }).exceptionally(ex -> {
                log.error("获取用户标签失败, orgId: {}, userId: {}", orgId, userId, ex);
                return null;
            });

            // 3.3 处理用户技能
            skillsFuture.thenAccept(skills -> {
                if (CollectionUtils.isNotEmpty(skills)) {
                    profile.setUserSkills(convertToUserSkills(skills));
                }
            }).exceptionally(ex -> {
                log.error("获取用户技能失败, orgId: {}, userId: {}", orgId, userId, ex);
                return null;
            });

            // 3.4 处理职责和任务
            rvTaskFuture.thenAccept(task -> {
                if (task != null) {
                    profile.setUserRvTasks(convertToUserRvTask(task));
                }
            }).exceptionally(ex -> {
                log.error("获取用户职责任务失败, orgId: {}, userId: {}", orgId, userId, ex);
                return null;
            });

            // 3.5 处理绩效信息
            perfsFuture.thenAccept(perfs -> {
                if (CollectionUtils.isNotEmpty(perfs)) {
                    profile.setUserPerfs(convertToUserPerfs(perfs));
                }
            }).exceptionally(ex -> {
                log.error("获取用户绩效失败, orgId: {}, userId: {}", orgId, userId, ex);
                return null;
            });

            // 3.6 处理内部任职履历
            careerFuture.thenAccept(careers -> {
                if (CollectionUtils.isNotEmpty(careers)) {
                    profile.setCareerHistories(convertToCareerHistories(careers));
                }
            }).exceptionally(ex -> {
                log.error("获取用户内部任职履历失败, orgId: {}, userId: {}", orgId, userId, ex);
                return null;
            });

            // 3.7 处理外部工作经历
            workFuture.thenAccept(works -> {
                if (CollectionUtils.isNotEmpty(works)) {
                    profile.setWorkHistories(convertToWorkHistories(works));
                }
            }).exceptionally(ex -> {
                log.error("获取用户外部工作经历失败, orgId: {}, userId: {}", orgId, userId, ex);
                return null;
            });

            return profile;
        } catch (Exception e) {
            log.error("构建用户画像失败, orgId: {}, userId: {}", orgId, userId, e);
            return null;
        }
    }

    // 辅助转换方法
    private UserInfo convertToUserInfo(UserBasic4Get userBasic) {
        UserInfo userInfo = new UserInfo();
        BeanCopierUtil.copy(userBasic, userInfo);
        return userInfo;
    }

    private List<UserTag> convertToUserTags(List<UserTag4Get> userTags4Get) {
        return BeanCopierUtil.convertList(userTags4Get, UserTag4Get.class, UserTag.class);
    }

    private List<UserSkill> convertToUserSkills(List<UserSkill4Get> userSkills4Get) {
        return BeanCopierUtil.convertList(userSkills4Get, UserSkill4Get.class, UserSkill.class);
    }

    private UserRvTask convertToUserRvTask(UserRvTask4Get userRvTask4Get) {
        UserRvTask userRvTask = new UserRvTask();
        BeanCopierUtil.copy(userRvTask4Get, userRvTask);
        return userRvTask;
    }

    private List<UserPerf> convertToUserPerfs(List<UserPerf4Get> userPerfs4Get) {
        return BeanCopierUtil.convertList(userPerfs4Get, UserPerf4Get.class, UserPerf.class);
    }

    private List<CareerHistory> convertToCareerHistories(List<CareerHistory4Get> careerHistories4Get) {
        return BeanCopierUtil.convertList(careerHistories4Get, CareerHistory4Get.class, CareerHistory.class);
    }

    private List<WorkHistory> convertToWorkHistories(List<WorkHistory4Get> workHistories4Get) {
        return BeanCopierUtil.convertList(workHistories4Get, WorkHistory4Get.class, WorkHistory.class);
    }
}
