package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
/**
 * 员工职责任务盘点结果(DwdUserJqTaskRv)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 16:12:37
 */
@Data
@TableName(value = "dwd_user_jq_task_rv")
public class DwdUserJqTaskRv {
    /**
     * 主键id
     */
    @TableField(value = "id")
    private String id;
    /**
     * 机构 id
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 三方用户id
     */
    @TableField(value = "third_user_id")
    private String thirdUserId;

    @TableField(value = "user_id")
    private String userId;

    /**
     * 三方部门id
     */
    @TableField(value = "third_dept_id")
    private String thirdDeptId;

    @TableField(value = "dept_id")
    private String deptId;
    /**
     * 用户姓名
     */
    @TableField(value = "user_name")
    private String userName;
    /**
     * 人员状态，-1-删除，0-禁用，1-启用
     */
    @TableField(value = "user_status")
    private Integer userStatus;
    /**
     * 三方岗位id
     */
    @TableField(value = "third_position_id")
    private String thirdPositionId;

    @TableField(value = "position_id")
    private String positionId;

    /**
     * 三方岗位名称
     */
    @TableField(value = "third_position_name")
    private String thirdPositionName;
    /**
     * 任职资格中的职责任务id
     */
    @TableField(value = "jq_task_id")
    private String jqTaskId;
    /**
     * 职责任务名称
     */
    @TableField(value = "jq_task_name")
    private String jqTaskName;
    /**
     * 职责任务所属的任务模型id
     */
    @TableField(value = "jq_task_model_id")
    private String jqTaskModelId;
    /**
     * 职责任务得分
     */
    @TableField(value = "jq_task_score")
    private Double jqTaskScore;
    /**
     * 职责任务得分（10分制）
     */
    @TableField(value = "jq_task_score10")
    private Double jqTaskScore10;
    /**
     * 职责任务达标结果，0达标 1未达标
     */
    @TableField(value = "jq_task_result")
    private Integer jqTaskResult;
    /**
     * 是否删除(0-否,1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

}
