package com.yxt.talent.bk.svc.export;

import com.alibaba.fastjson2.JSON;
import com.yxt.common.exception.ApiException;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.util.MapBuilder;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.I18nComponent;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.common.utils.DownInfoUtil;
import com.yxt.talent.bk.core.dashboard.bean.CommonExportParam;
import com.yxt.talent.bk.core.dashboard.bean.DashBoardPersonalDimVO;
import com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO;
import com.yxt.talent.bk.core.dashboard.bean.UserDeptAuthDTO;
import com.yxt.talent.bk.core.dashboard.bean.UserGroupAuthDTO;
import com.yxt.talent.bk.core.spmodel.entity.DwdDept;
import com.yxt.talent.bk.core.spmodel.entity.DwdUser;
import com.yxt.talent.bk.core.spmodel.mapper.DwdDeptMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserMapper;
import com.yxt.talent.bk.core.spmodel.mapper.DwdUserSkillRtStatisticsMapper;
import com.yxt.talent.bk.svc.dashboard.TalentBoardService;
import com.yxt.ubiz.export.bean.ExportFileInfo;
import com.yxt.ubiz.export.bean.ExportParam;
import com.yxt.ubiz.export.bean.SimpleTemplateParam;
import com.yxt.ubiz.export.common.enums.ExportFileTypeEnum;
import com.yxt.ubiz.export.core.AbstractExportWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.util.CellAddress;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/2
 */
@Service
@Slf4j
@AllArgsConstructor
public class DashBoardPersonalDimExportService extends AbstractExportWrapper {

    private final ILock lockService;
    private final AuthService authService;
    private final I18nComponent i18nComponent;
    private final TalentBoardService talentBoardService;
    private final DwdUserSkillRtStatisticsMapper dwdUserSkillRtStatisticsMapper;
    private final DwdUserMapper dwdUserMapper;
    private final DwdDeptMapper dwdDeptMapper;

    private static final int MAX_LEASE_TIME = 100;

    private final Map<String, String> headData = MapBuilder.<String, String>newMapBuilder()
            .put("fullName", "apis.talentbk.dashboard.personal.dim.temp.fullName")
            .put("username", "apis.talentbk.dashboard.personal.dim.temp.username")
            .put("skillReachCount", "apis.talentbk.dashboard.personal.dim.temp.skillReachCount")
            .put("skillReachRate", "apis.talentbk.dashboard.dept.dim.temp.skillReachRate")
            .put("taskReachCount", "apis.talentbk.dashboard.personal.dim.temp.taskReachCount")
            .put("taskReachRate", "apis.talentbk.dashboard.dept.dim.temp.taskReachRate").immutableMap();

    @Override
    public Map<String, String> getExportHeader(Object o) {
        return headData;
    }

    @Override
    public void loadData(Object b, BiConsumer<List<?>, ExportParam> consumerList) {
        //查询所有的符合参数的部门数据
        CommonExportParam exportParam = JSON.parseObject(JSON.toJSONString(b), CommonExportParam.class);
        List<Long> groupIds = exportParam.getUserGroupIds();
        List<String> deptIds = exportParam.getDeptIds();
        String orgId = exportParam.getOrgId();
        String userId = exportParam.getOptUserId();
        SimpleTemplateParam tempParam = new SimpleTemplateParam();
        tempParam.setHasRemarkRows(new boolean[]{false});
        tempParam.setHeadCells(org.assertj.core.util.Lists.newArrayList(CellAddress.A1));
        tempParam.setSheetNum(0);
        //获取权限范围的群组和部门
        List<DashBoardPersonalDimVO> emptyDataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deptIds) && CollectionUtils.isEmpty(groupIds)) {
            consumerList.accept(emptyDataList, tempParam);
            return;
        }
        if (CollectionUtils.isEmpty(deptIds)) {
            //查询当前人员的权限范围
            UserDeptAuthDTO userDeptAuthDTO = talentBoardService.getUserAuthDept(orgId, userId, exportParam.getAdmin());
            if (!userDeptAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userDeptAuthDTO.getDeptIds())) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
            //如果是空，说明该用户拥有所有部门的权限
            deptIds = userDeptAuthDTO.getDeptIds();
        }
        //获取权限下的群组集合。如果没有传，则查询下权限下的群组数据
        if (CollectionUtils.isEmpty(groupIds)) {
            UserGroupAuthDTO userGroupAuthDTO = talentBoardService.getUserAuthGroupIds(orgId, userId,
                    exportParam.getAdmin());
            if (!userGroupAuthDTO.isHasAllAuth() && CollectionUtils.isEmpty(userGroupAuthDTO.getGroupIds())) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
            //如果是空，说明该用户拥有所有群组的权限
            groupIds = userGroupAuthDTO.getGroupIds();
        }
        //        List<String> dwdDeptIds = talentBoardService.transferDeptId(deptIds, orgId);
        List<String> dwdDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<DwdDept> deptList = dwdDeptMapper.getByIds(orgId, deptIds);
            dwdDeptIds = deptList.stream().map(DwdDept::getDeptId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dwdDeptIds)) {
                consumerList.accept(emptyDataList, tempParam);
                return;
            }
        }
        boolean flag = true;
        int size = 1000;
        int current = 1;
        while (flag) {
            //分页查询，分批次设置
            int offset = (current - 1) * size;
            List<DwsUserSkillRtDTO> userList = dwdUserSkillRtStatisticsMapper.getPageByDeptGroup(orgId, dwdDeptIds,
                    groupIds, offset, size);
            List<DashBoardPersonalDimVO> resultData = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(userList)) {
                List<String> userIds = userList.stream().map(DwsUserSkillRtDTO::getUserId).distinct()
                        .collect(Collectors.toList());
                List<DwdUser> dwdUsers = dwdUserMapper.getUserInfoByUserIds(orgId, userIds);
                Map<String, DwdUser> dwdUserIdMap = StreamUtil.list2map(dwdUsers, DwdUser::getUserId);
                userList.forEach(user -> {
                    DashBoardPersonalDimVO dashBoardPersonalDimVO = setPersonListExportData(dwdUserIdMap, user);
                    resultData.add(dashBoardPersonalDimVO);
                    consumerList.accept(resultData, tempParam);
                    resultData.clear();
                });
            }
            if (CollectionUtils.isNotEmpty(userList) && userList.size() >= size) {
                current++;
                continue;
            }
            flag = false;
        }
    }

    @NotNull
    private DashBoardPersonalDimVO setPersonListExportData(Map<String, DwdUser> dwdUserIdMap,
            DwsUserSkillRtDTO user) {
        DashBoardPersonalDimVO dashBoardPersonalDimVO = new DashBoardPersonalDimVO();
        dashBoardPersonalDimVO.setThirdUserId(user.getUserId());
        DwdUser dwdUser = dwdUserIdMap.getOrDefault(user.getUserId(), null);
        if (Objects.nonNull(dwdUser)) {
            dashBoardPersonalDimVO.setFullName(dwdUser.getFullName());
            dashBoardPersonalDimVO.setUserId(dwdUser.getUserId());
            dashBoardPersonalDimVO.setUsername(dwdUser.getUserName());
        }
        dashBoardPersonalDimVO.setSkillReachCount(user.getMatchedSkillCnt());
        if (user.getTotalSkillCnt() <= 0L || user.getMatchedSkillCnt() <= 0L) {
            dashBoardPersonalDimVO.setSkillReachRate("0%");
        } else {
            String skillReachRate = talentBoardService.getRateCal(user.getTotalSkillCnt(), user.getMatchedSkillCnt());
            dashBoardPersonalDimVO.setSkillReachRate(skillReachRate + "%");
        }
        dashBoardPersonalDimVO.setTaskReachCount(user.getMatchedRtCnt());
        if (user.getTotalRtCnt() <= 0L || user.getMatchedRtCnt() <= 0L) {
            dashBoardPersonalDimVO.setTaskReachRate("0%");
        } else {
            String rtReachRate = talentBoardService.getRateCal(user.getTotalRtCnt(), user.getMatchedRtCnt());
            dashBoardPersonalDimVO.setTaskReachRate(rtReachRate + "%");
        }
        return dashBoardPersonalDimVO;
    }

    public void exportPersonalDimDashBoard(CommonExportParam searchParam, String orgId, String userId,
            String fullname) {
        //校验参数
        if (CollectionUtils.isEmpty(searchParam.getDeptIds()) && CollectionUtils.isEmpty(
                searchParam.getUserGroupIds())) {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_PARAM_ERROR);
        }
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_DASHBOARD_PERSONAL_DIM_EXPORT, orgId, userId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                ExportFileInfo exportFileInfo = new ExportFileInfo();
                //设置业务的查询参数
                exportFileInfo.setQueryParams(searchParam);
                exportFileInfo.setTemplatePath("excel/dashboard_personal_dim.xlsx");
                //设置下载信息
                exportFileInfo.setDownInfo(DownInfoUtil.getDownInfo(ModuleConstants.FUNCTION_NAME));
                exportFileInfo.setLocale(authService.getLocale());
                exportFileInfo.setFileType(ExportFileTypeEnum.EXCEL);
                //不需要后缀.后缀由fileType的suffix决定,此文件名必须唯一
                String fileName = i18nComponent.getI18nValue(ExportConstants.DASHBOARD_PERSONAL_DIM_EXPORT_FILE_NAME);
                exportFileInfo.setName(fileName + "_" + System.currentTimeMillis());
                exportFileInfo.setFileName(fileName + System.nanoTime());
                exportFileInfo.setOrgId(orgId);
                exportFileInfo.setUserId(userId);
                exportFileInfo.setFullname(fullname);
                exportFileInfo.setExportTopic(TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_FILE);
                exportFileInfo.setUsemq(true);
                exportFileInfo.setDownloadI18n(true);
                export(exportFileInfo);
            } catch (Exception ex) {
                log.warn("exportDeptDimDashBoard  orgId={},userId={};err", orgId, userId, ex);
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_FILE_EXPORT_ING);
        }
    }
}
