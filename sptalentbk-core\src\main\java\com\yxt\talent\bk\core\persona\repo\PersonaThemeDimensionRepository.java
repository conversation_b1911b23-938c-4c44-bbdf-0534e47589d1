package com.yxt.talent.bk.core.persona.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeDimensionEntity;
import com.yxt.talent.bk.core.persona.mapper.PersonaThemeDimensionMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class PersonaThemeDimensionRepository
        extends ServiceImpl<PersonaThemeDimensionMapper, PersonaThemeDimensionEntity> {

    /**
     * 同一个主题条件下，查询维度名称是否存在
     * @param orgId
     * @param dimensionName
     * @param themeId
     * @return
     */
    public List<PersonaThemeDimensionEntity> queryThemeDimensionByName(String orgId, String dimensionName, String themeId) {
        LambdaQueryWrapper<PersonaThemeDimensionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PersonaThemeDimensionEntity::getOrgId, orgId);
        wrapper.eq(PersonaThemeDimensionEntity::getThemeId, themeId);
        wrapper.eq(PersonaThemeDimensionEntity::getDimensionName, dimensionName);
        return list(wrapper);
    }

    /**
     * 查询维度详情
     * @param orgId
     * @param dimensionId
     * @return
     */
    public PersonaThemeDimensionEntity queryThemeDimensionById(String orgId, String dimensionId) {
        LambdaQueryWrapper<PersonaThemeDimensionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PersonaThemeDimensionEntity::getOrgId, orgId);
        wrapper.eq(PersonaThemeDimensionEntity::getId, dimensionId);
        return getOne(wrapper);
    }

    /**
     * 查询某个主题下所有维度
     * @param orgId
     * @param themeIds
     * @return
     */
    public List<PersonaThemeDimensionEntity> queryThemeDimensionByThemeIds(String orgId, Collection<String> themeIds,
            int sourceType) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(themeIds)) {
            log.warn("LOG10290:orgId={}, sourceType={}", orgId, sourceType);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PersonaThemeDimensionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PersonaThemeDimensionEntity::getOrgId, orgId);
        wrapper.in(PersonaThemeDimensionEntity::getThemeId, themeIds);
        if (sourceType != -1) {
            wrapper.eq(PersonaThemeDimensionEntity::getDimensionSource, sourceType);
        }
        wrapper.orderByDesc(PersonaThemeDimensionEntity::getCreateTime);
        List<PersonaThemeDimensionEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }


    /**
     * 删除某个维度
     * @param orgId
     * @param dimensionId
     * @return
     */
    public void removeDimensionById(String orgId, String dimensionId) {
        LambdaQueryWrapper<PersonaThemeDimensionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PersonaThemeDimensionEntity::getOrgId, orgId);
        wrapper.eq(PersonaThemeDimensionEntity::getId, dimensionId);
        remove(wrapper);
    }

}
