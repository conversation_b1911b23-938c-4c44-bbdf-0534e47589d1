package com.yxt.talent.bk.core.search.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 筛选组/项关系表
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Data
@Deprecated
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("bk_search_filter_group")
public class SearchFilterGroup extends CreatorEntity {

    @TableId("id")
    private String id;

    @TableField("org_id")
    private String orgId;

    /**
     * 筛选组名称
     */
    @TableField("group_name")
    private String groupName;

    /**
     * 筛选组数据集合
     */
    @TableField("filter_value")
    private String filterValue;

    /**
     * 0-非默认筛选组，1-默认筛选组
     */
    @TableField("group_type")
    private Integer groupType;
}
