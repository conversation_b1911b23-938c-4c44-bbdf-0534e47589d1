package com.yxt.talent.bk.svc.usergroup.component;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.audit.bean.OneFieldAuditLog;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.usergroup.mapper.UserGroupMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * UserGroupSimpleLogProvider
 *
 * <AUTHOR> geyan
 * @Date 22/3/24 4:57 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class UserGroupSimpleLogProvider implements AuditLogDataProvider<Long, OneFieldAuditLog> {
    private final UserGroupMapper userGroupMapper;
    @Override
    public OneFieldAuditLog before(Long param, AuditLogBasicBean logBasic) {
        String groupName = userGroupMapper.getNameById(param);
        return new OneFieldAuditLog("群组名称", groupName);
    }

    @Override
    public OneFieldAuditLog after(Long param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public Pair<String, String> entityInfo(Long param, OneFieldAuditLog beforeObj, OneFieldAuditLog afterObj, AuditLogBasicBean logBasic) {
        String entityFormat = logBasic.getLogPoint().getPointName();
        String groupName = CommonUtils.firstNotEmpty(beforeObj, afterObj, OneFieldAuditLog::getValue);
        if (StringUtils.isEmpty(groupName)) {
            groupName = userGroupMapper.getNameById(param);
        }
        return Pair.of(String.valueOf(param), String.format(entityFormat, groupName));
    }
}
