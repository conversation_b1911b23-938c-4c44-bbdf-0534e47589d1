<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.tag.mapper.CatalogMapper">

    <select id="getNameByIds" resultType="com.yxt.spsdk.common.bean.SpIdNameBean">
        select id,bk_catalog_name as name from bk_catalog where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
