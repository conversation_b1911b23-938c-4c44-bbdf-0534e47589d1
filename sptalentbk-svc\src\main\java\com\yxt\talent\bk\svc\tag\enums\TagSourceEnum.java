package com.yxt.talent.bk.svc.tag.enums;

import lombok.Getter;

/**
 * 标签来源枚举类
 *
 * <AUTHOR>
 * @since 2022/8/11
 */
public enum TagSourceEnum {
    /**
     * 标签来源(0-内置,1-自建,2-固定)
     */
    SOURCE_0(0, "内置"), SOURCE_1(1, "自建"), SOURCE_2(2, "固定");

    @Getter
    private int type;
    @Getter
    private String name;

    TagSourceEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

}
