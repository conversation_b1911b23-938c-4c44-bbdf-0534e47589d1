<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.pool.mapper.ProjectTargetMapMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.pool.entity.ProjectTargetMap">
        <!--@mbg.generated-->
        <!--@Table bk_project_target_map-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="project_id" jdbcType="CHAR" property="projectId"/>
        <result column="project_type" jdbcType="TINYINT" property="projectType"/>
        <result column="target_id" jdbcType="CHAR" property="targetId"/>
        <result column="target_type" jdbcType="TINYINT" property="targetType"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        org_id,
        project_id,
        project_type,
        target_id,
        target_type,
        create_user_id,
        create_time,
        update_user_id,
        update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bk_project_target_map
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.orgId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="project_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.projectId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.projectType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="target_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.targetId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="target_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.targetType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.createUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.updateUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=CHAR}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into bk_project_target_map
        (id, org_id, project_id, project_type, target_id, target_type, create_user_id, create_time,
         update_user_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.projectId,jdbcType=CHAR},
             #{item.projectType,jdbcType=TINYINT}, #{item.targetId,jdbcType=CHAR}, #{item.targetType,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.yxt.talent.bk.core.pool.entity.ProjectTargetMap">
        <!--@mbg.generated-->
        insert into bk_project_target_map
        (id, org_id, project_id, project_type, target_id, target_type, create_user_id, create_time,
         update_user_id, update_time)
        values (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{projectId,jdbcType=CHAR},
                #{projectType,jdbcType=TINYINT},
                #{targetId,jdbcType=CHAR}, #{targetType,jdbcType=TINYINT}, #{createUserId,jdbcType=CHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update id             = #{id,jdbcType=CHAR},
                                org_id         = #{orgId,jdbcType=CHAR},
                                project_id     = #{projectId,jdbcType=CHAR},
                                project_type   = #{projectType,jdbcType=TINYINT},
                                target_id      = #{targetId,jdbcType=CHAR},
                                target_type    = #{targetType,jdbcType=TINYINT},
                                create_user_id = #{createUserId,jdbcType=CHAR},
                                create_time    = #{createTime,jdbcType=TIMESTAMP},
                                update_user_id = #{updateUserId,jdbcType=CHAR},
                                update_time    = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.bk.core.pool.entity.ProjectTargetMap">
        <!--@mbg.generated-->
        insert into bk_project_target_map
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="orgId != null and orgId != ''">
                org_id,
            </if>
            <if test="projectId != null and projectId != ''">
                project_id,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="targetId != null and targetId != ''">
                target_id,
            </if>
            <if test="targetType != null">
                target_type,
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=CHAR},
            </if>
            <if test="orgId != null and orgId != ''">
                #{orgId,jdbcType=CHAR},
            </if>
            <if test="projectId != null and projectId != ''">
                #{projectId,jdbcType=CHAR},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=TINYINT},
            </if>
            <if test="targetId != null and targetId != ''">
                #{targetId,jdbcType=CHAR},
            </if>
            <if test="targetType != null">
                #{targetType,jdbcType=TINYINT},
            </if>
            <if test="createUserId != null and createUserId != ''">
                #{createUserId,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id = #{id,jdbcType=CHAR},
            </if>
            <if test="orgId != null and orgId != ''">
                org_id = #{orgId,jdbcType=CHAR},
            </if>
            <if test="projectId != null and projectId != ''">
                project_id = #{projectId,jdbcType=CHAR},
            </if>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=TINYINT},
            </if>
            <if test="targetId != null and targetId != ''">
                target_id = #{targetId,jdbcType=CHAR},
            </if>
            <if test="targetType != null">
                target_type = #{targetType,jdbcType=TINYINT},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="countByOrgIdAndTrainingId" resultType="int">
        select count(*)
        from bk_project_target_map a
        where a.org_id = #{orgId}
          and a.target_id = #{trainingId}
    </select>

    <select id="selectByOrgIdAndPoolId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bk_project_target_map a
        where a.org_id = #{orgId}
        and a.project_id = #{poolId}
        and a.project_type = 0
    </select>

    <select id="selectTargetIdByProjectIdAndTargetType" resultType="java.lang.String">
        select a.target_id from bk_project_target_map a
        where a.org_id = #{orgId}
        and a.project_id = #{projectId}
        and a.target_type = #{targetType}
    </select>

    <select id="selectByOrgIdAndProjectIdAndTargetIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bk_project_target_map a
        where a.org_id = #{orgId}
        and a.project_id = #{projectId}
        <choose>
            <when test="targetIds != null and targetIds.size() != 0">
                and a.target_id in
                <foreach collection="targetIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <delete id="removeByProjectIdAndTargetIdIn">
        delete a
        from bk_project_target_map a
        where a.org_id = #{orgId}
          and a.project_id = #{projectId}
        <choose>
            <when test="targetIds != null and targetIds.size() != 0">
                and a.target_id in
                <foreach collection="targetIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <select id="selectTargetIdByOrgIdAndProjectId" resultType="java.lang.String">
        select
        distinct a.target_id
        from bk_project_target_map a
        where a.org_id = #{orgId}
        and a.project_id = #{projectId}
    </select>
</mapper>
