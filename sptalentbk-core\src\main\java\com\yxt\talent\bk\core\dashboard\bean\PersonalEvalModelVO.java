package com.yxt.talent.bk.core.dashboard.bean;

import com.yxt.talent.bk.core.sd.bean.SdModelIndicator4ResultDto;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class PersonalEvalModelVO {
    private String modelName;

    private String modelId;

    private Integer modelType;

    private List<SkillCatalogModel4Get> skillCatalogModels = new ArrayList<>();

    private List<RtModelMap4Get> rtModel = new ArrayList<>();

    private List<SdModelIndicator4ResultDto> indicatorResults;
}
