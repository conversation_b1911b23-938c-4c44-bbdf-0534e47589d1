package com.yxt.talent.bk.svc.pool.component;

import com.alibaba.fastjson.JSON;
import com.yxt.spevalfacade.bean.evaluation.Evaluation4List;
import com.yxt.spevalfacade.bean.evaluation.EvaluationFacade;
import com.yxt.spevalfacade.service.SpEvalApiFacade;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.StringConcatBuilder;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.svc.pool.bean.PoolEval4Log;
import com.yxt.talent.bk.svc.pool.bean.PoolEvalParam4Log;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * PoolBindEvalLogProvider
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 4:59 pm
 */
@Slf4j
@AllArgsConstructor
@Component
public class PoolEvalLogProvider implements AuditLogDataProvider<PoolEvalParam4Log, PoolEval4Log> {
    private final PoolRepository poolRepository;
    private final SpEvalApiFacade spEvalApiFacade;
    @Override
    public PoolEvalParam4Log convertParam(Object param, AuditLogBasicBean logBasic) {
        PoolEvalParam4Log param4Log;
        if (param instanceof PoolEvalParam4Log) {
            param4Log = (PoolEvalParam4Log)param;
        } else {
            param4Log = JSON.parseObject(JSON.toJSONString(param), PoolEvalParam4Log.class);
        }
        if (param4Log == null) {
            param4Log = new PoolEvalParam4Log();
        }
        param4Log.setEvalNames(evalLog(logBasic.getOrgId(), param4Log).getEvalNames());
        return param4Log;
    }

    @Override
    public PoolEval4Log before(PoolEvalParam4Log param, AuditLogBasicBean logBasic) {
        return new PoolEval4Log(param.getEvalNames());
    }

    @Override
    public PoolEval4Log after(PoolEvalParam4Log param, AuditLogBasicBean logBasic) {
        return new PoolEval4Log(param.getEvalNames());
    }

    @Override
    public Pair<String, String> entityInfo(PoolEvalParam4Log param, PoolEval4Log beforeObj, PoolEval4Log afterObj, AuditLogBasicBean logBasic) {
        return Pair.of(param.getPoolId(),
                String.format("人才池-%s-%s", poolRepository.getNameById(param.getPoolId()), param.getEvalNames()));
    }

    private PoolEval4Log evalLog(String orgId, PoolEvalParam4Log param) {
        PoolEval4Log eval4Log = new PoolEval4Log();
        if (CollectionUtils.isNotEmpty(param.getEvalIds())) {
            StringConcatBuilder evalNames = new StringConcatBuilder(StringPool.COMMA);
            EvaluationFacade queryBean = new EvaluationFacade();
            queryBean.setOrgId(orgId);
            queryBean.setEvalIds(param.getEvalIds());
            for (Evaluation4List eval : spEvalApiFacade.searchEvalList(queryBean).getDatas()) {
                evalNames.append(eval.getName()).appendConcat();
            }
            eval4Log.setEvalNames(evalNames.output());
        }
        return eval4Log;
    }
}
