package com.yxt.talent.bk.core.udp.bean;

import lombok.Data;

import java.util.List;

/**
 * UdpQueryUserBean
 *
 * <AUTHOR> ha<PERSON><PERSON>
 * @Date 24/7/24 2:30 pm
 */
@Data
public class UdpQueryUserBean {
    private Integer status;
    private List<String> deptIds;
    /**
     * parentDeptIds一定要是deptIds的子集
     */
    private List<String> parentDeptIds;
    private List<String> positionIds;
    private List<String> gradeIds;
    private List<String> userIds;
}
