package com.yxt.talent.bk.core.usergroup.repo;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.core.usergroup.bean.GroupMemberVO;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupMember;
import com.yxt.talent.bk.core.usergroup.mapper.UserGroupMemberMapper;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/17
 */
@Slf4j
@Repository
public class UserGroupMemberRepository extends ServiceImpl<UserGroupMemberMapper, UserGroupMember> {

    public Page<GroupMemberVO> findPage(IPage<GroupMemberVO> iPage, Long groupId, String orgId) {
        return getBaseMapper().findPage(iPage, groupId, orgId);
    }

    private LambdaQueryWrapper<UserGroupMember> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public List<UserGroupMember> getGroupMemberListByGroupId(String orgId, Long groupId) {
        LambdaQueryWrapper<UserGroupMember> query = getQueryWrapper();
        query.eq(UserGroupMember::getOrgId, orgId);
        query.eq(UserGroupMember::getGroupId, groupId);
        query.eq(UserGroupMember::getDeleted, YesOrNo.NO.getValue());
        query.orderByDesc(UserGroupMember::getCreateTime);
        return list(query);
    }

    public List<UserGroupMember> listByGroupIds(String orgId, Collection<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<UserGroupMember> query = getQueryWrapper();
        query.eq(UserGroupMember::getOrgId, orgId);
        query.in(UserGroupMember::getGroupId, groupIds);
        query.eq(UserGroupMember::getDeleted, YesOrNo.NO.getValue());
        return list(query);
    }

    public void batchInsert(List<UserGroupMember> userGroupMembers) {
        getBaseMapper().batchInsert(userGroupMembers);
    }

    public void deleteByGroupId(String orgId, String userId, Long id) {
        getBaseMapper().deleteByGroupId(orgId, userId, id);
    }

    public void deleteByGroupIdAndUserIds(String orgId, List<String> deleteUids, long groupId) {
        getBaseMapper().deleteByGroupIdAndUserIds(orgId, deleteUids, groupId);
    }
}
