package com.yxt.talent.bk.svc.profile.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.common.Constants;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Setter
@Getter
@SqlTable("dws_user")
@Schema(name = "人才画像-人员基本信息")
public class UserBasic4Get {

    @SqlIgnore
    @Schema(description = "绚星2.0平台用户id")
    private String userId;

    @SqlIgnore
    @Schema(description = "姓名")
    private String fullname;

    @SqlIgnore
    @Schema(description = "账号")
    private String username;

    @JsonIgnore
    @Schema(description = "三方用户id")
    private String thirdUserId;

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "禁用/启用(0-禁用 1-启用)")
    private Integer enabled;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "照片地址")
    private String avatarUrl;

    @Schema(description = "大学")
    private String university;

    @Schema(description = "学历")
    private String education;

    @Schema(description = "性别(0-未知 1-男 2-女)")
    private Integer gender;

    @Schema(description = "政治面貌")
    private String political;

    @Schema(description = "婚姻面貌(0-未知 1-已婚 2-未婚)")
    private Integer marital;

    @Schema(description = "入职时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime entryTime;

    @Schema(description = "司龄")
    private BigDecimal serviceYears;

    @JsonIgnore
    @Schema(description = "三方部门id")
    private String thirdDeptId;

    @Schema(description = "部门id")
    private String deptId;

    @Schema(description = "部门名称")
    private String thirdDeptName;

    @Schema(description = "三方部门名称全路径，销售部->广州大区->销售一组")
    private String thirdDeptNamePath;

    @JsonIgnore
    @Schema(description = "三方职级id")
    private String thirdJobgradeId;

    @Schema(description = "职级id")
    private String jobgradeId;

    @Schema(description = "职级名称")
    private String thirdJobgradeName;

    @SqlIgnore
    @Schema(description = "跨部门任职意愿")
    private String willingness;

    @SqlIgnore
    @Schema(description = "可接受工作调配地")
    private String workLocation;

    @SqlIgnore
    @Schema(description = "可接受调派时间")
    private String dispatchTime;

    @JsonIgnore
    @Schema(description = "三方岗位id")
    private String thirdPositionId;

    @Schema(description = "岗位id")
    private String positionId;

    @Schema(description = "岗位名称")
    private String thirdPositionName;

    @Schema(description = "是否管理者(0-否 1-是)")
    private Integer manager;

    @Schema(description = "职业资格证书，多个证书使用半角分号分割")
    private String profCerts;

    @Schema(description = "工作年限")
    private String workYears;

    @Schema(description = "专业")
    private String major;


}
