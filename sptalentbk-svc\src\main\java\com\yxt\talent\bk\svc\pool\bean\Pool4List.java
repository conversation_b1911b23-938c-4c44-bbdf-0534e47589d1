package com.yxt.talent.bk.svc.pool.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(name = "人才池列表")
public class Pool4List {
    @Schema(description = "主键")
    private String id;
    @Schema(description = "人才池名称")
    private String poolName;
    @Schema(description = "分类Id")
    private String catalogId;
    @Schema(description = "分类名称")
    private String catalogName = "";
    @Schema(description = "预期人数")
    private Integer expectNum;
    @Schema(description = "现有人数")
    private Integer realNum;
    @Schema(description = "饱和度")
    private BigDecimal saturability;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "创建日期")
    @JsonFormat(pattern = Constants.SDF_YEAR2DAY, timezone = Constants.STR_GMT8)
    @DateFormatField
    private Date createTime;
}
