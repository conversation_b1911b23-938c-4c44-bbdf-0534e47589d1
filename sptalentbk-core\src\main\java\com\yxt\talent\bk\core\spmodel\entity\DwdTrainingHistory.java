package com.yxt.talent.bk.core.spmodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
/**
 * 学习培训历史(DwdTrainingHistory)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 10:55:41
 */
@Data
@TableName(value = "dwd_training_history")
public class DwdTrainingHistory {
    /**
     * 主键Id
     */
    @TableField(value = "id")
    private String id;
    /**
     * 三方用户id
     */
    @TableField(value = "third_user_id")
    private String thirdUserId;

    @TableField(value = "user_id")
    private String userId;

    /**
     * 机构Id
     */
    @TableField(value = "org_id")
    private String orgId;
    /**
     * 项目id
     */
    @TableField(value = "project_id")
    private String projectId;
    /**
     * 项目名称
     */
    @TableField(value = "project_name")
    private String projectName;
    /**
     * 培训开始时间
     */
    @TableField(value = "start_time")
    private LocalDateTime startTime;
    /**
     * 培训结束时间
     */
    @TableField(value = "end_time")
    private LocalDateTime endTime;
    /**
     * 完成状态（0-未完成 1-进行中, 2-已完成）
     */
    @TableField(value = "completion_status")
    private Integer completionStatus;
    /**
     * 是否删除(0-否,1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 项目得分
     */
    @TableField(value = "project_score")
    private Double projectScore;
    /**
     * 项目结果，0-不合格,1-合格
     */
    @TableField(value = "passed")
    private Integer passed;
    /**
     * 是否优秀学员，0-否,1-是
     */
    @TableField(value = "outstanding")
    private Integer outstanding;

}
