package com.yxt.talent.bk.svc.tag.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标签信息
 *
 * <AUTHOR>
 * @since 2022/8/8
 */
@Data
@NoArgsConstructor
public class TagBean {

    @Schema(description = "标签id")
    private String tagId;

    @Schema(description = "标签值id列表")
    private List<String> tagValueIdList;

}
