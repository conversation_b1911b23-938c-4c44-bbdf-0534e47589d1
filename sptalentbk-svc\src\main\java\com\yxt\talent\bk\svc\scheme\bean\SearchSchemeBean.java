package com.yxt.talent.bk.svc.scheme.bean;

import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/8/17 15:43
 **/
@Data
public class SearchSchemeBean extends SearchRuleBean {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "筛选方案名称")
    @Size(max = 30, message = "apis.talentbk.scheme.name.too.long")
    private String schemeName;

    @Schema(description = "描述")
    @Size(max = 300, message = "apis.talentbk.scheme.desc.too.long")
    private String schemeDesc;
}
