package com.yxt.talent.bk.svc.mq.consumer;

import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.ubiz.export.core.AbstractExportMqCustomer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/2/1 15:57 15:57
 */

@Component
@AllArgsConstructor
@Slf4j
@RocketMQMessageListener(consumerGroup = TalentBkRocketMqConstant.GROUP_PREFIX         + TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_FILE, topic = TalentBkRocketMqConstant.TOPIC_SPTALENTBK_EXPORT_FILE, messageModel = MessageModel.CLUSTERING, consumeThreadNumber = 2, consumeTimeout = 30)
public class ExportMqConsumer extends AbstractExportMqCustomer implements RocketMQListener<String> {
    @Override
    public void onMessage(String message) {
        log.info("ExportMqConsumer {}", message);
        message(message);
    }
}
