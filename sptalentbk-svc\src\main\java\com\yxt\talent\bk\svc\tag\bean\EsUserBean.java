package com.yxt.talent.bk.svc.tag.bean;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.elasticsearch.annotations.Document;

import java.util.Map;
import java.util.Set;

/**
 * es中user对象
 *
 * <AUTHOR>
 * @since 2022/8/8
 */
@Data
@NoArgsConstructor
@Document(indexName = "#{@bkConfigService.indexName}", type = TalentBkConstants.ES_TYPE)
@SuperBuilder
public class EsUserBean {

    private String orgId;

    /**
     * 用户id
     */
    private String userId;
    /**
     * 领导风格标签（内置标签，可手动修改）
     */
    private Set<String> leadershipStyle = Sets.newHashSet();
    /**
     * 自建标签
     */
    private Map<String, Set<String>> diylabels = Maps.newHashMap();

}
