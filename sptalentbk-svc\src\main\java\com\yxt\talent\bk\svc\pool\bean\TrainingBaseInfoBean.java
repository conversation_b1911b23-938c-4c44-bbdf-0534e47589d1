package com.yxt.talent.bk.svc.pool.bean;

import com.yxt.sptalentapifacade.bean.eval.TrainingSimpleBaseInfoBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *  测训基础信息bean
 * <AUTHOR>
 * @since 2022/9/19 16:00
 * @version 1.0
 */
@Data
public class TrainingBaseInfoBean {
    private TrainingSimpleBaseInfoBean baseInfoBean;
    @Schema(description = "学习进度配置 1：统计全部，2：统计必修")
    private Integer studyProgressConfig;
}
