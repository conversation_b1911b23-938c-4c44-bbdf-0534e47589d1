package com.yxt.talent.bk.svc.mq.consumer;

import com.yxt.common.util.BeanHelper;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.core.component.RedisComponent;
import com.yxt.talentbkfacade.constant.BkFacadeContants;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * CopyOrgFirstListener
 *
 * <AUTHOR> harleyge
 * @Date 12/9/24 2:14 pm
 */
@Component
@AllArgsConstructor
@Slf4j
@RocketMQMessageListener(consumerGroup = TalentBkRocketMqConstant.GROUP_PREFIX + BkFacadeContants.TOPIC_UDP_ORG_INIT,         topic = BkFacadeContants.TOPIC_UDP_ORG_INIT, consumeThreadNumber = 2, consumeTimeout = 30)
public class CopyOrgFirstConsumer implements RocketMQListener<String> {
    private final RedisComponent redisComponent;
    @Override
    public void onMessage(String message) {
        try {
            OrgInit4Mq bean = BeanHelper.json2Bean(message, OrgInit4Mq.class);
            String targetOrgId = bean.getTargetOrgId();
            if (StringUtils.isAnyBlank(bean.getSourceOrgId(), bean.getTargetOrgId())) {
                log.warn("CopyOrgFirstListener empty[sourceOrgId, targetOrgId] message {}", message);
            } else {
                log.info("CopyOrgFirstListener save message {}", message);
                String saveKey = String.format(TalentBkRedisKeys.CACHE_COPY_ORG_DATA, targetOrgId);
                redisComponent.setValue(saveKey, message, 400, TimeUnit.DAYS);
            }
        } catch (Exception ex) {
            log.error("CopyOrgFirstListener error message {}", message, ex);
        }
    }
}
