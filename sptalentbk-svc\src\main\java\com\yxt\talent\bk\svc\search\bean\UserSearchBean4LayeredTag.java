package com.yxt.talent.bk.svc.search.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "人才搜索-主页-列表搜索入参-分层标签")
public class UserSearchBean4LayeredTag {
    @Schema(description = "标签key",example = "gradeName")
    private String itemKey;
    @Schema(description = "搜索逻辑【0-且,1-或】",example = "1")
    private Integer logic = 1;
    @Schema(description="标签值")
    private List<String> values;
}
