package com.yxt.talent.bk.core.heir.bean;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirPosRawBean
 *
 * <AUTHOR> geyan
 * @Date 16/8/23 7:48 pm
 */
@Data
public class HeirPosRawBean implements HeirPosRiskSupport, UdpLangSupport {
    private String id;

    private String name;
    private String idFullPath;
    @Schema(description = "源岗位是不是已删除")
    private Integer sourceDeleted;

    @Schema(description = "空表示没有上级")
    private String parentPosId;

    @Schema(description = "目标继任数量,0:未设置")
    private Integer heirTargetQty;

    @Schema(description = "有效人数")
    private Integer heirValidQty;

    @Schema(description = "0:自动规则，1:手动规则")
    private Integer riskRuleType;

    @Schema(description = "风险规则id")
    private Long riskLevelId;

    @Schema(description = "是否设置权限,1:打开")
    private Integer permissionFlag;

    private String createUserId;

    private int posType;
    //-1:无效，0：补全数据，1：有效数据
    private int dataState;
    private int hasPerm;

    @Override
    public UdpLangUnitBean deptLangProperty() {
        if (posType == HeirPosTypeEnum.DEPT.getType()) {
            return new UdpLangUnitBean(id, name, this::setName);
        }
        return null;
    }
}
