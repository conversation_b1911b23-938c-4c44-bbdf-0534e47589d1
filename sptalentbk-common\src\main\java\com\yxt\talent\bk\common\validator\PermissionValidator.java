package com.yxt.talent.bk.common.validator;

import com.google.common.collect.Lists;
import com.yxt.common.Constants;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.coreapi.client.bean.dataauth.DataScopeReq;
import com.yxt.coreapi.client.bean.dataauth.DataScopeResp;
import com.yxt.coreapi.client.service.CoreApiFacade;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 数据权限
 * <AUTHOR>
 * @Date 2023/12/11 15:25
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class PermissionValidator {

    private static final String NAVCODE = "sp_gwnl_file_search";

    /**
     * 员工人员管辖范围
     */
    private static final String PERMISSION_CODE = "sp_file_talentlist_get_extent";
    /**
     * 人才群组列表
     */
    private static final String GROUP_NAVCODE = "sp_gwnl_file_group";

    private static final String GROUP_PERMISSION_CODE = "sp_file_talentgroup_get_extent";

    private final CoreApiFacade coreApiFacade;

    public Map<String, List<String>> getPermissionMsg(String orgId, String userId, List<String> deptIds, String navCode,
                                                      String dataPermsCode, UserCacheDetail userDetail) {
        DataScopeReq dataScopeReq = new DataScopeReq();
        dataScopeReq.setOrgId(orgId);
        dataScopeReq.setUserId(userId);
        dataScopeReq.setNavCode(navCode);
        dataScopeReq.setDataPermsCode(dataPermsCode);
        dataScopeReq.setProductCode(Constants.XXV2_PRODUCT_CODE);
        log.info("getPermissionMsg core dataScopeReq={}", BeanHelper.bean2Json(dataScopeReq));
        List<DataScopeResp> dataScopeRespList = getDataScopes(dataScopeReq, deptIds);
        log.info("getPermissionMsg core dataScopeRespList={}", BeanHelper.bean2Json(dataScopeRespList));
        List<String> newUserIds = new ArrayList<>();
        List<String> newDeptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataScopeRespList)) {
            DataScopeResp firstDataScope = dataScopeRespList.get(0);
            if (null != firstDataScope.getHasAllScope() && 1 == firstDataScope.getHasAllScope()) {
                userDetail.setAdmin(StringPool.ONE);
            } else {
                for (DataScopeResp dataScopeResp : dataScopeRespList) {
                    if (CollectionUtils.isNotEmpty(dataScopeResp.getDepts())) {
                        newDeptIds.addAll(StreamUtil.mapList(dataScopeResp.getDepts(), DataScopeResp.Dept::getDeptId));
                    }
                    if (CollectionUtils.isNotEmpty(dataScopeResp.getUserIds())) {
                        // FIXME 这里包括了在自己部门下的兼职的人员id，需要存疑
                        newUserIds.addAll(dataScopeResp.getUserIds());
                    }
                }
            }
        }

        Map<String, List<String>> returnMaps = new HashMap<>();
        returnMaps.put("userIds", newUserIds);
        returnMaps.put("deptIds", newDeptIds);
        return returnMaps;
    }


    private List<DataScopeResp> getDataScopes(DataScopeReq dataScopeReq, List<String> deptIds) {
        List<DataScopeResp> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(deptIds)) {
            DataScopeResp dataScopeResp = coreApiFacade.getDateScope(dataScopeReq);
            if (dataScopeResp != null) {
                result.add(dataScopeResp);
                return result;
            }
        }
        List<List<String>> splitList = Lists.partition(deptIds, 500);
        for (List<String> childList : splitList) {
            dataScopeReq.setDeptIds(childList);
            DataScopeResp dataScopeResp = coreApiFacade.getDateScope(dataScopeReq);
            if (dataScopeResp != null) {
                result.add(dataScopeResp);
            }
        }
        return result;
    }

    public List<String> findGroupPermission(String orgId, String userId) {
        DataScopeReq dataScopeReq = new DataScopeReq();
        dataScopeReq.setOrgId(orgId);
        dataScopeReq.setUserId(userId);
        dataScopeReq.setNavCode(GROUP_NAVCODE);
        dataScopeReq.setDataPermsCode(GROUP_PERMISSION_CODE);
        dataScopeReq.setProductCode(Constants.XXV2_PRODUCT_CODE);
        log.info("findGroupPermissionMsg core dataScopeReq={}", BeanHelper.bean2Json(dataScopeReq));
        List<DataScopeResp> dataScopeRespList = findGroupDataScopes(dataScopeReq);
        log.info("findGroupPermissionMsg core dataScopeRespList={}", BeanHelper.bean2Json(dataScopeRespList));
        List<String> newUserIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataScopeRespList)) {
            for (DataScopeResp dataScopeResp : dataScopeRespList) {
                if (CollectionUtils.isNotEmpty(dataScopeResp.getUserIds())) {
                    newUserIds.addAll(dataScopeResp.getUserIds());
                }
            }
        }
        return newUserIds;
    }


    private List<DataScopeResp> findGroupDataScopes(DataScopeReq dataScopeReq) {
        List<DataScopeResp> result = Lists.newArrayList();
        DataScopeResp dataScopeResp = coreApiFacade.getDateScope(dataScopeReq);
        if (dataScopeResp != null) {
            result.add(dataScopeResp);
            return result;
        }
        return result;
    }

}
