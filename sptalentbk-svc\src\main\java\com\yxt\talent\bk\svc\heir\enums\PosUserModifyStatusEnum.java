package com.yxt.talent.bk.svc.heir.enums;

/**
 * 继任状态
* <AUTHOR>
*  @date 2023/8/16
**/
public enum PosUserModifyStatusEnum {

    /**
     * 重置
     */
    INIT(-1),
    /**
     * 暂不处理
     */
    PENDING(0),
    /**
     * 已处理
     */
    MODIFY(1),
    ;

    private final Integer value;

    PosUserModifyStatusEnum(int value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
