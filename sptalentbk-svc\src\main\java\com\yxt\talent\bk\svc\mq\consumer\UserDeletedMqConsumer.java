package com.yxt.talent.bk.svc.mq.consumer;

import com.yxt.common.util.BeanHelper;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.bk.svc.heir.HeirPosUserService;
import com.yxt.talent.bk.svc.mq.constant.HeirRocketMqConstants;
import com.yxt.talent.bk.svc.pool.PoolUserService;
import com.yxt.talent.bk.svc.udp.bean.UdpUserDelSyncBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * UserSyncDeletedMqConsumer
 *
 * <AUTHOR> geyan
 * @Date 21/8/23 8:33 pm
 */
@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = HeirRocketMqConstants.GROUP_PREFIX + HeirRocketMqConstants.TOPIC_C_UDP_USER_DELETE,
        topic = HeirRocketMqConstants.TOPIC_C_UDP_USER_DELETE,
        consumeThreadNumber = 1, consumeTimeout = 30)
public class UserDeletedMqConsumer implements RocketMQListener<String> {

    @Autowired
    private HeirPosUserService heirPosUserService;
    @Autowired
    private PoolUserService poolUserService;
    @Override
    public void onMessage(String message) {
        log.info("UserSyncDeletedMqConsumer body={}", message);
        try {
            UdpUserDelSyncBean bean = BeanHelper.json2Bean(message, UdpUserDelSyncBean.class);
            if (bean != null) {
                heirPosUserService.handleUserDel(bean.getOrgId(), bean.getDatas());
                poolUserService.removeDelUser(bean.getOrgId(), bean.getDatas());
            }
        } catch (Exception e) {
            log.warn("UserSyncDeletedMqConsumer error: {}, {}", e.getMessage(), message);
        }
    }
}
