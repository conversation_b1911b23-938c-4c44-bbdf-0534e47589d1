package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.svc.heir.bean.CalcCycleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class CycleAndRemindUserResp {

    @Schema(description = "主键(岗位或部门id)")
    @JsonSerialize(using = ToStringSerializer.class)
    private String id;

    @Schema(description = "执行周期配置")
    private CalcCycleBean calcCycleBean;

    @Schema(description = "提醒周期配置")
    private CalcCycleBean remindCycleBean;

    @Schema(description = "提醒人员")
    private List<UdpUserBriefBean> udpUserBriefBeans;
}
