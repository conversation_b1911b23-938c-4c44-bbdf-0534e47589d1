package com.yxt.talent.bk.api.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.spsdk.logsave.LogSaveService;
import com.yxt.talent.bk.svc.common.DemoCopyService;
import com.yxt.talent.bk.svc.heir.HeirOrgLevelConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * CommonNotifyJob
 *
 * <AUTHOR> harleyge
 * @Date 17/7/24 5:21 pm
 */
@Slf4j
@Component
@AllArgsConstructor
public class CommonNotifyJob {
    private LogSaveService logSaveService;
    private HeirOrgLevelConfigService heirOrgLevelConfigService;
    private DemoCopyService demoCopyService;

    @XxlJob("errorLogNotifyJob")
    public ReturnT<String> errorLogNotify(String param) {
        demoCopyService.checkDemoCopyTimeout();
        logSaveService.dingNotice();
        return ReturnT.SUCCESS;
    }

    @XxlJob("fixRiskConfigRange")
    public ReturnT<String> fixRiskConfigRange(String param) {
        if (StringUtils.isNotEmpty(param)) {
            for (String orgId : param.split(",")) {
                orgId = orgId.trim();
                if (orgId.length() >= 32) {
                    heirOrgLevelConfigService.fixRiskRange(orgId);
                } else {
                    log.warn("invalid orgId {}", orgId);
                }
            }
        }
        return ReturnT.SUCCESS;
    }
}
