package com.yxt.talent.bk.svc.pool.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Schema(name ="人次池，测训项目加人")
public class PoolProject4User {

    @Schema(name = "人员id, 如果flag = 1,则排除userId里的人")
    private List<String> userIds;

    @Schema(name = "周期模式的开始时间 yyyy-MM-dd HH:mm,可以不传值")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date startTime;

    @Schema(name = "项目id")
    private String projectId;

    @Schema(name = "是否传人才池全部人员, 0-否， 1-是")
    private Integer flag = 0;

    @Schema(name = "人才池id")
    private String poolId;
}
