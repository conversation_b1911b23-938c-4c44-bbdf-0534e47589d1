package com.yxt.talent.bk.svc.udp;

import com.yxt.common.pojo.IdName;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.bk.core.udp.bean.UdpPositionBean;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.base.BkConfigService;
import com.yxt.talent.bk.svc.udp.rpc.UdpRpc;
import com.yxt.udpfacade.bean.org.OrgBean;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * UdpQueryService
 *
 * <AUTHOR> geyan
 * @Date 18/8/23 3:25 pm
 */
@Service
@AllArgsConstructor
@Slf4j
public class UdpQueryService {

    private final UdpRpc udpRpc;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final BkConfigService bkConfigService;

    public List<IdName> listPositionByIds(String orgId, List<String> posIds) {
        return udpRpc.queryPositionNameByIds(orgId, posIds);
    }
    /**
     * 查询岗位名称和用户数量
     * @param orgId
     * @param posIds
     * @return
     */
    public List<UdpPositionBean> listPositionStaByIds(String orgId, List<String> posIds) {
        List<IdName> positionNames = udpRpc.queryPositionNameByIds(orgId, posIds);
        List<UdpPositionBean> list = BeanCopierUtil.convertList(positionNames, positionName -> {
            UdpPositionBean udpPosition = new UdpPositionBean();
            udpPosition.setId(positionName.getId());
            udpPosition.setName(positionName.getName());
            return udpPosition;
        });
        if (!list.isEmpty()) {
            Map<String, UdpPositionBean> userQtyMap = StreamUtil.list2map(udpLiteUserRepository.queryPositionUserQty(
                            orgId, list.stream().map(UdpPositionBean::getId).collect(Collectors.toList())),
                UdpPositionBean::getId);
            for (UdpPositionBean udpPositionBean : list) {
                UdpPositionBean userQty = userQtyMap.get(udpPositionBean.getId());
                if (userQty != null) {
                    udpPositionBean.setUserCount(userQty.getUserCount());
                }
            }
        }
        return list;
    }

    public String demoCopyOrgId(String orgId) {
        if (StringUtils.isNotBlank(bkConfigService.getDemoOrgId())) {
            OrgBean orgBean = udpRpc.getOrgInfo(orgId);
            if (orgBean != null && Objects.equals(orgBean.getOrgType(), 2)
                && StringUtils.isNotBlank(orgBean.getSalesOrgTemplateId())) {
                return bkConfigService.getDemoOrgId().trim();
            }
        }
        return orgId;
    }

}
