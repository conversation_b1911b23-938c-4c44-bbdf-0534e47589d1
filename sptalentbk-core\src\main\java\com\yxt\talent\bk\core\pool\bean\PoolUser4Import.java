package com.yxt.talent.bk.core.pool.bean;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PoolUser4Import implements UdpLangSupport {
    private String id;

    private String userName;

    private String fullName;

    private String errMsg;

    private Integer status;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(id, fullName, this::setFullName);
    }
}
