package com.yxt.talent.bk.svc.search.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 人才透视Bean
 */
@Schema(name = "某透视维度结果")
@Data
public class UserSearchListAnalyseItemBean {
    @Schema(description="key（用于透视条件查询）",example = "18-22")
    private String key;
    @Schema(description="名称",example = "18-22岁")
    private String name;
    @Schema(description="结果值",example = "9527")
    private Long value;
    @JsonIgnore
    private long orderIndex;

}
