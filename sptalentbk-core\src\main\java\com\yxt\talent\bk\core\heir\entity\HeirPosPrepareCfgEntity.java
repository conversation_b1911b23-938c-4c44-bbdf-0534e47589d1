package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.SnowFlowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * HeirPosPrepareCfgEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:50 am
 */
@Data
@TableName("bk_heir_pos_prepare_cfg")
public class HeirPosPrepareCfgEntity extends SnowFlowIdEntity {
    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "bk_heir_pos pkId")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_POS_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String posId;

    @Schema(description = "0:岗位，1:部门")
    private Integer posType;

    @Schema(description = "准备度规则等级id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_ORG_LEVEL_CFG_ID)
    private Long prepareLevelId;

    @Schema(description = "规则配置id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.SPM_RULE_ID)
    private Long ruleCfgId;

    @Schema(description = "准备度规则配置数据")
    private String ruleCfgData;

    @Schema(description = "规则配置md5")
    private String ruleCfgMd5;
}
