package com.yxt.talent.bk.core.usergroup.bean;

import com.yxt.talent.bk.common.bean.udp.UdpLangSupport;
import com.yxt.talent.bk.common.bean.udp.UdpLangUnitBean;
import lombok.Data;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Data
public class UserGroupManagerBean implements UdpLangSupport {
    private String userId;
    private Long groupId;
    private String fullname;

    @Override
    public UdpLangUnitBean userLangProperty() {
        return new UdpLangUnitBean(userId, fullname, this::setFullname);
    }
}
