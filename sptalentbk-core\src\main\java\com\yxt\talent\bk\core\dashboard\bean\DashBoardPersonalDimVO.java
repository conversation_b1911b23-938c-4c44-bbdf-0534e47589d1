package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class DashBoardPersonalDimVO {

    @Schema(description = "用户ID")
    private String userId;
    @Schema(description = "第三方用户ID")
    private String thirdUserId;
    @Schema(description = "账号")
    private String username;
    @Schema(description = "姓名")
    private String fullName;
    @Schema(description = "用户能力达项标数")
    private long skillReachCount;
    @Schema(description = "用户能力达标率")
    private String skillReachRate;
    @Schema(description = "用户任务项达标数")
    private long taskReachCount;
    @Schema(description = "用户任务达标率")
    private String taskReachRate;
}
