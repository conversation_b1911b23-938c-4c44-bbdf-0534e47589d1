package com.yxt.talent.bk.core.usergroup.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @program: sptalentbkapi
 * @description:
 **/
@Data
public class UserRuleDetail4Get {

    @Schema(description = "标签搜索类型，1：交集，2：并集", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer tagSearchType;

    @Schema(description = "基础搜索类型，1：交集，2：并集", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer baseSearchType;

    @Schema(description = "部门名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    List<String> deptNames;

    @Schema(description = "岗位名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    List<String> positionNames;

    @Schema(description = "职级名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    List<String> gradeNames;

    @Schema(description = "用户名称", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    List<String> userNames;

    @Schema(description = "所选标签", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    List<UserRuleDetailTag4Get> tags;

}
