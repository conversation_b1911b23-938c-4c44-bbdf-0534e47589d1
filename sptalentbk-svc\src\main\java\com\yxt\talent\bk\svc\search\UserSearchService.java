package com.yxt.talent.bk.svc.search;
import cn.hutool.core.lang.Pair;
import com.yxt.common.Constants;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.DateUtil;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.DlcComponent;
import com.yxt.export.ExcelUtil;
import com.yxt.export.OutputStrategy;
import com.yxt.talent.bk.common.bean.UserSearchDateRangeBean;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.constants.UserSearchConstants;
import com.yxt.talent.bk.common.es.ESQueryConveter;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.common.utils.FillBeanUtil;
import com.yxt.talent.bk.common.utils.UserSearchUtils;
import com.yxt.talent.bk.core.search.bean.UserSearchAnalyseDimensionBean;
import com.yxt.talent.bk.core.tag.bean.TagValueBean4UserSearch;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.entity.TagValueEntity;
import com.yxt.talent.bk.core.tag.mapper.TagValueMapper;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.core.tag.repo.TagValueRepository;
import com.yxt.talent.bk.svc.base.CoreFactorService;
import com.yxt.talent.bk.svc.search.bean.*;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean4Dept;
import com.yxt.udpfacade.bean.dept.DeptBean;
import com.yxt.udpfacade.bean.dept.DeptBean4Search;
import com.yxt.udpfacade.service.UdpFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.range.DateRangeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.valuecount.ValueCount;
import org.elasticsearch.search.aggregations.metrics.valuecount.ValueCountAggregationBuilder;
import org.springframework.data.domain.Page;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilterBuilder;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SourceFilter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.elasticsearch.index.query.QueryBuilders.termQuery;


@Service
@RequiredArgsConstructor
@Slf4j
public class UserSearchService {
    public static final String DATA_PERMISSION_CODE = "sp_file_userView_extent";
    public static final String NAV_CODE = "sp_gwnl_file_search";
    private static final int MAX_LEASE_TIME = 60 * 5;
    private static final long EXPORT_MAX_SIZE = 30000;
    private static final int MAX_LIMIT = 99999;
    private static final String BUCKET_PAGING = "paging";
    private static final String BUCKET_PAGINGTOTAL = "pagingTotal";
    private static final String ES_TOO_MANY_MSG = "Elasticsearch exception [type=search_phase_execution_exception, reason=all shards failed]";
    private final ElasticsearchRestTemplate esTemplate;
    private final TagRepository tagRepository;
    private final TagValueMapper tagValueMapper;
    private final TagValueRepository tagValueRepository;
    private final ILock lockService;
    private final DlcComponent dlcComponent;
    private final CoreFactorService coreFactorService;
    private final UserAuthService userAuthService;
    private final UdpFacade udpFacade;
    private final SearchAnalyseDimensionService searchAnalyseDimensionService;
    private final RedisRepository talentRedisRepository;
    private final ESQueryConveter esQueryConveter;

    /**
     * 导出查询结果
     *
     * @param userCache
     * @param search
     * @return
     */
    public Map<String, String> exportResult(UserCacheBasic userCache, UserSearchBean search) {

        String lockKey = String.format(
                TalentBkRedisKeys.CACHE_KEY_USER_SEARCH_RESULT_EXPORT, userCache.getOrgId(),
                userCache.getUserId());

        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                return doExportResult(userCache, search);
            } catch (ElasticsearchException e) {
                boolean eb = null != e.getDetailedMessage() && e.getDetailedMessage().contains(ES_TOO_MANY_MSG);
                if (eb) {
                    throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_USERSEARCH_TOO_MANY_CLAUSES);
                }
                log.error("{}.exportResult -> ", this.getClass().getName(), e);
            } catch (Exception e) {
                log.error("exportResultException", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }

        return new HashMap<>();
    }

    /**
     * 导出策略
     *
     * @param isRv 是否开通人才盘点
     * @return
     */
    private OutputStrategy getOutputStrategy(boolean isRv) {
        String excelPath = isRv ?
                ExportConstants.EXPORT_USER_SEARCH_RV_TEMPLATE_PATH :
                ExportConstants.EXPORT_USER_SEARCH_TEMPLATE_PATH;
        return new OutputStrategy() {
            @Override
            public String write(String path, String fileName, Object data) throws IOException {
                String filePath = path + fileName;
                ExcelUtil.exportWithTemplate(FillBeanUtil.generationFillBenList(data), filePath, excelPath);
                return fileName;
            }

            @Override
            public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
                return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                        .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE)
                        .appCode(ModuleConstants.APP_CODE).moduleCode(ModuleConstants.MODULE_CODE).fileName(fileName)
                        .name("员工信息导出").build();
            }
        };
    }

    private Map<String, String> doExportResult(UserCacheBasic userCache, UserSearchBean search) {

        //将部门id 转换为部门Name
        deptIdToDeptName(userCache.getOrgId(), search, null);
        // 自定义普通标签、分层标签 转Id查询
        ordinaryTagNameToId(userCache.getOrgId(), null, search);

        //查询数据
        PageRequest pageRequest = new PageRequest();
        pageRequest.setCurrent(1);
        pageRequest.setSize(EXPORT_MAX_SIZE);

        NativeSearchQuery nativeSearchQuery = generateListSearchQuery(pageRequest, userCache, search);
        log.debug("LOG11200:{}{}", System.lineSeparator(), esQueryConveter.extractJson(nativeSearchQuery));
        long count = esTemplate.count(nativeSearchQuery, UserSearchListBean.class);
        if (count > EXPORT_MAX_SIZE) {
            throw new ApiException("apis.talentbk.usersearch.export.overrange");
        }

        List<UserSearchListBean> userSearchList = esTemplate.queryForList(nativeSearchQuery, UserSearchListBean.class);
        List<Object> objList = new ArrayList<>();
        objList.add(getExportInfo(userSearchList));
        //是否开通人才盘点
        boolean isRv = coreFactorService.isOpenRv(userCache.getOrgId());
        OutputStrategy outputStrategy = getOutputStrategy(isRv);

        String fileName = "员工信息导出_" + System.currentTimeMillis() + ExportConstants.FILE_SUFFIX_XLSX;
        long taskId = dlcComponent.prepareExport(fileName, outputStrategy);
        String path = dlcComponent.upload2Disk(fileName, objList, outputStrategy, taskId);

        Map<String, String> result = new HashMap<>(1);
        result.put(ExportConstants.EXPORT_URL_KEY, path);
        return result;
    }

    /**
     * 导出数据处理
     */
    private List<UserSearch4ExportBean> getExportInfo(List<UserSearchListBean> userSearchList) {
        List<UserSearch4ExportBean> list = BeanCopierUtil
                .convertList(userSearchList, UserSearchListBean.class, UserSearch4ExportBean.class);
        // 处理司龄为  2.9 ,3.6 年
        list.forEach(a -> {
            if (StringUtils.isNotEmpty(a.getEntryDate())) {
                Date date = DateUtil.formatDate(a.getEntryDate());
                String entryYear = DateUtils.getEntryYear(date);
                a.setEntryDate(entryYear);
            }
            a.setUserStatusStr(a.getUserStatus() != null && a.getUserStatus().intValue() == 0 ? "已禁用":"已启用");
        });
        return list;
    }

    /**
     * 人才搜索列表
     *
     * @param pageRequest
     * @param userCache
     * @param search
     * @return
     */
    public PagingList<UserSearchListBean> findPage(PageRequest pageRequest, UserCacheBasic userCache,
            UserSearchBean search) {
        PagingList<UserSearchListBean> result = new PagingList<>();

        try {
            doFindPage(pageRequest, userCache, search, result);
        } catch (ElasticsearchException e) {
            log.error("LOG10570:", e);
            boolean eb = null != e.getDetailedMessage() && e.getDetailedMessage().contains(ES_TOO_MANY_MSG);
            if (eb) {
                throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_USERSEARCH_TOO_MANY_CLAUSES);
            }
            log.error("LOG10560:{}.findPage -> ", this.getClass().getName(), e);
        }

        // 返回结果
        return result;
    }

    private void doFindPage(PageRequest pageRequest, UserCacheBasic userCache, UserSearchBean search,
            PagingList<UserSearchListBean> result) {

        //初始化PagingList，用于异常情况时 保证前端正常
        Paging paging = new Paging();
        paging.setOffset((pageRequest.getCurrent() - 1) * pageRequest.getSize());
        paging.setLimit(pageRequest.getSize());
        paging.setCount(0);
        paging.setPages(0);
        result.setDatas(new ArrayList<>());
        result.setPaging(paging);

        // 自定义普通标签、分层标签 转Id查询
        ordinaryTagNameToId(userCache.getOrgId(), null, search);
        //将部门id 转换为部门Name
        deptIdToDeptName(userCache.getOrgId(), search, null);
        //查询数据
        NativeSearchQuery nativeSearchQuery = generateListSearchQuery(pageRequest, userCache, search);
        log.debug("LOG11170:{}{}", System.lineSeparator(), esQueryConveter.extractJson(nativeSearchQuery));
        Page<UserSearchListBean> page = esTemplate.queryForPage(nativeSearchQuery, UserSearchListBean.class);

        // 处理司龄为  2.9 ,3.6 年
        page.getContent().forEach(a -> {
            if (StringUtils.isNotEmpty(a.getEntryDate())) {
                Date date = DateUtil.formatDate(a.getEntryDate());
                String entryYear = DateUtils.getEntryYear(date);
                a.setEntryDate(entryYear);
            }
            // 处理部门名称为短mingc
            if (StringUtils.isNotEmpty(a.getDepartmentName())) {
                a.setDepartmentName(UserSearchUtils.getShortDeptName(a.getDepartmentName()));
            }
        });

        //覆盖PagingList、并设置结果
        paging.setCount(page.getTotalElements());
        paging.setPages(page.getTotalPages());
        result.setDatas(page.getContent());
        result.setPaging(paging);
    }

    /**
     * 普通标签、分层标签，转Id查询
     */
    private void ordinaryTagNameToId(String orgId, List<UserSearchListAnalyseBean> analyseBeans,
            UserSearchBean search) {
        // 单独抽出，
        Map<String, List<TagValueBean4UserSearch>> tagValueMap = getTagValueMap(orgId, analyseBeans, search);
        if (CollectionUtils.isNotEmpty(search.getStandardTags())) {
            search.getStandardTags().forEach(a -> {
                List<TagValueBean4UserSearch> valueList = tagValueMap.get(a.getItemKey());
                if (CollectionUtils.isEmpty(valueList)) {
                    return;
                }

                List<String> tagValueIds = new ArrayList<>();
                Map<String, String> map = valueList.stream().collect(
                        Collectors.toMap(TagValueBean4UserSearch::getValueName, TagValueBean4UserSearch::getId));
                List<String> values = a.getValues();
                values.forEach(value -> tagValueIds.add(map.get(value)));

                a.setValues(tagValueIds);

            });
        }
        if (CollectionUtils.isNotEmpty(search.getOrdinaryTags())) {
            search.getOrdinaryTags().forEach(a -> {
                List<TagValueBean4UserSearch> valueList = tagValueMap.get(a.getItemKey());
                if (CollectionUtils.isEmpty(valueList)) {
                    return;
                }

                Map<String, String> map = valueList.stream().collect(
                        Collectors.toMap(TagValueBean4UserSearch::getValueName, TagValueBean4UserSearch::getId));
                a.setValue(map.get(a.getValue()));
            });
        }
    }

    /**
     * 获取透视与查询的分层、普通标签值
     * Map<tageKey, List<TagValueBean4UserSearch>>
     *
     * @param orgId
     * @param analyseBeans
     * @param search
     * @return
     */
    private Map<String, List<TagValueBean4UserSearch>> getTagValueMap(String orgId,
            List<UserSearchListAnalyseBean> analyseBeans, UserSearchBean search) {
        Map<String, List<TagValueBean4UserSearch>> result = new HashMap<>();
        Set<String> tagKeys = new HashSet<>();
        if (search != null && CollectionUtils.isNotEmpty(search.getStandardTags())) {
            search.getStandardTags().forEach(a -> tagKeys.add(a.getItemKey()));
        }
        if (search != null && CollectionUtils.isNotEmpty(search.getOrdinaryTags())) {
            search.getOrdinaryTags().forEach(a -> tagKeys.add(a.getItemKey()));
        }
        if (CollectionUtils.isNotEmpty(analyseBeans)) {
            analyseBeans.forEach(a -> tagKeys.add(a.getDimensionKey()));
        }

        if (CollectionUtils.isNotEmpty(tagKeys)) {
            List<TagValueBean4UserSearch> tagValueBeans = tagValueMapper.findByOrgIdAndTagKeys(orgId, tagKeys);
            if (CollectionUtils.isNotEmpty(tagValueBeans)) {
                result = tagValueBeans.stream().collect(Collectors.groupingBy(TagValueBean4UserSearch::getTagKey));
            }
        }
        return result;
    }

    /**
     * 人才维度透视
     *
     * @param limit
     * @param userCache
     * @param search
     * @return
     */
    public List<UserSearchListAnalyseBean> findAnalyseList(UserCacheBasic userCache, UserSearchBean search, int limit,
            int offset, String dimensionKey) {
        List<UserSearchListAnalyseBean> result = new ArrayList<>();
        try {
            result = doFindAnalyseList(userCache, search, limit, offset, dimensionKey);
        } catch (ElasticsearchException e) {
            boolean eb = null != e.getDetailedMessage() && e.getDetailedMessage().contains(ES_TOO_MANY_MSG);
            if (eb) {
                throw new ApiException(BkApiErrorKeys.APIS_TALENTBK_USERSEARCH_TOO_MANY_CLAUSES);
            }
            log.error("{}.findAnalyseList -> ", this.getClass().getName(), e);
        }

        return result;
    }

    /**
     * 人才维度透视
     *
     * @param limit
     * @param userCache
     * @param search
     * @return
     */
    private List<UserSearchListAnalyseBean> doFindAnalyseList(UserCacheBasic userCache, UserSearchBean search,
            int limit, int offset, String dimensionKey) {

        // 透视上下文变量设置
        UserSearchAnalyseContext context = new UserSearchAnalyseContext();
        // 分页参数
        context.setLimit(limit);
        // 分页参数
        context.setOffset(offset);
        // 执行分页的透视维度
        context.setDimensionKey(dimensionKey);
        context.setUserId(userCache.getUserId());
        context.setOrgId(userCache.getOrgId());
        context.setSearch(search);

        // 该机构透视维度
        List<UserSearchAnalyseDimensionBean> adList = searchAnalyseDimensionService
                .findByOrgId(userCache.getOrgId(), userCache.getUserId());
        context.setAdList(adList);
        // 执行分页的透视维度处理
        preparePagingDimension(context);
        // 如果含有盘点的透视维度、需要判断是否开通盘点
        prepareRvTag(context);
        // 自定义普通标签、分层标签 转Id查询 // 查询限制为客户创建的普通标签和分层标签
        ordinaryTagNameToId(context.getOrgId(), null, context.getSearch());
        //将部门id 转换为部门Name
        deptIdToDeptName(context.getOrgId(), context.getSearch(), context.getAdList());
        //用于最后的排序,代码位置不能变
        Map<String, Integer> orderMap = context.getAdList().stream().collect(Collectors
                .toMap(UserSearchAnalyseDimensionBean::getItemKey, UserSearchAnalyseDimensionBean::getOrderIndex,
                        (o1, o2) -> o2));
        context.setDimensionOrderMap(orderMap);
        // 透视维度中，分拣客户自定义普通标签(分拣后设置到Context中的adOrdinaryList中)，客户自定义标签透视需要特殊处理
        prepareAnalyseTag(context);
        // 透视维度中，有部门且搜索条件中选择了部门，则透视按照已选择部门进行
        prepareDeptTag(context);
        // 透视维度中，有岗位且搜索条件中选择了岗位，则透视按照已选择岗位进行
        preparePositionTag(context);
        // 透视维度中，有职级且搜索条件中选择了职级，则透视按照已选择职级进行
        prepareGradeTag(context);

        // 条件生成、透视聚合生成
        NativeSearchQuery nativeSearchQuery = generateAnalyseSearchQuery(context);
        if (nativeSearchQuery == null) {
            return new ArrayList<>();
        }
        // 聚合查询ES
        log.debug("LOG11150:{}{}", System.lineSeparator(), esQueryConveter.extractJson(nativeSearchQuery));
        AggregatedPage<UserSearchListBean> aggregatedPage = esTemplate
                .queryForPage(nativeSearchQuery, UserSearchListBean.class);
        // 设置结果处理需要的上下文数据
        context.setAggregations(aggregatedPage.getAggregations());
        // 结果处理
        analyseResultDispose(context);

        return context.getResult();
    }

    private void queryAggregatedPage(UserSearchAnalyseContext context, NativeSearchQuery nativeSearchQuery) {
        String cacheKey = String
                .format(TalentBkRedisKeys.CACHE_KEY_USER_SEARCH_AGGREGATED, context.getOrgId(), context.getUserId());
        if (StringUtils.isNotBlank(context.getDimensionKey())) {
            String valueByKey = talentRedisRepository.getValueByKey(cacheKey);
            if (StringUtils.isNotEmpty(valueByKey)) {
                AggregatedPage<UserSearchListBean> aggregatedPage = BeanHelper
                        .json2Bean(valueByKey, AggregatedPage.class);
                // 设置结果处理需要的上下文数据
                context.setAggregations(aggregatedPage.getAggregations());
                return;
            }
        }

        AggregatedPage<UserSearchListBean> aggregatedPage = esTemplate
                .queryForPage(nativeSearchQuery, UserSearchListBean.class);
        // 设置结果处理需要的上下文数据
        context.setAggregations(aggregatedPage.getAggregations());

        // 设置缓存并设置时间
        if (StringUtils.isBlank(context.getDimensionKey())) {
            talentRedisRepository.setValue(cacheKey, BeanHelper.bean2Json(aggregatedPage), 30, TimeUnit.MINUTES);
        }

    }


    /**
     * 聚合结果处理 <br>
     * 如: 聚合结果转换、排序、附加0值等
     *
     * @param context
     */
    private void analyseResultDispose(UserSearchAnalyseContext context) {
        // 聚合结果 转换为 result
        transformAnalyseResult(context);
        //分层标签是按id聚合，需要把id转成看得懂的文字
        anslyseResultTagValueIdToName(context);
        // 标签排序；
        List<UserSearchListAnalyseBean> result = context.getResult().stream()
                .sorted(Comparator.comparing(UserSearchListAnalyseBean::getOrderIndex)).collect(Collectors.toList());
        context.setResult(result);
        //设置上下文变量中的 tagValueMap、tagValueMap, 变更代码位置需谨慎
        setContextForTagKeyMap(context);
        // 聚合的标签在ES中没有时，需要展示0，所以在此附加
        analyseReplenishZero(context);
        // 聚合的标签在ES中没有时，需要展示0，所以在此附加
        analyseReplenishZeroForSpecialTag(context);
        // 标签值排序
        analyseDimensionItemSort(context);
        // 全路径部门转换
        analyseResultShortDeptName(context);
    }

    /**
     * 分层标签是按id聚合，需要把id转成看得懂的文字
     *
     * @param context
     */
    private void anslyseResultTagValueIdToName(UserSearchAnalyseContext context) {
        // 分层标签是按id聚合，需要把id转成看得懂的文字
        Map<String, List<TagValueBean4UserSearch>> tagValueMap = getTagValueMap(context.getOrgId(), context.getResult(),
                null);
        context.getResult().forEach(bean -> {
            List<TagValueBean4UserSearch> tagValueList = tagValueMap.get(bean.getDimensionKey());
            if (CollectionUtils.isNotEmpty(tagValueList)) {
                Map<String, String> valueNameMap = tagValueList.stream().collect(
                        Collectors.toMap(TagValueBean4UserSearch::getId, TagValueBean4UserSearch::getValueName));
                if (CollectionUtils.isNotEmpty(bean.getItems().getDatas())) {
                    bean.getItems().getDatas().forEach(a -> {
                        if (!valueNameMap.containsKey(a.getName())) {
                            return;
                        }
                        a.setName(valueNameMap.get(a.getName()));
                    });
                }
            }
        });
    }

    private void analyseResultShortDeptName(UserSearchAnalyseContext context) {
        List<UserSearchListAnalyseBean> result = context.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        result.forEach(a -> {
            if (!UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue().equalsIgnoreCase(a.getDimensionKey())) {
                return;
            }

            List<UserSearchListAnalyseItemBean> items = a.getItems().getDatas();
            if (CollectionUtils.isEmpty(items)) {
                return;
            }
            items.forEach(item -> item.setName(UserSearchUtils.getShortDeptName(item.getName())) // 转为短部门名称
            );
        });

    }

    /**
     * 设置上下文变量中的 tagValueMap、tagValueMap
     *
     * @param context
     */
    private void setContextForTagKeyMap(UserSearchAnalyseContext context) {

        List<String> keyList = context.getResult().stream().map(UserSearchListAnalyseBean::getDimensionKey)
                .collect(Collectors.toList());
        List<TagEntity> tagByTagKeys = tagRepository.findTagByTagKeys(context.getOrgId(), keyList);
        if (CollectionUtils.isEmpty(tagByTagKeys)) {
            return;
        }

        List<String> tagIds = tagByTagKeys.stream().map(TagEntity::getId).collect(Collectors.toList());
        List<TagValueEntity> tagValues = tagValueRepository.findByTagIds(context.getOrgId(), tagIds);
        if (CollectionUtils.isEmpty(tagValues)) {
            return;
        }

        Map<String, List<TagValueEntity>> tagValMap = tagValues.stream()
                .collect(Collectors.groupingBy(TagValueEntity::getTagId));
        Map<String, String> tagKeyMap = tagByTagKeys.stream()
                .collect(Collectors.toMap(TagEntity::getTagKey, TagEntity::getId, (o1, o2) -> o2));

        context.setTagKeyMap(tagKeyMap);
        context.setTagValueMap(tagValMap);

    }

    private List<UserSearchListAnalyseItemBean> bucketConvertToAnalyseItem(UserSearchAnalyseDimensionBean ad,
                                                                           List<? extends MultiBucketsAggregation.Bucket> buckets) {
        List<UserSearchListAnalyseItemBean> itemBeanList = new ArrayList<>();
        buckets.forEach(bucket -> {
            // 过滤掉 标签为空的聚合， 岗位仅有分隔符，认为是空值，这个可以注调了，已经在聚合中处理
            if (StringUtils.isBlank(bucket.getKeyAsString()) || UserSearchConstants.POSITION_AGGS_SPLIT_STR
                    .equals(bucket.getKeyAsString())) {
                return;
            }
            UserSearchListAnalyseItemBean tempItem = new UserSearchListAnalyseItemBean();
            tempItem.setKey(bucket.getKeyAsString());
            tempItem.setValue(bucket.getDocCount());
            tempItem.setOrderIndex(bucket.getDocCount() * -1);
            if (ad.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.BIRTHDAY.getValue()) || ad
                    .getItemKey().equalsIgnoreCase(UserSearchConstants.XxGroupTagEnum.AGE_GROUP.getKey())) {
                // 转换为 xx-xx岁
                tempItem.setName(UserSearchConstants.getAgeRangeKVEnumMap().get(bucket.getKeyAsString()));
            } else if (ad.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.SEX.getValue())) {
                // 转为男女
                tempItem.setName(UserSearchConstants.getSexEnumMap().get(bucket.getKeyAsString()));
            } else if (UserSearchConstants.EsSpecialLabel.POSITION_ID.getValue()
                    .equalsIgnoreCase(ad.getItemKey())) {
                // 岗位聚合结果处理<br>
                // 岗位采用脚本聚合，聚合采用(name+id)方式， 所以返回给前端时候，需要去除id。
                String[] split = bucket.getKeyAsString().split(UserSearchConstants.POSITION_AGGS_SPLIT_STR);
                if (split.length > 0 && "null".equals(split[0])) {
                    return;
                }
                tempItem.setName(split.length > 0 ? split[0] : "");
                tempItem.setKey(split.length > 1 ? split[1] : "");
            } else {
                tempItem.setName(bucket.getKeyAsString());
            }
            itemBeanList.add(tempItem);
        });
        return itemBeanList;
    }


    /**
     * 聚合结果转换处理
     *
     * @param context
     */
    private void transformAnalyseResult(UserSearchAnalyseContext context) {

        List<UserSearchListAnalyseBean> finalResult = context.getResult();
        context.getAdList().forEach(ad -> {
            UserSearchListAnalyseBean analyse = new UserSearchListAnalyseBean();
            analyse.setDimensionKey(ad.getItemKey());
            analyse.setDimensionName(ad.getItemName());
            analyse.setOrderIndex(context.getDimensionOrderMap().get(ad.getItemKey()));
            MultiBucketsAggregation aggTerms = null;
            Aggregation aggregation = context.getAggregations().get(ad.getItemKey());
            if (aggregation instanceof ParsedFilter) {
                ParsedFilter parsedFilter = (ParsedFilter) aggregation;
                aggTerms = parsedFilter.getAggregations().get(ad.getItemKey());

                /**
                 ParsedCardinality pagingTotal = parsedFilter.getAggregations().get("pagingTotal");
                 if(null != pagingTotal ){
                 totalCount = pagingTotal.getValue();
                 }
                 **/
            } else {
                aggTerms = context.getAggregations().get(ad.getItemKey());
                /**
                 ParsedCardinality pagingTotal = context.getAggregations().get("pagingTotal");
                 if(null != pagingTotal ){
                 totalCount = pagingTotal.getValue();
                 }
                 **/
            }

            if (aggTerms == null) {
                return;
            }
            analyse.getItems().setDatas(bucketConvertToAnalyseItem(ad, aggTerms.getBuckets()));
            finalResult.add(analyse);
        });

        // 处理自定义普通标签
        if (CollectionUtils.isNotEmpty(context.getAdOrdinaryList())) {
            UserSearchListAnalyseBean analyse = new UserSearchListAnalyseBean();
            analyse.setDimensionKey(UserSearchConstants.EsSpecialLabel.ORDINARY_TAG.getValue());
            analyse.setDimensionName(UserSearchConstants.EsSpecialLabel.ORDINARY_TAG.getName());
            analyse.setOrderIndex(
                    context.getDimensionOrderMap().get(UserSearchConstants.EsSpecialLabel.ORDINARY_TAG.getValue()));
            List<UserSearchListAnalyseItemBean> itemBeanList = new ArrayList<>();
            context.getAdOrdinaryList().forEach(ad -> {
                UserSearchListAnalyseItemBean tempItem = new UserSearchListAnalyseItemBean();
                // 客户自定义普通标签
                ParsedFilter parsedFilter = context.getAggregations().get(ad.getItemKey());
                ValueCount valueCount = parsedFilter.getAggregations().get(ad.getItemKey());

                tempItem.setKey(ad.getItemKey());
                tempItem.setValue(valueCount.getValue());
                tempItem.setName(ad.getItemName());
                tempItem.setOrderIndex(valueCount.getValue() * -1);
                itemBeanList.add(tempItem);
            });
            analyse.getItems().setDatas(itemBeanList);
            context.getResult().add(analyse);
        }
    }

    /**
     * 聚合结果维度项排序
     */
    private void analyseDimensionItemSort(UserSearchAnalyseContext context) {
        //设置排序字段
        analyseDimensionItemSortSub(context);
        for (UserSearchListAnalyseBean temp : context.getResult()) {
            List<UserSearchListAnalyseItemBean> items = temp.getItems().getDatas();
            if (CollectionUtils.isEmpty(items)) {
                return;
            }

            items = items.stream().sorted(Comparator.comparing(UserSearchListAnalyseItemBean::getOrderIndex))
                    .collect(Collectors.toList());
            temp.getItems().setDatas(items);
        }
    }

    /**
     * 透视维度项，在ES中不存在时，补充0，
     * <br>示例： 男：20，女：0
     *
     * @param context
     */
    private void analyseReplenishZero(UserSearchAnalyseContext context) {

        if (context.getTagKeyMap().isEmpty() || context.getTagValueMap().isEmpty()) {
            return;
        }

        for (int i = 0; i < context.getResult().size(); i++) {
            UserSearchListAnalyseBean temp = context.getResult().get(i);
            if (UserSearchConstants.XxGroupTagEnum.AGE_GROUP.getKey().equals(temp.getDimensionKey())
                    || UserSearchConstants.XxGroupTagEnum.WORK_DATE_GROUP.getKey().equals(temp.getDimensionKey())) {
                continue;
            }
            String tagId = context.getTagKeyMap().get(temp.getDimensionKey());
            List<String> itemKeyNames = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(temp.getItems().getDatas())) {
                itemKeyNames = temp.getItems().getDatas().stream().map(UserSearchListAnalyseItemBean::getName)
                        .collect(Collectors.toList());
            }

            List<TagValueEntity> tagValueEntityList = context.getTagValueMap().get(tagId);
            if (CollectionUtils.isEmpty(tagValueEntityList)) {
                continue;
            }


            List<UserSearchListAnalyseItemBean> items = temp.getItems().getDatas();
            for (TagValueEntity value : tagValueEntityList) {
                if (itemKeyNames.contains(value.getValueName())) {
                    continue;
                }
                UserSearchListAnalyseItemBean itemBean = new UserSearchListAnalyseItemBean();
                itemBean.setOrderIndex(Integer.MAX_VALUE);
                itemBean.setKey(value.getValueName());
                itemBean.setName(value.getValueName());
                itemBean.setValue(0L);
                items.add(itemBean);
            }

            // 生成假分页信息
            int totalCount = items.size();
            Paging paging = new Paging();
            paging.setOffset(context.getOffset());
            paging.setLimit(context.getLimit());
            paging.setCount(totalCount);
            if (totalCount > 0) {
                int pages = (totalCount / context.getLimit()) + (totalCount % context.getLimit() == 0 ? 0 : 1);
                paging.setPages(pages);
            } else {
                paging.setPages(0);
            }
            // 分页信息
            temp.getItems().setPaging(paging);

            int start = context.getOffset();
            int end = context.getOffset() + context.getLimit();
            end = end > totalCount ? totalCount : end;
            items = context.getOffset() >= totalCount ? new ArrayList<>() : items.subList(start, end);

            temp.getItems().setDatas(items);
        }

    }

    /**
     * 透视维度项，在ES中不存在时，补充0 (部门、岗位、职级、普通标签)
     * <br>示例： 职级1：20，职级2：0
     *
     * @param context
     */
    private void analyseReplenishZeroForSpecialTag(UserSearchAnalyseContext context) {
        // 部门、职级、普通标签 补0
        for (int i = 0; i < context.getResult().size(); i++) {
            UserSearchListAnalyseBean temp = context.getResult().get(i);
            List<UserSearchListAnalyseItemBean> items = temp.getItems().getDatas();
            // 非部门、职级、普通标签
            if (!UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue().equalsIgnoreCase(temp.getDimensionKey())
                    && !UserSearchConstants.EsSpecialLabel.POSITION_ID.getValue()
                    .equalsIgnoreCase(temp.getDimensionKey()) && !UserSearchConstants.EsSpecialLabel.GRADE_NAME
                    .getValue().equalsIgnoreCase(temp.getDimensionKey())
                    && !UserSearchConstants.EsSpecialLabel.ORDINARY_TAG.getValue()
                    .equalsIgnoreCase(temp.getDimensionKey())) {
                continue;
            }

            List<String> itemKeyNames = BeanCopierUtil.convertList(items, UserSearchListAnalyseItemBean::getName);

            List<String> dimensionItemList = null;
            if (UserSearchConstants.EsSpecialLabel.ORDINARY_TAG.getValue().equalsIgnoreCase(temp.getDimensionKey())) {
                dimensionItemList = context.getAdOrdinaryList().stream()
                        .map(UserSearchAnalyseDimensionBean::getItemName).collect(Collectors.toList());
            } else {
                Map<String, List<String>> dimensionItemMap = context.getAdList().stream().collect(Collectors
                        .toMap(UserSearchAnalyseDimensionBean::getItemKey,
                                a -> (null == a.getValues() ? new ArrayList<>() : a.getValues())));
                dimensionItemList = dimensionItemMap.get(temp.getDimensionKey());
            }

            // 附件0值到列表中 --岗位除外
            if (!UserSearchConstants.EsSpecialLabel.POSITION_ID.getValue().equalsIgnoreCase(temp.getDimensionKey())) {
                List<String> finalItemKeyNames = itemKeyNames;
                List<String> zeroList = dimensionItemList.stream().filter(a -> !finalItemKeyNames.contains(a))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(zeroList)) {
                    for (String a : zeroList) {
                        UserSearchListAnalyseItemBean itemBean = new UserSearchListAnalyseItemBean();
                        itemBean.setOrderIndex(Integer.MAX_VALUE);
                        itemBean.setKey(a);
                        itemBean.setName(a);
                        itemBean.setValue(0L);
                        items.add(itemBean);
                    }
                }
            }

            if (UserSearchConstants.EsSpecialLabel.ORDINARY_TAG.getValue().equalsIgnoreCase(temp.getDimensionKey())) {
                items = items.stream().sorted(Comparator.comparing(UserSearchListAnalyseItemBean::getOrderIndex))
                        .collect(Collectors.toList());
            }

            // 生成假分页信息
            Pair<Paging, List<UserSearchListAnalyseItemBean>> pagePair = CommonUtils.simulatePaging(items, context.getOffset(), context.getLimit());
            // 分页信息
            temp.getItems().setPaging(pagePair.getKey());
            // 设置该页数据
            temp.getItems().setDatas(pagePair.getValue());

            /**
             if(paging != null && CollectionUtils.isNotEmpty(zeroList)){
             // 设置分页数据
             boolean b = paging.getCount() - paging.getOffset() < paging.getLimit() ;
             if(b){
             // 算法
             long startIndex = ( paging.getCount() - paging.getOffset() ) > 0 ? 0 : Math.abs( paging.getCount() - paging.getOffset() );
             long endIndex = paging.getLimit() - (paging.getCount() - paging.getOffset()) -1 ;
             // 将零值维度附加到列表中
             if(zeroList.size() > endIndex){
             for(int x = (int)startIndex; x <= endIndex; x++){
             UserSearchListAnalyseItemBean itemBean = new UserSearchListAnalyseItemBean();
             itemBean.setOrderIndex(Integer.MAX_VALUE);
             itemBean.setKey(zeroList.get(x));
             itemBean.setName(zeroList.get(x));
             itemBean.setValue(0L);
             items.add(itemBean);
             }
             }
             }

             // 重置总页数、总条数等
             paging.setCount(paging.getCount() + zeroList.size());
             if(paging.getCount() > 0){
             long pages = (paging.getCount()/context.getLimit()) + (paging.getCount() % context.getLimit() == 0 ? 0 : 1);
             paging.setPages(pages);
             }
             }
             **/

        }

    }

    private void analyseDimensionItemSortSub(UserSearchAnalyseContext context) {

        for (UserSearchListAnalyseBean temp : context.getResult()) {
            if (CollectionUtils.isEmpty(temp.getItems().getDatas())) {
                continue;
            }
            String tagId = context.getTagKeyMap().get(temp.getDimensionKey());
            if (StringUtils.isEmpty(tagId)) {
                continue;
            }
            List<TagValueEntity> tagValueEntityList = context.getTagValueMap().get(tagId);
            Map<String, Integer> orderIndexMap = tagValueEntityList.stream().collect(
                    Collectors.toMap(TagValueEntity::getValueName, TagValueEntity::getOrderIndex, (o1, o2) -> o2));
            temp.getItems().getDatas().forEach(a -> {
                if (orderIndexMap.containsKey(a.getName())) {
                    a.setOrderIndex(orderIndexMap.get(a.getName()));
                }
            });
        }
    }

    /**
     * 分拣客户自定义普通标签
     */
    private void prepareAnalyseTag(UserSearchAnalyseContext context) {
        if (CollectionUtils.isEmpty(context.getAdList())) {
            return;
        }
        // 判断是否有自定义普通标签，并过滤掉
        AtomicBoolean hasOrdinaryTag = new AtomicBoolean(false);
        for (int i = 0; i < context.getAdList().size(); i++) {
            if (UserSearchConstants.EsSpecialLabel.ORDINARY_TAG.getValue()
                    .equalsIgnoreCase(context.getAdList().get(i).getItemKey())) {
                hasOrdinaryTag.set(true);
                context.getAdList();
                context.getAdList().remove(i);
                break;
            }
        }
        // 无自定义普通标签，结束
        if (!hasOrdinaryTag.get()) {
            return;
        }

        // 分拣可进行透视的普通标签
        List<TagEntity> ordinaryTags = tagRepository.findTagBy(context.getOrgId(), 0, 1, null, null,null);
        for (TagEntity a : ordinaryTags) {
            UserSearchAnalyseDimensionBean temp = new UserSearchAnalyseDimensionBean();
            temp.setItemName(a.getTagName());
            temp.setItemKey(a.getTagKey());
            context.getAdOrdinaryList().add(temp);
        }

        // 查看查询条件是否有普通标签，如果有，以条件查询条件中的为透视依据
        /** 留一个迭代
         List<UserSearchAnalyseDimensionBean> tempList = new ArrayList<>();
         UserSearchBean search = context.getSearch();
         if(CollectionUtils.isNotEmpty(search.getOrdinaryTags())){
         List<String> sotItemKeys = search.getOrdinaryTags().stream().map(UserSearchBean4OrdinaryTag::getItemKey).collect(Collectors.toList());
         context.getAdOrdinaryList().forEach(a -> {
         if(!sotItemKeys.contains(a.getItemKey())){
         tempList.add(a);
         }
         });
         }
         if(CollectionUtils.isNotEmpty(tempList)){
         context.getAdOrdinaryList().removeAll(tempList);
         }
         **/
    }

    /**
     * 如果存在分页维度参数，则只聚合该参数维度
     *
     * @param context
     */
    private void preparePagingDimension(UserSearchAnalyseContext context) {
        if (StringUtils.isBlank(context.getDimensionKey())) {
            return;
        }
        // 比较itemKey是否和dimensionKey相同，相同则保留，不同则移除，有一个例外：部门需要匹配itemKey或者DEPARTMENT_NAME
        for (UserSearchAnalyseDimensionBean bean : context.getAdList()) {
            if (bean.getItemKey().equalsIgnoreCase(context.getDimensionKey()) ||
                (UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue().equalsIgnoreCase(context.getDimensionKey())
                    && bean.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.DEPARTMENT_ID.getValue())))
            {
                // 上方判断 departmentName 和 departmentId均可。
                // 不要使用 Arrays.asList(bean); 后面要remove
                ArrayList<UserSearchAnalyseDimensionBean> beans = new ArrayList<>();
                beans.add(bean);
                context.setAdList(beans);
                break;
            }
        }
    }

    /**
     * 人才盘点标签处理，业务场景 ： 开通盘点要素，透视过盘点标签，然后又关闭了盘点。
     *
     * @param context
     */
    private void prepareRvTag(UserSearchAnalyseContext context) {

        List<String> itemKeys = context.getAdList().stream().map(UserSearchAnalyseDimensionBean::getItemKey)
                .collect(Collectors.toList());
        AtomicBoolean isIncludeRvTag = new AtomicBoolean(false);
        itemKeys.forEach(itemKey -> {
            if (UserSearchConstants.getEsRvLabelEnumMap().containsKey(itemKey)) {
                isIncludeRvTag.set(true);
            }
        });

        if (!isIncludeRvTag.get()) {
            return;
        }

        //是否开通人才盘点
        boolean isRv = coreFactorService.isOpenRv(context.getOrgId());
        if (isRv) {
            return;
        }

        // 未开通、去除人才盘点标签
        List<UserSearchAnalyseDimensionBean> removeList = new ArrayList<>();
        for (UserSearchAnalyseDimensionBean ad : context.getAdList()) {
            if (UserSearchConstants.getEsRvLabelEnumMap().containsKey(ad.getItemKey())) {
                removeList.add(ad);
            }
        }
        context.getAdList().removeAll(removeList);

    }

    /**
     * 透视维度中，有部门且搜索条件中选择了部门，则透视按照已选择部门进行
     */
    private void prepareDeptTag(UserSearchAnalyseContext context) {

        UserSearchBean search = context.getSearch();
        if (null == search.getOtherTags() || CollectionUtils.isEmpty(search.getOtherTags().getDepartments())) {
            return;
        }
        context.getAdList().forEach(ad -> {
            if (ad.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue())) {
                List<String> deptNames = search.getOtherTags().getDepartments().stream()
                        .map(UserSearchBean4DeptTag::getDepartmentName).collect(Collectors.toList());
                ad.setValues(deptNames);
            }
        });
    }

    /**
     * 透视维度中，有岗位且搜索条件中选择了岗位，则透视按照已选择岗位进行
     */
    private void preparePositionTag(UserSearchAnalyseContext context) {
        preparePositionOrDeptTag(context, UserSearchConstants.EsSpecialLabel.POSITION_ID.getValue());
    }

    /**
     * 透视维度中，有职级且搜索条件中选择了职级，则透视按照已选择职级进行
     */
    private void prepareGradeTag(UserSearchAnalyseContext context) {
        preparePositionOrDeptTag(context, UserSearchConstants.EsSpecialLabel.GRADE_NAME.getValue());
    }

    private void preparePositionOrDeptTag(UserSearchAnalyseContext context, String tagKey) {

        UserSearchBean search = context.getSearch();
        if (CollectionUtils.isEmpty(search.getStandardTags())) {
            return;
        }

        search.getStandardTags().forEach(a -> {
            if (!a.getItemKey().equalsIgnoreCase(tagKey)) {
                return;
            }
            context.getAdList().forEach(ad -> {
                if (ad.getItemKey().equalsIgnoreCase(a.getItemKey())) {
                    ad.setValues(a.getValues());
                }
            });

        });

    }

    /**
     * 列表查询条件
     *
     * @param pageRequest
     * @param userCache
     * @param search
     * @return
     */
    private NativeSearchQuery generateListSearchQuery(PageRequest pageRequest, UserCacheBasic userCache,
            UserSearchBean search) {

        QueryBuilder queryBuilder = generateQueryBuilder(userCache.getUserId(), userCache.getOrgId(), search);
        //查询指定字段
        SourceFilter sourceFilter = new FetchSourceFilterBuilder().withIncludes(UserSearchConstants.ES_SOURCE_INCLUDES)
                .build();

        //设置查询条件
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(queryBuilder);
        nativeSearchQueryBuilder.withSourceFilter(sourceFilter);
        if (pageRequest != null) {
            nativeSearchQueryBuilder.withPageable(UserSearchUtils.generatePageable(pageRequest));
        }

        return nativeSearchQueryBuilder.build();
    }

    /**
     * 透视查询条件
     *
     * @param context
     * @return
     */
    private NativeSearchQuery generateAnalyseSearchQuery(UserSearchAnalyseContext context) {

        if (CollectionUtils.isEmpty(context.getAdList()) && CollectionUtils.isEmpty(context.getAdOrdinaryList())) {
            return null;
        }

        QueryBuilder queryBuilder = generateQueryBuilder(context.getUserId(), context.getOrgId(), context.getSearch());
        //设置查询条件 聚合不需要返回具体数据，设置size 为 1
        NativeSearchQueryBuilder nsqb = new NativeSearchQueryBuilder();
        nsqb.withQuery(queryBuilder);
        nsqb.withPageable(org.springframework.data.domain.PageRequest.of(0, 1));

        // 设置聚合维度
        context.getAdList().forEach(ad -> {

            String itemKey = ad.getItemKey();
            String esKey = UserSearchUtils.generateEsPropertiesKey(itemKey);
            // 时间范围值 工龄、年龄 暂定值为 年龄【0-17,18-22,23-30,...】 工龄【0-1，1-5,5-10,...】
            if (itemKey.equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.BIRTHDAY.getValue()) || itemKey
                    .equalsIgnoreCase(UserSearchConstants.XxGroupTagEnum.AGE_GROUP.getKey())) {
                generateAnalyseBirthday(nsqb, context.getOrgId(), itemKey);
                return;
            }

            if (itemKey.equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.WORK_DATE.getValue()) || itemKey
                    .equalsIgnoreCase(UserSearchConstants.XxGroupTagEnum.WORK_DATE_GROUP.getKey())) {
                generateAnalyseWorkDate(nsqb, context.getOrgId(), itemKey);
                return;
            }

            if (ad.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue())
                    && CollectionUtils.isNotEmpty(ad.getValues())) {
                generateAnalyseDepartment(nsqb, ad, context);
                return;
            }

            /**  岗位，使用岗位id聚合结果为 id:32,id:40这样，没有办法(麻烦)把id翻译为岗位名， 使用脚本聚合(dev环境性能参考：500w 约6s )。 **/
            if (ad.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.POSITION_ID.getValue())) {
                generateAnalysePosition(nsqb, itemKey, esKey);
                return;
            }

            // 其他标签
            generateAnalyseOtherStandardTag(nsqb, esKey, itemKey);

        });

        // 用户自定义普通标签 平铺 count 聚合， 在结果读取时特殊处理
        context.getAdOrdinaryList().forEach(ad -> {
            String esKey = UserSearchUtils.generateEsPropertiesKey(ad.getItemKey());
            // nsqb.addAggregation(AggregationBuilders.count(ad.getItemKey()).field(esKey)); //  count 字段,字段为字符串空时候也会统计，业务不需要统计空值。
            BoolQueryBuilder mustItemName = QueryBuilders.boolQuery().mustNot(termQuery(esKey, ""));
            ValueCountAggregationBuilder subAggregation = AggregationBuilders.count(ad.getItemKey()).field(esKey);
            nsqb.addAggregation(
                    AggregationBuilders.filter(ad.getItemKey(), mustItemName).subAggregation(subAggregation));
        });

        return nsqb.build();
    }

    private void generateAnalyseBirthday(NativeSearchQueryBuilder nsqb, String orgId, String itemKey) {

        String esItemKey = itemKey;
        if (UserSearchConstants.getXxGroupTagEnumMap().containsKey(itemKey)) {
            esItemKey = UserSearchConstants.getXxGroupTagEnumMap().get(itemKey);
        }

        List<TagValueEntity> tagValues = tagValueMapper.findByOrgIdAndTagKey(orgId, itemKey);
        if (CollectionUtils.isEmpty(tagValues)) {
            log.warn("{} -> generateAnalyseSearchQuery birthday tagValues is Empty; orgId:{} itemKey:{}",
                this.getClass().getName(), orgId, itemKey);
            return;
        }

        DateRangeAggregationBuilder dateRangeBuilder = AggregationBuilders.dateRange(itemKey).field(esItemKey)
                .format(Constants.SDF_YEAR2DAY);
        tagValues.forEach(val -> {
            String range = val.getValueName();
            // 兼容 51岁以上、其他、18-22岁等值，并进行转换
            if (!UserSearchConstants.getAgeRangeKVEnumMap().containsKey(range) && UserSearchConstants
                    .getAgeRangeVKEnumMap().containsKey(range)) {
                range = UserSearchConstants.getAgeRangeVKEnumMap().get(range);
            }
            UserSearchDateRangeBean dateRangeBean = UserSearchUtils.rangeToDate(range, true);
            if (dateRangeBean == null) {
                return;
            }
            dateRangeBuilder.addRange(range, dateRangeBean.getStartDate(), dateRangeBean.getEndDate());
        });
        nsqb.addAggregation(dateRangeBuilder);

    }

    private void generateAnalyseWorkDate(NativeSearchQueryBuilder nsqb, String orgId, String itemKey) {
        String esItemKey = itemKey;
        if (UserSearchConstants.getXxGroupTagEnumMap().containsKey(itemKey)) {
            esItemKey = UserSearchConstants.getXxGroupTagEnumMap().get(itemKey);
        }

        List<TagValueEntity> tagValues = tagValueMapper.findByOrgIdAndTagKey(orgId, itemKey);
        if (CollectionUtils.isEmpty(tagValues)) {
            log.warn("{} -> generateAnalyseSearchQuery workDate tagValues is Empty; orgId:{} itemKey:{}",
                this.getClass().getName(), orgId, itemKey);
            return;
        }

        DateRangeAggregationBuilder dateRangeBuilder = AggregationBuilders.dateRange(itemKey).field(esItemKey)
                .format(Constants.SDF_YEAR2DAY);
        tagValues.forEach(val -> {
            UserSearchDateRangeBean dateRangeBean = UserSearchUtils.rangeToDate(val.getValueName(), false);
            if (dateRangeBean == null) {
                return;
            }
            dateRangeBuilder.addRange(val.getValueName(), dateRangeBean.getStartDate(), dateRangeBean.getEndDate());
        });
        nsqb.addAggregation(dateRangeBuilder);

    }

    /**
     * 搜索条件中含有部门时候，部门特殊处理 使用 script
     * 脚本示例： if(doc['departmentName'].value.indexOf('中智薪税公司党总支')>=0){return '中智薪税公司党总支';}
     * 返回示例：
     * "test-aggs": {
     * "doc_count_error_upper_bound": 0,
     * "sum_other_doc_count": 0,
     * "buckets": [
     * {
     * "key": "上海外企服务分公司党总支",
     * "doc_count": 6
     * },
     * {
     * "key": "职业发展公司党总支",
     * "doc_count": 3
     * },
     * {
     * "key": "中智薪税公司党总支",
     * "doc_count": 2
     * }
     * ]
     * }
     *
     * @param nsqb
     * @param ad
     * @param context
     */
    private void generateAnalyseDepartment(NativeSearchQueryBuilder nsqb, UserSearchAnalyseDimensionBean ad,
            UserSearchAnalyseContext context) {
        List<UserSearchBean4DeptTag> departments = context.getSearch().getOtherTags().getDepartments();

        StringBuilder scriptSb = new StringBuilder();
        if (departments.size() == 1 && StringUtils.isBlank(departments.get(0).getParentId())
                && departments.get(0).getIncludeAll() == 1) {
            // 特殊处理 由于部门全路径不包含根部门，此处特殊处理
            String departmentName = departments.get(0).getDepartmentName();
            String script = String.format(UserSearchConstants.DEPT_ROOT_AGGS_SCRIPT_TEMPLATE, departmentName);
            scriptSb.append(script);
        } else {
            for (String value : ad.getValues()) {
                String script = "";
                // 包含子部门人数
                if (departments.get(0).getIncludeAll() == 1) {
                    script = String
                            .format(UserSearchConstants.DEPT_INCLUDE_AGGS_SCRIPT_TEMPLATE, ad.getItemKey(), value,
                                    value);
                } else {
                    script = String
                            .format(UserSearchConstants.DEPT_AGGS_SCRIPT_TEMPLATE, ad.getItemKey(), value, value);
                }
                scriptSb.append(script);
            }
        }

        nsqb.addAggregation(
                AggregationBuilders.terms(ad.getItemKey()).script(new Script(scriptSb.toString())).size(MAX_LIMIT)
                        .order(BucketOrder.count(false)));
    }

    private void generateAnalysePosition(NativeSearchQueryBuilder nsqb, String itemKey, String esKey) {
        BoolQueryBuilder mustNotBlank = QueryBuilders.boolQuery().mustNot(termQuery(esKey, ""));
        Script script = new Script(UserSearchConstants.POSITION_AGGS_SCRIPT_TEMPLATE);
        TermsAggregationBuilder subAggregation = AggregationBuilders.terms(itemKey).script(script).size(MAX_LIMIT)
                .order(BucketOrder.count(false));
        nsqb.addAggregation(AggregationBuilders.filter(itemKey, mustNotBlank).subAggregation(subAggregation));
    }

    private void generateAnalyseOtherStandardTag(NativeSearchQueryBuilder nsqb, String esKey, String itemKey) {
        BoolQueryBuilder mustNotBlank = QueryBuilders.boolQuery().mustNot(termQuery(esKey, ""));
        TermsAggregationBuilder subAggregation = AggregationBuilders.terms(itemKey).field(esKey).size(MAX_LIMIT)
                .order(BucketOrder.count(false));
        // 分页
        nsqb.addAggregation(AggregationBuilders.filter(itemKey, mustNotBlank).subAggregation(subAggregation));
    }


    /**
     * 将deptId 转换为 DeptPathName
     *
     * @param orgId
     * @param search
     */
    private void deptIdToDeptName(String orgId, UserSearchBean search, List<UserSearchAnalyseDimensionBean> adList) {
        if (adList != null) {
            adList.forEach(a -> {
                if (!"departmentId".equalsIgnoreCase(a.getItemKey())) {
                    return;
                }
                a.setItemKey("departmentName");
            });
        }

        if (null == search.getOtherTags()) {
            return;
        }
        List<UserSearchBean4DeptTag> departments = search.getOtherTags().getDepartments();
        if (CollectionUtils.isEmpty(departments)) {
            return;
        }

        String[] fields = {"routingPathName", "parentId", "id"};
        DeptBean4Search searchBean = new DeptBean4Search();
        searchBean.setOrgId(orgId);
        CommonList<DeptBean> deptBeanCommonList = udpFacade.searchAllDepartmentList(searchBean, fields);




        Map<String, String> routingPathNameMap = deptBeanCommonList.getDatas().stream()
                .collect(Collectors.toMap(DeptBean::getId, DeptBean::getRoutingPathName, (a1, a2) -> a2));
        Map<String, String> parentIdMap = deptBeanCommonList.getDatas().stream()
                .collect(Collectors.toMap(DeptBean::getId, DeptBean::getParentId, (a1, a2) -> a2));

        departments.forEach(deptTag -> {
            String routingPathName = routingPathNameMap.get(deptTag.getDepartmentId());
            if (!StringUtils.isBlank(routingPathName)) {
                deptTag.setDepartmentName(routingPathName);
                deptTag.setParentId(parentIdMap.get(deptTag.getDepartmentId()));
            }
        });

    }

    /**
     * 生成es搜索条件
     */
    private QueryBuilder generateQueryBuilder(String userId, String orgId, UserSearchBean search) {
        ArrayList<BoolQueryBuilder> bqbList = new ArrayList<>();

        // 机构Id隔离、权限隔离、已删除用户隔离
        authQueryBuilder(bqbList, userId, orgId);
        // keyword 条件模糊搜索、用户状态隔离等非标签常规搜索
        generateCommonQueryBuilder(bqbList, search);
        // 客户自定义普通标签条件
        generateOrdinaryTagsQueryBuilder(bqbList, search);
        // 入职日期范围
        generateOtherTagsQueryBuilder(bqbList, search);
        // 部门
        generateDeptTagsQueryBuilder(bqbList, search);
        // 其他常规标签
        generateStandardTagsQueryBuilder(bqbList, search);
        // 透视区条件标签【点击某个透视项进行筛选】
        generateAnalyseTagQueryBuilder(bqbList, search);

        // 将bqbList中条件加入must查询组 相当于mysql用 and 链接起来
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        bqbList.forEach(bqd -> mainQueryBuilder.must().add(bqd));

        return mainQueryBuilder;
    }

    /**
     * 机构隔离、权限隔离-我管辖的
     */
    private void authQueryBuilder(List<BoolQueryBuilder> bqbList, String userId, String orgId) {

        // 机构隔离
        BoolQueryBuilder orgIdQueryBuilder = QueryBuilders.boolQuery()
                .must(termQuery(UserSearchConstants.EsSpecialLabel.ORG_ID.getValue(), orgId));
        bqbList.add(orgIdQueryBuilder);

        // 未删除的用户
        BoolQueryBuilder unDeletedQueryBuilder = QueryBuilders.boolQuery()
                .must(termQuery(UserSearchConstants.EsSpecialLabel.USER_DELETED.getValue(), 0));
        bqbList.add(unDeletedQueryBuilder);

        // 不展示外部用户 visible_state = 1
        BoolQueryBuilder unvisibleQueryBuilder = QueryBuilders.boolQuery()
            .mustNot(termQuery(UserSearchConstants.EsSpecialLabel.USER_VISIABLE.getValue(), 1));
        bqbList.add(unvisibleQueryBuilder);

        /**
         // 权限隔离 - 我管辖的用户Id列表
         UserAuthBean userAuth = udpUserAuthService.getAuthUserIds(orgId, userId, NAV_CODE, DATA_PERMISSION_CODE);
         // 不是管理员, 需要所管辖具体用户id
         if(userAuth.getAdmin() != 1){
         List<String> userIds = userAuth.getUserIds();
         if(CollectionUtils.isEmpty(userIds)){
         userIds = new ArrayList<>();
         userIds.add(ApiUtil.getUuid());// 字符串， 以保证查不出任何用户
         }
         BoolQueryBuilder authUserIdQueryBuilder = QueryBuilders.boolQuery()
         .should(QueryBuilders.termsQuery(UserSearchConstants.EsSpecialLabel.userId.getValue(),userIds));
         bqbList.add(authUserIdQueryBuilder);
         }
         **/

        UserAuthBean4Dept userAuth = userAuthService.getAuthDeptIds(orgId, userId, NAV_CODE, DATA_PERMISSION_CODE);
        if (userAuth.getIsAll() != 1) {
            List<String> deptIds = userAuth.getDeptIds();
            if (CollectionUtils.isEmpty(deptIds)) {
                deptIds = new ArrayList<>();
                // 字符串， 以保证查不出任何用户
                deptIds.add(ApiUtil.getUuid());
            }

            BoolQueryBuilder authUserIdQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders
                    .termsQuery(UserSearchConstants.EsSpecialLabel.DEPARTMENT_ID.getValue(), deptIds));
            bqbList.add(authUserIdQueryBuilder);
        }

    }

    /**
     * keyword 搜索条件
     */
    private void generateCommonQueryBuilder(ArrayList<BoolQueryBuilder> bqbList, UserSearchBean search) {
        // 关键字搜索
        if (StringUtils.isNotBlank(search.getKeyword())) {
            BoolQueryBuilder keywordQueryBuilder = QueryBuilders.boolQuery();
            for (UserSearchConstants.KeyWordTagEnum kwTag : UserSearchConstants.KeyWordTagEnum.values()) {
                keywordQueryBuilder
                        .should(QueryBuilders.wildcardQuery(kwTag.getValue(), "*" + search.getKeyword() + "*"));
            }
            bqbList.add(keywordQueryBuilder);
        }
        // 用户状态隔离
        if (null != search.getUserStatusFilterSwitch() && search.getUserStatusFilterSwitch() == 1) {
            bqbList.add(QueryBuilders.boolQuery().must(
                termQuery(UserSearchConstants.ES_USER_STATUS,search.getUserStatusFilterSwitch().toString())));
        }
    }

    /**
     * 客户自定义普通标签条件
     */
    private void generateOrdinaryTagsQueryBuilder(ArrayList<BoolQueryBuilder> bqbList, UserSearchBean search) {
        List<UserSearchBean4OrdinaryTag> ordinaryTags = search.getOrdinaryTags();
        if (CollectionUtils.isEmpty(ordinaryTags)) {
            return;
        }

        Integer logic = ordinaryTags.get(0).getLogic();
        BoolQueryBuilder bqb = QueryBuilders.boolQuery();
        List<QueryBuilder> qbList = new ArrayList<>();
        for (UserSearchBean4OrdinaryTag tag : ordinaryTags) {
            String esTagName = UserSearchUtils.generateEsPropertiesKey(tag.getItemKey());
            qbList.add(termQuery(esTagName, tag.getValue()));
        }

        if (UserSearchConstants.SearchLogic.AND.getValue() == logic) {
            bqb.must().addAll(qbList);
        } else {
            bqb.should().addAll(qbList);
        }
        bqbList.add(bqb);

    }

    /**
     * 入职日期范围
     */
    private void generateOtherTagsQueryBuilder(ArrayList<BoolQueryBuilder> bqbList, UserSearchBean search) {
        // 入职日期范围
        UserSearchBean4OtherTag otherTags = search.getOtherTags();
        if (otherTags != null && (otherTags.getEntryDateEnd() != null || otherTags.getEntryDateStart() != null)) {

            RangeQueryBuilder rangeQueryBuilder = QueryBuilders
                    .rangeQuery(UserSearchConstants.EsSpecialLabel.ENTRY_DATE.getValue());
            //日期格式化
            rangeQueryBuilder.format(Constants.SDF_YEAR2DAY);
            if (otherTags.getEntryDateStart() != null) {
                // >= 起始日期
                rangeQueryBuilder.gte(otherTags.getEntryDateStart());
            }
            if (otherTags.getEntryDateEnd() != null) {
                // <= 结束日期
                rangeQueryBuilder.lte(otherTags.getEntryDateEnd());
            }

            BoolQueryBuilder bqb = QueryBuilders.boolQuery();
            bqb.must(rangeQueryBuilder);

            bqbList.add(bqb);
        }
    }

    private void generateAnalyseTagQueryBuilder(ArrayList<BoolQueryBuilder> bqbList, UserSearchBean search) {
        UserSearchBean4AnalyseTag analyseTag = search.getAnalyseTag();
        if (CommonUtils.anyMatch(Objects::isNull, analyseTag, analyseTag.getDimensionKey(), analyseTag.getDimensionItemKey())) {
            return;
        }

        // 根据 analyseTag.getDimensionKey() 判断是普通的值，还是范围值， 日期范围(年龄、工龄、司龄)、证书、部门

        String esTagName = UserSearchUtils.generateEsPropertiesKey(analyseTag.getDimensionKey());
        String dimensionItemKey = analyseTag.getDimensionItemKey();


        if (analyseTag.getDimensionKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.BIRTHDAY.getValue())
                || analyseTag.getDimensionKey()
                .equalsIgnoreCase(UserSearchConstants.XxGroupTagEnum.AGE_GROUP.getKey())) {
            //年龄
            RangeQueryBuilder rangeQueryBuilder = generateAgeQueryBuilder(dimensionItemKey,
                    UserSearchConstants.EsSpecialLabel.BIRTHDAY.getValue());
            CommonUtils.notNullConsume(rangeQueryBuilder,
                    queryBuilder -> bqbList.add(QueryBuilders.boolQuery().must(queryBuilder)));
        } else if (analyseTag.getDimensionKey()
                .equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.WORK_DATE.getValue())) {
            // 工龄
            RangeQueryBuilder rangeQueryBuilder = generateWorkDateQueryBuilder(dimensionItemKey, esTagName);
            CommonUtils.notNullConsume(rangeQueryBuilder,
                    queryBuilder -> bqbList.add(QueryBuilders.boolQuery().must(queryBuilder)));
        } else if (analyseTag.getDimensionKey()
                .equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.ORDINARY_TAG.getValue())) {
            esTagName = UserSearchUtils.generateEsPropertiesKey(analyseTag.getDimensionItemKey());
            bqbList.add(QueryBuilders.boolQuery().must(QueryBuilders.existsQuery(esTagName))
                    .mustNot(QueryBuilders.termsQuery(esTagName, "")));
        } else if (analyseTag.getDimensionKey()
                .equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue())) {
            // 部门
            boolean searchDept =
                    search.getOtherTags() != null && CollectionUtils.isNotEmpty(search.getOtherTags().getDepartments());

            // 特殊处理根部门
            if (searchDept) {
                List<UserSearchBean4DeptTag> departments = search.getOtherTags().getDepartments();
                if (departments.size() == 1 && StringUtils.isBlank(departments.get(0).getParentId())
                        && departments.get(0).getIncludeAll() == 1) {
                    // 根部门特殊处理 只有有部门标签就行
                    bqbList.add(QueryBuilders.boolQuery().must(QueryBuilders
                            .existsQuery(UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue())));
                    return;
                }
            }

            AtomicInteger includeAll = new AtomicInteger(0);
            if (searchDept) {
                search.getOtherTags().getDepartments().forEach(a -> {
                    if (dimensionItemKey.equalsIgnoreCase(a.getDepartmentName())) {
                        includeAll.set(a.getIncludeAll());
                    }
                });
            }

            if (includeAll.get() == 1) {
                bqbList.add(
                        QueryBuilders.boolQuery().must(QueryBuilders.wildcardQuery(esTagName, dimensionItemKey + "*")));
            } else {
                bqbList.add(QueryBuilders.boolQuery().must(termQuery(esTagName, dimensionItemKey)));
            }

        } else {
            // 其他
            bqbList.add(QueryBuilders.boolQuery().must(termQuery(esTagName, dimensionItemKey)));
        }

    }

    /**
     * 年龄
     **/
    private RangeQueryBuilder generateAgeQueryBuilder(String rangeValue, String esTagName) {

        // 兼容 51岁以上、其他、18-22岁等值，并进行转换
        if (!UserSearchConstants.getAgeRangeKVEnumMap().containsKey(rangeValue) && UserSearchConstants
                .getAgeRangeVKEnumMap().containsKey(rangeValue)) {
            rangeValue = UserSearchConstants.getAgeRangeVKEnumMap().get(rangeValue);
        }

        // 年龄【0-17,18-22,23-30,...】
        UserSearchDateRangeBean dateRangeBean = UserSearchUtils.rangeToDate(rangeValue, true);
        if (dateRangeBean == null) {
            return null;
        }

        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(esTagName);
        //日期格式化
        rangeQueryBuilder.format(Constants.SDF_YEAR2DAY);
        if (dateRangeBean.getStartDate() != null) {
            // 大于等于 开始日期
            rangeQueryBuilder.gt(dateRangeBean.getStartDate());
            //rangeQueryBuilder.includeLower(true); //包含开始日期 不设置默认为true
        }
        if (dateRangeBean.getEndDate() != null) {
            // 小于等于 结束日期
            rangeQueryBuilder.lte(dateRangeBean.getEndDate());
            //rangeQueryBuilder.includeUpper(true); //包含结束日期 不设置默认为true
        }

        return rangeQueryBuilder;
    }

    /**
     * 工龄
     **/
    private RangeQueryBuilder generateWorkDateQueryBuilder(String rangeValue, String esTagName) {
        // 工龄
        UserSearchDateRangeBean dateRangeBean = UserSearchUtils.rangeToDate(rangeValue, false);
        if (dateRangeBean == null) {
            return null;
        }

        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(esTagName);
        //日期格式化
        rangeQueryBuilder.format(Constants.SDF_YEAR2DAY);
        if (dateRangeBean.getStartDate() != null) {
            // 大于 开始日期
            rangeQueryBuilder.gt(dateRangeBean.getStartDate());
            // 不包含开始日期
            rangeQueryBuilder.includeLower(false);
        }
        if (dateRangeBean.getEndDate() != null) {
            // 小于等于 结束日期
            rangeQueryBuilder.lte(dateRangeBean.getEndDate());
            // 包含结束日期, 不设置默认为true
            rangeQueryBuilder.includeUpper(true);
        }

        return rangeQueryBuilder;
    }

    /**
     * 生成部门搜索条件
     *
     * @param bqbList
     * @param search
     */
    private void generateDeptTagsQueryBuilder(ArrayList<BoolQueryBuilder> bqbList, UserSearchBean search) {
        if (null == search.getOtherTags()) {
            return;
        }
        List<UserSearchBean4DeptTag> departments = search.getOtherTags().getDepartments();
        if (CollectionUtils.isEmpty(departments)) {
            return;
        }

        BoolQueryBuilder bqb = QueryBuilders.boolQuery();
        if (departments.size() == 1 && StringUtils.isBlank(departments.get(0).getParentId())
                && departments.get(0).getIncludeAll() == 1) {
            // 根部门特殊处理 只有有部门标签就行
            bqb.should(QueryBuilders.existsQuery(UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue()));
        } else {
            for (UserSearchBean4DeptTag dept : departments) {
                //包含子部门数据
                if (dept.getIncludeAll() == 1) {
                    bqb.should(QueryBuilders
                            .wildcardQuery(UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue(),
                                    dept.getDepartmentName() + "*"));
                } else {
                    bqb.should(termQuery(UserSearchConstants.EsSpecialLabel.DEPARTMENT_NAME.getValue(),
                            dept.getDepartmentName()));
                }
            }
        }

        if (null != bqb) {
            bqbList.add(bqb);
        }

    }

    private void generateStandardTagsQueryBuilder(ArrayList<BoolQueryBuilder> bqbList, UserSearchBean search) {
        // 根据前端传递过来的logic，来组织搜索条件
        List<UserSearchBean4LayeredTag> standardTags = search.getStandardTags();
        if (CollectionUtils.isEmpty(standardTags)) {
            return;
        }

        for (UserSearchBean4LayeredTag tag : standardTags) {
            if (CollectionUtils.isEmpty(tag.getValues())) {
                continue;
            }

            BoolQueryBuilder bqb = QueryBuilders.boolQuery();
            // 返回es中真正的标签名， 示例 ：divlabels.xxxxx
            String esTagName = UserSearchUtils.generateEsPropertiesKey(tag.getItemKey());
            tag.getValues().forEach(value -> {
                // 时间范围值 工龄、年龄 暂定值为 年龄【0-17,18-22,23-30,...】 工龄【0-1，1-5,5-10,...】
                if (tag.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.BIRTHDAY.getValue()) || tag
                        .getItemKey().equalsIgnoreCase(UserSearchConstants.XxGroupTagEnum.AGE_GROUP.getKey())) {

                    String esItemKey = UserSearchConstants.getXxGroupTagEnumMap().getOrDefault(tag.getItemKey(), esTagName);
                    RangeQueryBuilder rangeQueryBuilder = generateAgeQueryBuilder(value, esItemKey);
                    CommonUtils.notNullConsume(rangeQueryBuilder, queryBuilder -> bqb.should(queryBuilder));
                    return;
                }

                // 工龄
                if (tag.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.WORK_DATE.getValue()) || tag
                        .getItemKey().equalsIgnoreCase(UserSearchConstants.XxGroupTagEnum.WORK_DATE_GROUP.getKey())) {
                    String esItemKey = UserSearchConstants.getXxGroupTagEnumMap().getOrDefault(tag.getItemKey(), esTagName);
                    RangeQueryBuilder rangeQueryBuilder = generateWorkDateQueryBuilder(value, esItemKey);
                    CommonUtils.notNullConsume(rangeQueryBuilder, queryBuilder -> bqb.should(queryBuilder));
                    return;
                }

                if (tag.getItemKey().equalsIgnoreCase(UserSearchConstants.EsSpecialLabel.SEX.getValue())) {
                    bqb.should(termQuery(esTagName, UserSearchConstants.getSexVKEnumMap().get(value)));
                    return;
                }

                // 其他普通值
                if (UserSearchConstants.SearchLogic.AND.getValue() == tag.getLogic()) {
                    bqb.must(termQuery(esTagName, value));
                } else {
                    bqb.should(termQuery(esTagName, value));
                }
            });

            if (bqb.hasClauses()) {
                bqbList.add(bqb);
            }
        }
    }

}
