package com.yxt.talent.bk.core.tag.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2022/8/15
 */
@Data
@NoArgsConstructor
@TableName(value = "bk_user_tag")
@EqualsAndHashCode(callSuper = true)
public class UserTagEntity extends CreatorEntity {
    /**
    * 主键
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 用户id
    */
    private String userId;

    /**
    * 标签id(bk_tag.id)
    */
    private String tagId;

    /**
    * 是否删除(0-否,1-是)
    */
    private Integer deleted;

}
