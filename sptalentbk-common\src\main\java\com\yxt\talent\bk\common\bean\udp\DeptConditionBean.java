package com.yxt.talent.bk.common.bean.udp;

import com.yxt.talent.bk.common.utils.CommonUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * DeptFilterBean
 *
 * <AUTHOR> harleyge
 * @Date 28/8/24 11:58 am
 */
@Data
public class DeptConditionBean {
    private List<String> deptIds;
    private List<String> parentDeptIds;

    public static <T> DeptConditionBean createBy(List<T> list,
                                                 Function<T, String> deptIdGetter,
                                                 Function<T, Integer> includeAllGetter) {
        DeptConditionBean filterBean = new DeptConditionBean();
        filterBean.setDeptIds(new ArrayList<>());
        filterBean.setParentDeptIds(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                String deptId = deptIdGetter.apply(item);
                filterBean.deptIds.add(deptId);
                if (CommonUtils.isTrue(includeAllGetter.apply(item))) {
                    filterBean.parentDeptIds.add(deptId);
                }
            });
        }
        return filterBean;
    }
}
