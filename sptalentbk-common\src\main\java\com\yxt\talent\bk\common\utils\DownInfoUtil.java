package com.yxt.talent.bk.common.utils;

import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.ubiz.export.bean.DownInfo;

/**
 * @description:
 * @author: suocl
 * @time: 2024/1/11 16:30
 */
public class DownInfoUtil {

    private DownInfoUtil() {
        //空构造函数
    }

    /**
     * @param funcationName 下载中心functionCode,示例:pc_dlc_bbs_post_list
     * @return
     */
    public static DownInfo getDownInfo(String funcationName) {
        DownInfo downInfo = new DownInfo();
        downInfo.setAppCode(ModuleConstants.APP_CODE);
        downInfo.setModuleCode(ModuleConstants.MODULE_CODE);
        downInfo.setFunctionName(funcationName);
        downInfo.setUseOriginFileName(true);
        return downInfo;
    }
}
