package com.yxt.talent.bk.core.tag.bean;

import com.yxt.talent.bk.common.constants.TalentBkConstants;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Document;

import java.util.List;
import java.util.Map;

@Document(indexName = "#{@bkConfigService.indexName}", type = TalentBkConstants.ES_TYPE)
@Data
public class UserTagListBean4Es{

    /**
     * 用户id
     */
    private String userId;
    /**
     * 账号
     */
    private String username;
    /**
     * 中文姓名
     */
    private String cnname;
    /**
     * 部门全路径名称
     */
    private String departmentName;
    /**
     * 岗位名称
     */
    private String positionName;
    /**
     * 职级名称
     */
    private String gradeName;
    /**
     * 用户状态
     */
    private String userStatus;
    /**
     * 任职风险
     */
    private List<String> tenureRisk;
    /**
     * 盘点结果
     */
    private String reviewResult;
    /**
     * 绩效
     */
    private String performanceLevel;
    /**
     * 能力
     */
    private String abilityLevel;
    /**
     * 潜力
     */
    private String potentialLevel;
    /**
     * 年龄段
     */
    private String birthday;
    /**
     * 持续高绩效
     */
    private String performanceTrend;
    /**
     * 近一年学分TOP占比
     */
    private String scoreRatio;
    /**
     * 连续学习N周
     */
    private String unspacedLrn;
    /**
     * 性格特点
     */
    private List<String> characterTraits;
    /**
     * 近一年学习时长TOP占比
     */
    private String lrnDurationRatio;
    /**
     * 学习主动性偏好
     */
    private String lrnPreferences;
    /**
     * 乐于分享
     */
    private String enjoySharing;
    /**
     * 善于提问
     */
    private String goodAtAsking;
    /**
     * 职业驱动力
     */
    private List<String> professionDriver;
    /**
     * 客户自定标签
     */
    private Map<String, List<String>> diylabels;

    /**
     * 其他标签
     */
    private List<String> exams;
    private List<String> certs;
    private List<String> o2os;
    private String nativePlace;
    private String politicalAffiliation;
    private String entryDate;
    private String workDate;
    private List<String> advantageCommAbliity;
    private List<String> inferiorityCommAbliity;
    private List<String> otherCommAbliity;
    private List<String> advantageProAbliity;
    private List<String> inferiorityProAbliity;
    private List<String> otherProAbliity;
    private String highestEducation;
    private String highestSchool;
    private String major;
    private List<String> leadershipStyle;

}
