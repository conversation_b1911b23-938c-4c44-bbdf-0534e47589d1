package com.yxt.talent.bk.core.component;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.yxt.common.repo.RedisRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * RedisComponent
 *
 * <AUTHOR> harleyge
 * @Date 17/7/24 1:54 pm
 */
@Slf4j
@Component
@AllArgsConstructor
public class RedisComponent {
    private RedisRepository talentRedisRepository;

    private static final String VALUE_SET_NX = "1";

    public void addToSet(String key, String setItem, Pair<Long, TimeUnit> expire) {
        if (StringUtils.isAnyEmpty(key, setItem)) {
            return;
        }
        getRedisRepository().opsForSet().add(key, setItem);
        expire(key, expire);
    }

    public void delMemberOfSet(String key, String setItem) {
        getRedisRepository().opsForSet().remove(key, setItem);
    }
    public Set<String> memberOfSet(String key) {
        Set<String> ret = getRedisRepository().opsForSet().members(key);
        return ret != null ? ret : new HashSet<>();
    }

    public void expire(String key, Pair<Long, TimeUnit> expire) {
        getRedisRepository().expire(key, expire.getKey(), expire.getValue());
    }
    public void setValue(String key, String value, Pair<Long, TimeUnit> expire) {
        getRedisRepository().setValue(key, value, expire.getKey(), expire.getValue());
    }
    public void setValue(String key, String value, long ttl, TimeUnit timeUnit) {
        getRedisRepository().setValue(key, value, ttl, timeUnit);
    }

    public String getValue(String key) {
        return getRedisRepository().getValueByKey(key);
    }

    public void setHmValue(String key, String subKey, String value, Pair<Long, TimeUnit> expire) {
        getRedisRepository().setHashValue(key, subKey, value);
        getRedisRepository().expire(key, expire.getKey(), expire.getValue());
    }
    public void setHmValue(String key, String subKey, String value, long ttl, TimeUnit timeUnit) {
        getRedisRepository().setHashValue(key, subKey, value);
        getRedisRepository().expire(key, ttl, timeUnit);
    }

    public String getHmValue(String key, String subKey) {
        return getRedisRepository().getHashValue(key, subKey);
    }

    /**
     * 设置有失效时间的类型的缓存
     * @param key
     * @param valGet
     * @param timeUnit
     * @return
     */
    public <T> T cacheValue(String key, Supplier<T> valGet, Class<T> clazz,
                            long time, TimeUnit timeUnit) {
        ValueOperations valOps = getRedisRepository().opsForValue();
        Object ret = valOps.get(key);
        if (ret == null) {
            ret = valGet.get();
            if (ret == null) {
                return null;
            }
            valOps.set(key, JSON.toJSONString(ret), time, timeUnit);
            return (T) ret;
        } else {
            return JSON.parseObject(ret.toString(), clazz);
        }
    }

    public void removeKey(String key) {
        getRedisRepository().delete(key);
    }

    public RedisRepository getRedisRepository() {
        return talentRedisRepository;
    }

    /***
     * 往缓存中设置一个值为1的常量,作为并发控制
     *
     * @param key
     * @return boolean
     * <AUTHOR>
     * @date 2019/12/29 14:51
     */
    public boolean setNx(String key, Pair<Integer, TimeUnit> timeOut) {
        Boolean result = talentRedisRepository.opsForValue()
                .setIfAbsent(key, VALUE_SET_NX, timeOut.getKey(), timeOut.getValue());
        if (result == null) {
            return false;
        } else {
            return result.booleanValue();
        }
    }
}
