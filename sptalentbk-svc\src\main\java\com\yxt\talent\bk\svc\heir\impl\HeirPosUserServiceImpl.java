package com.yxt.talent.bk.svc.heir.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.Validate;
import com.yxt.msgfacade.bean.Todo4Create;
import com.yxt.msgfacade.bean.Todo4DeleteBatch;
import com.yxt.msgfacade.bean.Todo4Done;
import com.yxt.msgfacade.bean.Todo4DoneBatch;
import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangFullBean;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.enums.HeirPosTypeEnum;
import com.yxt.talent.bk.common.utils.CallUtils;
import com.yxt.talent.bk.common.utils.CommonUtils;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserBean;
import com.yxt.talent.bk.core.heir.bean.HeirPosUserIdDTO;
import com.yxt.talent.bk.core.heir.bean.HeirRemindTodoIdDTO;
import com.yxt.talent.bk.core.heir.bean.PosUserBriefBean;
import com.yxt.talent.bk.core.heir.bean.RemindTodoParam;
import com.yxt.talent.bk.core.heir.entity.HeirPosUserEntity;
import com.yxt.talent.bk.core.heir.entity.HeirPrepareHisEntity;
import com.yxt.talent.bk.core.heir.entity.HeirRemindTodoEntity;
import com.yxt.talent.bk.core.heir.ext.HeirPosExt;
import com.yxt.talent.bk.core.heir.ext.HeirPosUserExt;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPrepareHisMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPrepareRemindUserMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirRemindTodoMapper;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.heir.repo.HeirPosUserRepository;
import com.yxt.talent.bk.core.heir.repo.PackageDataRepository;
import com.yxt.talent.bk.core.udp.bean.UdpUserBriefBean;
import com.yxt.talent.bk.core.udp.bean.UdpUserLocaleBean;
import com.yxt.talent.bk.core.udp.mapper.UdpLiteUserMapper;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.common.TodoRpc;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.HeirPosUserService;
import com.yxt.talent.bk.svc.heir.bean.HeirEditParam4Log;
import com.yxt.talent.bk.svc.heir.bean.HeirPos4Notify;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareBean;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareDumpParam;
import com.yxt.talent.bk.svc.heir.bean.RemindTodoOperateDTO;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserListReq;
import com.yxt.talent.bk.svc.heir.bean.req.PosUserUpdateReq;
import com.yxt.talent.bk.svc.heir.bean.resp.PosUserListResp;
import com.yxt.talent.bk.svc.heir.constants.HeirNoticeConstants;
import com.yxt.talent.bk.svc.heir.enums.PkgBizTypeEnum;
import com.yxt.talent.bk.svc.heir.enums.PosUserModifyStatusEnum;
import com.yxt.talent.bk.svc.heir.enums.PosUserStatusEnum;
import com.yxt.talent.bk.svc.heir.enums.TodoStatusEnum;
import com.yxt.talent.bk.svc.udp.rpc.UdpRpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.javers.common.collections.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * HeirPosUserServiceImpl
 *
 * <AUTHOR>
 * @date 2023/08/16
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class HeirPosUserServiceImpl implements HeirPosUserService {

    private final HeirPosUserMapper posUserMapper;
    private final HeirPosMapper heirPosMapper;
    private final HeirRemindTodoMapper heirRemindTodoMapper;
    private final HeirPrepareRemindUserMapper heirPrepareRemindUserMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final HeirPrepareHisMapper heirPrepareHisMapper;
    private final HeirPosRepository heirPosRepository;
    private final HeirPosUserRepository heirPosUserRepository;
    private final PackageDataRepository packageDataRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final HeirPosService heirPosService;
    private final AuthService authService;
    private final TodoRpc todoRpc;
    private final UdpRpc udpRpc;

    @DbHintMaster
    @Override
    public void createPosUsers(UserCacheBasic currentUser, String posId, Integer posType, List<String> userIds) {
        String orgId = currentUser.getOrgId();
        HeirPosExt posExt = heirPosMapper.getById(orgId, posId);
        if (posExt == null && posType == HeirPosTypeEnum.DEPT.getType()) {
            posExt = heirPosService.init(currentUser, posId, posType);
        }
        if (posExt == null) {
            throw new ApiException(BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE);
        }
        Set<String> userIdSet = userIds.stream().collect(Collectors.toSet());
        // 校验添加的继任者是否是本岗位的在职人员
        List<String> currentPositionUserIds = udpLiteUserRepository.listIdByPosition(orgId, posId, userIds);
        if (CollectionUtils.isNotEmpty(currentPositionUserIds)) {
            log.info("position exist users. positionId: {} userIds: {}", posId, currentPositionUserIds);
            throw new ApiException(BkApiErrorKeys.HEIR_USER_POSITION_EXIST);
        }

        // 过滤已存在的用户
        List<String> existUserIds = posUserMapper.listExistUserId(orgId, posId, userIds);
        if (CollectionUtils.isNotEmpty(existUserIds)) {
            log.info("exist userIds. posId: {} userIds: {}", posId, existUserIds);
            userIdSet.removeAll(existUserIds);
        }
        if (CollectionUtils.isEmpty(userIdSet)) {
            log.info("no user will be add. posId: {} userIds: {}", posId, userIds);
            return;
        }
        // 查询需要添加的用户信息
        List<String> addUserIds = udpLiteUserRepository.existUserIds(orgId, userIdSet);
        List<HeirPosUserEntity> saveList = new ArrayList<>();
        addUserIds.stream().forEach(userId -> {
            HeirPosUserEntity posUser = new HeirPosUserEntity();
            posUser.init(orgId, currentUser.getUserId());
            posUser.setPosId(posId);
            posUser.setPosType(posType);
            posUser.setUserId(userId);
            posUser.setHeirStatus(PosUserStatusEnum.GOING.getValue());
            posUser.setModifyStatus(PosUserModifyStatusEnum.INIT.getValue());
            saveList.add(posUser);
        });
        AuditLogHooker.modLogParam(HeirEditParam4Log.class, logParam -> {
            logParam.setUserIds(addUserIds);
        });
        BatchOperationUtil.batchSave(saveList, posUserMapper::insertBatchSomeColumn);
        heirPosRepository.updatePosValidQty(orgId, posId);
        log.info("createPosUsers finish. posId: {} userIds: {}", posId, userIdSet);
    }

    @DbHintMaster
    @Override
    public void updatePosUsers(UserCacheBasic currentUser, PosUserUpdateReq req) {
        String orgId = currentUser.getOrgId();
        List<HeirPosUserExt> existUsers = posUserMapper.listExistByIds(orgId, req.getIds());
        List<HeirPosUserEntity> updateList = new ArrayList<>();
        // 批量调整准备度
        if (req.getPrepareLevelId() != null) {
            boolean hasValidUser = existUsers.stream()
                .filter(x -> x.getHeirStatus() == PosUserStatusEnum.GOING.getValue()).findFirst().isPresent();
            if (!hasValidUser) {
                throw new ApiException(BkApiErrorKeys.HEIR_USER_EXIT);
            }
        }
        existUsers.stream().forEach(userExt -> {
            // 已删除的用户无法重新加入
            if (req.getHeirStatus() != null && req.getHeirStatus() == PosUserStatusEnum.GOING.getValue()
                && userExt.getUserDeleted() == YesOrNo.YES.getValue()) {
                throw new ApiException(BkApiErrorKeys.HEIR_USER_DELETE);
            }

            // 调整准备度过滤掉已退出的用户
            if (req.getPrepareLevelId() != null && userExt.getHeirStatus() == PosUserStatusEnum.EXIT.getValue()) {
                log.info("posUser is exit, skip. id: {}", userExt.getId());
                return;
            }
            // 更新继任状态时跳过状态修改前后一样的数据
            if (req.getHeirStatus() != null && req.getHeirStatus().equals(userExt.getHeirStatus())) {
                log.info("posUser heirStatus not modify, skip. id: {}", userExt.getId());
                return;
            }

            HeirPosUserEntity entity = new HeirPosUserEntity();
            entity.setId(userExt.getId());
            entity.init(orgId, currentUser.getUserId());
            entity.setPrepareLevelId(req.getPrepareLevelId());
            entity.setHeirStatus(req.getHeirStatus());
            entity.setQuitReason(req.getQuitReason());
            if (req.getHeirStatus() != null && req.getHeirStatus() == PosUserStatusEnum.EXIT.getValue()) {
                entity.setExitTime(DateUtil.currentTime());
            }
            updateList.add(entity);
        });

        BatchOperationUtil.batchSave(updateList, posUserMapper::batchUpdate);
        if (!updateList.isEmpty()) {
            //批量调整是对同一个posId操作的
            String posId = existUsers.get(0).getPosId();
            List<HeirPosUserIdDTO> targetUserIds = updateList.stream().map(item -> new HeirPosUserIdDTO(item.getId(), item.getUserId()))
                .collect(Collectors.toList());
            // 批量调整准备度
            if (req.getPrepareLevelId() != null && req.getPrepareLevelId() > 0) {
                asyncBatchDoneTodo(orgId, posId, targetUserIds, currentUser.getUserId());
            } else if (req.getHeirStatus() != null && req.getHeirStatus() == PosUserStatusEnum.EXIT.getValue()) {
                //退出删除待办
                asyncBatchDoneTodo(orgId, posId, targetUserIds, null);
            }
        }
        if (req.getHeirStatus() != null) {
            heirPosRepository.updatePosValidQty(orgId, req.getPosId());
        }
        log.info("updatePosUsers finish. posId: {} ids: {}", req.getPosId(), updateList.stream().map(HeirPosUserEntity::getId).collect(
            Collectors.toList()));
    }

    @Override
    public PagingList<PosUserListResp> list(PageRequest pageReq, UserCacheBasic currentUser, PosUserListReq req) {
        Page<PosUserListResp> page = new Page<>(pageReq.getCurrent(), pageReq.getSize(), true);
        IPage<HeirPosUserExt> pageList = heirPosUserRepository.list(page , currentUser.getOrgId(), req.getPosId(),
            req.getHeirStatus(), req.getPrepareLevelId());
        return BeanCopierUtil.toPagingList(pageList, HeirPosUserExt.class, PosUserListResp.class);
    }

    @Override
    public PagingList<PosUserBriefBean> validBriefPage(UserCacheBasic currentUser, PageRequest pageReq, String posId) {
        Page<PosUserBriefBean> page = new Page<>(pageReq.getCurrent(), pageReq.getSize(), true);
        IPage<PosUserBriefBean> pageList = heirPosRepository.listValidBrief(page,
            currentUser.getOrgId(), posId, PosUserStatusEnum.GOING.getValue());
        return BeanCopierUtil.toPagingList(pageList, PosUserBriefBean.class, PosUserBriefBean.class);
    }

    @Override
    public List<PosUserBriefBean> listUserBrief(UserCacheBasic currentUser, String posId) {
        String orgId = currentUser.getOrgId();
        List<PosUserBriefBean> list = heirPosRepository.listValidBrief(new Page(-1, Integer.MAX_VALUE, false),
            currentUser.getOrgId(), posId, null).getRecords();
        if (CollectionUtils.isNotEmpty(list)) {
            Locale locale = authService.getLocale();
            Map<Long, HeirPrepareBean> prepareMap = heirPosService.queryPrepareMap(orgId, locale);
            for (PosUserBriefBean record : list) {
                HeirPrepareBean prepareBean = prepareMap.get(record.getPrepareLevelId());
                if (prepareBean != null) {
                    record.setLevelName(prepareBean.getLevelName());
                    record.setLevelColor(prepareBean.getColorCode());
                }
            }
        }
        return list;
    }

    @DbHintMaster
    @Override
    public void delete(UserCacheBasic currentUser, String posId, List<String> userIds) {
        String orgId = currentUser.getOrgId();
        HeirPosExt posExt = heirPosMapper.getById(orgId, posId);
        Validate.isNotNull(posExt, BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE);

        List<HeirPosUserIdDTO> needDeleteList = posUserMapper.getIdByUserIds(orgId, posId, userIds);
        posUserMapper.deleteByUserIds(orgId, currentUser.getUserId(), DateUtil.currentTime(), posId, userIds);
        heirPosRepository.updatePosValidQty(orgId, posId);
        log.info("delete posUser finish. posId: {} userIds: {}", posId, userIds);
        asyncBatchDoneTodo(orgId, posId, needDeleteList, null);
    }

    @DbHintMaster
    @Override
    public void handleUserDel(String orgId, List<String> userIds) {
        List<HeirPosUserBean> posUsers = BatchOperationUtil.batchQuery(userIds,
            subUserIds -> posUserMapper.selectOnHeirByUserIds(orgId, subUserIds));
        Date currentTime = DateUtil.currentTime();
        BatchOperationUtil.batchSave(posUsers, subPosUsers -> {
            posUserMapper.batchQuitHeir(orgId, subPosUsers.stream().map(HeirPosUserBean::getId).collect(Collectors.toList()), currentTime);
        });
        posUsers.stream().map(HeirPosUserBean::getPosId).collect(Collectors.toSet())
                .forEach(posId -> heirPosRepository.updatePosValidQty(orgId, posId));
        IArrayUtils.list2Map(posUsers, HeirPosUserBean::getPosId).forEach((posId, subPosUsers) -> {
            //待办完成处理
            asyncBatchDoneTodo(orgId, posId, subPosUsers.stream()
                .map(item -> new HeirPosUserIdDTO(item.getId(), item.getUserId()))
                .collect(Collectors.toList()), null);
        });
    }

    /**
     *
     * @param orgId
     * @param posId
     * @param targetUserIds
     */
    private void asyncBatchDoneTodo(String orgId, String posId, List<HeirPosUserIdDTO> targetUserIds, String doneUserId) {
        if (CollectionUtils.isEmpty(targetUserIds)) {
            return;
        }
        CallUtils.asyncCall(() -> {
            List<HeirRemindTodoIdDTO> todoIds = heirRemindTodoMapper.tgtRecordRemind(orgId, posId,
                BeanCopierUtil.convertList(targetUserIds, HeirPosUserIdDTO::getId));
            BatchOperationUtil.batchExecute(todoIds, 200, subList -> {
                List<HeirRemindTodoIdDTO> doneList = new ArrayList<>();
                List<HeirRemindTodoIdDTO> delList = new ArrayList<>();
                if (StringUtils.isNotBlank(doneUserId)) {
                    subList.forEach(item -> {
                        if (doneUserId.equals(item.getRemindUserId())) {
                            //操作用户完成自己的待办
                            doneList.add(item);
                        } else {
                            delList.add(item);
                        }
                    });
                } else {
                    delList.addAll(subList);
                }
                if (!doneList.isEmpty()) {
                    log.info("HeirRemindTodoDone orgId {} posId {} doneList {}",
                        orgId, posId, JSON.toJSONString(doneList));
                    //完成待办
                    Todo4DoneBatch doneBatch = new Todo4DoneBatch();
                    doneBatch.setOrgId(orgId);
                    doneBatch.setTodos(BeanCopierUtil.convertList(doneList, item -> {
                        Todo4DoneBatch.Todo todo = new Todo4DoneBatch.Todo();
                        todo.setTodoId(item.getTodoId());
                        todo.setUserIds(Lists.newArrayList(item.getRemindUserId()));
                        return todo;
                    }));
                    doneBatch.setOperateUserId(doneUserId);
                    doneBatch.setSceneCode(HeirNoticeConstants.TODO_CODE_GWNL_SUCCESSION_CHANGE);
                    todoRpc.batchDone(doneBatch);
                    heirRemindTodoMapper.batchEndTodo(orgId, BeanCopierUtil.convertList(doneList, HeirRemindTodoIdDTO::getId),
                        TodoStatusEnum.DONE.getCode());
                }
                if (!delList.isEmpty()) {
                    //删除用户或者退出要取消待办
                    Todo4DeleteBatch delTodo = new Todo4DeleteBatch();
                    delTodo.setOrgId(orgId);
                    delTodo.setTodoIds(BeanCopierUtil.convertList(delList, HeirRemindTodoIdDTO::getTodoId));
                    delTodo.setOperateUserId(TalentBkConstants.CODE_OPERATOR_ID);
                    delTodo.setSceneCode(HeirNoticeConstants.TODO_CODE_GWNL_SUCCESSION_CHANGE);
                    todoRpc.deleteBatch(delTodo);
                    heirRemindTodoMapper.batchEndTodo(orgId, BeanCopierUtil.convertList(delList, HeirRemindTodoIdDTO::getId),
                        TodoStatusEnum.CANCEL.getCode());
                }
            });
        });
    }

    public void asyncBatchCreateTodo(String orgId, HeirPos4Notify pos4Notify, List<HeirPosUserIdDTO> targetUserIds) {
        if (CollectionUtils.isEmpty(targetUserIds)) {
            return;
        }
        String posId = pos4Notify.getPosId();
        List<String> remindUserIds = heirPrepareRemindUserMapper.posRemindUserIds(posId);
        if (CollectionUtils.isNotEmpty(remindUserIds)) {
            //创建准备度完成待办
            CallUtils.asyncCall(() -> {
                for (HeirPosUserIdDTO targetUser : targetUserIds) {
                    for (String remindUserId : remindUserIds) {
                        RemindTodoOperateDTO todoOpt = new RemindTodoOperateDTO();
                        todoOpt.setDoneTodo(false);
                        todoOpt.setOrgId(orgId);
                        todoOpt.setPosId(posId);
                        todoOpt.setRemindUserId(remindUserId);
                        todoOpt.setTargetUserId(targetUser.getUserId());
                        todoOpt.setTargetRecordId(targetUser.getId());
                        todoOpt.setJumpUrl(targetUser.getJumpUrl());
                        todoOpt.setTodoParam(targetUser.getTodoParam());
                        operateRemindTodo(pos4Notify, todoOpt);
                    }
                }
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void prepareUrgeHandle(UserBasicBean userBasic, String posId, String userId, Long prepareLevelId,
                                  HeirPrepareDumpParam dumpParam) {
        String orgId = userBasic.getOrgId();
        HeirPosUserEntity heirPosUser = heirPosUserRepository.getByUserId(orgId, posId, userId);
        if (heirPosUser == null) {
            throw new ApiException(BkApiErrorKeys.HEIR_USER_DELETE);
        }
        if (prepareLevelId != null && !Optional.ofNullable(dumpParam.getPrepareLevelIds())
                .map(levelIds -> levelIds.contains(prepareLevelId)).orElse(false)) {
            throw new ApiException(BkApiErrorKeys.UNKNOWN_ERROR);
        }
        int modifyStatus = prepareLevelId != null ? YesOrNo.YES.getValue() : YesOrNo.NO.getValue();
        //处理历史记录保存
        HeirPrepareHisEntity hisEntity = new HeirPrepareHisEntity();
        hisEntity.setPosId(posId);
        hisEntity.setUserId(userId);
        hisEntity.setOptUserId(userBasic.getUserId());
        hisEntity.setUserLevelId(heirPosUser.getPrepareLevelId());
        hisEntity.setChangeLevelId(prepareLevelId);
        hisEntity.setModifyStatus(modifyStatus);
        hisEntity.setRuleDataId(dumpParam.getRuleDataId());
        hisEntity.setMatchDataId(packageDataRepository.savePackage(orgId, PkgBizTypeEnum.HEIR_RULE_MATCH.getCode(),
                String.valueOf(heirPosUser.getId()), dumpParam.getRuleMatchResult()));
        hisEntity.init(orgId, userBasic.getUserId());
        heirPrepareHisMapper.insert(hisEntity);
        //继任准备度处理结果保存
        HeirPosUserEntity updatePosUser = new HeirPosUserEntity();
        updatePosUser.setId(heirPosUser.getId());
        updatePosUser.setModifyStatus(modifyStatus);
        updatePosUser.setPrepareLevelId(prepareLevelId);
        updatePosUser.init(orgId, userBasic.getUserId());
        heirPosUserRepository.updateById(updatePosUser);
        //代办删除
        asyncBatchDoneTodo(orgId, posId,
                Lists.newArrayList(new HeirPosUserIdDTO(heirPosUser.getId(), heirPosUser.getUserId())), userBasic.getUserId());
    }

    private void operateRemindTodo(HeirPos4Notify pos4Notify, RemindTodoOperateDTO todoOpt) {
        String orgId = todoOpt.getOrgId();
        String unionKey = todoOpt.getPosId() + todoOpt.getRemindUserId() + todoOpt.getTargetUserId();
        String key = String.format(TalentBkRedisKeys.LOCK_KEY_HEIR_REMIND_TODO_OPT, unionKey);
        CommonUtils.lockRun(key, 2, TimeUnit.MINUTES, () -> {
            HeirRemindTodoEntity remindTodo = heirRemindTodoMapper.getRemindTodo(todoOpt.getOrgId(), todoOpt.getPosId(),
                todoOpt.getRemindUserId(), todoOpt.getTargetRecordId());
            if (todoOpt.isDoneTodo()) {
                if (remindTodo != null) {
                    Todo4Done todo4Done = new Todo4Done();
                    todo4Done.setSceneCode(HeirNoticeConstants.TODO_CODE_GWNL_SUCCESSION_CHANGE);
                    todo4Done.setTodoId(remindTodo.getTodoId());
                    todo4Done.setGroupUserInfos(Lists.newArrayList(todoRpc.buildUser(orgId, remindTodo.getRemindUserId())));
                    todo4Done.setOperateUserId(remindTodo.getTargetUserId());
                    todoRpc.done(todo4Done);
                    heirRemindTodoMapper.batchEndTodo(orgId, Lists.newArrayList(remindTodo.getId()),
                        TodoStatusEnum.DONE.getCode());
                }
            } else {
                if (remindTodo != null) {
                    RemindTodoParam newParam = todoOpt.getTodoParam();
                    if (newParam == null) {
                        return;
                    }
                    RemindTodoParam oldParam = JSON.parseObject(remindTodo.getRemindParam(), RemindTodoParam.class);
                    if (oldParam == null || !Objects.equals(oldParam.getPrepareId(), newParam.getPrepareId())
                        || !Objects.equals(oldParam.getPrepareName(), newParam.getPrepareName())) {
                        //更新待办登记信息
                        HeirRemindTodoEntity updateTodo = new HeirRemindTodoEntity();
                        updateTodo.setId(remindTodo.getId());
                        updateTodo.setRemindParam(JSON.toJSONString(newParam));
                        updateTodo.init(orgId);
                        heirRemindTodoMapper.updateById(updateTodo);
                    }
                } else {
                    operateRemindTodoCreate(pos4Notify, todoOpt);
                }
            }
        });
    }

    private void operateRemindTodoCreate(HeirPos4Notify pos4Notify, RemindTodoOperateDTO todoOpt) {
        String orgId = todoOpt.getOrgId();
        HeirRemindTodoEntity createTodo = new HeirRemindTodoEntity();
        createTodo.setOrgId(todoOpt.getOrgId());
        createTodo.setPosId(todoOpt.getPosId());
        createTodo.setRemindUserId(todoOpt.getRemindUserId());
        createTodo.setTargetUserId(todoOpt.getTargetUserId());
        createTodo.setTargetRecordId(todoOpt.getTargetRecordId());
        createTodo.setRemindParam(JSON.toJSONString(todoOpt.getTodoParam()));
        createTodo.init(orgId);
        createTodo.setTodoId(String.valueOf(createTodo.getId()));
        createTodo.setTodoStatus(TodoStatusEnum.CREATE.getCode());
        String locale = Optional.ofNullable(IArrayUtils.getFirst(udpLiteUserMapper.queryLocaleByIds(orgId, Lists.newArrayList(todoOpt.getRemindUserId()))))
                .map(UdpUserLocaleBean::getLocale).orElse(null);
        UdpLangFullBean fullBean = new UdpLangFullBean();

        //创建待办
        Todo4Create todo4Create = new Todo4Create();
        todo4Create.setSceneCode(HeirNoticeConstants.TODO_CODE_GWNL_SUCCESSION_CHANGE);
        todo4Create.setOrgId(orgId);
        todo4Create.setOperateUserId(createTodo.getTargetUserId());
        todo4Create.setGroupUserInfos(Lists.newArrayList(todoRpc.buildUser(orgId, todoOpt.getRemindUserId())));
        todo4Create.setOrganizer("继任");
        todo4Create.setTodoId(createTodo.getTodoId());
        Map<String, String> params = new HashMap<>();
        //标题参数
        params.put("{{typeName}}", "继任");
        udpLiteUserRepository.fillUserInfo(orgId, Lists.newArrayList(todoOpt.getTargetUserId()), item -> item, (userId, userBrief) -> {
            fullBean.setId(userId);
            fullBean.setFullname(userBrief.getFullname());
        }, Lists.newArrayList("fullname"));
        //内容参数
        String namePrefix = "岗位继任-";
        IdName heirName = new IdName();
        heirName.setId(pos4Notify.getPosId());
        heirName.setName(pos4Notify.getPosName());
        if (pos4Notify.getPosType() == HeirPosTypeEnum.DEPT.getType()) {
            namePrefix = "部门继任-";
            fullBean.setDeptId(heirName.getId());
            fullBean.setDeptName(heirName.getName());
            fullBean.setDeptNameSetter(heirName::setName);
        } else {
            fullBean.setPositionId(heirName.getId());
            fullBean.setPositionName(heirName.getName());
            fullBean.setPositionNameSetter(heirName::setName);
        }
        if (StringUtils.isNotEmpty(heirName.getName())) {
            if (StringUtils.isNotEmpty(locale)) {
                TalentbkUtil.udpTranslate(Pair.of(locale, orgId), Lists.newArrayList(fullBean));
            }
            params.put("{{userName}}", fullBean.getFullname());
            params.put("{{successionNmae}}", namePrefix + heirName.getName());
        } else {
            log.warn("SendTodo orgId {} posId {} targetUserId {} heirName not found",
                    orgId, todoOpt.getPosId(), todoOpt.getTargetUserId());
            return;
        }
        String jumpUrl = todoOpt.getJumpUrl();
        params.put("{{reminderTime}}", DateUtil.now());
        params.put("{{url}}", jumpUrl);
        todo4Create.setParams(params);
        todo4Create.setCustomParams(Maps.of("name", heirName.getName()));
        todo4Create.setJumpUrl(jumpUrl);
        todoRpc.create(todo4Create);
        heirRemindTodoMapper.insert(createTodo);
    }
}
