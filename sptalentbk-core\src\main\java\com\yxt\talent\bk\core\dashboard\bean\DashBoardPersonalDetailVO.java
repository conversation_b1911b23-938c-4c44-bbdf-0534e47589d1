package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/25
 */
@Data
public class DashBoardPersonalDetailVO {
    @Schema(description = "用户ID")
    private String userId;
    @Schema(description = "头像")
    private String imgUrl;
    @Schema(description = "第三方用户ID")
    private String thirdUserId;
    @Schema(description = "账号")
    private String username;
    @Schema(description = "姓名")
    private String fullName;
    @Schema(description = "部门ID")
    private String deptId;
    @Schema(description = "部门名称")
    private String deptName;
    @Schema(description = "岗位ID")
    private String positionId;
    @Schema(description = "岗位名称")
    private String positionName;
    @Schema(description = "用户评估过的能力模型和任务模型集合")
    private List<PersonalEvalModelSimpleVO> models;
    //    @Schema(description = "用户评估过的能力模型和任务模型集合")
    //    private List<PersonalEvalModelVO> models;
}
