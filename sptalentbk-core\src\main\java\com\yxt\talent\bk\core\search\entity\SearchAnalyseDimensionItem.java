package com.yxt.talent.bk.core.search.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 搜索透视维度项表
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Data
@Deprecated
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("bk_search_analyse_dimension_item")
public class SearchAnalyseDimensionItem extends CreatorEntity {

    @TableId("id")
    private String id;

    @TableField("org_id")
    private String orgId;

    /**
     * 透视项key
     */
    @TableField("item_key")
    private String itemKey;

    /**
     * 透视项名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * 透视项分类名称
     */
    @TableField("item_catalog_name")
    private String itemCatalogName;

    @TableField("order_index")
    private int orderIndex;
}
