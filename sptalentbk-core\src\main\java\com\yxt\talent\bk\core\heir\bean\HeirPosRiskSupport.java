package com.yxt.talent.bk.core.heir.bean;

/**
 * HeirPosRiskSupport
 *
 * <AUTHOR> geyan
 * @Date 14/9/23 6:09 pm
 */
public interface HeirPosRiskSupport {
    /**
     * 风险等级规则类型
     * @return
     */
    Integer getRiskRuleType();

    /**
     * 目标数量
     * @return
     */
    Integer getHeirTargetQty();

    /**
     * 有效数量
     * @return
     */

    Integer getHeirValidQty();

    /**
     * 风险等级id
     * @return
     */
    Long getRiskLevelId();

    /**
     * 设置风险等级id
     * @param riskLevelId
     */
    default void setRiskLevelId(Long riskLevelId) {
        //NO SONAR
    }

    /**
     * 设置风险等级名称
     * @param riskLevelName
     */
    default void setRiskLevelName(String riskLevelName) {
        //NO SONAR
    }

    /**
     * 设置风险等级颜色
     * @param riskLevelColor
     */
    default void setRiskLevelColor(String riskLevelColor) {
        //NO SONAR
    }
}
