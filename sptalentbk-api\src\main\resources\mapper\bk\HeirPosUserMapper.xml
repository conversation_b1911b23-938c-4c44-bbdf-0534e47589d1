<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirPosUserMapper">
    <select id="listExistUserId" resultType="java.lang.String">
        select user_id from bk_heir_pos_user where org_id = #{orgId} and pos_id = #{posId} and deleted = 0
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="listExistByIds" resultType="com.yxt.talent.bk.core.heir.ext.HeirPosUserExt">
        select pu.id, pu.pos_id, pu.heir_status, if(u.deleted is null,1,u.deleted) userDeleted from bk_heir_pos_user pu
        LEFT JOIN udp_lite_user_sp u ON pu.user_id = u.id AND pu.org_id = u.org_id
        where pu.org_id = #{orgId} and pu.deleted = 0
        and pu.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="batchUpdate">
        <foreach collection="list" separator=";" item="item">
            update bk_heir_pos_user
            <set>
                <if test="item.prepareLevelId != null and item.prepareLevelId != 0">
                    prepare_level_id = #{item.prepareLevelId},
                </if>
                <if test="item.prepareLevelId != null and item.prepareLevelId == 0">
                    prepare_level_id = null,
                </if>
                <if test="item.heirStatus != null">
                    heir_status = #{item.heirStatus},
                </if>
                <if test="item.quitReason != null">
                    quit_reason = #{item.quitReason},
                </if>
                <if test="item.exitTime != null">
                    exit_time = #{item.exitTime},
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <select id="list" resultType="com.yxt.talent.bk.core.heir.ext.HeirPosUserExt">
        SELECT
        u.username, u.fullname, u.dept_id,u.dept_name, u.position_id,u.position_name, u.status,
        pu.id, pu.user_id, pu.prepare_level_id, pu.calc_level_id, pu.heir_status, pu.quit_reason, pu.exit_time,
        if(u.deleted is null,1,u.deleted) as user_deleted
        FROM bk_heir_pos_user pu
        LEFT JOIN udp_lite_user_sp u ON pu.user_id = u.id AND pu.org_id = u.org_id
        WHERE pu.org_id = #{orgId} and pu.pos_id = #{posId} and pu.deleted = 0
        <if test="heirStatus != null">
            and pu.heir_status = #{heirStatus}
        </if>
        <if test="prepareLevelId != null">
            and pu.prepare_level_id = #{prepareLevelId}
        </if>
        order by pu.heir_status asc, u.status desc
    </select>

    <select id="listByPosIds" resultType="com.yxt.talent.bk.core.heir.ext.HeirPosUserExt">
        SELECT
        pu.pos_id,
        u.username, u.fullname, u.dept_id,u.dept_name, u.position_id,u.position_name, u.status,
        pu.id, pu.user_id, pu.prepare_level_id, pu.calc_level_id, pu.heir_status, pu.quit_reason, pu.exit_time,
        if(u.deleted is null,1,u.deleted) as user_deleted
        FROM bk_heir_pos_user pu
        LEFT JOIN udp_lite_user_sp u ON pu.user_id = u.id AND pu.org_id = u.org_id
        WHERE pu.org_id = #{orgId} and pu.deleted = 0 and pu.pos_id in
        <foreach collection="posIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by pu.heir_status asc, u.status desc
    </select>

    <select id="listValidBrief" resultType="com.yxt.talent.bk.core.heir.bean.PosUserBriefBean">
        SELECT pu.user_id, u.username, u.fullname, u.img_url, u.dept_id, ud.name as dept_name, pu.prepare_level_id, pu.heir_status
        FROM bk_heir_pos_user pu
        JOIN udp_lite_user_sp u ON pu.user_id = u.id and u.org_id = pu.org_id
        left join udp_dept ud on ud.id = u.dept_id
        WHERE pu.org_id = #{orgId} and pu.pos_id = #{posId} and pu.deleted = 0
        <if test="heirStatus != null">
            and pu.heir_status = #{heirStatus}
        </if>
    </select>

    <select id="posValidUserPrepare" resultType="com.yxt.talent.bk.core.heir.bean.PosUserPrepareBean">
        select pu.id,pu.user_id,pu.calc_level_id from bk_heir_pos_user pu
        join udp_lite_user_sp u ON pu.user_id = u.id and u.deleted = 0 and pu.org_id = u.org_id
        where pu.org_id = #{orgId} and pu.pos_id = #{posId} and pu.deleted = 0 and pu.heir_status = 0
    </select>

    <select id="posValidUserQty" resultType="int">
        select count(pu.id) from bk_heir_pos_user pu
        join udp_lite_user_sp u ON pu.user_id = u.id and u.deleted = 0 and u.org_id = pu.org_id
        where pu.org_id = #{orgId} and pu.pos_id = #{posId} and pu.deleted = 0 and pu.heir_status = 0
    </select>

    <update id="batchUpdateUserPrepare">
        update bk_heir_pos_user set calc_level_id = case
        <foreach collection="list" item="item">
            when id = #{item.id} then #{item.calcLevelId}
        </foreach>
        else calc_level_id end,
        modify_status = -1,
        update_time = now()
        where org_id = #{orgId} and id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="getIdByUserIds" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosUserIdDTO">
        select id,user_id from bk_heir_pos_user where org_id = #{orgId} and deleted = 0 and pos_id = #{posId}  and user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByUserIds">
        update bk_heir_pos_user set update_user_id = #{currentUserId}, update_time = #{currentTime}, deleted = 1
        where org_id = #{orgId} and deleted = 0 and pos_id = #{posId}  and user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="queryUserPosIds" resultType="string">
        select distinct pos_id from bk_heir_pos_user
        where org_id = #{orgId} and deleted = 0 and heir_status = 0 and user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectUsePreparUser" resultType="java.lang.Long">
        select id from bk_heir_pos_user where org_id = #{orgId} and deleted = 0
        and prepare_level_id=#{prepareLevelId} limit 1
    </select>

    <select id="selectShouldRemindUser" resultType="com.yxt.talent.bk.core.heir.entity.HeirPosUserEntity">
        select pu.* from bk_heir_pos_user pu inner join bk_heir_pos pos on pu.org_id = pos.org_id and pu.pos_id = pos.id
        where pu.org_id =#{orgId} and pu.deleted =0 and pos.deleted =0 and pu.heir_status=0 and pu.calc_level_id is not null
        and pu.modify_status != 0
        and pu.pos_id=#{posId}  and (pu.prepare_level_id is null or (pu.prepare_level_id!=pu.calc_level_id))
    </select>

    <select id="selectOnHeirByUserIds" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosUserBean">
        select id,pos_id,user_id from bk_heir_pos_user where org_id = #{orgId} and deleted = 0 and heir_status = 0
        and user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="batchQuitHeir">
        update bk_heir_pos_user set heir_status = 1,quit_reason = '用户删除', exit_time = #{currentTime}, update_time = #{currentTime}
        where org_id = #{orgId} and id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="listBriefByIds" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosUserBriefBean">
        select id,user_id,prepare_level_id from bk_heir_pos_user where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listPage4Open" resultType="com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosUserBean">
        select h.id,h.org_id,h.pos_id,h.user_id,u.third_user_id,
               h.prepare_level_id,h.create_time,h.update_time,h.deleted
        from bk_heir_pos_user h
        join udp_lite_user_sp u on u.id = h.user_id and u.org_id = #{orgId} and u.deleted = 0
        left join udp_dept d on d.id = h.pos_id and h.pos_type = 1 and d.deleted = 0
        where h.org_id = #{orgId} and h.heir_status = 0 and h.deleted = 0 and (h.pos_type = 0 or d.id is not null)
        order by h.id
    </select>
</mapper>
