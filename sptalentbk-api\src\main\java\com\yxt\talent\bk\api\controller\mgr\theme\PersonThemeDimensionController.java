package com.yxt.talent.bk.api.controller.mgr.theme;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.talent.bk.api.component.PersonaThemeComponent;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.persona.bean.Portrait4List;
import com.yxt.talent.bk.svc.persona.bean.Theme4List;
import com.yxt.talent.bk.svc.persona.bean.ThemeDimension4Create;
import com.yxt.talent.bk.svc.persona.bean.ThemeDimension4Update;
import com.yxt.talent.bk.svc.persona.bean.ThemeDimensionTag4Create;
import com.yxt.talent.bk.svc.persona.theme.ThemeDimensionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Slf4j
@Deprecated
@RestController
@RequestMapping("/mgr/theme")
@AllArgsConstructor
@Tag(name = "主题和维度")
public class PersonThemeDimensionController extends BaseController {
    private final ThemeDimensionService themeDimensionService;
    private final PersonaThemeComponent personaThemeComponent;

    @Deprecated
    @Operation(summary = "添加维度")
    @PostMapping(value = "/add/dimension", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public void createDimension(@RequestBody ThemeDimension4Create create) {
        UserCacheDetail userDetail = getUserCacheDetail();
        themeDimensionService.createDimension(userDetail.getUserId(), userDetail.getOrgId(), create);
    }

    @Deprecated
    @Operation(summary = "更新维度名称")
    @PutMapping(value = "/dimension", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public void updateDimension(@RequestBody ThemeDimension4Update update) {
        UserCacheDetail userDetail = getUserCacheDetail();
        themeDimensionService.updateDimension(userDetail.getUserId(), userDetail.getOrgId(), update);
    }

    @Deprecated
    @Operation(summary = "添加维度标签")
    @PostMapping(value = "/dimension/tag", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public void createDimensionTag(@RequestBody ThemeDimensionTag4Create create) {
        UserCacheDetail userDetail = getUserCacheDetail();
        themeDimensionService.createDimensionTag(userDetail.getUserId(), userDetail.getOrgId(), create);
    }

    @Deprecated
    @Operation(summary = "删除维度")
    @Parameter(name = "dimensionId", description = "维度Id", in = ParameterIn.PATH)
    @DeleteMapping(value = "/{dimensionId}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public void removeDimension(@PathVariable String dimensionId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        themeDimensionService.removeDimension(userDetail.getOrgId(), dimensionId);
    }

    @Deprecated
    @Operation(summary = "删除维度标签")
    @Parameter(name = "dimensionTagMapId", description = "维度标签关系Id", in = ParameterIn.PATH)
    @DeleteMapping(value = "/dimension/{dimensionTagMapId}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public void removeDimensionTag(@PathVariable String dimensionTagMapId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        themeDimensionService.removeDimensionTag(userDetail.getOrgId(), dimensionTagMapId);
    }

    @Deprecated
    @Operation(summary = "维度主题列表")
    @GetMapping(value = "/list")
    @ResponseStatus(HttpStatus.OK)
    @Parameters({             @Parameter(name = "sourceType", description = "主题来源，-1:全部，0-内置,1-自建", in = ParameterIn.QUERY)})
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public List<Theme4List> getTheme4List(@RequestParam(required = false, defaultValue = "-1") int sourceType) {
        UserCacheDetail userDetail = getUserCacheDetail();
        personaThemeComponent.initThemeDimensionTag(userDetail.getOrgId(), userDetail.getUserId());
        return personaThemeComponent.getThemeList(userDetail.getOrgId(), sourceType);
    }


    @Deprecated
    @Operation(summary = "用户画像")
    @GetMapping(value = "/persona/portrait")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_THEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public List<Portrait4List> getPersonaPortrait(@RequestParam(name = "userId", required = false) String userId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        if (StringUtils.isBlank(userId)) {
            userId = userDetail.getUserId();
        }
        return personaThemeComponent.personaPortrait(userDetail.getOrgId(), userId);
    }
}
