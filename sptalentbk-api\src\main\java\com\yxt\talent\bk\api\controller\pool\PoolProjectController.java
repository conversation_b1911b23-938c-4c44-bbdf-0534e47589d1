package com.yxt.talent.bk.api.controller.pool;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.spsdk.audit.annotations.EasyAuditLogSelect;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.pool.PoolProjectService;
import com.yxt.talent.bk.svc.pool.bean.PoolProjectBindBean;
import com.yxt.talent.bk.svc.pool.bean.TrainingBaseInfoBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/mgr/poolproject")
@Slf4j
@AllArgsConstructor
@Tag(name = "人才池绑定培训和测评")
public class PoolProjectController extends BaseController {
    private final PoolProjectService poolProjectService;

    @Operation(summary = "删除绑定的培训或测评")
    @Auditing
    @EasyAuditLogSelect({@EasyAuditLog(value = AuditLogConstants.POOL_REMOVE_PROJECT, conditionExp = "#bean.targetType == 3",
            paramExp = "#bean"),
            @EasyAuditLog(value = AuditLogConstants.POOL_REMOVE_EVAL, conditionExp = "#bean.targetType != 3",
                    paramExp = "#bean.evalParam4Log()")})
    @PostMapping(value = "", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN)
    public void deleteProject(@RequestBody PoolProjectBindBean bean) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        poolProjectService.deletePoolProject(bean, userCacheBasic.getOrgId(), userCacheBasic.getUserId());
    }

    @Operation(summary = "培训发展：获取指定学员、项目的简单进度信息（学员得分、完成率、合格状态）")
    @GetMapping(value = "/trainingdevelopmentbaseinfo/{poolId}/{userId}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public List<TrainingBaseInfoBean> trainingDevelopmentBaseInfo(@PathVariable String poolId, @PathVariable String userId) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return poolProjectService.trainingDevelopmentBaseInfo(userCacheBasic.getOrgId(), userId, poolId);
    }

    @Operation(summary = "培训发展：根据人才池获取测评ids")
    @GetMapping(value = "/findevalids/{poolId}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public List<String> findEvalIds(@PathVariable String poolId) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return poolProjectService.findEvalIdsByPoolId(userCacheBasic.getOrgId(), poolId);
    }

}
