package com.yxt.talent.bk.core.usergroup.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.common.utils.SqlUtils;
import com.yxt.talent.bk.core.tag.bean.Tag4Page;
import com.yxt.talent.bk.core.usergroup.bean.UserGroup4Get;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupModulePageVO;
import com.yxt.talent.bk.core.usergroup.entity.UserGroup;
import com.yxt.talent.bk.core.usergroup.mapper.UserGroupMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/17
 */
@Slf4j
@Repository
public class UserGroupRepository extends ServiceImpl<UserGroupMapper, UserGroup> {

    private LambdaQueryWrapper<UserGroup> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public UserGroup getInfoById(String orgId, Long id) {
        LambdaQueryWrapper<UserGroup> queryWrapper = getQueryWrapper();
        queryWrapper.eq(UserGroup::getOrgId, orgId);
        queryWrapper.eq(UserGroup::getId, id);
        queryWrapper.eq(UserGroup::getDeleted, YesOrNo.NO.getValue());
        return getOne(queryWrapper);
    }

    public void deleteLogic(String userId, Long id) {
        getBaseMapper().deleteLogic(userId, id);
    }

    public IPage<UserGroup4Get> find4Page(IPage<Tag4Page> page, String orgId, String keyword, List<String> deptIds,
            List<String> userIds, int teamGroup) {
        return getBaseMapper().find4Page(page, orgId, keyword, deptIds, userIds, teamGroup);
    }

    public IPage<UserGroup> selectList(IPage<UserGroup> page, int weekDate, int day, List<String> orgIds) {
        return getBaseMapper().selectCaculateGroup(page, weekDate, day, orgIds);
    }

    public List<UserGroup> selectAllList(int weekDate, int day, List<String> orgIds) {
        return getBaseMapper().selectAllCaculateGroup(weekDate, day, orgIds);
    }

    public List<String> findAllCreateUserIds(String orgId) {
        LambdaQueryWrapper<UserGroup> queryWrapper = getQueryWrapper();
        queryWrapper.eq(UserGroup::getOrgId, orgId);
        queryWrapper.eq(UserGroup::getDeleted, YesOrNo.NO.getValue());
        queryWrapper.eq(UserGroup::getEnabled, YesOrNo.YES.getValue());
        queryWrapper.eq(UserGroup::getTeamGroup, YesOrNo.NO.getValue());
        queryWrapper.select(UserGroup::getCreateUserId);
        List<UserGroup> userGroupList = list(queryWrapper);
        if (CollectionUtils.isEmpty(userGroupList)) {
            return new ArrayList<>();
        }
        return userGroupList.stream().map(UserGroup::getCreateUserId).filter(StringUtils::isNotBlank).distinct()
                .collect(Collectors.toList());
    }

    public Page<UserGroupModulePageVO> findAuthData4PageModule(IPage<UserGroupModulePageVO> pageable, String orgId,
            List<String> rangeUserIds, String groupName) {
        groupName = SqlUtils.escapeLike(groupName);
        return getBaseMapper().findAuthData4PageModule(pageable, orgId, rangeUserIds, groupName);
    }

    public long getAllCount(String orgId, List<Long> groupIds) {
        return getBaseMapper().getAllCount(orgId, groupIds);
    }

    public List<UserGroup> findAllAuthData(String orgId, List<String> rangeUserIds) {
        return getBaseMapper().findAllAuthData(orgId, rangeUserIds);
    }

    public Page<UserGroup> findAuthData4PageByIds(IPage<UserGroup> pageable, String orgId, List<Long> groupIds) {
        return getBaseMapper().findAuthData4PageByIds(pageable, orgId, groupIds);
    }

    public List<UserGroup> findByIds(String orgId, List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserGroup> queryWrapper = getQueryWrapper();
        queryWrapper.eq(UserGroup::getOrgId, orgId);
        queryWrapper.eq(UserGroup::getDeleted, YesOrNo.NO.getValue());
        queryWrapper.eq(UserGroup::getEnabled, YesOrNo.YES.getValue());
        queryWrapper.eq(UserGroup::getTeamGroup, YesOrNo.NO.getValue());
        queryWrapper.in(UserGroup::getId, groupIds);
        queryWrapper.orderByDesc(UserGroup::getCreateTime);
        return list(queryWrapper);
    }

    public List<UserGroup> listByOrgId(String orgId) {
        LambdaQueryWrapper<UserGroup> queryWrapper = getQueryWrapper();
        queryWrapper.eq(UserGroup::getOrgId, orgId);
        queryWrapper.eq(UserGroup::getDeleted, YesOrNo.NO.getValue());
        return list(queryWrapper);
    }
}
