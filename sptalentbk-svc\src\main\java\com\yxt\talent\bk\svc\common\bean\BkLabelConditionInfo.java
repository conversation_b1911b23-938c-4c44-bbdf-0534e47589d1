package com.yxt.talent.bk.svc.common.bean;

import com.yxt.spmodel.facade.bean.rule.LabelConditionInfo;
import com.yxt.spmodel.facade.bean.rule.LabelRuleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * BkLabelConditionInfo
 *
 * <AUTHOR> harley<PERSON>
 * @Date 25/7/24 11:09 am
 */
@Data
public class BkLabelConditionInfo {
    @Schema(name = "规则列表 最多不超过10条", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<BkLabelRuleInfo> rules;

    @Schema(name = "规则间逻辑 1：与 2：或", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer logic;

    @Schema(name = "状态 0：正常 1：异常")
    private Integer state = 0;

    public void copyOf(LabelConditionInfo conditionInfo) {
        this.logic = conditionInfo.getLogic();
        this.state = conditionInfo.getState();
        if (conditionInfo.getRules() != null) {
            rules = new ArrayList<>(conditionInfo.getRules().size());
            for (LabelRuleInfo rule : conditionInfo.getRules()) {
                BkLabelRuleInfo bkRule = new BkLabelRuleInfo();
                bkRule.copyOf(rule);
                rules.add(bkRule);
            }
        }
    }
}
