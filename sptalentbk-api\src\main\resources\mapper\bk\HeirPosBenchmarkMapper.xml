<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.heir.mapper.HeirPosBenchmarkMapper">

    <select id="posUser" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosUserBean">
        select id,user_id,deleted from bk_heir_pos_benchmark where pos_id = #{posId}
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            and user_id in
            <foreach collection="userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateDeleted">
        update bk_heir_pos_benchmark set deleted =
        case
        <foreach collection="list" item="item">
            when id = #{item.id} then #{item.updateDeleted}
        </foreach>
        else deleted end,update_time = now()
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="removeByIds">
        update bk_heir_pos_benchmark set deleted = 1 where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="removeByUserIds">
        update bk_heir_pos_benchmark set deleted = 1,update_time = now()
        where pos_id = #{posId} and user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="posTopUsers" resultType="com.yxt.talent.bk.core.heir.bean.HeirPosUserBean">
        select pos_id,user_id,username,fullname,img_url from
        (select pb.pos_id,pb.user_id,rank() over (PARTITION by pos_id ORDER by user_id desc) as rank_val,
        u.username,u.fullname,u.img_url
         from bk_heir_pos_benchmark pb
        join udp_lite_user_sp u on u.id = pb.user_id and u.deleted = 0 and u.org_id = pb.org_id
        and u.status = 1 and u.position_id = pb.pos_id
        where pb.deleted = 0 and pb.pos_id in
        <foreach collection="posIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) as t where rank_val &lt;= 3
    </select>

    <select id="listPage4Open" resultType="com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosUserBean">
        select h.id,h.org_id,h.pos_id,h.user_id,u.third_user_id,
               h.create_time,h.update_time,h.deleted
        from bk_heir_pos p
        join bk_heir_pos_benchmark h on h.pos_id = p.id
        join udp_lite_user_sp u on u.id = h.user_id and u.org_id = #{orgId} and u.deleted = 0
        where p.org_id = #{orgId} and p.deleted = 0 and h.deleted = 0
        order by h.id
    </select>
</mapper>
