package com.yxt.talent.bk.core.spmodel.mapper;

import com.yxt.talent.bk.core.dashboard.bean.PersonalEvalModelSimpleVO;
import com.yxt.talent.bk.core.spmodel.entity.DwdUserJqTaskRv;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工职责任务盘点结果(DwdUserJqTaskRv)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 16:12:37
 */
public interface DwdUserJqTaskRvMapper {

    List<DwdUserJqTaskRv> listByUserId(@Param("orgId") String orgId, @Param("userId") String userId,
            @Param("positionId") String positionId);

    List<PersonalEvalModelSimpleVO> getUsedRtModelListByUserId(@Param("orgId") String orgId,
            @Param("userId") String userId);

}

