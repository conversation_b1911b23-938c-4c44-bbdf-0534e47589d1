package com.yxt.talent.bk.svc.heir.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.bifrost.udp.localization.handle.L10NUdpTranslateHandle;
import com.yxt.msgfacade.bean.MsgBean;
import com.yxt.msgfacade.service.MsgFacade;
import com.yxt.talent.bk.common.utils.IArrayUtils;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.udp.bean.UdpUserLocaleBean;
import com.yxt.talent.bk.core.udp.mapper.UdpLiteUserMapper;
import com.yxt.talent.bk.svc.common.SendMsgLangBase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Locale;
import java.util.Map;

@Slf4j
@Component
public class MessageUtil {

    @Autowired
    private MsgFacade msgFacade;
    @Autowired
    private UdpLiteUserMapper udpLiteUserMapper;
    @Autowired
    private L10NUdpTranslateHandle l10NUdpTranslateHandle;

    public void sendTemMsg(MsgBean msgBean) {
        String jsonStr = JSON.toJSONString(msgBean);
        log.debug("sendTemMsg@begin@templateCode:{}@targetId:{}@msgBean:{}",
            msgBean.getTemplateCode(),msgBean.getTargetId(),jsonStr);
        try {
            msgFacade.sendTemMsg(msgBean);
        } catch (Exception e) {
            log.error("sendTemMsg@templateCode:"+msgBean.getTemplateCode()+"@targetId:"+msgBean.getTargetId()+"@error:{}", e);
        }
        log.debug("sendTemMsg@end@templateCode:{}@targetId:{}", msgBean.getTemplateCode(),msgBean.getTargetId());
    }

    public void sendTemMsg(SendMsgLangBase msgLangBase) {
        MsgBean msgBean = msgLangBase.msgBean();
        if (CollectionUtils.isEmpty(msgBean.getUserIds())) {
            log.warn("sendTemMsg emptyUserIds orgId {} templateCode {}", msgBean.getOrgId(), msgBean.getTemplateCode());
            return;
        }
        if (!l10NUdpTranslateHandle.provider().isEnableLocalization(msgBean.getOrgId())) {
            //未开启多语言直接发送
            sendTemMsg(msgBean);
        } else {
            String defaultLocale = Locale.SIMPLIFIED_CHINESE.toString();
            Map<String, List<String>> localeUserIdMap = IArrayUtils.list2Map(udpLiteUserMapper.queryLocaleByIds(msgBean.getOrgId(), msgBean.getUserIds()),
                    item -> StringUtils.isBlank(item.getLocale()) ?  defaultLocale : item.getLocale(),
                    UdpUserLocaleBean::getId);
            if (log.isDebugEnabled())  {
                log.debug("sendTemMsg orgId {} templateCode {} localeUserIdMap {}",
                        msgBean.getOrgId(), msgBean.getTemplateCode(), JSON.toJSON(localeUserIdMap));
            }

            //默认的语种直接发送，并从map中移除
            List<String> defaultUserIds = localeUserIdMap.remove(defaultLocale);
            if (CollectionUtils.isNotEmpty(defaultUserIds)) {
                msgBean.setUserIds(defaultUserIds);
                sendTemMsg(msgBean);
            }
            List<SendMsgLangBase> transList = Lists.newArrayList(msgLangBase);
            localeUserIdMap.forEach((lang, userIds) -> {
                TalentbkUtil.bkUdpTranslate(Pair.of(lang, msgBean.getOrgId()), false, transList);
                msgBean.setUserIds(userIds);
                sendTemMsg(msgLangBase.msgBean());
            });
        }
    }
}
