package com.yxt.talent.bk.svc.profile.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "人才画像-职业特征评估")
public class UserCharacter4Get {

    @Schema(description = "职业驱动力")
    private List<String> driving;

    @Schema(description = "性格特点")
    private List<String> trait;

    @Schema(description = "任职风险")
    private List<String> jobRisk;
}
