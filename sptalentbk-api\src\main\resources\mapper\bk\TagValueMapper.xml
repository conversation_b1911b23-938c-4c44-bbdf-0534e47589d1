<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.tag.mapper.TagValueMapper">

    <select id="findByOrgIdAndTagKey" resultType="com.yxt.talent.bk.core.tag.entity.TagValueEntity">
        SELECT T2.* FROM bk_tag T1
        JOIN bk_tag_value T2 ON T1.id = T2.tag_id
        WHERE T1.org_id = #{orgId} AND tag_key = #{tagKey} AND T1.tag_enable = 1
    </select>

    <select id="findByOrgIdAndTagKeys" resultType="com.yxt.talent.bk.core.tag.bean.TagValueBean4UserSearch">
        SELECT
        T2.id,
        T2.tag_Id as tagId,
        T1.tag_Name as tagName,
        T1.tag_Key as tagKey,
        T2.value_Name as valueName,
        T2.order_Index as orderIndex
        FROM bk_tag T1
        JOIN bk_tag_value T2 ON T1.id = T2.tag_id
        WHERE T1.org_id = #{orgId}
        AND T1.tag_enable = 1 AND T1.tag_source = 1
        AND T1.tag_key in
        <foreach collection="tagKeys" item="tagKey" open="(" separator="," close=")">
            #{tagKey}
        </foreach>

    </select>

    <select id="listTagValue" resultType="com.yxt.talent.bk.core.tag.bean.TagValueBean">
        select id, tag_id tagId, value_name valueName
        from
        bk_tag_value
        where org_id = #{orgId}
        and id in
        <choose>
            <when test="tagValueIds != null and tagValueIds.size() != 0">
                <foreach collection="tagValueIds" item="tagValueId" open="(" separator="," close=")">
                    #{tagValueId}
                </foreach>
            </when>
            <otherwise>
                ('')
            </otherwise>
        </choose>
    </select>

    <select id="listTagValueByTagId" resultType="com.yxt.talent.bk.core.tag.bean.TagValueBean">
        select id, value_name valueName
        from
        bk_tag_value
        where org_id = #{orgId}
        and tag_id = #{tagId}
    </select>
</mapper>
