package com.yxt.talent.bk.api.config;

import com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer;
import com.yxt.common.component.redis.GzipSerializer;
import com.yxt.common.config.WafRedisClientConfig;
import com.yxt.common.repo.RedisRepository;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import com.yxt.spsdk.audit.AuditLogConfig;
import com.yxt.spsdk.logsave.LogSaveConfig;
import com.yxt.talent.bk.svc.base.BkConfigService;
import com.yxt.talent.bk.svc.common.enums.AuditLogPointEnum;
import com.yxt.ubiz.export.component.ExportMDUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.yxt.common.config.WafRedisClientConfig.REDIS_CONNECTION_FACTORY_BEAN;

@Slf4j
@EnableCaching
@SpringBootConfiguration
public class TalentConfig {

    private static final int KEEP_ALIVE_SECONDS = 60;
    private static final String FUTURE_TASK_EXECUTOR = "futureTaskExecutor";
    private static final String FUTURE_TASK_POOL_NAME = "futureTaskExecutor-";

    @Autowired
    private BkConfigService bkConfigService;

    @Bean(WafRedisClientConfig.REDIS_PROPERTIES_BEAN)
    @ConfigurationProperties("sptalentapi.redis")
    public RedisProperties talentRedisProperties() {
        return new RedisProperties();
    }

    @Bean
    public CacheManager redisCacheManager(
        @Qualifier(REDIS_CONNECTION_FACTORY_BEAN) RedisConnectionFactory redisConnectionFactory) {
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
        RedisCacheConfiguration defaultCacheConfig = getDefaultCacheConfiguration();
        return new RedisCacheManager(redisCacheWriter, defaultCacheConfig.entryTtl(Duration.ofHours(8L)),
            getRedisCacheConfigurationMap()
        );
    }

    @Bean
    public StringRedisTemplate talentJsonStringRedisTemplate(
        @Qualifier(REDIS_CONNECTION_FACTORY_BEAN) RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate(redisConnectionFactory);
        RedisSerializer<Object> jsonSerializer = new GenericJackson2JsonRedisSerializer();
        // 功能增强,增加压缩机制(gzip压缩+jackson序列化)
        template.setValueSerializer(new GzipSerializer(jsonSerializer));
        template.afterPropertiesSet();
        return template;
    }

    @Bean
    @Primary
    public RedisTemplate<String, Object> exportTalentRedisTemplate(@Qualifier(REDIS_CONNECTION_FACTORY_BEAN) RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        GenericFastJsonRedisSerializer serializer = new GenericFastJsonRedisSerializer();
        redisTemplate.setKeySerializer(RedisSerializer.string());
        redisTemplate.setValueSerializer(serializer);
        redisTemplate.setHashKeySerializer(RedisSerializer.string());
        redisTemplate.setHashValueSerializer(serializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public ExportMDUtil initExportBean(@Qualifier("exportTalentRedisTemplate") RedisTemplate<String, Object> talentRedisTemplate,
            @Qualifier("rocketMQTemplate") RocketMQTemplate rocketMQTemplate) {
        return new ExportMDUtil(talentRedisTemplate, rocketMQTemplate);
    }

    private RedisCacheConfiguration getDefaultCacheConfiguration() {
        RedisSerializer<Object> jsonSerializer = new GenericJackson2JsonRedisSerializer();
        RedisSerializationContext.SerializationPair<Object> pair =
            RedisSerializationContext.SerializationPair.fromSerializer(jsonSerializer);
        return RedisCacheConfiguration.defaultCacheConfig().serializeValuesWith(pair).disableCachingNullValues();
    }

    private Map<String, RedisCacheConfiguration> getRedisCacheConfigurationMap() {
        Map<String, RedisCacheConfiguration> map = new HashMap<>(2);
        return map;
    }
    @Bean
    public RedisRepository talentRedisRepository(
        @Qualifier(REDIS_CONNECTION_FACTORY_BEAN) RedisConnectionFactory redisConnectionFactory,
        @Qualifier("talentJsonStringRedisTemplate") StringRedisTemplate talentJsonStringRedisTemplate) {
        return new RedisRepository(new StringRedisTemplate(redisConnectionFactory), talentJsonStringRedisTemplate);
    }

    @Bean(FUTURE_TASK_EXECUTOR)
    public AsyncTaskExecutor asyncTaskExecutor() {
        int cpus = Runtime.getRuntime().availableProcessors();
        cpus = Math.max(cpus, 4);
        int initPoolSize = cpus << 4;
        int queueCapacity = 100;
        log.debug(FUTURE_TASK_EXECUTOR + "[corePoolSize={}, initPoolSize={}, queueCapacity={}]", initPoolSize,
            initPoolSize, queueCapacity
        );
        final RejectedExecutionHandler rejectedHandler = new ThreadPoolExecutor.CallerRunsPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn(
                    "CompletableFuture线程池容量不够，考虑增加线程数量，但更推荐将线程消耗数量大的程序使用单独的线程池");
                super.rejectedExecution(r, e);
            }
        };
        final ThreadPoolTaskExecutor poolTaskExecutor = new ThreadPoolTaskExecutor();
        poolTaskExecutor.setCorePoolSize(initPoolSize);
        poolTaskExecutor.setMaxPoolSize(initPoolSize);
        poolTaskExecutor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        poolTaskExecutor.setQueueCapacity(queueCapacity);
        poolTaskExecutor.setThreadNamePrefix(FUTURE_TASK_POOL_NAME);
        poolTaskExecutor.setRejectedExecutionHandler(rejectedHandler);
        poolTaskExecutor.initialize();
        return poolTaskExecutor;
    }

    @Bean
    public OkHttpClient okHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.connectTimeout(5L, TimeUnit.SECONDS);
        builder.readTimeout(20L, TimeUnit.SECONDS);
        builder.writeTimeout(20L, TimeUnit.SECONDS);
        builder.retryOnConnectionFailure(false);
        ConnectionPool connectionPool = new ConnectionPool(50, 50, TimeUnit.SECONDS);
        builder.connectionPool(connectionPool);
        return builder.build();
    }

    @Bean
    public AuditLogConfig auditLogConfig() {
        AuditLogConfig ret = new AuditLogConfig();
        ret.setEnabledGetter(() -> bkConfigService.isAuditLogEnabled());
        //业务定义的操作日志枚举AuditLogPointEnum
        ret.setLogPointEnums(AuditLogPointEnum.values());
        return ret;
    }

    @Bean
    public LogSaveConfig logSaveConfig() {
        LogSaveConfig logSave = new LogSaveConfig();
        logSave.setSaveStyle(LogSaveConfig.SAVE_STYLE_FILTER_BIZ);
        logSave.setLogTable("bk_sys_operate_log");
        logSave.setEnabled(() -> bkConfigService.isSaveLogEnabled());
        logSave.setDingURL(bkConfigService.getSaveLogDing());
        logSave.setObserverFunc(bizLine -> bkConfigService.getSaveLogObserver());
        return logSave;
    }
}
