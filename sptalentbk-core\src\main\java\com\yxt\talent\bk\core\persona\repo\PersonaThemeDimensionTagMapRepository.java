package com.yxt.talent.bk.core.persona.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeDimensionTagMapEntity;
import com.yxt.talent.bk.core.persona.mapper.PersonaThemeDimensionTagMapMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
public class PersonaThemeDimensionTagMapRepository
        extends ServiceImpl<PersonaThemeDimensionTagMapMapper, PersonaThemeDimensionTagMapEntity> {

    /**
     * 查询主题关联所有的维度标签
     *
     * @param orgId
     * @param id
     * @return
     */
    public PersonaThemeDimensionTagMapEntity queryThemeDimensionTagById(String orgId, String id) {
        LambdaQueryWrapper<PersonaThemeDimensionTagMapEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PersonaThemeDimensionTagMapEntity::getOrgId, orgId);
        wrapper.eq(PersonaThemeDimensionTagMapEntity::getId, id);
        return getOne(wrapper);
    }

    /**
     * 查询主题关联所有的维度标签
     *
     * @param orgId
     * @param themeIds
     * @return
     */
    public List<PersonaThemeDimensionTagMapEntity> queryThemesDimensionTag(String orgId, Collection<String> themeIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(themeIds)) {
            log.warn("LOG10320:orgId={}", orgId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PersonaThemeDimensionTagMapEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PersonaThemeDimensionTagMapEntity::getOrgId, orgId);
        wrapper.in(PersonaThemeDimensionTagMapEntity::getThemeId, themeIds);
        wrapper.orderByDesc(PersonaThemeDimensionTagMapEntity::getCreateTime);
        List<PersonaThemeDimensionTagMapEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }


    public List<PersonaThemeDimensionTagMapEntity> queryByDimensionId(String orgId, String dimensionId) {
        LambdaQueryWrapper<PersonaThemeDimensionTagMapEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PersonaThemeDimensionTagMapEntity::getOrgId, orgId);
        wrapper.eq(PersonaThemeDimensionTagMapEntity::getDimensionId, dimensionId);
        wrapper.orderByDesc(PersonaThemeDimensionTagMapEntity::getCreateTime);
        return list(wrapper);
    }

    /**
     * 删除维度，所有维度标签同步删除
     *
     * @param orgId
     * @param themeId
     * @param dimensionId
     */
    public void removeThemeDimensionTag(String orgId, String themeId, String dimensionId) {
        LambdaQueryWrapper<PersonaThemeDimensionTagMapEntity> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(PersonaThemeDimensionTagMapEntity::getOrgId, orgId);
        updateWrapper.eq(PersonaThemeDimensionTagMapEntity::getThemeId, themeId);
        updateWrapper.eq(PersonaThemeDimensionTagMapEntity::getDimensionId, dimensionId);
        remove(updateWrapper);
    }

    /**
     * 删除标签
     *
     * @param orgId
     * @param dimensionTagId
     */
    public void removeTag(String orgId, String dimensionTagId) {
        LambdaQueryWrapper<PersonaThemeDimensionTagMapEntity> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(PersonaThemeDimensionTagMapEntity::getOrgId, orgId);
        updateWrapper.eq(PersonaThemeDimensionTagMapEntity::getId, dimensionTagId);
        remove(updateWrapper);
    }

    /**
     * 标签删除 维度标签关系删除
     *
     * @param orgId
     * @param tagId
     */
    public void removeByTag(String orgId, String tagId) {
        LambdaQueryWrapper<PersonaThemeDimensionTagMapEntity> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(PersonaThemeDimensionTagMapEntity::getOrgId, orgId);
        updateWrapper.eq(PersonaThemeDimensionTagMapEntity::getTagId, tagId);
        remove(updateWrapper);
    }


    /**
     * 查询维度标签已存在的数据
     *
     * @param orgId
     * @param dimensionId
     * @param tagIds
     */
    public List<PersonaThemeDimensionTagMapEntity> queryDimensionTagByTagIdAndDimensionId(String orgId,
            String dimensionId, Collection<String> tagIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(tagIds)) {
            log.warn("LOG10320:orgId={}, dimensionId={}", orgId, dimensionId);
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<PersonaThemeDimensionTagMapEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonaThemeDimensionTagMapEntity::getOrgId, orgId);
        queryWrapper.eq(PersonaThemeDimensionTagMapEntity::getDimensionId, dimensionId);
        queryWrapper.in(PersonaThemeDimensionTagMapEntity::getTagId, tagIds);
        return list(queryWrapper);
    }
}
