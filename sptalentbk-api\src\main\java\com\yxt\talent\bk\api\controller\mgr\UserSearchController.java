package com.yxt.talent.bk.api.controller.mgr;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.bk.api.component.PersonaThemeComponent;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.search.SearchFilterService;
import com.yxt.talent.bk.svc.search.UserSearchService;
import com.yxt.talent.bk.svc.search.bean.DimensionBean;
import com.yxt.talent.bk.svc.search.bean.DimensionBeanList;
import com.yxt.talent.bk.svc.search.bean.FilterGroup4Create;
import com.yxt.talent.bk.svc.search.bean.FilterGroupNameBean;
import com.yxt.talent.bk.svc.search.bean.FilterTagBean;
import com.yxt.talent.bk.svc.search.bean.TagAndValueBean;
import com.yxt.talent.bk.svc.search.bean.TagBean4Search;
import com.yxt.talent.bk.svc.search.bean.TagBean4Select;
import com.yxt.talent.bk.svc.search.bean.UserSearchBean;
import com.yxt.talent.bk.svc.search.bean.UserSearchListAnalyseBean;
import com.yxt.talent.bk.svc.search.bean.UserSearchListBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Slf4j
@Deprecated
@RestController
@RequestMapping(value = "/mgr/search/")
@AllArgsConstructor
@Tag(name = "人才搜索主页接口")
public class UserSearchController extends BaseController {
    private final UserSearchService userSearchService;
    private final SearchFilterService searchFilterService;
    private final PersonaThemeComponent personaThemeComponent;

    @Deprecated
    @Operation(summary = "人才搜索主页-列表")
    @Parameters({             @Parameter(name = "limit", description = "每页记录数. 默认值为20.", in = ParameterIn.QUERY),             @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "                     + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY)})
    @PostMapping(value = "/list", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_USER_SEARCH_LIST, action = Constants.LOG_TYPE_GETLIST,
          type = AuthType.TOKEN)
    public PagingList<UserSearchListBean> list(@RequestBody UserSearchBean search) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return userSearchService.findPage(ApiUtil.getPageRequest(getRequest()), userCacheBasic, search);
    }

    @Deprecated
    @Operation(summary = "人才搜索主页-透视" )
    @Parameters({             @Parameter(name = "limit", description = "透视记录数. 默认值为20.", in = ParameterIn.QUERY),             @Parameter(name = "offset", description = "上一页最后一条记录号, offset必须是limit的倍数, 第一页为0. "                     + "当前页码可以通过offset/limit + 1来获得. 默认值为0.", in = ParameterIn.QUERY),             @Parameter(name = "dimensionKey", description = "维度分页必传, 点击页码时，回传当前点击的dimensionKey", in = ParameterIn.QUERY)})
    @PostMapping(value = "/analyse", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_USER_SEARCH_LIST, action = Constants.LOG_TYPE_GETLIST,
          type = AuthType.TOKEN)
    public CommonList<UserSearchListAnalyseBean> analyse(@RequestBody UserSearchBean search,
                                                         @RequestParam(name = "limit", defaultValue = "20") int limit,
                                                         @RequestParam(name = "offset", defaultValue = "0") int offset ,
                                                         @RequestParam(name = "dimensionKey", defaultValue = "") String dimensionKey) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        List<UserSearchListAnalyseBean> analyseList = userSearchService.findAnalyseList(userCacheBasic, search, limit,offset,dimensionKey);
        return new CommonList<>(analyseList);
    }

    @Deprecated
    @Operation(summary = "人才搜索主页-导出信息")
    @PostMapping(value = "/export")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_USER_SEARCH_EXPORT, action = Constants.LOG_TYPE_GETLIST,
          type = AuthType.TOKEN)
    public Map<String, String> exportScoreTemplate(@RequestBody UserSearchBean search) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return userSearchService.exportResult(userCacheBasic, search);
    }

    @Deprecated
    @Operation(summary = "人才搜索-勾选标签列表")
    @Parameter(name = "flag", description = "0-筛选条件标签，1-透视条件标签", in = ParameterIn.QUERY)
    @GetMapping(value = "/taglist/{flag}")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<TagBean4Select> findFilterSelectTag(@PathVariable int flag) {
        UserCacheDetail userDetail = getUserCacheDetail();
        List<TagBean4Select> filterSelectTag = searchFilterService.findFilterSelectTag(userDetail.getOrgId(), flag, userDetail.getUserId());
        return new CommonList<>(filterSelectTag);
    }

    @Deprecated
    @Operation(summary = "人才搜索-筛选组名称列表")
    @GetMapping(value = "/groupname/list")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<FilterGroupNameBean> findGroupNameList() {
        UserCacheDetail userDetail = getUserCacheDetail();
        personaThemeComponent.initThemeDimensionTag(userDetail.getOrgId(), userDetail.getUserId());
        List<FilterGroupNameBean> groupNameList = searchFilterService.findGroupNameList(userDetail.getOrgId());
        return new CommonList<>(groupNameList);
    }

    @Deprecated
    @Operation(summary = "人才搜索-获取标签分层列表")
    @PostMapping(value = "/tagvalue/list", produces = Constants.MEDIATYPE)
    @Parameter(name = "tagIds", description = "标签id列表")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<FilterTagBean> findFilterTagBean(@RequestBody List<String> tagIds) {
        UserCacheDetail userDetail = getUserCacheDetail();
       List<FilterTagBean> filterTagBeans = searchFilterService.findFilterTagBean(userDetail.getOrgId(), tagIds);
        return new CommonList<>(filterTagBeans);
    }

    @Deprecated
    @Operation(summary = "人才搜索-获取筛选组明细信息")
    @Parameter(name = "groupId", description = "分组id", in = ParameterIn.PATH)
    @GetMapping(value = "/groupmsg/{groupId}", produces = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public FilterGroup4Create findGroupMsg(@PathVariable String groupId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        return searchFilterService.findGroupMsg(userDetail.getOrgId(), groupId);
    }

    @Deprecated
    @Operation(summary = "人才搜索-保存筛选组")
    @PostMapping(value = "/save/group", produces = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public ResponseEntity<String> saveGroup(@RequestBody FilterGroup4Create bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        String id = searchFilterService.saveGroup(userDetail.getOrgId(), bean, userDetail.getUserId());
        return ApiUtil.createdResponse(getRequest(), id);
    }

    @Deprecated
    @Operation(summary = "人才搜索-更新筛选组")
    @PutMapping(value = "/group", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void updateGroup(@RequestBody FilterGroup4Create bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        searchFilterService.updateGroup(userDetail.getOrgId(), bean, userDetail.getUserId());
    }

    @Deprecated
    @Operation(summary = "人才搜索-删除筛选组")
    @Parameter(name = "groupId", description = "标签筛选组id", in = ParameterIn.PATH)
    @DeleteMapping(value = "/{groupId}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void deleteGroup(@PathVariable String groupId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        searchFilterService.deleteGroup(userDetail.getOrgId(), groupId);
    }

    @Deprecated
    @Operation(summary = "人才搜索-更新筛选组名称")
    @PutMapping(value = "/groupName", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void updateGroupName(@RequestBody FilterGroup4Create bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        searchFilterService.updateGroupName(userDetail.getOrgId(), bean, userDetail.getUserId());
    }

    @Deprecated
    @Operation(summary = "人才搜索-维度名称列表")
    @GetMapping(value = "/dimension/list")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<DimensionBean> dimensionList() {
        UserCacheDetail userDetail = getUserCacheDetail();
        personaThemeComponent.initThemeDimensionTag(userDetail.getOrgId(), userDetail.getUserId());
        List<DimensionBean> list = searchFilterService.dimensionList(userDetail.getOrgId(), userDetail.getUserId());
        return new CommonList<>(list);
    }

    @Deprecated
    @Operation(summary = "人才搜索-保存更新维度列表")
    @PostMapping(value = "/dimension", consumes = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public void saveDimension(@RequestBody DimensionBeanList bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        searchFilterService.saveDimension(userDetail.getOrgId(), userDetail.getUserId(), bean);
    }


    @Deprecated
    @Operation(summary = "人才搜索-贴标签列表")
    @PostMapping(value = "/tag/list", consumes = Constants.MEDIATYPE)
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<FilterTagBean> searchAllTag(@RequestBody TagBean4Search bean) {
        UserCacheDetail userDetail = getUserCacheDetail();
        personaThemeComponent.initThemeDimensionTag(userDetail.getOrgId(), userDetail.getUserId());
        List<FilterTagBean> list = searchFilterService.searchAllTag(userDetail.getOrgId(), bean);
        return new CommonList<>(list);
    }

    @Deprecated
    @Operation(summary = "人才搜索-人员搜索标签")
    @GetMapping(value = "/usertag/{userId}")
    @ResponseStatus(value = HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    public CommonList<TagAndValueBean> searchUserTag(@PathVariable String userId) {
        UserCacheDetail userDetail = getUserCacheDetail();
        List<TagAndValueBean> tagAndValueBeans = searchFilterService.searchUserTag(userDetail.getOrgId(), userId);
        return new CommonList<>(tagAndValueBeans);
    }
}
