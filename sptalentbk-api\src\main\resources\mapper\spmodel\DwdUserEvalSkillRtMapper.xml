<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdUserEvalSkillRtMapper">
<!--    <select id="queryModelIdByUserId" resultType="string">-->
<!--        select distinct skill_model_id-->
<!--        from dwd_user_eval_skill_rt-->
<!--        where org_id = '${orgId}'-->
<!--          and third_user_id = '${userId}'-->
<!--          and skill_model_id is not null-->
<!--          and skill_model_id != ""-->
<!--              and dw_status = 0 and evaluation_type !=3-->
<!--              and create_time >= date_sub(now(),interval 2 year)-->
<!--        order by skill_model_id-->
<!--    </select>-->

        <select id="queryModelIdByUserId" resultType="string">
        select distinct model_id AS skill_model_id
        from dwd_user_indicator_result
        where org_id = '${orgId}'
          and user_id = '${userId}'
          and model_id is not null
          and model_id != ''
          and deleted = 0
          and indicator_category = 1
        order by model_id
    </select>


<!--    <select id="queryByUserIdAndModelId" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdUserEvalSkillRt">-->
<!--        select skill_id, skill_name, ten_score, conclusion, skill_model_id, create_time as creatTime from-->
<!--        dwd_user_eval_skill_rt-->
<!--        where org_id = '${orgId}'-->
<!--        and third_user_id = '${userId}'-->
<!--        <if test="modelId!= null and modelId!= ''">-->
<!--            and skill_model_id = '${modelId}'-->
<!--        </if>-->
<!--        and dw_status = 0-->
<!--        and create_time >= date_sub(now(),interval 2 year)-->
<!--        order by skill_id-->
<!--    </select>-->

    <select id="queryByUserIdAndModelId" resultType="com.yxt.talent.bk.core.spmodel.entity.DwdUserEvalSkillRt">
        select indicator_id AS skill_id,
               indicator_name AS skill_name,
               score_ten AS ten_score,
               qualified AS conclusion,
               model_id AS skill_model_id,
               create_time as creatTime
        from   dwd_user_indicator_result
        where org_id = '${orgId}'
        and user_id = '${userId}'
        <if test="modelId!= null and modelId!= ''">
            and model_id = '${modelId}'
        </if>
        and indicator_category = 1
        and deleted = 0
        order by indicator_id
    </select>


    <select id="getUsedSkillModelListByUserId"
            resultType="com.yxt.talent.bk.core.dashboard.bean.PersonalEvalModelSimpleVO">
        select distinct model_id AS modelId
        from dwd_user_indicator_result
        where org_id = '${orgId}'
          and user_id = '${userId}'
          and model_id is not null
          and model_id != ''
          and deleted = 0
          and indicator_category = 1
    </select>

    <select id="queryAllByUserIdAndModelIds"
            resultType="com.yxt.talent.bk.core.spmodel.entity.DwdUserIndicatorResult">
        select indicator_id,
        indicator_name,
        score,
        score_ten,
        level_name,
        qualified,
        model_id,
        create_time,
        update_time
        from dwd_user_indicator_result
        where org_id = '${orgId}'
        and user_id = '${userId }'
        and model_id in
        <foreach collection="modelIds" item="modelId" open="(" close=")" separator=",">
            '${modelId}'
        </foreach>
    </select>

</mapper>
