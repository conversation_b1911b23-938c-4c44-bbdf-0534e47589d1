package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.DateUtil;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.enums.HeirRiskRuleTypeEnum;
import com.yxt.talent.bk.core.CreatorEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jodd.util.StringPool;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

/**
 * HeirPosEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:48 am
 */
@Data
@TableName("bk_heir_pos")
public class HeirPosEntity extends CreatorEntity {
    @TableId
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.DYN_MAP_POS_ID,
            asPkMapKey = BkDemoConstants.BK_POS_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String id;
    @Schema(description = "机构id")
    private String orgId;
    @Schema(description = "0:岗位，1:部门")
    private Integer posType;
    @Schema(description = "名称")
    private String posName;
    @Schema(description = "源岗位是不是已删除")
    private Integer sourceDeleted;

    @Schema(description = "空表示没有上级")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.DYN_MAP_POS_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String parentPosId;

    @Schema(description = "目标继任数量,0:未设置")
    private Integer heirTargetQty;

    @Schema(description = "有效人数")
    private Integer heirValidQty;

    @Schema(description = "0:自动规则，1:手动规则")
    private Integer riskRuleType;

    @Schema(description = "风险规则id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_ORG_LEVEL_CFG_ID)
    private Long riskLevelId;

    @Schema(description = "是否设置权限,1:打开")
    private Integer permissionFlag;

    private Integer deleted;

    @Schema(description = "计算周期配置")
    private String calcCycle;

    @Schema(description = "下次计算时间")
    private Date nextCalcTime;

    @Schema(description = "下次计算版本号")
    private Integer nextCalcVersion;

    @Schema(description = "提醒周期配置")
    private String remindCycle;

    @Schema(description = "下次提醒时间")
    private Date nextRemindTime;

    @Schema(description = "下次提醒版本号")
    private Integer nextRemindVersion;

    public void init(String userId) {
        if (StringUtils.isEmpty(userId)) {
            userId = TalentBkConstants.CODE_OPERATOR_ID;
        }
        Date nowTime = DateUtil.currentTime();
        if (StringUtils.isEmpty(id)) {
            setCreateTime(nowTime);
            setCreateUserId(userId);
        }
        setUpdateTime(nowTime);
        setUpdateUserId(userId);
    }

    public static HeirPosEntity createEntity(String orgId, String userId) {
        Date nowTime = DateUtil.currentTime();
        HeirPosEntity posEntity = new HeirPosEntity();
        posEntity.setOrgId(orgId);
        posEntity.setPosName(StringPool.EMPTY);
        posEntity.setSourceDeleted(YesOrNo.NO.getValue());
        posEntity.setParentPosId(StringPool.EMPTY);
        posEntity.setHeirTargetQty(0);
        posEntity.setHeirValidQty(0);
        posEntity.setNextCalcVersion(0);
        posEntity.setNextRemindVersion(0);
        posEntity.setRiskRuleType(HeirRiskRuleTypeEnum.AUTO.getType());
        posEntity.setPermissionFlag(YesOrNo.NO.getValue());
        posEntity.setDeleted(YesOrNo.NO.getValue());
        posEntity.setCreateTime(nowTime);
        posEntity.setCreateUserId(userId);
        posEntity.setUpdateTime(nowTime);
        posEntity.setUpdateUserId(userId);
        return posEntity;
    }
}
