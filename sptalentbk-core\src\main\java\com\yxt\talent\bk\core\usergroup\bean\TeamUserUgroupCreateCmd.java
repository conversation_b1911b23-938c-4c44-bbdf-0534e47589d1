package com.yxt.talent.bk.core.usergroup.bean;

import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.common.enums.RuleJobTypeEnum;
import com.yxt.talent.bk.common.enums.UserGroupTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.validation.constraints.NotBlank;

@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(name = "我的团队-人才看板-组织人才库-创建人才库")
public class TeamUserUgroupCreateCmd extends UserGroupBean {

    @NotBlank(message = "apis.talentbk.userGroup.deptId.notblank")
    @Schema(description = "人才库属于的部门id")
    private String deptId;

    @Schema(description = "从哪个部门复制,如果此字段有值, 则自定义筛选和常用筛选所传的字段无效")
    private String fromDeptId;

    public TeamUserUgroupCreateCmd() {
        super.setEnabled(YesOrNo.YES.getValue());
        super.setCaculateJob(RuleJobTypeEnum.DAY.getType());
        super.setGroupType(UserGroupTypeEnum.DYNAMIC.getType());
        super.setTeamGroup(YesOrNo.YES.getValue());
    }

}
