package com.yxt.talent.bk.api.controller.open.facade;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.bean.resp.DwdHeirPosResp;
import com.yxt.talent.bk.svc.heir.bean.resp.DwdHeirPosUserResp;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * HeirOpenController
 *
 * <AUTHOR> geyan
 * @Date 15/9/23 11:03 am
 */
@RestController
@AllArgsConstructor
@RequestMapping("/open/heir")
public class HeirOpenController extends BaseController {

    private final HeirPosService heirPosService;

    @Operation(summary = "继任主数据")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.OAUTH)
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/list")
    public PagingList<DwdHeirPosResp> posPage() {
        String orgId = getOauthOrgId();
        return heirPosService.listPage4Open(getPage(), orgId);
    }

    @Operation(summary = "继任用户数据")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATEMULTIPLE, type = AuthType.OAUTH)
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/user/list")
    public PagingList<DwdHeirPosUserResp> posUserPage(@RequestParam int userType) {
        String orgId = getOauthOrgId();
        return heirPosService.listUserPage4Open(getPage(), orgId, userType);
    }
}
