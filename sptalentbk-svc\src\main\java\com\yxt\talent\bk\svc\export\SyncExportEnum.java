package com.yxt.talent.bk.svc.export;

import com.yxt.common.component.SpringContextHolder;
import com.yxt.spsdk.common.bean.AsyncExportBase;
import com.yxt.spsdk.common.bean.AsyncExportEnumBase;
import com.yxt.spsdk.export.BaseOutputStrategy;
import com.yxt.talent.bk.core.dashboard.bean.DashboardPersonalDetailExportParam;
import com.yxt.talent.bk.core.heir.bean.PosExportParam;
import com.yxt.talent.bk.svc.heir.HeirPosService;

import java.util.function.Consumer;

/**
 * SyncExportEnum
 *
 * <AUTHOR> harleyge
 * @Date 2/8/24 11:04 am
 */
public enum SyncExportEnum implements AsyncExportEnumBase {
    /**
     * 数据导出
     */
    DASHBOARD_PERSONAL_DETAIL(DashboardPersonalDetailExportParam.class, null, exportBean -> {
        DashboardPersonalDetailExportParam exportUserDetail = (DashboardPersonalDetailExportParam) exportBean;
        SpringContextHolder.getBean(DashBoardPersonalDetailExportService.class)
                .doExportPersonalDetailDashBoard(exportUserDetail);
    }),
    HEIR_POS_EXPORT(PosExportParam.class, null, exportBean -> {
        PosExportParam param = (PosExportParam) exportBean;
        SpringContextHolder.getBean(HeirPosService.class).posExport(param);
    });

    private Class<? extends AsyncExportBase> asyncBean;
    private Class<? extends BaseOutputStrategy> writeStrategy;
    private Consumer<AsyncExportBase> selfHandle;

    SyncExportEnum(Class<? extends AsyncExportBase> asyncBean, Class<? extends BaseOutputStrategy> writeStrategy,
            Consumer<AsyncExportBase> selfHandle) {
        this.asyncBean = asyncBean;
        this.writeStrategy = writeStrategy;
        this.selfHandle = selfHandle;
    }

    public Class<? extends AsyncExportBase> getAsyncBean() {
        return asyncBean;
    }

    @Override
    public Class<? extends BaseOutputStrategy> getWriteStrategy() {
        return writeStrategy;
    }

    public Consumer<AsyncExportBase> getSelfHandle() {
        return selfHandle;
    }

    @Override
    public String bizCode() {
        return name();
    }

}
