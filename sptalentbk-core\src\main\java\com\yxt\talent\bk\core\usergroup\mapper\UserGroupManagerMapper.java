package com.yxt.talent.bk.core.usergroup.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupManagerBean;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupMgtBean;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupManager;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface UserGroupManagerMapper extends BaseMapper<UserGroupManager> {
    int updateBatch(List<UserGroupManager> list);

    int updateBatchSelective(List<UserGroupManager> list);

    int batchInsert(@Param("list") List<UserGroupManager> list);

    int insertOrUpdate(UserGroupManager record);

    int insertOrUpdateSelective(UserGroupManager record);

    List<UserGroupManagerBean> findList(@Param("orgId") String orgId, @Param("groupIds") List<Long> groupIds);

    void deleteByGroupId(@Param("orgId") String orgId, @Param("userId") String userId, @Param("groupId") Long groupId);

    List<String> findAllMgrUserIds(@Param("orgId") String orgId);

    List<UserGroupMgtBean> resTransferList(@Param("orgId") String orgId,
                                           @Param("userIds") List<String> userIds,
                                           @Param("groupIds") List<Long> groupIds);
    void execResTransfer(@Param("orgId") String orgId, @Param("list") List<UserGroupMgtBean> list);
}
