package com.yxt.talent.bk.api.controller.heir.stu;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.heir.ConfigPageImageService;
import com.yxt.talent.bk.svc.heir.HeirOrgLevelConfigService;
import com.yxt.talent.bk.svc.heir.bean.resp.ConfigPageImageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* <AUTHOR>
*  @date 2023/9/14
**/
@Slf4j
@Tag(name = "继任配置项")
@RestController
@RequestMapping("/mgr/stu/config")
@RequiredArgsConstructor
public class ConfigStuController extends BaseController {

    private final ConfigPageImageService configPageImageService;
    private final HeirOrgLevelConfigService heirOrgLevelConfigService;

    @Operation(summary = "轮播图列表")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/page_image/list")
    public CommonList<ConfigPageImageResp> list() {
        UserCacheBasic currentUser = getUserCacheBasic();
        return configPageImageService.list(currentUser);
    }

    @Operation(summary = "学员端是否可见自己继任的数据")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/client_show")
    public Integer clientShow() {
        UserCacheBasic currentUser = getUserCacheBasic();
        return heirOrgLevelConfigService.heirClientShow(currentUser.getOrgId());
    }
}

