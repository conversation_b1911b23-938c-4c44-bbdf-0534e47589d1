package com.yxt.talent.bk.core.pool.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.CreatorEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *  人才池表
 * <AUTHOR>
 * @since 2022/9/8 9:41
 * @version 1.0
 */
@Data
@NoArgsConstructor
@TableName("bk_pool")
@EqualsAndHashCode(callSuper = true)
public class Pool extends CreatorEntity {

    @TableField("id")
    @Schema(description = "主键")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = BkDemoConstants.BK_POOL_ID)
    private String id;

    @TableField("org_id")
    @Schema(description = "机构id")
    private String orgId;

    @TableField("pool_name")
    @Schema(description = "人才池名称")
    private String poolName;

    @TableField("catalog_id")
    @Schema(description = "人才池分类Id|字段空是默认分类id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_CATALOG_ID)
    private String catalogId;

    @TableField("expect_num")
    @Schema(description = "预期人数")
    private Integer expectNum;

    @TableField("real_num")
    @Schema(description = "现有人数")
    private Integer realNum;

    @TableField("saturability")
    @Schema(description = "饱和度")
    private BigDecimal saturability;

    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableField("deleted")
    private Integer deleted;
}
