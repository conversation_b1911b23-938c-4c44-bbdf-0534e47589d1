package com.yxt.talent.bk.svc.persona.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(name = "人才画像")
public class Portrait4List {
    @Schema(description = "主题id")
    private String themeId;

    @Schema(description = "主题id")
    private String themeName;

    @Schema(description = "当前用户维度覆盖率 ")
    private BigDecimal dimensionCoverage = BigDecimal.ONE;

    @Schema(description = "维度信息")
    private List<PortraitDimension4List> dimension4Lists;
}
