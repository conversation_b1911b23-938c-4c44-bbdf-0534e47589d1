<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.usergroup.mapper.SearchSchemeMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.usergroup.entity.SearchScheme">
        <!--@mbg.generated-->
        <!--@Table bk_search_scheme-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="scheme_name" jdbcType="VARCHAR" property="schemeName"/>
        <result column="scheme_desc" jdbcType="VARCHAR" property="schemeDesc"/>
        <result column="search_rule_id" jdbcType="BIGINT" property="searchRuleId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        org_id,
        deleted,
        create_time,
        update_user_id,
        update_time,
        create_user_id,
        scheme_name,
        scheme_desc,
        search_rule_id
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bk_search_scheme
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="create_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="scheme_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.schemeName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scheme_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.schemeDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="search_rule_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.searchRuleId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bk_search_scheme
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orgId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deleted != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT}
                            then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUserId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT}
                            then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUserId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="scheme_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.schemeName != null">
                        when id = #{item.id,jdbcType=BIGINT}
                            then #{item.schemeName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="scheme_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.schemeDesc != null">
                        when id = #{item.id,jdbcType=BIGINT}
                            then #{item.schemeDesc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="search_rule_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.searchRuleId != null">
                        when id = #{item.id,jdbcType=BIGINT}
                            then #{item.searchRuleId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into bk_search_scheme
        (id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, scheme_name,
         scheme_desc, search_rule_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=CHAR},
             #{item.deleted,jdbcType=TINYINT},
             #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP},
             #{item.createUserId,jdbcType=CHAR}, #{item.schemeName,jdbcType=VARCHAR},
             #{item.schemeDesc,jdbcType=VARCHAR},
             #{item.searchRuleId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.bk.core.usergroup.entity.SearchScheme">
        <!--@mbg.generated-->
        insert into bk_search_scheme
        (id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, scheme_name,
         scheme_desc, search_rule_id)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=CHAR}, #{deleted,jdbcType=TINYINT},
                #{createTime,jdbcType=TIMESTAMP},
                #{updateUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{createUserId,jdbcType=CHAR},
                #{schemeName,jdbcType=VARCHAR}, #{schemeDesc,jdbcType=VARCHAR},
                #{searchRuleId,jdbcType=BIGINT})
        on duplicate key update id             = #{id,jdbcType=BIGINT},
                                org_id         = #{orgId,jdbcType=CHAR},
                                deleted        = #{deleted,jdbcType=TINYINT},
                                create_time    = #{createTime,jdbcType=TIMESTAMP},
                                update_user_id = #{updateUserId,jdbcType=CHAR},
                                update_time    = #{updateTime,jdbcType=TIMESTAMP},
                                create_user_id = #{createUserId,jdbcType=CHAR},
                                scheme_name    = #{schemeName,jdbcType=VARCHAR},
                                scheme_desc    = #{schemeDesc,jdbcType=VARCHAR},
                                search_rule_id = #{searchRuleId,jdbcType=BIGINT}
    </insert>
    <insert id="insertOrUpdateSelective"
            parameterType="com.yxt.talent.bk.core.usergroup.entity.SearchScheme">
        <!--@mbg.generated-->
        insert into bk_search_scheme
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUserId != null">
                update_user_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="schemeName != null">
                scheme_name,
            </if>
            <if test="schemeDesc != null">
                scheme_desc,
            </if>
            <if test="searchRuleId != null">
                search_rule_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=CHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=CHAR},
            </if>
            <if test="schemeName != null">
                #{schemeName,jdbcType=VARCHAR},
            </if>
            <if test="schemeDesc != null">
                #{schemeDesc,jdbcType=VARCHAR},
            </if>
            <if test="searchRuleId != null">
                #{searchRuleId,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                org_id = #{orgId,jdbcType=CHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=CHAR},
            </if>
            <if test="schemeName != null">
                scheme_name = #{schemeName,jdbcType=VARCHAR},
            </if>
            <if test="schemeDesc != null">
                scheme_desc = #{schemeDesc,jdbcType=VARCHAR},
            </if>
            <if test="searchRuleId != null">
                search_rule_id = #{searchRuleId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <select id="findSchemeRuleVO" resultType="com.yxt.talent.bk.core.usergroup.bean.SchemeRuleVO">
        select a.id              schemeId,
               a.scheme_name     schemeName,
               a.scheme_desc     schemeDesc,
               b.id              ruleId,
               b.tag_search_type tagSearchType,
               b.tag_search      tagSearch

        from bk_search_scheme a
                 left join bk_search_rule b
                           on a.search_rule_id = b.id
                               and a.org_id = b.org_id
        where a.org_id = #{orgId}
          and a.deleted = 0
          and b.deleted = 0
          and a.create_user_id = #{userId}
        order by a.create_time desc
    </select>
</mapper>
