<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdUserSkillRtStatisticsMapper">
    <select id="getDeptUserSkillReachCount"
            resultType="com.yxt.talent.bk.core.dashboard.bean.DeptUserSkillRtReachDTO">
        select parent_id         as deptId
             , sum(skillReached) as skillReachCount
             , sum(rtReached)    as rtReachCount
        from (select if(dusrs.matched_skill_cnt >= dusrs.total_skill_cnt and dusrs.total_skill_cnt > 0, 1, 0) as skillReached
                   , if(dusrs.matched_rt_cnt >= dusrs.total_rt_cnt and dusrs.total_rt_cnt > 0, 1, 0)       as rtReached
                   , tt.parent_id
            from dwd_user_skill_rt_statistics dusrs
            join (
                select distinct du.user_id, ddc.parent_id, du.org_id
                from dws_user              du
                join dwd_user_group_member dugm on dugm.user_id = du.user_id and dugm.org_id = '${orgId}'
                <if test="groupIds != null and groupIds.size() > 0">
                    <!--@ignoreSql-->
                    and dugm.group_id in
                    <foreach collection="groupIds" item="groupId" separator="," open="(" close=")">
                        ${groupId}
                    </foreach>
                </if>
                join dim_dept_closure ddc on ddc.org_id = '${orgId}' and ddc.deleted = 0
                <if test="deptIds != null and deptIds.size() > 0">
                    and ddc.parent_id in
                    <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                        '${deptId}'
                    </foreach>
                </if>
                and ddc.dept_id = du.dept_id
                where du.org_id = '${orgId}'
                  and du.enabled = 1
                  and du.deleted = 0
            ) tt on tt.org_id = dusrs.org_id and tt.user_id = dusrs.user_id
            where dusrs.org_id = '${orgId}'
              and dusrs.deleted = 0
        ) a
        group by a.parent_id
    </select>
    <select id="getGroupUserSkillReachCount"
            resultType="com.yxt.talent.bk.core.dashboard.bean.GroupUserSkillRtReachDTO">
        select group_id          as groupId
             , sum(skillReached) as skillReachCount
             , sum(rtReached)    as rtReachCount
        from (select if(dusrs.matched_skill_cnt >= dusrs.total_skill_cnt and dusrs.total_skill_cnt > 0, 1, 0) as skillReached
                   , if(dusrs.matched_rt_cnt >= dusrs.total_rt_cnt and dusrs.total_rt_cnt > 0, 1, 0)       as rtReached
                   , tt.group_id
        from dwd_user_skill_rt_statistics dusrs
        join (select distinct du.user_id, dugm.group_id, du.org_id from dws_user du
        join dwd_user_group_member                                               dugm on
        dugm.user_id = du.user_id and dugm.org_id = '${orgId}' and dugm.group_id in
        <!--@ignoreSql-->
        <foreach collection="groupIds" item="groupId" separator="," open="(" close=")">
            ${groupId}
        </foreach>
        <if test="deptIds != null and deptIds.size() > 0">
            join dim_dept_closure ddc on ddc.org_id = '${orgId}' and ddc.deleted = 0 and ddc.parent_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
            and ddc.dept_id = du.dept_id
        </if>
        where du.org_id = '${orgId}'
          and du.enabled = 1
          and du.deleted = 0
            ) tt on tt.user_id = dusrs.user_id and tt.org_id = dusrs.org_id
        where dusrs.org_id = '${orgId}'
          and dusrs.deleted = 0
            ) a
        group by a.group_id
    </select>
    <select id="getDeptGroupAllCount" resultType="java.lang.Long">
        select count(du.user_id) as totalCount
        from dws_user du
        where du.org_id = '${orgId}'
        and du.deleted = 0
        and du.enabled = 1
        <if test="groupIds != null and groupIds.size() > 0">
        and exists(select 1 from dwd_user_group_member dugm
            where du.user_id = dugm.user_id and dugm.org_id = '${orgId}' and dugm.group_id in
            <!--@ignoreSql-->
            <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                ${groupId}
            </foreach>
            )
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
        and exists(select 1 from dim_dept_closure ddc
            where ddc.dept_id = du.dept_id and ddc.deleted = 0 and ddc.parent_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
            )
        </if>
    </select>
    <select id="getPageByDeptGroup"
            resultType="com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO">
        select tt.user_id                         as userId
             , ifnull(dusrs.total_skill_cnt, 0)   as totalSkillCnt
             , ifnull(dusrs.matched_skill_cnt, 0) as matchedSkillCnt
             , ifnull(dusrs.total_rt_cnt, 0)      as totalRtCnt
             , ifnull(dusrs.matched_rt_cnt, 0)    as matchedRtCnt
        from (select du.user_id, du.org_id
        from dws_user du
        where du.org_id = '${orgId}'
        and du.deleted = 0
        and du.enabled = 1
        <if test="groupIds != null and groupIds.size() > 0">
        and exists(select 1 from dwd_user_group_member dugm
            where du.user_id = dugm.user_id and dugm.org_id = '${orgId}' and dugm.group_id in
            <!--@ignoreSql-->
            <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                ${groupId}
            </foreach>
            )
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
        and exists(select 1 from dim_dept_closure ddc
            where ddc.dept_id = du.dept_id and ddc.deleted = 0 and ddc.parent_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
            )
        </if>
        ) tt
        left join dwd_user_skill_rt_statistics dusrs
                  on tt.user_id = dusrs.user_id and tt.org_id = dusrs.org_id and dusrs.deleted = 0
        <!--@ignoreSql-->
        limit ${offset}, ${limitSize}
    </select>
    <select id="getAllByDeptGroup"
            resultType="com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO">
        select tt.user_id                         as userId
             , ifnull(dusrs.total_skill_cnt, 0)   as totalSkillCnt
             , ifnull(dusrs.matched_skill_cnt, 0) as matchedSkillCnt
             , ifnull(dusrs.total_rt_cnt, 0)      as totalRtCnt
             , ifnull(dusrs.matched_rt_cnt, 0)    as matchedRtCnt
        from (select distinct du.user_id, du.org_id
        from dws_user du
        <if test="groupIds != null and groupIds.size() > 0">
            join dwd_user_group_member dugm
            on du.user_id = dugm.user_id and dugm.org_id = '${orgId}' and dugm.group_id in
            <!--@ignoreSql-->
            <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                ${groupId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            join dim_dept_closure ddc
            on ddc.dept_id = du.dept_id and ddc.org_id = '${orgId}' and ddc.deleted = 0 and ddc.parent_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
        </if>
        where du.org_id = '${orgId}'
          and du.deleted = 0
          and du.enabled = 1
            )                                  tt
        left join dwd_user_skill_rt_statistics dusrs
                  on tt.user_id = dusrs.user_id and tt.org_id = dusrs.org_id and dusrs.deleted = 0
    </select>
    <select id="getUserSkillRtByUserIds"
            resultType="com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO">
        select du.user_id                         as userId
             , ifnull(dusrs.total_skill_cnt, 0)   as totalSkillCnt
             , ifnull(dusrs.matched_skill_cnt, 0) as matchedSkillCnt
             , ifnull(dusrs.total_rt_cnt, 0)      as totalRtCnt
             , ifnull(dusrs.matched_rt_cnt, 0)    as matchedRtCnt
        from dws_user                          du
        left join dwd_user_skill_rt_statistics dusrs
                  on dusrs.user_id = du.user_id and dusrs.deleted = 0 and dusrs.org_id = du.org_id
        where du.org_id = '${orgId}'
          and du.enabled = 1
          and du.deleted = 0 and du.user_id in
        <!--@ignoreSql-->
        <foreach collection="userIds" item="userId" open="(" close="," separator=",">
            '${userId}'
        </foreach>
    </select>
    <select id="getAllByDeptGroupLimitOne"
            resultType="com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO">
        select tt.user_id                         as userId
             , ifnull(dusrs.total_skill_cnt, 0)   as totalSkillCnt
             , ifnull(dusrs.matched_skill_cnt, 0) as matchedSkillCnt
             , ifnull(dusrs.total_rt_cnt, 0)      as totalRtCnt
             , ifnull(dusrs.matched_rt_cnt, 0)    as matchedRtCnt
        from (select distinct du.user_id, du.org_id
        from dws_user du
        <if test="groupIds != null and groupIds.size() > 0">
            join dwd_user_group_member dugm
            on du.user_id = dugm.user_id and dugm.org_id = '${orgId}' and dugm.group_id in
            <!--@ignoreSql-->
            <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                ${groupId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            join dim_dept_closure ddc
            on ddc.dept_id = du.dept_id and ddc.org_id = '${orgId}' and ddc.deleted = 0 and ddc.parent_id in
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                '${deptId}'
            </foreach>
        </if>
        where du.org_id = '${orgId}'
          and du.deleted = 0
          and du.enabled = 1
            )                                  tt
        left join dwd_user_skill_rt_statistics dusrs
                  on tt.user_id = dusrs.user_id and tt.org_id = dusrs.org_id and dusrs.deleted = 0
        limit 1
    </select>
    <select id="getUserSkillRtByUserIdsOne"
            resultType="com.yxt.talent.bk.core.dashboard.bean.DwsUserSkillRtDTO">
        select du.user_id                         as userId
             , ifnull(dusrs.total_skill_cnt, 0)   as totalSkillCnt
             , ifnull(dusrs.matched_skill_cnt, 0) as matchedSkillCnt
             , ifnull(dusrs.total_rt_cnt, 0)      as totalRtCnt
             , ifnull(dusrs.matched_rt_cnt, 0)    as matchedRtCnt
        from dws_user                          du
        left join dwd_user_skill_rt_statistics dusrs
                  on dusrs.user_id = du.user_id and dusrs.deleted = 0 and dusrs.org_id = du.org_id
        where du.org_id = '${orgId}'
          and du.enabled = 1
          and du.deleted = 0
          and du.user_id in
            <foreach collection="userIds" item="userId" open="(" close="," separator=",">
                '${userId}'
            </foreach>
        <!--@ignoreSql-->
        limit 1
    </select>
</mapper>
