package com.yxt.talent.bk.api.controller.heir;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.AuditLogConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.heir.HeirPosPrepareCfgService;
import com.yxt.talent.bk.svc.heir.bean.req.PrepareCfgReq;
import com.yxt.talent.bk.svc.heir.bean.req.PrepareCycleReq;
import com.yxt.talent.bk.svc.heir.bean.resp.CycleAndRemindUserResp;
import com.yxt.talent.bk.svc.heir.bean.resp.HeirPrepareCfgResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/mgr/heir/prepareCfg")
@Slf4j
@AllArgsConstructor
@Tag(name = "准备度规则接口")
public class HeirPosPrepareCfgController extends BaseController {

    private final HeirPosPrepareCfgService heirPosPrepareCfgService;
    @Operation(summary = "准备度规则保存")
    @Parameters({         @Parameter(name = "PrepareCfgReq", description = "准备度规则设置")})
    @PostMapping(value = "/savePrepareCfg")
    @ResponseStatus(HttpStatus.OK)
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_PREPARE_RULE_EDIT, paramExp = "#prepareCfgReq")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public String savePrepareCfg(@RequestBody PrepareCfgReq prepareCfgReq) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return heirPosPrepareCfgService.saveprepareCfg(userCacheBasic, prepareCfgReq);
    }

    @Operation(summary = "准备度周期及提醒人员保存")
    @Parameters({         @Parameter(name = "PrepareCfgReq", description = "准备度规则设置")})
    @PostMapping(value = "/savePrepareCycle")
    @ResponseStatus(HttpStatus.OK)
    @Auditing
    @EasyAuditLog(value = AuditLogConstants.HEIR_PREPARE_CFG_EDIT, paramExp = "#prepareCycleReq")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    public void savePrepareCycle(@RequestBody PrepareCycleReq prepareCycleReq) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        heirPosPrepareCfgService.saveprepareCycle(userCacheBasic, prepareCycleReq);
    }

    @Operation(summary = "获取准备度规则条件")
    @Parameters({         @Parameter(name = "posId", description = "岗位id", in = ParameterIn.QUERY),         @Parameter(name = "prepareLevelId", description = "准备度规则等级id", in = ParameterIn.QUERY)})
    @GetMapping(value = "/getDetail")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public HeirPrepareCfgResp getDetail(@RequestParam("posId") String posId,@RequestParam("prepareLevelId") Long prepareLevelId) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return heirPosPrepareCfgService.getPrepareCfgDetail(userCacheBasic.getOrgId(), posId,prepareLevelId);
    }

    @Operation(summary = "获取准备度规则执行周期及通知人员")
    @Parameters({         @Parameter(name = "posId", description = "岗位id", in = ParameterIn.QUERY)})
    @GetMapping(value = "/getCycleAndRemindUser")
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETSINGLE, type = AuthType.TOKEN)
    public CycleAndRemindUserResp getCycleAndRemindUser(@RequestParam("posId") String posId) {
        UserCacheBasic userCacheBasic = getUserCacheBasic();
        return heirPosPrepareCfgService.getCycleAndRemindUser(userCacheBasic, posId);
    }
}
