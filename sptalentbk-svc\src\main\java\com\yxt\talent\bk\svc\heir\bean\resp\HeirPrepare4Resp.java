package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.bk.svc.common.bean.BkLabelConditionBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirPrepare4Dump
 *
 * <AUTHOR> harleyge
 * @Date 25/7/24 2:57 pm
 */
@Data
public class HeirPrepare4Resp {
    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @Schema(description = "名称")
    private String levelName;
    @Schema(description = "颜色设置")
    private String colorCode;
    private BkLabelConditionBean ruleConfig;
    private int levelMatched;
}
