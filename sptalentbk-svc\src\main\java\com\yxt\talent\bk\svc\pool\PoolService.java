package com.yxt.talent.bk.svc.pool;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.DlcComponent;
import com.yxt.export.ExcelUtil;
import com.yxt.export.OutputStrategy;
import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.spsdk.common.bean.ExportParam;
import com.yxt.spsdk.common.utils.YxtExportUtils;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.constants.CatalogConstants;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.constants.PoolConstants;
import com.yxt.talent.bk.common.constants.TalentBkAuthCodes;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.utils.*;
import com.yxt.talent.bk.core.pool.bean.PoolExport4AuditLog;
import com.yxt.talent.bk.core.pool.bean.PoolMgr4List;
import com.yxt.talent.bk.core.pool.bean.PoolUserQtyBean;
import com.yxt.talent.bk.core.pool.entity.Pool;
import com.yxt.talent.bk.core.pool.entity.PoolMgr;
import com.yxt.talent.bk.core.pool.entity.ProjectTargetMap;
import com.yxt.talent.bk.core.pool.mapper.ProjectTargetMapMapper;
import com.yxt.talent.bk.core.pool.repo.PoolMgrRepository;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.core.pool.repo.PoolUserRepository;
import com.yxt.talent.bk.core.usergroup.bean.SchemeUser4Export;
import com.yxt.talent.bk.svc.catalog.PoolCatalogService;
import com.yxt.talent.bk.svc.catalog.bean.Catalog4List;
import com.yxt.talent.bk.svc.pool.bean.Pool4BasicInfo;
import com.yxt.talent.bk.svc.pool.bean.Pool4Export;
import com.yxt.talent.bk.svc.pool.bean.Pool4List;
import com.yxt.talent.bk.svc.pool.bean.Pool4Save;
import com.yxt.talent.bk.svc.pool.bean.Pool4Search;
import com.yxt.talent.bk.svc.pool.bean.PoolProjectBindBean;
import com.yxt.talent.bk.svc.pool.bean.poolmgr.PoolMgrBean;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
public class PoolService {
    private final PoolRepository poolRepository;
    private final PoolMgrRepository poolMgrRepository;
    private final PoolUserRepository poolUserRepository;
    private final ILock lockService;
    private final DlcComponent dlcComponent;
    private final PoolCatalogService poolCatalogService;
    private final UserAuthService userAuthService;
    private final PoolProjectService poolProjectService;
    private final ProjectTargetMapMapper projectTargetMapMapper;

    private static final int MAX_LEASE_TIME = 100;
    private static final long EXPORT_MAX_SIZE = 30000;

    /**
     * 人才池列表
     *
     * @return
     */
    public PagingList<Pool4List> findPage(UserBasicBean userBasic, PageRequest pageRequest,
                                          Pool4Search search) {
        Page<Pool> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        String orgId = userBasic.getOrgId();
        // 获取有权限看的人才池id
        List<String> authPoolIds = getAuthPoolIds(userBasic);
        IPage<Pool> ipageList = poolRepository.getBaseMapper()
            .findPageBy(page, search.getCatalogId(), orgId, SqlUtils.escapeLike(search.getKeyword()), pageRequest.getOrderBy(),
                pageRequest.getDirection(), authPoolIds);

        PagingList<Pool4List> pool4ListPagingList = BeanCopierUtil.toPagingList(ipageList, Pool.class, Pool4List.class);
        if (CollectionUtils.isNotEmpty(pool4ListPagingList.getDatas())) {
            List<Catalog4List> byOrgId = poolCatalogService.findByOrgId(orgId, null);
            Map<String, String> catalogMap =
                byOrgId.stream().collect(Collectors.toMap(Catalog4List::getId, Catalog4List::getCatalogName));
            pool4ListPagingList.getDatas().stream().forEach(a -> {
                if (catalogMap.containsKey(a.getCatalogId())) {
                    a.setCatalogName(catalogMap.get(a.getCatalogId()));
                }
            });
        }
        if (search.getEnabledUserQty() == YesOrNo.YES.getValue()) {
            Map<String, Integer> userQtyMap = StreamUtil.list2map(poolUserRepository.poolEnableUserQty(orgId,
                    pool4ListPagingList.getDatas().stream().map(Pool4List::getId).collect(Collectors.toList())),
                    PoolUserQtyBean::getPoolId, PoolUserQtyBean::getUserQty);
            pool4ListPagingList.getDatas().forEach(pool ->
                    pool.setRealNum(Optional.ofNullable(userQtyMap.get(pool.getId())).orElse(0)));
        }
        return pool4ListPagingList;
    }

    private List<String> getAuthPoolIds(UserBasicBean userBasic) {
        List<String> poolIds = new ArrayList<>();
        String orgId = userBasic.getOrgId();


        if (!userBasic.isAdminUser()) {
            List<String> verifyUserIds = poolMgrRepository.getBaseMapper().findAllMgrIdByOrgId(orgId);
            // 权限隔离 - 我管辖的用户Id列表
            UserAuthBean userAuth = userAuthService.verifyPermission(userBasic, TalentBkAuthCodes.POOL_NAV_CODE, TalentBkAuthCodes.POOL_DATA_PERMISSION_VIEW,
                    verifyUserIds);
            // 不是管理员, 需要所管辖具体用户id
            if (!userAuth.isAllUserAllow()) {
                List<String> userIds = userAuth.getUserIds();
                if (CollectionUtils.isNotEmpty(userIds)) {
                    poolIds = poolMgrRepository.getBaseMapper().findAuthPoolIdsByMgrIds(orgId, userIds);
                }
                // userids是空 或者 不是空但没有任何其管理的人是管理者
                if (CollectionUtils.isEmpty(poolIds)) {
                    // 字符串，以保证查不出任何数据
                    poolIds.add(ApiUtil.getUuid());
                }

            }
        }
        return poolIds;
    }

    /**
     * 获取人才池基本信息
     *
     * @param orgId
     * @param id
     * @return
     */
    public Pool4BasicInfo getBasicInfo(String orgId, String id) {

        Pool4BasicInfo result = new Pool4BasicInfo();
        Pool pool = poolRepository.getById(id, orgId);
        if (pool != null) {
            BeanCopierUtil.copy(pool, result);
            List<PoolMgr4List> poolMgrByPoolId = poolMgrRepository.findPoolMgrByPoolId(orgId, id);
            List<PoolMgrBean> poolMgrBeans =
                BeanCopierUtil.convertList(poolMgrByPoolId, PoolMgr4List.class, PoolMgrBean.class);
            result.setMgrList(poolMgrBeans);
        }

        return result;
    }

    /**
     * 创建
     *
     * @param orgId
     * @param userId
     * @param pool
     */
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public String create(String orgId, String userId, Pool4Save pool) {

        if (pool.getExpectNum() != null && (pool.getExpectNum() < 0 || pool.getExpectNum() > 999999)) {
            throw new ApiException(PoolConstants.VERIFY_SAVE_EXPECTNUM_RANGE);
        }

        // 无分类、设置为默认分类
        if (StringUtils.isBlank(pool.getCatalogId())) {
            List<Catalog4List> catalog4Lists =
                poolCatalogService.findByOrgId(orgId, CatalogConstants.CatalogType.DEFAULT.getValue());
            pool.setCatalogId(catalog4Lists.get(0).getId());

        }

        // 保存人才池
        Pool bean = new Pool();
        BeanCopierUtil.copy(pool, bean);
        bean.setOrgId(orgId);
        EntityUtil.setCreateInfo(userId, bean);
        poolRepository.save(bean);

        // 保存管理者
        if (CollectionUtils.isEmpty(pool.getMgrIdList())) {
            // 前端未传管理者，默认当前用户
            pool.getMgrIdList().add(userId);
        }
        List<PoolMgr> mgrList = new ArrayList<>();
        for (String id : pool.getMgrIdList()) {
            PoolMgr mgr = new PoolMgr();
            EntityUtil.setCreateInfo(userId, mgr);
            mgr.setOrgId(orgId);
            mgr.setPoolId(bean.getId());
            mgr.setMgrId(id);
            mgrList.add(mgr);
        }
        poolMgrRepository.saveBatch(mgrList);
        return bean.getId();
    }

    /**
     * 编辑
     *
     * @param orgId
     * @param userId
     */
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void update(String orgId, String userId, Pool4Save bean) {
        if (bean == null || StringUtils.isBlank(bean.getId())) {
            throw new ApiException(PoolConstants.VERIFY_UPDATE_POOLID_NOTBLANK);
        }

        if (bean.getExpectNum() != null && (bean.getExpectNum() < 0 || bean.getExpectNum() > 999999)) {
            throw new ApiException(PoolConstants.VERIFY_SAVE_EXPECTNUM_RANGE);
        }

        //保存人才池
        boolean isEditExpectNum;
        Pool pool = poolRepository.getById(bean.getId(), orgId);
        isEditExpectNum = !pool.getExpectNum().equals(bean.getExpectNum());
        pool.setCatalogId(bean.getCatalogId());
        pool.setExpectNum(bean.getExpectNum());
        pool.setPoolName(bean.getPoolName());
        pool.setRemark(bean.getRemark());
        EntityUtil.setUpdatedInfo(userId, pool);
        poolRepository.updateById(pool);

        // 删除管理者
        poolMgrRepository.removeByOrgId(orgId, pool.getId());
        // 保存管理者
        List<PoolMgr> mgrList = new ArrayList<>();
        for (String id : bean.getMgrIdList()) {
            PoolMgr mgr = new PoolMgr();
            EntityUtil.setCreateInfo(userId, mgr);
            mgr.setOrgId(orgId);
            mgr.setPoolId(pool.getId());
            mgr.setMgrId(id);
            mgrList.add(mgr);
        }
        poolMgrRepository.saveBatch(mgrList);

        // 如果编辑了期望人数，更新饱和度
        if (isEditExpectNum) {
            this.updateSaturabilityById(orgId, bean.getId());
        }

    }


    /**
     * 删除
     *
     * @param poolId
     */
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void delete(UserCacheBasic userCacheDetail, String poolId) {
        String orgId = userCacheDetail.getOrgId();
        Pool pool = poolRepository.getById(poolId, orgId);
        if (pool == null) {
            throw new ApiException(PoolConstants.VERIFY_DELETE_NOT_EXIST);
        }
        if (pool.getRealNum() > 0) {
            throw new ApiException(PoolConstants.VERIFY_DELETE_REAL_NUM_NOT_ZERO);
        }

        // 如果RealNum为0 但是count 不为0 执行逻辑删除 【人员出池情况】
        long count = poolUserRepository.countByPoolId(orgId, poolId);
        if (count > 0) {
            pool.setDeleted(1);
            pool.setUpdateTime(new Date());
            poolRepository.updateById(pool);
        } else {
            poolRepository.removeById(orgId, poolId);
            poolMgrRepository.removeByOrgId(orgId, poolId);
        }

        // 删除绑定的培训和测评
        List<ProjectTargetMap> projectTargetMaps =
            projectTargetMapMapper.selectByOrgIdAndPoolId(userCacheDetail.getOrgId(), poolId);
        if (CollectionUtils.isNotEmpty(projectTargetMaps)) {
            PoolProjectBindBean bean = new PoolProjectBindBean();
            bean.setPoolId(poolId);
            bean.setProjectIds(StreamUtil.mapList(projectTargetMaps, ProjectTargetMap::getTargetId));
            poolProjectService.deletePoolProject(bean, userCacheDetail.getOrgId(), userCacheDetail.getUserId());
        }
    }

    /**
     * 导出查询结果
     */
    public Map<String, String> exportResult(UserBasicBean userBasic, Pool4Search search,
                                            UserCacheDetail userDetail) {
        String orgId = userBasic.getOrgId();
        String userId = userBasic.getUserId();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_POOL_RESULT_EXPORT, orgId, userId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                return doExportResult(userBasic, search, userDetail);
            } catch (Exception e) {
                log.error("exportResultException", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
    }

    private Map<String, String> doExportResult(UserBasicBean userBasic, Pool4Search search,
                                               UserCacheDetail userDetail) {
        String orgId = userBasic.getOrgId();
        //查询数据
        PageRequest pageRequest = new PageRequest();
        pageRequest.setCurrent(1);
        pageRequest.setSize(EXPORT_MAX_SIZE);

        PagingList<Pool4List> page = this.findPage(userBasic, pageRequest, search);

        List<Pool4Export> exportList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getDatas())) {

            // 设置人才池管理者
            Set<String> poolIds = page.getDatas().stream().map(Pool4List::getId).collect(Collectors.toSet());
            List<PoolMgr4List> poolMgrList = poolMgrRepository.findPoolMgrByPoolIds(orgId, poolIds);
            Map<String, List<PoolMgr4List>> poolMgrMap =
                poolMgrList.stream().collect(Collectors.groupingBy(PoolMgr4List::getPoolId));

            page.getDatas().forEach(a -> {
                Pool4Export export = new Pool4Export();
                String[] fields = {"createTime", "saturability", "expectNum"};
                BeanCopierUtil.copy(a, export, fields, false);

                // 设置管理者
                List<PoolMgr4List> poolMgrs = poolMgrMap.get(a.getId());
                if (CollectionUtils.isNotEmpty(poolMgrs)) {
                    StringBuilder sb = new StringBuilder();
                    poolMgrs.forEach(o -> sb.append(o.getMgrName()).append(","));
                    if (sb.length() > 0) {
                        sb.delete(sb.length() - 1, sb.length());
                    }
                    export.setMgrNames(sb.toString());
                }
                export.setExpectNum(a.getExpectNum() <= 0 ? "-" : a.getExpectNum() + "");
                // 饱和度处理
                String saturability = a.getExpectNum() <= 0 ? "-" : a.getSaturability().longValue() + "%";
                export.setSaturability(saturability);
                // 日期格式处理
                export.setCreateTime(DateUtil.formatSimpleDate(a.getCreateTime()));

                exportList.add(export);
            });
        }
        StringConcatBuilder poolNames = new StringConcatBuilder(StringPool.COMMA);
        IArrayUtils.forEach(page.getDatas(), data -> poolNames.append(data.getPoolName()).appendConcat());
        AuditLogHooker.setLogAfterData(new PoolExport4AuditLog(poolNames.output()));

        List<Object> objList = new ArrayList<>();
        objList.add(exportList);


        String fileName = TalentbkUtil.getMessage(ExportConstants.SCHEME_POOL_EXPORT_NAME, userDetail.getLocale())
                + "_" + System.currentTimeMillis() + ExportConstants.FILE_SUFFIX_XLSX + TalentBkConstants.FILE_ORID;

        String exportName = TalentbkUtil.getMessage(ExportConstants.SCHEME_POOL_EXPORT_NAME, userDetail.getLocale());
        ExportParam exportParam = new ExportParam();
        exportParam.setModuleCode(ModuleConstants.MODULE_CODE);
        exportParam.setFileName(fileName);
        exportParam.setName(exportName);
        String path = YxtExportUtils.exportList(userDetail, exportParam,
                Pool4Export.class, exportList);
        Map<String, String> result = new HashMap<>(1);
        result.put(ExportConstants.EXPORT_URL_KEY, path);
        return result;
    }

    /**
     * 导出策略
     */
    private OutputStrategy getOutputStrategy() {
        return new OutputStrategy() {
            @Override
            public String write(String path, String fileName, Object data) throws IOException {
                String filePath = path + fileName;
                ExcelUtil.exportWithTemplate(FillBeanUtil.generationFillBenList(data), filePath,
                    ExportConstants.EXPORT_POOL_TEMPLATE_PATH);
                return fileName;
            }

            @Override
            public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
                return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                    .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE)
                    .appCode(ModuleConstants.APP_CODE).moduleCode(ModuleConstants.MODULE_CODE).fileName(fileName)
                    .name("人才池列表").build();
            }
        };
    }

    /**
     * 更新现有人数、饱和度
     *
     * @param orgId
     * @param id
     */
    @Async
    public void updateSaturabilityById(String orgId, String id) {
        poolRepository.getBaseMapper().updateSaturabilityById(orgId, id);
    }

}
