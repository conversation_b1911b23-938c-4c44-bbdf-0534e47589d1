package com.yxt.talent.bk.svc.common;

import com.google.common.collect.Lists;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.spsdk.common.bean.ResourceTransferMq;
import com.yxt.spsdk.common.bean.ResourceTransferQty;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.heir.bean.HeirPosPermUserBean;
import com.yxt.talent.bk.core.heir.mapper.HeirPosPermissionMapper;
import com.yxt.talent.bk.core.pool.bean.PoolMgtBean;
import com.yxt.talent.bk.core.pool.mapper.PoolMapper;
import com.yxt.talent.bk.core.pool.mapper.PoolMgrMapper;
import com.yxt.talent.bk.core.usergroup.bean.UserGroupMgtBean;
import com.yxt.talent.bk.core.usergroup.mapper.UserGroupManagerMapper;
import com.yxt.talent.bk.core.usergroup.mapper.UserGroupMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ResourceTransferService
 *
 * <AUTHOR> harleyge
 * @Date 14/9/24 2:28 pm
 */
@Service
@AllArgsConstructor
public class ResourceTransferService {
    private final UserGroupMapper userGroupMapper;
    private final UserGroupManagerMapper userGroupManagerMapper;
    private final PoolMapper poolMapper;
    private final PoolMgrMapper poolMgrMapper;
    private final HeirPosPermissionMapper heirPosPermissionMapper;

    public int userGroupQty(String orgId, String userId) {
        return userGroupMapper.resTransferGroupIds(orgId, userId).size();
    }

    public ResourceTransferQty transferUserGroup(ResourceTransferMq transferReq) {
        String orgId = transferReq.getOrgId();
        ResourceTransferQty ret = new ResourceTransferQty();
        List<Long> groupIds = userGroupMapper.resTransferGroupIds(orgId, transferReq.getFrom());
        ret.setSuccessQty(groupIds.size());
        if (!groupIds.isEmpty()) {
            //转移群组创建人
            userGroupMapper.execResTransfer(orgId, transferReq.getFrom(), transferReq.getTo(), transferReq.getUpdateUserId());
            //转移群组管理人
            List<UserGroupMgtBean> updateList = CommonUtils.transferToUserUnique(
                    userGroupManagerMapper.resTransferList(orgId, Lists.newArrayList(transferReq.getFrom(), transferReq.getTo()), groupIds),
                    UserGroupMgtBean::getGroupId,
                    UserGroupMgtBean::getUserId, UserGroupMgtBean::setUserId,
                    UserGroupMgtBean::setDeleted,
                    transferReq.getFrom(), transferReq.getTo()
            );
            BatchOperationUtil.batchSave(updateList, subList -> userGroupManagerMapper.execResTransfer(orgId, subList));
        }
        return ret;
    }

    public int poolQty(String orgId, String userId) {
        return poolMapper.resTransferPoolIds(orgId, userId).size();
    }

    public ResourceTransferQty transferPool(ResourceTransferMq transferReq) {
        String orgId = transferReq.getOrgId();
        ResourceTransferQty ret = new ResourceTransferQty();
        List<String> poolIds = poolMapper.resTransferPoolIds(orgId, transferReq.getFrom());
        ret.setSuccessQty(poolIds.size());
        if (!poolIds.isEmpty()) {
            //转移人才池创建人
            poolMapper.execResTransfer(orgId, transferReq.getFrom(), transferReq.getTo(), transferReq.getUpdateUserId());
            //转移人才池负责人
            List<PoolMgtBean> updateList = CommonUtils.transferToUserUnique(
                    poolMgrMapper.resTransferList(orgId, Lists.newArrayList(transferReq.getFrom(), transferReq.getTo()), poolIds),
                    PoolMgtBean::getPoolId,
                    PoolMgtBean::getMgrId, PoolMgtBean::setMgrId,
                    PoolMgtBean::setDeleted,
                    transferReq.getFrom(), transferReq.getTo()
            );
            BatchOperationUtil.batchSave(updateList, subList -> poolMgrMapper.execResTransfer(orgId, subList));
        }
        return ret;
    }

    public int heirSuccessionQty(String orgId, String userId) {
        return heirPosPermissionMapper.resTransferCount(orgId, Lists.newArrayList(userId));
    }

    public ResourceTransferQty transferHeirSuccession(ResourceTransferMq transferReq) {
        String orgId = transferReq.getOrgId();
        ResourceTransferQty ret = new ResourceTransferQty();
        //转移继任权限用户
        List<HeirPosPermUserBean> updateList = CommonUtils.transferToUserUnique(
                heirPosPermissionMapper.resTransferList(orgId, Lists.newArrayList(transferReq.getFrom(), transferReq.getTo())),
                HeirPosPermUserBean::getPosId,
                HeirPosPermUserBean::getUserId, HeirPosPermUserBean::setUserId,
                HeirPosPermUserBean::setDeleted,
                transferReq.getFrom(), transferReq.getTo()
        );
        BatchOperationUtil.batchSave(updateList, subList -> heirPosPermissionMapper.execResTransfer(orgId, subList));
        ret.setSuccessQty((int) updateList.stream().map(HeirPosPermUserBean::getPosId).distinct().count());
        return ret;
    }

    public static ResourceTransferService self() {
        return SpringContextHolder.getBean(ResourceTransferService.class);
    }
}
