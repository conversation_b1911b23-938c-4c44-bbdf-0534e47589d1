package com.yxt.talent.bk.core.search.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.search.entity.SearchFilterGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @deprecated 在奇点中,人才标签和人才搜索完全重做了,这块代码已经不再使用, 后续可以考虑删除
 */
@Deprecated
public interface SearchFilterGroupMapper extends BaseMapper<SearchFilterGroup> {

    List<SearchFilterGroup> findGroupNamesByOrgId(@Param("orgId") String orgId);
}
