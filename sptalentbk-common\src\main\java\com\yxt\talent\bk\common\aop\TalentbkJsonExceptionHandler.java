package com.yxt.talent.bk.common.aop;

import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.yxt.common.Constants;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.ErrorData;
import com.yxt.common.pojo.api.ErrorResponse;
import com.yxt.common.repo.ConfigRepository;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StringUtil;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.io.IOException;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * JsonExceptionHandler
 */
@RequiredArgsConstructor
@Slf4j
@ControllerAdvice("com.yxt")
@ConditionalOnProperty(name = "talent.base.exceptionhandler.enabled", havingValue = "true", matchIfMissing = true)
public class TalentbkJsonExceptionHandler {

    private static final String MSG_415 = "{\"code\":415,\"message\":\"Unsupported Media Type\"}";
    private static final String MSG_405 = "{\"code\":405,\"message\":\"Method Not Allowed\"}";
    private static final String MSG_404 = "{\"code\":404,\"message\":\"Not Found\"}";
    private static final String MSG_REQUEST_JSON_FORMAT_ERROR = "{\"code\":415,\"message\":\"Json Format Error!\"}";
    private static final String MSG_INVALID = "{\"code\":400,\"message\":\"Invalid error message!\"}";
    private static final int MSG_NO_CODE = 2;

    private final MessageSourceService msgSource;
    private final AuthService authService;
    private final ConfigRepository configRepository;

    /**
     * handleHystrixRuntimeException
     *
     * @param ex HystrixRuntimeException
     * @return ResponseEntity<Object>
     */
    @ExceptionHandler(HystrixRuntimeException.class)
    public ResponseEntity<Object> handleHystrixRuntimeException(HttpServletRequest request,
            HystrixRuntimeException ex) {
        Throwable cause = ex.getCause();
        if (cause instanceof FeignException) {
            return handleFeignException(request, (FeignException) cause);
        } else {
            return handleGeneralException(request, ex);
        }
    }

    /**
     * handleFeignException
     *
     * @param ex FeignException
     * @return ResponseEntity<Object>
     */
    @ExceptionHandler(FeignException.class)
    public ResponseEntity<Object> handleFeignException(HttpServletRequest request, FeignException ex) {
        logError(request, ex, true, false);
        ResponseEntity<Object> responseEntity;
        if (ex.status() >= HttpStatus.CONTINUE.value()) {
            String content = ex.contentUTF8();
            if (StringUtils.isNotBlank(content)) {
                responseEntity = new ResponseEntity<>(content, HttpStatus.valueOf(ex.status()));
            } else {
                ErrorData error = toErrorData(ex);
                responseEntity = new ResponseEntity<>(new ErrorResponse(error), getErrorHeaders(error),
                                               HttpStatus.UNAVAILABLE_FOR_LEGAL_REASONS);
            }
        } else {
            ErrorData error = toErrorData(ex);
            responseEntity = new ResponseEntity<>(new ErrorResponse(error), getErrorHeaders(error),
                    HttpStatus.UNAVAILABLE_FOR_LEGAL_REASONS);
        }
        return responseEntity;
    }

    /**
     * handleApiException
     *
     * @param ex ApiException
     * @return ResponseEntity<Object>
     */
    @ExceptionHandler(ApiException.class)
    public ResponseEntity<Object> handleApiException(ApiException ex) {
        String msg = getMessage(ex.getErrorKey(), ex.getValues(), authService.getLocale());
        if (configRepository.isLogApiException()) {
            log.warn("handleApiException key:{}", ex.getErrorKey(), ex);
        }
        return generateResponse(ex.getErrorKey(), msg, ex.getValues());
    }

    /**
     * handelMethodArgumentNotValidException
     *
     * @param ex MethodArgumentNotValidException
     * @return ResponseEntity<Object>
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> handleMethodArgumentNotValidException(HttpServletRequest request,
            MethodArgumentNotValidException ex) {
        log.warn("MethodArgumentNotValidException, Request: {} {}", request.getMethod(), getRequestUri(request), ex);
        List<ObjectError> errors = ex.getBindingResult().getAllErrors();
        String key;
        if (errors.isEmpty()) {
            key = BkApiErrorKeys.UNKNOWN_ERROR;
        } else {
            key = errors.get(0).getDefaultMessage();
        }
        if (StringUtils.isBlank(key)) {
            key = BkApiErrorKeys.UNKNOWN_ERROR;
        }
        String msg = getMessage(key, null, authService.getLocale());
        return generateResponse(key, msg);
    }

    /**
     * request415
     *
     * @return String
     */
    @ExceptionHandler(HttpMediaTypeException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    @ResponseBody
    public String request415() {
        return MSG_415;
    }

    /**
     * request405
     *
     * @return String
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ResponseBody
    public String request405() {
        return MSG_405;
    }

    /**
     * request405
     *
     * @return String
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    public String request404() {
        return MSG_404;
    }

    /**
     * requestJsonFormatError
     *
     * @return String
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    @ResponseBody
    public String requestJsonFormatError(HttpServletRequest request, HttpMessageNotReadableException ex) {
        logError(request, ex, true, true);
        return MSG_REQUEST_JSON_FORMAT_ERROR;
    }

    /**
     * handleGeneralException
     *
     * @param ex Exception
     * @return ResponseEntity<Object>
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleGeneralException(HttpServletRequest request, Exception ex) {
        logError(request, ex);
        String key = BkApiErrorKeys.UNKNOWN_ERROR;
        String msg = getMessage(key, null, authService.getLocale());
        return configRepository.isReturnErrorTrace() ?
                generateResponse(key, msg, new String[]{ExceptionUtils.getStackTrace(ex)}) :
                generateResponse(key, msg);
    }

    private HttpHeaders getErrorHeaders(ErrorData error) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(Constants.HEADER_NAME_YXTERRORKEY, error.getKey());
        return headers;
    }

    private ResponseEntity<Object> generateResponse(String key, String msg) {
        return generateResponse(key, msg, null);
    }

    private ResponseEntity<Object> generateResponse(String key, String msg, Object[] args) {
        String[] msgs = msg.split(";");
        if (msgs.length < MSG_NO_CODE) {
            msgs = new String[]{"400", "Invalid resource format!"};
        }

        ErrorData errorData = new ErrorData();
        errorData.setKey(key);

        int status;
        if (msgs.length == MSG_NO_CODE) {
            errorData.setMessage(msgs[1]);
            status = StringUtil.str2Int(msgs[0], 400);
        } else {
            errorData.setCode(StringUtil.str2Long(msgs[0], 1L));
            errorData.setMessage(msgs[2]);
            status = StringUtil.str2Int(msgs[1], 400);
        }

        if (args != null && args.length > 0) {
            errorData.setData(args);
        }
        return new ResponseEntity<>(new ErrorResponse(errorData), getErrorHeaders(errorData),
                HttpStatus.valueOf(status));
    }

    private String getMessage(String code, Object[] args, Locale locale) {
        String msg;
        try {
            msg = msgSource.getMessage(code, args, locale);
        } catch (Exception e) {
            msg = MSG_INVALID;
            log.warn("msgSource.getMessage() error:", e);
        }
        return msg;
    }

    private ErrorData toErrorData(FeignException ex) {
        ErrorData error = new ErrorData();
        error.setCode(2001001);
        error.setKey(BkApiErrorKeys.UNKNOWN_ERROR);
        error.setMessage(ex.getMessage());
        if (configRepository.isReturnErrorTrace()) {
            error.setData(ExceptionUtils.getStackTrace(ex));
        }
        return error;
    }

    private void logError(HttpServletRequest request, Exception ex) {
        String requestUri = getRequestUri(request);
        log.error("Unhandled exception, Request: {} {}", request.getMethod(), requestUri, ex);
    }

    private void logError(HttpServletRequest request, Exception ex, boolean logHeader, boolean logBody) {
        String requestUri = getRequestUri(request);
        Map<String, String> errorHeader = null;
        String errorBody = null;
        if (logHeader) {
            errorHeader = BeanHelper.getRequestHeaders(request);
        }
        if (configRepository.isLogRequest() && logBody && StringUtils.isNotBlank(request.getContentType())
                && StringUtils.contains(request.getContentType().toLowerCase(), MediaType.APPLICATION_JSON_VALUE)) {
            try {
                errorBody = BeanHelper.getRequestBody(request);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        log.error("Unhandled exception, Request: method {} url: {} header: {} body: \n {}", request.getMethod(),
                requestUri, errorHeader != null ? errorHeader : "Log Disabled",
                errorBody != null ? errorBody : "Log Disabled", ex);
    }

    private String getRequestUri(HttpServletRequest request) {
        String requestUri;
        String queryString = request.getQueryString();
        if (queryString != null && queryString.length() > 0) {
            requestUri = String.format("%s?%s", request.getRequestURI(), queryString);
        } else {
            requestUri = request.getRequestURI();
        }

        return requestUri;
    }
}
