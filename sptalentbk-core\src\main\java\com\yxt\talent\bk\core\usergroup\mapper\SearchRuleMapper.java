package com.yxt.talent.bk.core.usergroup.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.usergroup.entity.SearchRule;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface SearchRuleMapper extends BaseMapper<SearchRule> {
	int updateBatch(List<SearchRule> list);

	int updateBatchSelective(List<SearchRule> list);

	int batchInsert(@Param("list") List<SearchRule> list);

	int insertOrUpdate(SearchRule record);

	int insertOrUpdateSelective(SearchRule record);

	int deleteLogicById(@Param("orgId") String orgId, @Param("userId") String userId, @Param("id") Long id);
}