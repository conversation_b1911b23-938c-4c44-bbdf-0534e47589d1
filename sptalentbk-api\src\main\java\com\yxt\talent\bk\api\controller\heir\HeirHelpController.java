package com.yxt.talent.bk.api.controller.heir;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.yxt.common.annotation.Auth;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.enums.AuthType;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.spsdk.democopy.PreSetIdMapRepository;
import com.yxt.spsdk.democopy.bean.DemoCopyContext;
import com.yxt.spsdk.udpbase.QueryUdpUtils;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.api.job.CommonNotifyJob;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.common.constants.CatalogConstants;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.core.common.repo.CatalogRepository;
import com.yxt.talent.bk.core.component.RedisComponent;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.pool.entity.Pool;
import com.yxt.talent.bk.core.pool.entity.PoolUser;
import com.yxt.talent.bk.core.pool.mapper.PoolMapper;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.core.pool.repo.PoolUserRepository;
import com.yxt.talent.bk.svc.common.DemoCopyService;
import com.yxt.talent.bk.svc.common.bean.DemoCopyResult;
import com.yxt.talent.bk.svc.heir.HeirPosPrepareCfgService;
import com.yxt.talent.bk.svc.heir.HeirPosService;
import com.yxt.talent.bk.svc.heir.ReadinessComputeService;
import com.yxt.talent.bk.svc.heir.bean.resp.DwdHeirPosResp;
import com.yxt.talent.bk.svc.heir.bean.resp.DwdHeirPosUserResp;
import com.yxt.talent.bk.svc.pool.PoolUserService;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.groovy.util.Maps;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * HeirHelpController
 *
 * <AUTHOR> geyan
 * @Date 24/8/23 6:00 pm
 */
@RestController
@RequestMapping(value = "/mgr/heir/help")
@Slf4j
@AllArgsConstructor
@Tag(name = "手动接口")
public class HeirHelpController extends BaseController {

    private final HeirPosPrepareCfgService posPrepareCfgService;
    private final HeirPosRepository heirPosRepository;
    private final HeirPosMapper heirPosMapper;
    private final HeirPosService heirPosService;
    private final ReadinessComputeService readinessComputeService;
    private final DemoCopyService demoCopyService;
    private final RedisComponent redisComponent;
    private final PoolUserService poolUserService;

    @Operation(summary = "手动计算用户准备度")
    @Auth(type = AuthType.NONE)
    @GetMapping(value = "/manualCalcUserPrepare")
    @ResponseStatus(HttpStatus.OK)
    public void manualCalcUserPrepare(@RequestParam String orgId,
                                      @RequestParam String posId) {
        normalCheckApi();
        int posType = heirPosRepository.getPosType(posId);
        posPrepareCfgService.calcPosUserPrepare(orgId, posId, posType);
    }

    @Operation(summary = "手动发送通知(用户准备度变更)")
    @Auth(type = AuthType.NONE)
    @GetMapping(value = "/sendPrepareChangeUser")
    @ResponseStatus(HttpStatus.OK)
    public void sendPrepareChangeUser(@RequestParam String posId) {
        normalCheckApi();
        readinessComputeService.sendPrepareChangeUser(heirPosMapper.selectById(posId));
    }

    @Operation(summary = "继任主数据")
    @Auth(type = AuthType.NONE)
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/list")
    public List<DwdHeirPosResp> posPage(@RequestParam String orgId) {
        normalCheckApi();
        return heirPosService.listPage4Open(getPage(), orgId).getDatas();
    }

    @Operation(summary = "继任用户数据")
    @Auth(type = AuthType.NONE)
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/user/list")
    public List<DwdHeirPosUserResp> posUserPage(@RequestParam String orgId,
                                                @RequestParam int userType) {
        normalCheckApi();
        return heirPosService.listUserPage4Open(getPage(), orgId, userType).getDatas();
    }

    @Operation(summary = "demo-copy-status")
    @Auth(type = AuthType.NONE)
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/demo-copy-status")
    public DemoCopyResult demoCopyStatus(@RequestParam String orgId) {
        normalCheckApi();
        String resultKey = String.format(TalentBkRedisKeys.CACHE_ORG_COPY_RESULT, orgId);
        return JSON.parseObject(redisComponent.getValue(resultKey), DemoCopyResult.class);
    }

    @Operation(summary = "manualSendBizCode")
    @Auth(type = AuthType.NONE)
    @ResponseStatus(HttpStatus.OK)
    @PutMapping("/manualSendBizCode")
    public List<String> manualSendBizCode(@RequestParam String orgId,
                           @RequestParam String bizCode) {
        if (bizCode.equalsIgnoreCase("_reset")) {
            String resultKey = String.format(TalentBkRedisKeys.CACHE_ORG_COPY_RESULT, orgId);
            redisComponent.removeKey(resultKey);
        } else if (bizCode.equalsIgnoreCase("_restart")) {
            String resultKey = String.format(TalentBkRedisKeys.CACHE_ORG_COPY_RESULT, orgId);
            redisComponent.removeKey(resultKey);
            demoCopyService.getDemoCopyStartRelated().forEach(startCode
                    -> demoCopyService.demoCopyBizDone(orgId, startCode, YesOrNo.YES.getValue()));
        } else {
            demoCopyService.demoCopyBizDone(orgId, bizCode, YesOrNo.YES.getValue());
        }
        OrgInit4Mq orgInit = new OrgInit4Mq();
        orgInit.setSourceOrgId(orgId);
        orgInit.setTargetOrgId(orgId);
        return demoCopyService.deleteEntitySQL(orgInit);
    }

    @Operation(summary = "delPoolInvalidUser")
    @Auth(type = AuthType.NONE)
    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/delPoolInvalidUser")
    public void fixPoolUser(@RequestParam String orgId, @RequestBody List<String> userIds) {
        normalCheckApi();
        QueryUdpUtils.fillSpUserInfo(orgId, userIds, item -> item, (userId, userInfo) -> {
            if (userInfo.getDeleted() == YesOrNo.NO.getValue()) {
                userIds.remove(userId);
            }
        });
        poolUserService.removeDelUser(orgId, userIds);
    }

    @Operation(summary = "executeDemoCopy")
    @Auth(type = AuthType.NONE)
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/executeDemoCopy")
    public List<String> executeDemoCopy(@RequestParam String orgId,
                                        @RequestParam(required = false) boolean execCopy) {
        normalCheckApi();
        String orgInitDataKey = String.format(TalentBkRedisKeys.CACHE_COPY_ORG_DATA, orgId);
        OrgInit4Mq orgInit = JSON.parseObject(redisComponent.getValue(orgInitDataKey), OrgInit4Mq.class);
        if (orgInit == null) {
            throw new ApiException("demoCopy init Data null");
        }
        List<String> deleteSql = demoCopyService.preGenIdMap(orgInit);
        if (execCopy) {
            demoCopyService.demoCopy(orgInit);
        }
        return deleteSql;
    }

    @Operation(summary = "tokenApiAuthInfo")
    @Auth(type = AuthType.TOKEN)
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/tokenApiAuthInfo")
    public Map<String, Object> tokenApiAuthInfo(@RequestHeader String orgId,
                                                @RequestHeader String userId,
                                                @RequestHeader String user) {
        return Maps.of("orgId", orgId, "userId", userId, "user", user);
    }
}
