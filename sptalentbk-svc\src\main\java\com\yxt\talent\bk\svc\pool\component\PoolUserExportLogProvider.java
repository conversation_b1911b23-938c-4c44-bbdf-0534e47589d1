package com.yxt.talent.bk.svc.pool.component;

import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.bk.core.pool.bean.PoolExport4AuditLog;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Param;
import com.yxt.talent.bk.core.pool.mapper.PoolMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * PoolExportLogProvider
 *
 * <AUTHOR> harleyge
 * @Date 6/8/24 10:57 am
 */
@Slf4j
@AllArgsConstructor
@Component
public class PoolUserExportLogProvider implements AuditLogDataProvider<PoolUser4Param, PoolExport4AuditLog> {
    private final PoolMapper poolMapper;
    @Override
    public PoolExport4AuditLog before(PoolUser4Param param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public PoolExport4AuditLog after(PoolUser4Param param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public Pair<String, String> entityInfo(PoolUser4Param param, PoolExport4AuditLog beforeObj, PoolExport4AuditLog afterObj, AuditLogBasicBean logBasic) {
        String entityName = String.format(logBasic.getLogPoint().getPointName(), poolMapper.getNameById(param.getPoolId()));
        return Pair.of(param.getPoolId(), entityName);
    }
}
