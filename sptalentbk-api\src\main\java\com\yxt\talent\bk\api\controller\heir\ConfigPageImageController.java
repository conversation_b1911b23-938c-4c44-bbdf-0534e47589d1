package com.yxt.talent.bk.api.controller.heir;

import com.alibaba.fastjson2.JSON;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.heir.ConfigPageImageService;
import com.yxt.talent.bk.svc.heir.bean.req.ConfigPageImageAddReq;
import com.yxt.talent.bk.svc.heir.bean.req.ConfigPageImageUpdateReq;
import com.yxt.talent.bk.svc.heir.bean.resp.ConfigPageImageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
* <AUTHOR>
*  @date 2023/9/14
**/
@Slf4j
@Tag(name = "轮播图设置")
@RestController
@RequestMapping("/mgr/config/page_image")
@RequiredArgsConstructor
public class ConfigPageImageController extends BaseController {

    private final ConfigPageImageService configPageImageService;

    @Operation(summary = "添加轮播图")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_CREATESINGLE, type = AuthType.TOKEN)
    @PostMapping("/add")
    public void add(@RequestBody @Valid ConfigPageImageAddReq req) {
        log.info("addConfigPageImage. req: {}", JSON.toJSONString(req));
        UserCacheBasic currentUser = getUserCacheBasic();
        configPageImageService.create(currentUser, req);
    }

    @Operation(summary = "修改轮播图")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_UPDATESINGLE, type = AuthType.TOKEN)
    @PutMapping("/update")
    public void update(@RequestBody @Valid ConfigPageImageUpdateReq req) {
        log.info("updateConfigPageImage. req: {}", JSON.toJSONString(req));
        UserCacheBasic currentUser = getUserCacheBasic();
        configPageImageService.update(currentUser, req);
    }

    @Operation(summary = "轮播图删除")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_DELETESINGLE, type = AuthType.TOKEN)
    @DeleteMapping("/delete")
    public void delete(HttpServletRequest request, @RequestParam String id) {
        log.info("deleteConfigPageImage. id: {}", id);
        UserCacheBasic currentUser = getUserCacheBasic();
        configPageImageService.delete(currentUser, id);
    }

    @Operation(summary = "轮播图列表")
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @GetMapping("/list")
    public CommonList<ConfigPageImageResp> list() {
        UserCacheBasic currentUser = getUserCacheBasic();
        return configPageImageService.list(currentUser);
    }

}

