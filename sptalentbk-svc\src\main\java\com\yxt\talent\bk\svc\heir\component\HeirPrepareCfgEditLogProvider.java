package com.yxt.talent.bk.svc.heir.component;

import com.alibaba.fastjson2.JSON;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirPosMapper;
import com.yxt.talent.bk.core.heir.mapper.HeirPrepareRemindUserMapper;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.heir.bean.CalcCycleBean;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareCfgEdit4Log;
import com.yxt.talent.bk.svc.heir.bean.HeirPrepareRuleEdit4Log;
import com.yxt.talent.bk.svc.heir.bean.req.PrepareCycleReq;
import com.yxt.talent.bk.svc.heir.enums.CycleEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * HeirBmUserEditLogProvider
 *
 * <AUTHOR> geyan
 * @Date 21/3/24 10:09 am
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirPrepareCfgEditLogProvider implements AuditLogDataProvider<PrepareCycleReq, HeirPrepareCfgEdit4Log> {
    private final HeirPosTypeSelector heirPosTypeSelector;
    private final HeirPosMapper heirPosMapper;
    private final HeirPrepareRemindUserMapper heirPrepareRemindUserMapper;
    private final UdpLiteUserRepository udpLiteUserRepository;

    @Override
    public HeirPrepareCfgEdit4Log before(PrepareCycleReq param, AuditLogBasicBean logBasic) {
        HeirPosEntity heirPos = heirPosMapper.selectById(param.getPosId());
        if (heirPos == null) {
            return null;
        }
        PrepareCycleReq beforeCycle = new PrepareCycleReq();
        beforeCycle.setPosId(heirPos.getId());
        beforeCycle.setCalcCycleBean(JSON.parseObject(heirPos.getCalcCycle(), CalcCycleBean.class));
        beforeCycle.setRemindCycleBean(JSON.parseObject(heirPos.getRemindCycle(), CalcCycleBean.class));
        beforeCycle.setUserIds(heirPrepareRemindUserMapper.posRemindUserIds(heirPos.getId()));
        return buildEditAuditLog(logBasic.getOrgId(), beforeCycle);
    }

    @Override
    public HeirPrepareCfgEdit4Log after(PrepareCycleReq param, AuditLogBasicBean logBasic) {
        return buildEditAuditLog(logBasic.getOrgId(), param);
    }

    @Override
    public Pair<String, String> entityInfo(PrepareCycleReq param,
                                           HeirPrepareCfgEdit4Log beforeObj, HeirPrepareCfgEdit4Log afterObj,
                                           AuditLogBasicBean logBasic) {
        String posName = heirPosTypeSelector.getPosNameById(logBasic.getOrgId(), param.getPosId()).getValue();
        return Pair.of(param.getPosId(), String.format(logBasic.getLogPoint().getPointName(), posName));
    }

    private HeirPrepareCfgEdit4Log buildEditAuditLog(String orgId, PrepareCycleReq param) {
        HeirPrepareCfgEdit4Log edit4Log = new HeirPrepareCfgEdit4Log();
        edit4Log.setCalcCycle(cycleDesc(param.getCalcCycleBean()));
        edit4Log.setRemindCycle(cycleDesc(param.getRemindCycleBean()));
        edit4Log.setRemindTime(Optional.ofNullable(param.getRemindCycleBean())
                .map(CalcCycleBean::getRemindTime).orElse(null));
        edit4Log.setRemindUser(udpLiteUserRepository.userNames4Log(orgId, param.getUserIds()));
        return edit4Log;
    }

    private String cycleDesc(CalcCycleBean cycleBean) {
        if (cycleBean == null) {
            return null;
        }
        CycleEnum cycleEnum = CycleEnum.getByCode(cycleBean.getCycle());
        return cycleEnum != null ? cycleEnum.getDescFunc().apply(cycleBean.getDay()) : null;
    }
}
