package com.yxt.talent.bk.svc.heir.bean.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlField;
import com.yxt.talent.bk.common.aop.sqlgenerator.SqlTable;
import com.yxt.talent.bk.core.heir.bean.open.DwdHeirPosUserEntity;
import com.yxt.talent.bk.svc.profile.bean.UserTag4Get;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * DwdHeirPosResp
 *
 * <AUTHOR> geyan
 * @Date 14/9/23 6:30 pm
 */
@SqlTable(DwdHeirPosUserEntity.TABLE)
@Data
public class OpenHeirPosUserResp {
    @Schema(description = "用户id")
    @SqlField("user_id")
    private String userId;
    private String fullname;
    //private String thirdUserId;
    @Schema(description = "0:继任用户，1:标杆用户")
    private Integer userType;

    @Schema(description = "准备等级名称")
    private String levelName;

    @Schema(description = "准备等级颜色")
    private String levelColor;

    @Schema(description = "年龄")
    private Integer thirdAge;

    @Schema(description = "照片地址")
    private String thirdAvatarUrl;

    @Schema(description = "性别(0-未知 1-男 2-女)")
    private Integer thirdGender;

    @Schema(description = "司龄")
    private Integer thirdServiceYears;

    @Schema(description = "职级名称")
    private String thirdJobgradeName;

    private List<UserTag4Get> userTagList;

    @Schema(description = "是否是当前部门下的用户")
    private int userUnderDept;
}
