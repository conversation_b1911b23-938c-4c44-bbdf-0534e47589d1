package com.yxt.talent.bk.core.pool.bean;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.Data;

import java.util.List;

/**
 * Pool4AuditLog
 *
 * <AUTHOR> geyan
 * @Date 15/3/24 2:03 pm
 */
@Data
public class Pool4AuditLog {
    private String orgId;
    @AuditLogField(name = "名称", orderIndex = 1)
    private String poolName;
    @AuditLogField(name = "分类", orderIndex = 2)
    private String catalogName;
    @AuditLogField(name = "期望人数", orderIndex = 3)
    private Integer expectNum;
    @AuditLogField(name = "管理者", orderIndex = 4)
    private String mgtUserNames;
    @AuditLogField(name = "备注", orderIndex = 5)
    private String remark;
    private List<String> mgtUserIds;
}
