package com.yxt.talent.bk.svc.catalog;

import com.alibaba.nacos.common.utils.StringUtils;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.CatalogConstants;
import com.yxt.talent.bk.core.TagCatalog;
import com.yxt.talent.bk.core.common.repo.CatalogRepository;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.svc.base.CoreFactorService;
import com.yxt.talent.bk.svc.tag.bean.TagCatalog4Create;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Service
@AllArgsConstructor
public class CatalogService {

    private final CatalogRepository catalogRepository;
    private final TagRepository tagRepository;
    private final CoreFactorService coreFactorService;


    /**
     * 查询 标签分类查询
     * @param orgId
     * @return java.util.List<com.yxt.talent.bk..pojo.TagCatalog4Create>
     * <AUTHOR>
     * @since 2022/8/22
     */
    public List<TagCatalog4Create> list4Vo(String orgId) {
        //是否开通人才盘点
        boolean rvOrNot =  coreFactorService.isOpenRv(orgId);
        List<TagCatalog> list = catalogRepository.listByCatalogType(orgId,CatalogConstants.CatalogSource.TAG.getValue(), rvOrNot);
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().sorted(Comparator.comparing(TagCatalog::getCreateTime).reversed()).collect(Collectors.toList());
            // 默认分类 置底
            List<TagCatalog> catalogs = list.stream().filter(tagCatalog -> "默认分类".equals(tagCatalog.getCatalogName()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(catalogs) && catalogs.size() == 1) {
                TagCatalog tagCatalog = catalogs.get(0);
                list.remove(tagCatalog);
                list.add(tagCatalog);
            }
        }
        return BeanCopierUtil.convertList(list, TagCatalog.class, TagCatalog4Create.class);
    }

    public TagCatalog4Create getById(String orgId, String id) {
        TagCatalog4Create tagCatalogCreateVo = new TagCatalog4Create();
        BeanUtils.copyProperties(catalogRepository.getOne(orgId, id), tagCatalogCreateVo, TagCatalog4Create.class);
        return tagCatalogCreateVo;
    }

    @DbHintMaster
    public TagCatalog saveOrUpdate(String orgId, String userId,
            @NotNull(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) TagCatalog4Create catalog) {
        TagCatalog tagCatalog = new TagCatalog();
        BeanUtils.copyProperties(catalog, tagCatalog, TagCatalog.class);

        if (StringUtils.isBlank(tagCatalog.getId())) {
            // 无id则为新增
            List<TagCatalog> listByName = catalogRepository.listByName(orgId, tagCatalog.getCatalogName(), CatalogConstants.CatalogSource.TAG.getValue());
            if (CollectionUtils.isNotEmpty(listByName)) {
                throw new ApiException(BkApiErrorKeys.ERROR_KEY_CATALOG_NAME_EXIST);
            }
            tagCatalog.setOrgId(orgId);
            // 只支持创建自建标签 值为2
            tagCatalog.setCatalogType(2);
            EntityUtil.setCreateInfo(userId, tagCatalog);
        } else {
            // 更新
            EntityUtil.setUpdatedInfo(userId, tagCatalog);
        }
        catalogRepository.saveOrUpdate(tagCatalog);
        return tagCatalog;
    }

    @DbHintMaster
    public Boolean removeById(String orgId, String id) {
        if (tagRepository.countByCatalogId(orgId, id) > 0) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_CATALOG_BE_USED);
        }
        return catalogRepository.removeById(orgId, id);
    }

    @DbHintMaster
    public void saveBatch(List<TagCatalog> saveCatalogList) {
        catalogRepository.saveBatch(saveCatalogList);
    }
}
