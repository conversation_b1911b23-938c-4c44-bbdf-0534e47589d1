package com.yxt.talent.bk.core.dashboard.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * SkillModelClassification4Get
 *
 * <AUTHOR>
 * @since 2021-05-25 17:33:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SkillModelClassification4Get {
    @Schema(description = "id", example = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String id;

    //2.如果是二级能力分类
    @Schema(description = "二级能力分类名称", example = "二级能力分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String name;

    @Schema(description = "排序", example = "1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected Integer orderIndex;

    @Schema(description = "分类类型：1-能力分类, 2-能力", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    protected int family;

    @Schema(description = "标准等级")
    private String standardLevel;

    @Schema(description = "标准等级名称")
    private String standardLevelName;

    @Schema(description = "二级分类的能力列表", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    protected List<SkillModelMap4Get> skillModelMaps = new ArrayList<>();

    @Schema(description = "能力")
    private Skill4Get skill;

}
