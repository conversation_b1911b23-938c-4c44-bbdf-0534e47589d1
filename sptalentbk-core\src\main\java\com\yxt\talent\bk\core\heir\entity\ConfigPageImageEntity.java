package com.yxt.talent.bk.core.heir.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.SnowFlowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirPosUserEntity
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 11:54 am
 */
@Data
@TableName("bk_config_page_image")
public class ConfigPageImageEntity extends SnowFlowIdEntity {
    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "图片地址")
    private String imageUrl;

    @Schema(description = "链接地址")
    private String linkUrl;

}
