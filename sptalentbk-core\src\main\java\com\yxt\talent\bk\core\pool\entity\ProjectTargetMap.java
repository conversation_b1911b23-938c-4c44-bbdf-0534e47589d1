package com.yxt.talent.bk.core.pool.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.bk.common.constants.BkDemoConstants;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 项目与外部关系表
 */
@Getter
@Setter
@TableName(value = "bk_project_target_map")
public class ProjectTargetMap extends CreatorEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 人才池id
     */
    @TableField(value = "project_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.BK_POOL_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String projectId;

    /**
     * 类型(1:人才池)
     */
    @TableField(value = "project_type")
    private Integer projectType;

    /**
     * 第三方id(1:测评 2:测训)
     */
    @TableField(value = "target_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = BkDemoConstants.DYN_MAP_POOL_TARGET, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_SKIP)
    private String targetId;

    /**
     * 类型(1:测评 2:测训)
     */
    @TableField(value = "target_type")
    private Integer targetType;

}
