package com.yxt.talent.bk.common.aop.uuid;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.UUID;

/**
 * 输入校验，某个字段是否是uuid
 *
 * <AUTHOR>
 */
public class UuidValidator implements ConstraintValidator<Uuid, String> {

	private static final Logger LOGGER = LoggerFactory.getLogger(UuidValidator.class);

	@Override
	public void initialize(Uuid uuid) {
		// 启动时执行
	}

	/**
	 * : 自定义校验逻辑：检查输入的字段是否是uuid
	 */
	@Override
	public boolean isValid(String uuid, ConstraintValidatorContext constraintValidatorContext) {
		if (StringUtils.isBlank(uuid)) {
			// 未传、null、空字符，校验通过
			return true;
		}
		return !isNotUuid(uuid);
	}

	/**
	 * 检查字符串是否是合法的uuid,
	 *
	 * @param uuidStr
	 * @return 是合法的uuud返回true,不是uuid返回false
	 */
	public static boolean isUuid(String uuidStr) {
		try {
			UUID.fromString(uuidStr).toString();
		} catch (Exception e) {
			LOGGER.error("checkIsUuid {}",uuidStr, e);
			return false;
		}
		return true;
	}

	private static boolean isNotUuid(String uuidStr) {
		return !isUuid(uuidStr);
	}
}
