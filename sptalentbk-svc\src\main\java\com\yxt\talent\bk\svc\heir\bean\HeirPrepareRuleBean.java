package com.yxt.talent.bk.svc.heir.bean;

import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * HeirPrepareRuleBean
 *
 * <AUTHOR> geyan
 * @Date 19/8/23 3:56 pm
 */
@Data
public class HeirPrepareRuleBean {
    @Schema(description = "排序号")
    private Integer orderIndex;
    @Schema(description = "准备度规则等级id")
    private Long prepareLevelId;
    private LabelRuleGroupBean ruleGroup;
    private LabelConditionJsonBean ruleCondition;
}
