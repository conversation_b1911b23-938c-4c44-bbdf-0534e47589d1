package com.yxt.talent.bk.api.controller.client;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.talent.bk.api.component.TalentSchemeComponent;
import com.yxt.talent.bk.api.controller.BaseController;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.svc.scheme.bean.Scheme4List;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description 继任学员端
 *
 * <AUTHOR>
 * @Date 2023/11/15 14:00
 **/

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/scheme/")
@Tag(name = "筛选学员端")
public class ClientSchemeController extends BaseController {

    private final TalentSchemeComponent talentSchemeComponent;

    @Operation(summary = "筛选方案列表")
    @GetMapping(value = "/list", consumes = Constants.MEDIATYPE)
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_SCHEME, action = Constants.LOG_TYPE_SEARCH, type = AuthType.TOKEN)
    public CommonList<Scheme4List> findSchemeList() {
        UserCacheDetail userDetail = getUserCacheDetail();
        List<Scheme4List> schemeList = talentSchemeComponent.findSchemeList(userDetail.getOrgId(), userDetail.getUserId());
        return new CommonList<>(schemeList);
    }
}
