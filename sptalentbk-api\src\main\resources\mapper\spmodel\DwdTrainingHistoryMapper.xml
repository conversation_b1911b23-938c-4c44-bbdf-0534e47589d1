<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.spmodel.mapper.DwdTrainingHistoryMapper">
    <select id="countByUserId" resultType="int">
        select count(1) as totalCount
        from dwd_training_history
        where org_id = '${orgId}'
        <if test="userId != null and userId != ''">
            and user_id = '${userId}'
        </if>
        <if test="userId == null or userId == ''">
            <!--@ignoreSql-->
            and 1 != 1
        </if>
          and deleted = 0
    </select>
</mapper>
