package com.yxt.talent.bk.common.bean.udp;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * UdpLangUnitBean
 *
 * <AUTHOR> harleyge
 * @Date 30/4/24 3:11 pm
 */
@Data
public class UdpLangUnitBean {
    private String id;
    private String name;
    private Consumer<String> nameSetter;

    public UdpLangUnitBean(String id, String name, Consumer<String> nameSetter) {
        this.id = id;
        this.name = name;
        this.nameSetter = nameSetter;
    }

    public static boolean validUnit(UdpLangUnitBean unitBean) {
        return unitBean != null && StringUtils.isNotEmpty(unitBean.id) && unitBean.nameSetter != null;
    }
}
