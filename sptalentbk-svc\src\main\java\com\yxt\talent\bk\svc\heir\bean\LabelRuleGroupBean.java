package com.yxt.talent.bk.svc.heir.bean;

import lombok.Data;

import java.util.List;

/**
 * LabelRuleCfgGroupBean
 *
 * <AUTHOR> geyan
 * @Date 18/8/23 7:33 pm
 */
@Data
public class LabelRuleGroupBean {
    private List<LabelRuleBean> rules;

    private List<LabelRuleGroupBean> conditions;

    /**
     * 是否且关系
     */
    private int logic;

    public boolean isCalcAnd() {
        return logic == 1;
    }
}
