package com.yxt.talent.bk.svc.persona.theme;

import com.yxt.common.exception.ApiException;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeDimensionEntity;
import com.yxt.talent.bk.core.persona.entity.PersonaThemeDimensionTagMapEntity;
import com.yxt.talent.bk.core.persona.repo.PersonaThemeDimensionRepository;
import com.yxt.talent.bk.core.persona.repo.PersonaThemeDimensionTagMapRepository;
import com.yxt.talent.bk.core.tag.entity.TagEntity;
import com.yxt.talent.bk.core.tag.repo.TagRepository;
import com.yxt.talent.bk.svc.persona.bean.ThemeDimension4Create;
import com.yxt.talent.bk.svc.persona.bean.ThemeDimension4Update;
import com.yxt.talent.bk.svc.persona.bean.ThemeDimensionTag4Create;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class ThemeDimensionService {
    private final PersonaThemeDimensionRepository dimensionRepository;
    private final TagRepository tagRepository;
    private final PersonaThemeDimensionTagMapRepository dimensionTagMapRepository;


    public void createDimension(String userId, String orgId, ThemeDimension4Create create) {
        List<PersonaThemeDimensionEntity> existDimensionNameList = dimensionRepository
                .queryThemeDimensionByName(orgId, create.getDimensionName(), create.getThemeId());
        if (CollectionUtils.isNotEmpty(existDimensionNameList)) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_DIMENSION_NAME_EXIST);
        }
        create.setDimensionName(TalentbkUtil.escapeStr(create.getDimensionName()));
        Validate.isTrue(StringUtils.isNotBlank(create.getDimensionName()),
                BkApiErrorKeys.ERROR_KEY_DIMENSION_NAME_NOT_EMPTY);
        PersonaThemeDimensionEntity createThemeDimension = new PersonaThemeDimensionEntity();
        createThemeDimension.setDimensionName(create.getDimensionName());
        createThemeDimension.setId(ApiUtil.getUuid());
        createThemeDimension.setDimensionSource(1);
        createThemeDimension.setOrgId(orgId);
        createThemeDimension.setThemeId(create.getThemeId());
        EntityUtil.setCreateInfo(userId, createThemeDimension);
        dimensionRepository.save(createThemeDimension);
    }

    /**
     * 维度删除 维度及维度关联标签数据一起删除
     * @param orgId
     * @param dimensionId
     */
    @Transactional(transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void removeDimension(String orgId, String dimensionId) {
        PersonaThemeDimensionEntity dimension = dimensionRepository
                .queryThemeDimensionById(orgId, dimensionId);
        Validate.isNotNull(dimension, BkApiErrorKeys.ERROR_KEY_DIMENSION_NOT_FOUND);
        Validate.isTrue(dimension.getDimensionSource() == 1,
                BkApiErrorKeys.ERROR_KEY_DIMENSION_BUILD_CANNOT_REMOVE);
        dimensionTagMapRepository.removeThemeDimensionTag(orgId, dimension.getThemeId(), dimensionId);
        dimensionRepository.removeDimensionById(orgId, dimensionId);
    }

    public void removeDimensionTag(String orgId, String dimensionTagId) {
        PersonaThemeDimensionTagMapEntity themeDimensionTag = dimensionTagMapRepository
                .queryThemeDimensionTagById(orgId, dimensionTagId);
        Validate.isNotNull(themeDimensionTag, BkApiErrorKeys.ERROR_KEY_DIMENSION_TAG_RELATION_NOT_FOUND);
        PersonaThemeDimensionEntity dimension = dimensionRepository
                .queryThemeDimensionById(orgId, themeDimensionTag.getDimensionId());
        if (dimension != null) {
            Validate.isTrue(dimension.getDimensionSource() == 1,
                    BkApiErrorKeys.ERROR_KEY_DIMENSION_TAG_RELATION_CANNOT_REMOVE);
        }
        dimensionTagMapRepository.removeTag(orgId, dimensionTagId);
    }

    public void updateDimension(String userId, String orgId, ThemeDimension4Update update) {
        PersonaThemeDimensionEntity dimension = dimensionRepository
                .queryThemeDimensionById(orgId, update.getDimensionId());
        Validate.isNotNull(dimension, BkApiErrorKeys.ERROR_KEY_DIMENSION_NOT_FOUND);
        dimension.setDimensionName(TalentbkUtil.escapeStr(dimension.getDimensionName()));
        Validate.isTrue(StringUtils.isNotBlank(dimension.getDimensionName()),
                BkApiErrorKeys.ERROR_KEY_DIMENSION_NAME_NOT_EMPTY);
        if (dimension.getDimensionName().equals(update.getDimensionName())) {
            return;
        }
        List<PersonaThemeDimensionEntity> existDimensionNameList = dimensionRepository
                .queryThemeDimensionByName(orgId, update.getDimensionName(), dimension.getThemeId());
        if (CollectionUtils.isNotEmpty(existDimensionNameList) && existDimensionNameList.stream()
                .noneMatch(e -> e.getId().equals(update.getDimensionId()))) {
            throw new ApiException(BkApiErrorKeys.ERROR_KEY_DIMENSION_NAME_EXIST);
        }
        dimension.setDimensionName(update.getDimensionName());
        EntityUtil.setUpdatedInfo(userId, dimension);
        dimensionRepository.saveOrUpdate(dimension);
    }

    public void createDimensionTag(String userId, String orgId, ThemeDimensionTag4Create create) {
        PersonaThemeDimensionEntity dimension = dimensionRepository
                .queryThemeDimensionById(orgId, create.getDimensionId());
        Validate.isNotNull(dimension, BkApiErrorKeys.ERROR_KEY_DIMENSION_NAME_EXIST);
        Validate.isTrue(dimension.getDimensionSource() == 1,
                BkApiErrorKeys.ERROR_KEY_DIMENSION_BUILD_CANNOT_BIND_TAG);
        if (CollectionUtils.isEmpty(create.getTagIds())) {
            return;
        }
        List<TagEntity> tagInfoList = tagRepository.queryTagByIds(orgId, create.getTagIds());
        Validate.isTrue(CollectionUtils.isNotEmpty(tagInfoList), BkApiErrorKeys.ERROR_KEY_TAG_NOT_FOUND);
        //保存已存在的
        List<PersonaThemeDimensionTagMapEntity> dimensionTagList = new ArrayList<>(tagInfoList.size());
        //查询已存在的维度标签
        List<PersonaThemeDimensionTagMapEntity> existDimensionTagList = dimensionTagMapRepository
                .queryDimensionTagByTagIdAndDimensionId(orgId, create.getDimensionId(), create.getTagIds());
        Map<String, PersonaThemeDimensionTagMapEntity> tagIdDimensionMap = StreamUtil
                .list2map(existDimensionTagList, PersonaThemeDimensionTagMapEntity::getTagId);
        tagInfoList.forEach(tag -> {
            if (!tagIdDimensionMap.containsKey(tag.getId())) {
                PersonaThemeDimensionTagMapEntity dimensionTagMap = new PersonaThemeDimensionTagMapEntity();
                dimensionTagMap.setId(ApiUtil.getUuid());
                dimensionTagMap.setDimensionId(create.getDimensionId());
                dimensionTagMap.setOrgId(orgId);
                dimensionTagMap.setTagId(tag.getId());
                dimensionTagMap.setThemeId(dimension.getThemeId());
                EntityUtil.setCreateInfo(userId, dimensionTagMap);
                dimensionTagList.add(dimensionTagMap);
            }
        });
        if (CollectionUtils.isNotEmpty(dimensionTagList)) {
            dimensionTagMapRepository.saveOrUpdateBatch(dimensionTagList);
        }
    }
}
