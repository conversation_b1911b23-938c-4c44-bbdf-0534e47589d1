package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Schema(name = "岗位可见人员列表")
public class PosPermissionListReq {

    @Schema(description = "继任id")
    @NotNull(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String posId;

    @Schema(description = "部门ids")
    private List<String> deptIds;

    @Schema(description = "搜索关键字")
    private String keyword;
}
