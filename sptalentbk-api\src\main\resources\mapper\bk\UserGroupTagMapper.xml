<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.usergroup.mapper.UserGroupTagMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.usergroup.entity.UserGroupTag">
    <!--@mbg.generated-->
    <!--@Table bk_user_group_tag-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="CHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="tag_id" jdbcType="BIGINT" property="tagId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, group_id,
    tag_id, group_name
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update bk_user_group_tag
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="group_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.groupId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="tag_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.tagId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="group_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.groupName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update bk_user_group_tag
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deleted != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateUserId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createUserId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="group_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.groupId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.groupId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="tag_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tagId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tagId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="group_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.groupName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.groupName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into bk_user_group_tag
    (id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, group_id,
      tag_id, group_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=CHAR}, #{item.deleted,jdbcType=TINYINT},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.createUserId,jdbcType=CHAR}, #{item.groupId,jdbcType=BIGINT}, #{item.tagId,jdbcType=BIGINT},
        #{item.groupName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.bk.core.usergroup.entity.UserGroupTag">
    <!--@mbg.generated-->
    insert into bk_user_group_tag
    (id, org_id, deleted, create_time, update_user_id, update_time, create_user_id, group_id,
      tag_id, group_name)
    values
    (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=CHAR}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updateUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=CHAR},
      #{groupId,jdbcType=BIGINT}, #{tagId,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR}
      )
    on duplicate key update
    id = #{id,jdbcType=BIGINT},
    org_id = #{orgId,jdbcType=CHAR},
    deleted = #{deleted,jdbcType=TINYINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_user_id = #{updateUserId,jdbcType=CHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    create_user_id = #{createUserId,jdbcType=CHAR},
    group_id = #{groupId,jdbcType=BIGINT},
    tag_id = #{tagId,jdbcType=BIGINT},
    group_name = #{groupName,jdbcType=VARCHAR}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.bk.core.usergroup.entity.UserGroupTag">
    <!--@mbg.generated-->
    insert into bk_user_group_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=CHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=CHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <delete id="deleteByGroupId">
        delete from bk_user_group_tag where org_id = #{orgId} and group_id = #{groupId}
    </delete>
</mapper>
