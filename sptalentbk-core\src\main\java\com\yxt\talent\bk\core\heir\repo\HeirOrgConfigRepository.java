package com.yxt.talent.bk.core.heir.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.core.heir.entity.HeirOrgConfigEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirOrgConfigMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * HeirOrgConfigRepository
 *
 * <AUTHOR> harleyge
 * @Date 19/9/24 3:34 pm
 */
@Slf4j
@Repository
@AllArgsConstructor
public class HeirOrgConfigRepository {
    private final HeirOrgConfigMapper heirOrgConfigMapper;

    public HeirOrgConfigEntity getByOrgId(String orgId) {
        return heirOrgConfigMapper.selectOne(
                new LambdaQueryWrapper<HeirOrgConfigEntity>().eq(HeirOrgConfigEntity::getOrgId, orgId)
                        .eq(HeirOrgConfigEntity::getDeleted, YesOrNo.NO.getValue()), false);
    }

    public void updateShowFlag(HeirOrgConfigEntity config) {
        heirOrgConfigMapper.updateShowFlag(config);
    }

    public void insert(HeirOrgConfigEntity entity) {
        heirOrgConfigMapper.insert(entity);
    }
}
