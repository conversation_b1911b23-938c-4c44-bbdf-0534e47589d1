package com.yxt.talent.bk.svc.search.bean;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(name = "根据标签id查询自建标签用户")
public class TagValueBean4Search {

    @Schema(description = "标签名称 支持模糊搜索")
    private String keyword;

    @Schema(description = "标签id 【必填】")
    @NotBlank(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    private String tagId;

    @Schema(description = "标签值id")
    private String tagValueId;
}
