package com.yxt.talent.bk.svc.pool.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(name = "培养发展列表")
public class PoolProject4Get {

    @Schema(name = "项目id")
    private String id;

    @Schema(name = "项目名称")
    private String projectName;

    @Schema(name = "开始时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date startTime;

    @Schema(name = "结束时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date endTime;

    @Schema(name = "项目负责人")
    private String inCharge;

    @Schema(name = "状态(0-未配置, 1-待发布, 2-已发布,3-结束)")
    private Integer publishStatus;

    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date createTime;
}
