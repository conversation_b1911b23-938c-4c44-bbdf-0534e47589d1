package com.yxt.talent.bk.svc.pool;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.EntityUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.DlcComponent;
import com.yxt.export.ExcelUtil;
import com.yxt.export.I18nComponent;
import com.yxt.export.ImportResult;
import com.yxt.export.OutputStrategy;
import com.yxt.idworker.YxtIdWorker;
import com.yxt.spevalfacade.bean.evaluation.BindEvaluaFacade;
import com.yxt.spevalfacade.bean.evaluation.Evaluation4User;
import com.yxt.spevalfacade.bean.evaluation.Evaluation4UserResultl;
import com.yxt.spevalfacade.service.SpEvalApiFacade;
import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.spsdk.common.bean.ExportParam;
import com.yxt.spsdk.common.utils.YxtExportUtils;
import com.yxt.spsdk.export.bean.DownTaskContext;
import com.yxt.spsdk.udpbase.QueryUdpUtils;
import com.yxt.spsdk.udpbase.bean.UdpUserBriefBean;
import com.yxt.talent.bk.common.bean.UserBasicBean;
import com.yxt.talent.bk.common.bean.udp.DeptConditionBean;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.common.constants.EsKeySearchConstant;
import com.yxt.talent.bk.common.constants.ExportConstants;
import com.yxt.talent.bk.common.constants.ModuleConstants;
import com.yxt.talent.bk.common.constants.TalentBkAuthCodes;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRedisKeys;
import com.yxt.talent.bk.common.constants.UserSearchConstants;
import com.yxt.talent.bk.common.es.ESQueryConveter;
import com.yxt.talent.bk.common.imports.ImportRequestBean;
import com.yxt.talent.bk.common.utils.DateUtils;
import com.yxt.talent.bk.common.utils.ExcelListener;
import com.yxt.talent.bk.common.utils.FillBeanUtil;
import com.yxt.talent.bk.common.utils.TalentbkUtil;
import com.yxt.talent.bk.core.pool.bean.PoolSimpleBean;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Export;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Import;
import com.yxt.talent.bk.core.pool.bean.PoolUser4List;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Param;
import com.yxt.talent.bk.core.pool.bean.PoolUserExperienceInfo4List;
import com.yxt.talent.bk.core.pool.bean.ReadinessImport;
import com.yxt.talent.bk.core.pool.bean.SearchDeptInfo;
import com.yxt.talent.bk.core.pool.bean.UserPoolInfoBean;
import com.yxt.talent.bk.core.pool.entity.Pool;
import com.yxt.talent.bk.core.pool.entity.PoolReadiness;
import com.yxt.talent.bk.core.pool.entity.PoolUser;
import com.yxt.talent.bk.core.pool.repo.PoolReadinessRepository;
import com.yxt.talent.bk.core.pool.repo.PoolRepository;
import com.yxt.talent.bk.core.pool.repo.PoolUserRepository;
import com.yxt.talent.bk.core.udp.bean.UdpUserNameBean;
import com.yxt.talent.bk.core.udp.entity.UdpLiteUser;
import com.yxt.talent.bk.core.udp.repo.UdpLiteUserRepository;
import com.yxt.talent.bk.svc.common.CommonService;
import com.yxt.talent.bk.svc.pool.bean.PoolUser4Add;
import com.yxt.talent.bk.svc.pool.bean.PoolUser4Update;
import com.yxt.talent.bk.svc.pool.bean.PoolUserAdd4Log;
import com.yxt.talent.bk.svc.pool.bean.PoolUserExperienceInfoBean;
import com.yxt.talent.bk.svc.pool.bean.readiness.PoolReadiness4List;
import com.yxt.talent.bk.svc.search.bean.UserSearchListBean;
import com.yxt.talent.bk.svc.strategy.PoolReadiness4ErrorStrategy;
import com.yxt.talent.bk.svc.strategy.PoolReadinessTempStrategry;
import com.yxt.talent.bk.svc.strategy.PoolUser4ErrorStrategy;
import com.yxt.talent.bk.svc.udp.UserAuthService;
import com.yxt.talent.bk.svc.udp.bean.UserAuthBean;
import com.yxt.talentbkfacade.bean.UserPoolProjectReq;
import com.yxt.talentbkfacade.bean.UserPoolProjectResp;
import com.yxt.udpfacade.service.UdpFacade;
import jakarta.validation.constraints.NotBlank;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilterBuilder;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SourceFilter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 人才池人员管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Service
@AllArgsConstructor
@Slf4j
public class PoolUserService {

    private static final String POOL_USER_SHEET_NAME = "sheet1";
    private final PoolUserRepository poolUserRepository;
    private final PoolRepository poolRepository;
    private final UdpLiteUserRepository udpLiteUserRepository;
    private final PoolReadinessService poolReadinessService;
    private final ElasticsearchRestTemplate elasticsearchRestTemplate;
    private final ILock lockService;
    private final CommonService commonService;
    private final I18nComponent i18nComponent;
    private final DlcComponent dlcComponent;
    private final PoolUser4ErrorStrategy poolUser4ErrorStrategy;
    private final PoolReadinessTempStrategry poolReadinessTempStrategry;
    private final PoolReadiness4ErrorStrategy poolReadiness4ErrorStrategy;
    private final PoolReadinessRepository poolReadinessRepository;
    private final PoolService poolService;
    private final SpEvalApiFacade spEvalApiFacade;
    private final UserAuthService userAuthService;
    private final UdpFacade udpFacade;
    private final ESQueryConveter esQueryConveter;

    public PagingList<PoolUser4List> page(PageRequest pageRequest, PoolUser4Param param) {
        String orgId = param.getOrgId();
        StopWatch watch = new StopWatch();
        param.setDeptIdCon(DeptConditionBean.createBy(param.getSearchDeptInfoList(),
                SearchDeptInfo::getDeptId, SearchDeptInfo::getIncludeAll));
        IPage<PoolUser4List> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        watch.start("人才池人员查询");
        // 查看在池人员
        page = poolUserRepository.page(page, param);
        watch.stop();
        if (Objects.nonNull(page) && CollectionUtils.isNotEmpty(page.getRecords())) {
            List<PoolUser4List> result = page.getRecords();
            watch.start("数据组装");
            List<UserSearchListBean> userSearchListBeans = this.common4ExportAndPage(orgId, result, param);
            watch.stop();
            watch.start("es查询标签字段");
            // 拼接es标签展示你字段
            this.poolUser4CreateFormatFromEs(userSearchListBeans, result);
            watch.stop();
        }
        log.debug("共计时" + watch.getTotalTimeSeconds());
        log.debug("慢接口666+" + watch.toString());
        return BeanCopierUtil.toPagingList(page);
    }

    public PagingList<PoolUser4List> page4Eval(PageRequest pageRequest, PoolUser4Param param) {
        String orgId = param.getOrgId();
        param.setDeptIdCon(DeptConditionBean.createBy(param.getSearchDeptInfoList(),
                SearchDeptInfo::getDeptId, SearchDeptInfo::getIncludeAll));
        IPage<PoolUser4List> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        // 查看在池人员
        page = poolUserRepository.page(page, param);
        if (Objects.nonNull(page) && CollectionUtils.isNotEmpty(page.getRecords())) {
            List<PoolUser4List> result = page.getRecords();
            // 调用公共代码
            List<UserSearchListBean> userSearchListBeans = this.common4ExportAndPage(orgId, result, param);
            //判断人员是否在测评当中
            // 拼接es标签展示你字段
            this.poolUser4CreateFormatFromEs(userSearchListBeans, result);
        }
        return BeanCopierUtil.toPagingList(page);
    }

    /**
     * 查找对应测评下的人员id
     */
    private List<String> evalExistUser(String orgId, String evalId) {
        if (StringUtils.isBlank(evalId)) {
            return new ArrayList<>(0);
        }
        // 查询测评对应人员
        BindEvaluaFacade search = new BindEvaluaFacade();
        search.setOrgId(orgId);
        search.setEvaluationId(evalId);
        Evaluation4UserResultl evaluation4UserResultl = spEvalApiFacade.getEvalustionUserList(search);
        if (Objects.isNull(evaluation4UserResultl) || CollectionUtils.isEmpty(evaluation4UserResultl.getUserList())) {
            return new ArrayList<>(0);
        }
        return evaluation4UserResultl.getUserList().stream().map(Evaluation4User::getUserId).distinct()
                .collect(Collectors.toList());
    }

    /**
     * 分页和导出list部分代码复用
     * @param orgId, result, param
     * @return java.util.List<com.yxt.talent.bk.svc.search.bean.UserSearchListBean>
     * <AUTHOR>
     * @since 2022/9/29
     */
    private List<UserSearchListBean> common4ExportAndPage(String orgId, List<PoolUser4List> result,
            PoolUser4Param param) {
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>(0);
        }
        // 查询准备度 根据orgId 查詢总数
        List<PoolReadiness4List> poolReadinessList = poolReadinessService.findByOrgId(orgId, param.getUserId());
        boolean notEmpty = CollectionUtils.isNotEmpty(poolReadinessList);
        int size = poolReadinessList.size();
        boolean existEvalOrNot = StringUtils.isNotBlank(param.getEvalId());
        String evalId = param.getEvalId();
        // 查询测评对应人员
        BindEvaluaFacade search = new BindEvaluaFacade();
        search.setOrgId(orgId);
        search.setEvaluationId(evalId);
        List<String> evalUserIdList = evalExistUser(orgId, param.getEvalId());
        Set<Integer> orderIndexSet = result.stream().filter(poolUser4List -> Objects.nonNull(poolUser4List.getOrderIndex()))
                .map(PoolUser4List::getOrderIndex).collect(Collectors.toSet());
        Map<Integer, String> readinessValueNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orderIndexSet)) {
            readinessValueNameMap = poolReadinessRepository.findMapByOrderIndex(orgId, orderIndexSet);
        }
        for (int i=0; i< result.size(); i++) {
            PoolUser4List poolUser4List = result.get(i);
            if (existEvalOrNot) {
                // 存在测评判断
                poolUser4List.setExistEval(this.getExistEvalOrNot(poolUser4List.getUserId(), evalUserIdList));
            }
            // 处理部门名称为短mingc
            if (StringUtils.isNotEmpty(poolUser4List.getDeptName())) {
                poolUser4List.setDeptName(this.getShortDeptName(poolUser4List.getDeptName()));
            }
            // 准备度赋值
            if (notEmpty) {
                poolUser4List.setReadiness(size);
            }
            // 准备度值
            String readinessName = readinessValueNameMap.get(poolUser4List.getOrderIndex());
            poolUser4List.setReadinessValueStr(StringUtils.isNotBlank(readinessName) ? readinessName : "");
        }
        // 获取userid 用来查询标签
        Set<String> userSet = result.stream().map(PoolUser4List::getUserId).collect(Collectors.toSet());
        // 查询es
        return this.generateListSearchQuery(userSet);
    }

    public Map<String, String> export(PoolUser4Param param, UserCacheDetail userDetail) {
        String orgId = param.getOrgId();
        // 查看在池人员
        List<PoolUser4List> result = poolUserRepository.page(param);
        List<UserSearchListBean> userSearchListBeans = this.common4ExportAndPage(orgId, result, param);
        // 拼接es标签展示你字段
        this.poolUser4CreateFormatFromEs(userSearchListBeans, result);
        List<PoolUser4Export> poolUser4Exports = BeanCopierUtil
                .convertList(result, PoolUser4List.class, PoolUser4Export.class);
        String lockKey = String
                .format(TalentBkRedisKeys.TALENTBK_POOL_USER_INFO_EXPORT_CACHE_KEY, orgId, param.getUserId());
        String filePath;
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                List<Object> list = new ArrayList<>();
                list.add(poolUser4Exports);
                String exportName = TalentbkUtil.getMessage(ExportConstants.POOL_USER_EXPORT, userDetail.getLocale());
                String fileName = TalentbkUtil.getMessage(ExportConstants.POOL_USER_EXPORT, userDetail.getLocale())
                        + "_" + System.currentTimeMillis() + ExportConstants.FILE_SUFFIX_XLSX + TalentBkConstants.FILE_ORID;

                ExportParam exportParam = new ExportParam();
                exportParam.setModuleCode(ModuleConstants.MODULE_CODE);
                exportParam.setFileName(fileName);
                exportParam.setName(exportName);
                filePath = YxtExportUtils.exportList(userDetail, exportParam,
                        PoolUser4Export.class, poolUser4Exports);
                log.debug("poolUserInfoExportStrategy-path={}", filePath);
            } catch (Exception e) {
                log.error("人才池详情导出异常", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        Map<String, String> map = new HashMap<>(1);
        map.put(ExportConstants.EXPORT_URL_KEY, filePath);
        return map;
    }

    /**
     * 导出策略
     */
    private OutputStrategy getOutputStrategy(String templateName, String templatePath) {
        return new OutputStrategy() {
            @Override
            public String write(String path, String fileName, Object data) throws IOException {
                String filePath = path + fileName;
                ExcelUtil.exportWithTemplate(FillBeanUtil.generationFillBenList(data), filePath, templatePath);
                return fileName;
            }

            @Override
            public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
                return DownInfo4Add.builder().orgId(userCache.getOrgId()).fullname(userCache.getFullname())
                        .userId(userCache.getUserId()).sourceCode(ModuleConstants.SOURCE_CODE)
                        .appCode(ModuleConstants.APP_CODE).moduleCode(ModuleConstants.MODULE_CODE).fileName(fileName)
                        .name(templateName).build();
            }
        };
    }

    /**
     * 判断人才池人员是否存在测评
     * @param userId
     * @return  1:存在 0：不存在
     * <AUTHOR>
     * @since 2022/9/26
     */
    private Integer getExistEvalOrNot(@NotBlank(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) String userId,
            List<String> evalUserIdList) {
        // 查询测评对应的人员
        if (CollectionUtils.isNotEmpty(evalUserIdList) && evalUserIdList.contains(userId)) {
            return 1;
        }
        return 0;
    }

    /**
     * 获取部门短名称
     *
     * @param deptPathName
     * @return
     */
    private String getShortDeptName(
            @NotBlank(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE) String deptPathName) {
        String[] split = deptPathName.split(UserSearchConstants.DEPT_PATH_SPLIT_STR);
        if (split.length > 0) {
            return split[split.length - 1];
        }
        return deptPathName;
    }

    /**
     * 拼接es标签展示你字段
     *
     * @param userSearchListBeans, targetList
     * @return void
     * <AUTHOR>
     * @since 2022/9/13
     */
    private void poolUser4CreateFormatFromEs(List<UserSearchListBean> userSearchListBeans,
            List<PoolUser4List> targetList) {
        if (CollectionUtils.isEmpty(userSearchListBeans)) {
            return;
        }
        userSearchListBeans.forEach(userSearch -> {
            String userId = userSearch.getUserId();
            for (PoolUser4List poolUser : targetList) {
                // 拼接【司龄、绩效、能力、潜力】标签 供页面显示
                if (userId.contains(poolUser.getUserId())) {
                    // 处理司龄为  2.9 ,3.6 年
                    if (StringUtils.isNotEmpty(userSearch.getEntryDate())) {
                        Date date = DateUtil.formatDate(userSearch.getEntryDate());
                        String entryYear = DateUtils.getEntryYear(date);
                        // 司龄
                        poolUser.setEntryDate(entryYear);
                    }
                    // 绩效
                    poolUser.setPerformanceLevel(userSearch.getPerformanceLevel());
                    // 能力
                    poolUser.setAbilityLevel(userSearch.getAbilityLevel());
                    // 潜力
                    poolUser.setPotentialLevel(userSearch.getPotentialLevel());
                }
            }
        });
    }

    /**
     * 根据具体userId查询es拼接查询条件
     *
     * @param userIdSet
     * @return void
     * <AUTHOR>
     * @since 2022/9/13
     */
    private BoolQueryBuilder searchEsByConditions(Set<String> userIdSet) {
        List<BoolQueryBuilder> bqbList = Lists.newArrayListWithCapacity(userIdSet.size());
        this.userIdQueryBuilder(userIdSet, bqbList);
        // 将bqbList中条件加入must查询组 相当于mysql用 and 链接起来
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        bqbList.forEach(bqd -> mainQueryBuilder.must().add(bqd));
        return mainQueryBuilder;
    }

    /**
     * 查询es指定字段
     *
     * @param userIdSet
     * @return
     */
    private List<UserSearchListBean> generateListSearchQuery(Set<String> userIdSet) {
        List<UserSearchListBean> userSearchListBeans = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(userIdSet)) {
            QueryBuilder queryBuilder = this.searchEsByConditions(userIdSet);
            //查询指定字段
            SourceFilter sourceFilter = new FetchSourceFilterBuilder().withIncludes(EsKeySearchConstant.POOL_USER_INFO)
                    .build();
            //设置查询条件
            NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
            nativeSearchQueryBuilder.withQuery(queryBuilder);
            nativeSearchQueryBuilder.withSourceFilter(sourceFilter);
            try {
                NativeSearchQuery query = nativeSearchQueryBuilder.build();
                log.debug("LOG11240:{}{}", System.lineSeparator(), esQueryConveter.extractJson(query));
                userSearchListBeans = elasticsearchRestTemplate
                        .queryForList(query, UserSearchListBean.class);
            } catch (ElasticsearchStatusException e) {
                String errorMes = "Elasticsearch exception [type=index_not_found_exception, reason=no such index]";
                if (errorMes.contains(e.getMessage())) {
                    log.error(errorMes);
                    return userSearchListBeans;
                }
                throw e;
            }
        }
        return userSearchListBeans;
    }

    /**
     * userId 精确搜索条件
     */
    private void userIdQueryBuilder(Set<String> userIdSet, List<BoolQueryBuilder> bqbList) {
        if (CollectionUtils.isNotEmpty(userIdSet)) {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            for (String userId : userIdSet) {
                // key为userId 的精确搜索条件 matchQuery
                queryBuilder.should(QueryBuilders.matchQuery(EsKeySearchConstant.USER_ID, userId));
            }
            bqbList.add(queryBuilder);
        }
    }

    /**
     * deptId 精确搜索条件
     */
    private void deptIdQueryBuilder(List<String> deptIdList, List<BoolQueryBuilder> bqbList) {
        if (CollectionUtils.isNotEmpty(deptIdList)) {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            for (String deptId : deptIdList) {
                // key为deptId 的精确搜索条件 matchQuery
                queryBuilder.should(QueryBuilders.matchQuery(EsKeySearchConstant.DEPARTMENT_ID, deptId));
            }
            bqbList.add(queryBuilder);
        }
    }

    /**
     * keyword 模糊搜索条件
     */
    private void keywordQueryBuilder(String keyword, List<BoolQueryBuilder> bqbList) {
        if (StringUtils.isNotBlank(keyword)) {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            for (UserSearchConstants.KeyWordPoolEnum kwPool : UserSearchConstants.KeyWordPoolEnum.values()) {
                // key为【cnname；positionName；userNo】模糊搜索条件 matchQuery
                queryBuilder.should(QueryBuilders.wildcardQuery(kwPool.getValue(), "*" + keyword + "*"));
            }
            bqbList.add(queryBuilder);
        }
    }

    @DbHintMaster
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void save(String orgId, String operator, PoolUser4Add param) {
        if (Objects.isNull(param)) {
            return;
        }
        Pool pool = poolRepository.getById(param.getPoolId(), orgId);
        if (pool == null) {
            return;
        }
        // poolId、userIdList controller 层校验了不为空
        String poolId = param.getPoolId();
        // param.getUserIdList() 的最带size为999  所以可以使用 in
        List<String> userIdList = param.getUserIdList();
        String lockKey = String.format(TalentBkRedisKeys.TALENTBK_POOL_ADD_USER_CACHE_KEY, orgId, poolId);
        // 防止重读添加人
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                // 查询已存在的在池人员
                List<String> existUserIds = poolUserRepository.findUserIdByPoolIdAndUserIds(orgId, poolId, userIdList);
                // 去掉已存在人员
                userIdList.removeAll(existUserIds);
                if (CollectionUtils.isNotEmpty(userIdList)) {
                    // 去掉禁用人员
                    List<UdpLiteUser> udpUsers = udpLiteUserRepository.getUdpUserInfo(orgId, userIdList);
                    List<String> disableUserIds = udpUsers.stream().filter(u -> u.getStatus() == 0).map(UdpLiteUser::getId)
                        .collect(Collectors.toList());
                    userIdList.removeAll(disableUserIds);
                }
                // 新增人员保存处理
                if (CollectionUtils.isNotEmpty(userIdList)) {
                    int size = userIdList.size();
                    Date now = DateUtil.currentTime();
                    List<PoolUser> saveList = Lists.newArrayListWithCapacity(size);
                    userIdList.forEach(data -> {
                        PoolUser save = new PoolUser();
                        save.setId(ApiUtil.getUuid());
                        save.setOrgId(orgId);
                        save.setPoolId(poolId);
                        save.setUserId(data);
                        save.setRecently(YesOrNo.YES.getValue());
                        save.setCreateTime(now);
                        save.setUpdateTime(now);
                        save.setUpdateUserId(operator);
                        save.setCreateUserId(operator);
                        saveList.add(save);
                    });
                    updateRecent(saveList);
                    poolUserRepository.saveBatch(saveList);
                    PoolUserAdd4Log addLog = new PoolUserAdd4Log();
                    addLog.setPoolId(poolId);
                    addLog.setPoolName(pool.getPoolName());
                    addLog.setAddUserNames(udpLiteUserRepository.userNames4Log(orgId, userIdList));
                    AuditLogHooker.setLogAfterData(addLog);
                }
            } catch (Exception e) {
                log.error("人才池：添加人员异常", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
    }

    @DbHintMaster
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void updateBatch(String orgId, String userId, PoolUser4Update update) {
        // update getPoolId  getUserIdList 均已判空
        String poolId = update.getPoolUser().getPoolId();
        List<String> userIdList = update.getPoolUser().getUserIdList();
        Integer updateType = update.getUpdateType();
        // 查询已存在人员
        List<PoolUser> poolUserList = poolUserRepository.findByPoolIdAndUserIds(orgId, poolId, userIdList);
        if (CollectionUtils.isNotEmpty(poolUserList)) {
            Date now = DateUtil.currentTime();
            PoolReadiness poolReadiness = null;
            if (StringUtils.isNotBlank(update.getReadinessId())) {
                poolReadiness = poolReadinessRepository.getById(orgId, update.getReadinessId());
            }
            boolean existOrNot = Objects.isNull(poolReadiness);
            poolUserList.forEach(poolUser -> {
                // 更新准备度
                if (updateType != 1) {
                    // 准备度是否存在校验
                    if (existOrNot) {
                        throw new ApiException(BkApiErrorKeys.POOL_READINESS_EXIST_CONFIRM);
                    }
                    poolUser.setReadinessId(update.getReadinessId());
                } else {
                    if (Objects.isNull(update.getEventPoolOut())) {
                        throw new ApiException(BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE);
                    }
                    // 出池
                    poolUser.setEventPoolOut(update.getEventPoolOut());
                    poolUser.setOutTime(now); // 出池时间
                    poolUser.setRemark(update.getRemark());
                }
                poolUser.setUpdateTime(now);
                poolUser.setUpdateUserId(userId);
            });
            poolUserRepository.saveOrUpdateBatch(poolUserList);
        }
    }

    /**
     * 人才池，人员导入
     *
     * @param bean
     * @param file
     * @param poolId
     * @return
     */
    @DbHintMaster
    public ImportResult importPoolUser(UserBasicBean userBasic, ImportRequestBean bean, MultipartFile file, String poolId) {
        String orgId = userBasic.getOrgId();
        String userId = userBasic.getUserId();
        String lockKey = String.format(TalentBkRedisKeys.TALENTBK_POOL_USER_IMPORT_CACHE_KEY, "importExcel", orgId);
        List<PoolUser4Import> addUserList = new ArrayList<>();
        List<PoolUser4Import> failedList = new ArrayList<>();
        String failedFilePath = "";
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                InputStream stream = getInputStream(bean, file);
                List<PoolUser4Import> imports = readImportUser(stream);

                if (CollectionUtils.isEmpty(imports)) {
                    return ImportResult.builder().totalCount(0).build();
                }
                validImportPoolUser(userBasic, failedList, imports, addUserList, poolId);
                if (CollectionUtils.isNotEmpty(addUserList)) {
                    savePoolUser(orgId, poolId, addUserList, userId);

                    PoolUserAdd4Log addLog = new PoolUserAdd4Log();
                    addLog.setPoolId(poolId);
                    addLog.setPoolName(poolRepository.getNameById(poolId));
                    addLog.setAddUserNames(udpLiteUserRepository.userNames4Log(orgId,
                            BeanCopierUtil.convertList(addUserList, PoolUser4Import::getId)));
                    AuditLogHooker.setLogAfterData(addLog);
                }
                // 导出错误数据
                failedFilePath = exportPoolUserFaildData(failedList);

                return ImportResult.builder().totalCount(imports.size()).failedCount(failedList.size())
                        .successCount(addUserList.size()).filePath(failedFilePath).build();
            } catch (ApiException e) {
                throw e;
            } catch (Exception e) {
                log.error("人才池人员导入异常， orgId={}, flipId:{}， errmgs:{}", orgId, poolId, e);
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        return ImportResult.builder().totalCount(0).build();
    }

    private String exportPoolUserFaildData(List<PoolUser4Import> faliedList) {
        if (CollectionUtils.isEmpty(faliedList)) {
            return StringUtils.EMPTY;
        }

        String fileName =
                i18nComponent.getI18nValue(ExportConstants.POOL_USER_FAIL_EXPORT_FILE_NAME) + System.currentTimeMillis()
                        + ExportConstants.FILE_SUFFIX_XLSX;
        return dlcComponent.upload2TemporaryDisk(fileName, faliedList, poolUser4ErrorStrategy);
    }

    private void savePoolUser(String orgId, String poolId, List<PoolUser4Import> addUserList, String userId) {
        List<PoolUser> addUser = new ArrayList<>();
        for (PoolUser4Import poolUser4Import : addUserList) {
            PoolUser user = new PoolUser();
            user.setId(ApiUtil.getUuid());
            user.setOrgId(orgId);
            user.setPoolId(poolId);
            user.setUserId(poolUser4Import.getId());
            user.setRecently(1);
            EntityUtil.setCreateInfo(userId, user);
            addUser.add(user);
        }
        updateRecent(addUser);
        poolUserRepository.saveBatch(addUser);
        //
        poolService.updateSaturabilityById(orgId, poolId);
    }

    /**
     * 更新历史入池数据的recent字段为0
     * @param userList
     */
    private void updateRecent(List<PoolUser> userList){
        if(CollectionUtils.isEmpty(userList)){
            return;
        }

        String orgId = userList.get(0).getOrgId();
        String poolId = userList.get(0).getPoolId();
        String currentUserId = userList.get(0).getCreateUserId();
        Date updateTime =userList.get(0).getCreateTime();

        List<List<PoolUser>> partition = Lists.partition(userList, 500);
        partition.forEach(subUserList -> {
            Set<String> userIds = subUserList.stream().map(PoolUser::getUserId).collect(Collectors.toSet());
            poolUserRepository.getBaseMapper().updateRecentlyBy(orgId,poolId,currentUserId,updateTime,userIds);
        });
    }



    private void validImportPoolUser(UserBasicBean userBasic, List<PoolUser4Import> failedList, List<PoolUser4Import> importUsers,
            List<PoolUser4Import> addUserList, String poolId) {
        String orgId = userBasic.getOrgId();
        //
        List<String> userNames = importUsers.stream().map(PoolUser4Import::getUserName).collect(Collectors.toList());
        List<String> resUsernames = userNames.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<PoolUser4Import> realUsers = poolUserRepository.findPoolUser4Import(resUsernames, orgId);
        List<PoolUser> poolUser = poolUserRepository.findPoolUser(orgId, poolId);
        List<String> existUserIds = poolUser.stream().map(PoolUser::getUserId).collect(Collectors.toList());
        Map<String, PoolUser4Import> userMap = StreamUtil.list2map(realUsers, PoolUser4Import::getUserName);
        // 权限内人员
        UserAuthBean authUser = userAuthService.verifyPermission(userBasic, TalentBkAuthCodes.POOL_NAV_CODE, TalentBkAuthCodes.POOL_DATA_PERMISSION_ADD,
                realUsers.stream().map(PoolUser4Import::getId).distinct().collect(Collectors.toList()));
        // 校验人员

        Set<String> userImport = new HashSet<>();
        for (PoolUser4Import importUser : importUsers) {
            String result = chkUserData(userBasic.getLocale(), importUser, userMap, existUserIds, authUser, userImport);
            PoolUser4Import poolUser4Import = userMap.get(importUser.getUserName());
            if (StringUtils.isEmpty(result)) {
                if (poolUser4Import != null) {
                    PoolUser4Import addUser = new PoolUser4Import();
                    addUser.setId(poolUser4Import.getId());
                    addUserList.add(addUser);
                }
            } else {
                PoolUser4Import failedUser = new PoolUser4Import();
                failedUser.setId(importUser.getId());
                failedUser.setErrMsg(result);
                failedUser.setFullName(importUser.getFullName());
                failedUser.setUserName(importUser.getUserName());
                failedList.add(failedUser);
            }
        }
    }

    private String chkUserData(String locateStr, PoolUser4Import importUser, Map<String, PoolUser4Import> userMap,
            List<String> existUserIds, UserAuthBean authUser, Set<String> userImport) {
        if (importUser.getUserName() == null) {
            return i18nComponent.getI18nValue(ExportConstants.POOL_USER_IMPORT_USERNAME_EMPTY, locateStr);
        }
        if (StringUtils.isBlank(importUser.getUserName().trim())) {
            return i18nComponent.getI18nValue(ExportConstants.POOL_USER_IMPORT_USERNAME_EMPTY, locateStr);
        }
        PoolUser4Import poolUser4Import = userMap.get(importUser.getUserName().trim());
        if (poolUser4Import == null) {
            return i18nComponent.getI18nValue(ExportConstants.POOL_USER_IMPORT_USERNAME_INVALID, locateStr);
        }
        if (poolUser4Import.getStatus() == 0) {
            return i18nComponent.getI18nValue(ExportConstants.POOL_USER_IMPORT_USERNAME_DISABLED, locateStr);
        }
        // 是否有权限点
        if (authUser.getAdmin() == 0 && !authUser.getUserIds().contains(poolUser4Import.getId())) {
            return i18nComponent.getI18nValue(ExportConstants.POOL_USER_IMPORT_USERNAME_NO_PERM, locateStr);
        }
        if (existUserIds.contains(poolUser4Import.getId())) {
            return i18nComponent.getI18nValue(ExportConstants.POOL_USER_IMPORT_USERNAME_EXIST, locateStr);
        }

        if (!userImport.contains(poolUser4Import.getId())) {
            userImport.add(poolUser4Import.getId());
        } else {
            return i18nComponent.getI18nValue(ExportConstants.POOL_USER_IMPORT_USERNAME_DUP, locateStr);
        }

        return "";
    }

    private List<PoolUser4Import> readImportUser(InputStream stream) {
        List<PoolUser4Import> list = new ArrayList<>();
        ExcelReader excelReader = EasyExcelFactory.read(stream).build();
        List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
        if (!sheets.get(0).getSheetName().equals(POOL_USER_SHEET_NAME)) {
            throw new ApiException(BkApiErrorKeys.EXCEL_SHEET_NAME_ERROR);
        }
        Map<String, ExcelListener> listenerMap = new HashMap<>(sheets.size());
        for (ReadSheet sheet : sheets) {
            String sheetName = sheet.getSheetName();
            if (StringUtils.isBlank(sheetName)) {
                continue;
            }

            ExcelListener listener = new ExcelListener();
            sheet.setCustomReadListenerList(Collections.singletonList(listener));
            sheet.setHeadRowNumber(2);
            listenerMap.put(sheetName, listener);
        }
        excelReader.read(sheets);
        listenerMap.forEach((sheetName, listener) -> {
            if (POOL_USER_SHEET_NAME.equals(sheetName)) {
                generateExcelList(listener, list);
            }
        });
        return list;
    }

    private void generateExcelList(ExcelListener listener, List<PoolUser4Import> users) {
        List<Map<Integer, String>> list = listener.getData();
        for (Map<Integer, String> map : list) {
            PoolUser4Import importData = new PoolUser4Import();
            int i = 0;
            importData.setFullName(map.get(i++));
            importData.setUserName(map.get(i));
            users.add(importData);
        }
    }

    private InputStream getInputStream(ImportRequestBean bean, MultipartFile file) throws IOException {
        InputStream stream2;
        if (Objects.nonNull(file)) {
            stream2 = file.getInputStream();
        } else {
            String downloadUrl = commonService
                    .getDownloadUrl(bean.getFileId(), BkApiErrorKeys.EXTRA_IMPORT_INVALID);
            stream2 = ExcelUtil.getRemoteInputStream(downloadUrl);
        }
        return stream2;
    }

    public Map<String, String> exportReadiness(String orgId, String poolId) {
        String lockKey = String.format(TalentBkRedisKeys.POOL_READINESS_IMPORT_TEMP, "readinessExport", orgId);
        String path = "";
        Map<String, String> map = new HashMap<>(1);
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                // 获取准备度
                String fileName =
                        i18nComponent.getI18nValue(ExportConstants.POOL_READONESS_IMPORT_TEMP_NAME) + DateUtils
                                .dateToString(new Date(), DateUtils.FORMATTER_DATE_TIME_SHORT)
                                + ExportConstants.FILE_SUFFIX_XLSX;
                List<PoolSimpleBean> list = new ArrayList<>();
                PoolSimpleBean poolSimpleBean = new PoolSimpleBean();
                poolSimpleBean.setOrgId(orgId);
                list.add(poolSimpleBean);
                path = dlcComponent.upload2TemporaryDisk(fileName, list, poolReadinessTempStrategry);
            } catch (ApiException e) {
                throw e;
            } catch (Exception e) {
                log.error("人才池准备度模板下载异常， orgId:{}, poolId:{}, errMsg:{}", orgId, poolId, e.getMessage());
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        map.put(ExportConstants.EXPORT_URL_KEY, path);
        return map;
    }

    public String templateUserImport(UserCacheDetail userCache) {
        // 获取准备度
        String exportName = TalentbkUtil.getMessage(ExportConstants.POOL_USER_IMPORT_TEMP_NAME, userCache.getLocale());
        String fileName = exportName + StringPool.UNDERSCORE + YxtIdWorker.getId() + ExportConstants.FILE_SUFFIX_XLSX;
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(PoolUser4ErrorStrategy.TEMP_VAR_POOLUSERIMPORT,
                "apis.talentbk.pool.user.import.desc");
        headerMap.put(PoolUser4ErrorStrategy.TEMP_VAR_POOLUSERIMPORTRULE1,
                "apis.talentbk.pool.user.import.rule1");
        headerMap.put(PoolUser4ErrorStrategy.TEMP_VAR_POOLUSERFULLNAME,
                "apis.talentbk.pool.user.info.export.tem.head.fullname");
        headerMap.put(PoolUser4ErrorStrategy.TEMP_VAR_POOLUSERNAME,
                "apis.talentbk.pool.user.info.export.tem.head.username");
        return YxtExportUtils.export2TmpDisk(userCache, fileName, (DownTaskContext taskCtx, String filePath) -> {
            try (OutputStream output = new FileOutputStream(filePath)) {
                YxtExportUtils.outputImportTemplate(userCache, ExportConstants.POOL_USER_IMPORT_TEMP_FILE,
                        output, headerMap);
            } catch (Exception e) {
                log.error("templateUserImport userId {}", userCache.getUserId(), e);
                throw new ApiException(BkApiErrorKeys.UNKNOWN_ERROR);
            }
        });
    }

    @DbHintMaster
    public ImportResult importreadiness(UserBasicBean userBasic, String poolId, ImportRequestBean bean,
            MultipartFile file) {
        String orgId = userBasic.getOrgId();
        String userId = userBasic.getUserId();
        String lockKey = String.format(TalentBkRedisKeys.CACHE_KEY_POOL_READINESS_IMPORT, "importExcel", orgId);
        List<ReadinessImport> addList = new ArrayList<>();
        List<ReadinessImport> failedList = new ArrayList<>();
        String failedFilePath = "";
        if (lockService.tryLock(lockKey, TalentBkConstants.MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                InputStream stream = getInputStream(bean, file);
                List<ReadinessImport> imports = readImportReadiness(stream);
                if (CollectionUtils.isEmpty(imports)) {
                    return ImportResult.builder().totalCount(0).build();
                }
                // 校验
                validImportPoolReadiness(userBasic, addList, failedList, imports, poolId);
                // 保存数据
                if (CollectionUtils.isNotEmpty(addList)) {
                    saveReadiness(orgId, poolId, addList, userId);
                }
                // 错误导出
                failedFilePath = exportReadinessData(failedList);

                return ImportResult.builder().totalCount(imports.size()).failedCount(failedList.size())
                        .successCount(addList.size()).filePath(failedFilePath).build();

            } catch (ApiException e) {
                throw e;
            } catch (Exception e) {
                log.error("准备度导入异常， orgId:{}, poolId，errmsg:{}", orgId, poolId, e);
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        return ImportResult.builder().totalCount(0).filePath(StringUtils.EMPTY).build();
    }

    private void saveReadiness(String orgId, String poolId, List<ReadinessImport> addList, String userId) {
        List<PoolUser> resList = new ArrayList<>();
        List<String> userIds = addList.stream().map(ReadinessImport::getUserId).collect(Collectors.toList());
        List<PoolUser> poolUsers = poolUserRepository.findByUserIds(orgId, poolId, userIds);
        Map<String, ReadinessImport> userMap = StreamUtil.list2map(addList, ReadinessImport::getUserId);
        for (PoolUser poolUser : poolUsers) {
            ReadinessImport readinessImport = userMap.get(poolUser.getUserId());
            if (readinessImport != null) {
                poolUser.setReadinessId(readinessImport.getId());
                EntityUtil.setUpdatedInfo(userId, poolUser);
                resList.add(poolUser);

            }
        }
        if (CollectionUtils.isNotEmpty(resList)) {
            poolUserRepository.saveOrUpdateBatch(resList);
        }

    }

    private String exportReadinessData(List<ReadinessImport> failedList) {
        if (CollectionUtils.isEmpty(failedList)) {
            return StringUtils.EMPTY;
        }
        String fileName = i18nComponent.getI18nValue(ExportConstants.POOL_READONESS_IMPORT_ERROR_NAME) + System
                .currentTimeMillis() + ExportConstants.FILE_SUFFIX_XLSX;
        return dlcComponent.upload2TemporaryDisk(fileName, failedList, poolReadiness4ErrorStrategy);
    }

    private void validImportPoolReadiness(UserBasicBean userBasic, List<ReadinessImport> addList, List<ReadinessImport> failedList,
            List<ReadinessImport> imports, String poolId) {
        String orgId = userBasic.getOrgId();
        // 获取所有准备度
        List<PoolReadiness> redinesses = poolReadinessRepository.list(orgId);
        // 获取导入excel中的账号
        List<String> usernames = imports.stream().map(ReadinessImport::getUserName).collect(Collectors.toList());
        List<String> resUserName = usernames.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<PoolUser4Import> realUsers = poolUserRepository.findPoolUser4Import(resUserName, orgId);
        Map<String, PoolUser4Import> userMap = StreamUtil.list2map(realUsers, PoolUser4Import::getUserName);
        // 获取人才池中的人员
        List<String> allPoolUsers = poolUserRepository.findAllPoolUser(orgId, poolId);

        for (ReadinessImport anImport : imports) {
            String msg = chkReadiness(userBasic.getLocale(), anImport, userMap, redinesses, allPoolUsers);
            if (StringUtils.isEmpty(msg)) {
                ReadinessImport readinessImport = new ReadinessImport();
                readinessImport.setId(anImport.getId());
                readinessImport.setUserId(anImport.getUserId());
                addList.add(readinessImport);
            } else {
                ReadinessImport readinessImport = new ReadinessImport();
                BeanCopierUtil.copy(anImport, readinessImport);
                readinessImport.setErrMsg(msg);
                failedList.add(readinessImport);
            }
        }
    }

    private String chkReadiness(String localeStr, ReadinessImport anImport, Map<String, PoolUser4Import> userMap,
            List<PoolReadiness> redinesses, List<String> allPoolUsers) {
        if (StringUtils.isEmpty(anImport.getUserName())) {
            return TalentbkUtil.getMessage(ExportConstants.POOL_USER_IMPORT_USERNAME_EMPTY, localeStr);
        }
        if (StringUtils.isEmpty(anImport.getUserName().trim())) {
            return TalentbkUtil.getMessage(ExportConstants.POOL_USER_IMPORT_USERNAME_EMPTY, localeStr);
        }
        PoolUser4Import poolUser4Import = userMap.get(anImport.getUserName());
        if (poolUser4Import == null) {
            return TalentbkUtil.getMessage(ExportConstants.POOL_USER_IMPORT_USERNAME_INVALID, localeStr);
        }
        if (poolUser4Import.getStatus() == 0) {
            //账号被禁用
            return TalentbkUtil.getMessage(ExportConstants.POOL_USER_IMPORT_USERNAME_DISABLED, localeStr);
        }
        if (!allPoolUsers.contains(poolUser4Import.getId())) {
            //账号不在人才池中
            return TalentbkUtil.getMessage(ExportConstants.POOL_READINESS_IMPORT_USERNAME_MISS, localeStr);
        }
        anImport.setUserId(poolUser4Import.getId());
        //准备度不能为空
        if (StringUtils.isEmpty(anImport.getReadinessName())) {
            return TalentbkUtil.getMessage(ExportConstants.POOL_READINESS_IMPORT_READINESS_EMPTY, localeStr);
        }
        if (StringUtils.isEmpty(anImport.getReadinessName().trim())) {
            return TalentbkUtil.getMessage(ExportConstants.POOL_READINESS_IMPORT_READINESS_EMPTY, localeStr);
        }
        String result = chkReadinessName(anImport.getReadinessName(), redinesses);
        if (StringUtils.isEmpty(result)) {
            //准备度不存在
            return TalentbkUtil.getMessage(ExportConstants.POOL_READINESS_IMPORT_READINESS_MISS, localeStr);
        } else {
            anImport.setId(result);
        }
        return StringUtils.EMPTY;
    }

    private String chkReadinessName(String redinessName, List<PoolReadiness> redinesses) {
        for (PoolReadiness rediness : redinesses) {
            if (redinessName.equals(rediness.getReadinessName())) {
                return rediness.getId();
            }
        }
        return StringUtils.EMPTY;
    }

    private List<ReadinessImport> readImportReadiness(InputStream stream) {
        List<ReadinessImport> list = new ArrayList<>();
        ExcelReader excelReader = EasyExcelFactory.read(stream).build();
        List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
        ReadSheet sheet = sheets.get(0);
        ExcelListener listener = new ExcelListener();
        sheet.setCustomReadListenerList(Collections.singletonList(listener));
        sheet.setHeadRowNumber(2);
        excelReader.read(sheet);
        readReadinessExcelList(listener, list);
        return list;
    }

    private void readReadinessExcelList(ExcelListener listener, List<ReadinessImport> users) {
        List<Map<Integer, String>> list = listener.getData();
        for (Map<Integer, String> map : list) {
            ReadinessImport importData = new ReadinessImport();
            int i = 0;
            importData.setFullName(map.get(i++));
            importData.setUserName(map.get(i++));
            importData.setReadinessName(map.get(i));
            users.add(importData);
        }
    }


    /**
     * 获取用户储备经历
     * @param pageRequest
     * @param orgId
     * @param userId
     * @param statusList 出池状态
     * @return
     */
    public PagingList<PoolUserExperienceInfoBean> findPoolUserExperienceInfo(PageRequest pageRequest
            , String orgId, String userId,List<Integer> statusList ) {

        // 校验 防止前端传过多无效状态打到数据库
        if(CollectionUtils.isNotEmpty(statusList) && statusList.size() > 10){
            throw new ApiException("apis.talentbk.pool.poolUserExperienceInfo.eventPoolOutList.size");
        }

        Page<PoolUserExperienceInfo4List> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<PoolUserExperienceInfo4List> pageList = poolUserRepository.getBaseMapper()
                .findPoolUserExperienceByUserIds(page, orgId,userId,statusList,pageRequest.getDirection());

        PagingList<PoolUserExperienceInfoBean> pagingList = BeanCopierUtil.toPagingList(pageList
                ,PoolUserExperienceInfo4List.class,PoolUserExperienceInfoBean.class);

        if(CollectionUtils.isEmpty(pagingList.getDatas())){
            return pagingList;
        }

        //获取数据，用于设置首次入池时间
        List<String> poolIds = pagingList.getDatas().stream().map(PoolUserExperienceInfoBean::getPoolId).collect(Collectors.toList());
        List<PoolUser> poolUserList = poolUserRepository.findByUserIdAndPoolIds(orgId, userId, poolIds);
        Map<String, List<PoolUser>> poolUserGroupMap = poolUserList.stream().collect(Collectors.groupingBy(PoolUser::getPoolId));

        //获取准备度
        List<PoolReadiness> poolReadinessList = poolReadinessRepository.list(orgId);
        Map<String, String> readinessMap = poolReadinessList.stream().collect(Collectors.toMap(PoolReadiness::getId, PoolReadiness::getReadinessName));

        poolUserList.sort(Comparator.comparingLong(o -> o.getCreateTime().getTime()));
        pagingList.getDatas().forEach(a -> {
            List<PoolUser> tempList = poolUserGroupMap.get(a.getPoolId());
            if(CollectionUtils.isNotEmpty(tempList)){
                tempList.sort(Comparator.comparingLong(o -> o.getCreateTime().getTime()));
                PoolUser tempPoolUser = tempList.get(0);
                // 设置首次入池时间
                a.setJoinTime(tempPoolUser.getCreateTime());
            }
            //设置准备度
            if(StringUtils.isNotEmpty(a.getReadinessId())){
                a.setReadinessName(readinessMap.get(a.getReadinessId()));
            }
            a.setDayCount(calcDayCount(a));
        });

        return pagingList;
    }

    private int calcDayCount(PoolUserExperienceInfoBean bean){
        long sartTime = bean.getJoinTime().getTime();
        long endTime = null == bean.getOutTime() ? DateUtil.currentTime().getTime() : bean.getOutTime().getTime();
        long dayTime = 1000 * 60 * 60 * 24L;
        long day = (endTime - sartTime) / dayTime;
        return (int)day;
    }

    /**
     * 数据清洗， 产线数据量不大，大概1000左右， TODO 清洗完成，下个迭代删除代码
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = TalentBkConstants.BK_TRANSACTION_MANAGER)
    public void dataCleaning(){
        List<String> orgIds = poolUserRepository.getBaseMapper().distinctOrgId();
        log.info("开始清洗数据 ==== 共计"+orgIds.size() +"个机构");
        for(int i = 0; i < orgIds.size(); i++){
            String orgId = orgIds.get(i);
            log.info("清洗第"+i+"个机构， 机构号："+orgId);
            List<PoolUser> list = poolUserRepository.findPoolUserByOrgId(orgId);
            Map<String, List<PoolUser>> mapList = list.stream().collect(Collectors.groupingBy(a -> a.getPoolId()+"#"+a.getUserId()));

            int saveQty = 0;
            for(String key : mapList.keySet()){
                List<PoolUser> tempList = mapList.get(key);
                tempList.sort(Comparator.comparingLong(o -> o.getCreateTime().getTime()));
                for (int ui = 0; ui < tempList.size(); ui++) {
                    //加入时间最靠近当前时间的数据 设置 为【最近一次入池】
                    PoolUser poolUser = tempList.get(ui);
                    Integer recently = 0;
                    if (ui == tempList.size() - 1) {
                        recently = 1;
                    }
                    if (!recently.equals(poolUser.getRecently())) {
                        LambdaUpdateWrapper<PoolUser> update  = new UpdateWrapper<PoolUser>().lambda();
                        update.eq(PoolUser::getId, poolUser.getId());
                        update.set(PoolUser::getRecently, recently);
                        poolUserRepository.update(update);
                        saveQty++;
                        log.info("updatePoolUser id {} recently {}", poolUser.getId(), recently);
                    }
                }
            }
            log.info("第"+i+"个机构清洗完成，共计"+saveQty+"条数据， 机构号："+orgId);
        }
    }

    public List<UserPoolInfoBean> userPoolList(String orgId, String userId) {
        return poolUserRepository.userPoolList(orgId, userId);
    }

    public void removeDelUser(String orgId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        List<String> poolIds = poolUserRepository.getBaseMapper().hasUserPoolIds(orgId, userIds);
        if (!poolIds.isEmpty()) {
            poolUserRepository.getBaseMapper().removePoolUser(orgId, userIds);
            for (String poolId : poolIds) {
                poolRepository.getBaseMapper().updateSaturabilityById(orgId, poolId);
            }
        }
        log.info("removeDelUser orgId {} poolIds {}", orgId, JSON.toJSONString(poolIds));
    }

    public PagingList<UdpUserBriefBean> pageUser(PageRequest pageReq, String orgId, List<String> poolIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(poolIds)) {
            return TalentbkUtil.emptyPage((int) pageReq.getSize());
        }
        Page<String> page = TalentbkUtil.toPage(pageReq);
        page.addOrder(OrderItem.asc("pu.user_id"));
        poolUserRepository.getBaseMapper().pageUserIds(page, orgId, poolIds);
        List<UdpUserBriefBean> userList = new ArrayList<>();
        QueryUdpUtils.fillSpUserInfo(orgId, page.getRecords(), item -> item,
                (userId, userInfo) -> userList.add(userInfo),
                UdpUserBriefBean::getUsername,UdpUserBriefBean::getFullname,
                UdpUserBriefBean::getUserNo, UdpUserBriefBean::getDeptName, UdpUserBriefBean::getPositionName);
        Page retPage = page;
        retPage.setRecords(userList);
        return BeanCopierUtil.toPagingList(retPage);
    }

    public List<UdpUserNameBean> listUserIds(String orgId, List<String> poolIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(poolIds)) {
            return Lists.newArrayList();
        }
        List<UdpUserNameBean> userList = new ArrayList<>();
        QueryUdpUtils.fillSpUserInfo(orgId, poolUserRepository.getBaseMapper()
                        .pageUserIds(TalentbkUtil.allPage(), orgId, poolIds).getRecords(), item -> item,
                (userId, userInfo) -> userList.add(new UdpUserNameBean(userInfo.getId(), userInfo.getFullname())),
                UdpUserBriefBean::getFullname);
        return userList;
    }

    public List<UserPoolProjectResp> inPoolTargetIdList(UserPoolProjectReq req) {
        if (CollectionUtils.isEmpty(req.getUserIds()) || CollectionUtils.isEmpty(req.getTargetIds())) {
            return Lists.newArrayList();
        }
        Map<String, List<String>> projectUserMap = new HashMap<>();
        BatchOperationUtil.batchExecute(req.getUserIds(), 100, subUserIds -> {
            poolUserRepository.getBaseMapper().inPoolsRefTarget(req.getOrgId(), subUserIds, req.getTargetIds()).forEach(userTargetId -> {
                String targetId = userTargetId.getTargetId();
                if (targetId != null) {
                    List<String> userIds = projectUserMap.get(targetId);
                    if (userIds == null) {
                        userIds = new ArrayList<>();
                        projectUserMap.put(targetId, userIds);
                    }
                    userIds.add(userTargetId.getUserId());
                }
            });
        });
        return projectUserMap.entrySet().stream().map(entry -> {
            UserPoolProjectResp userInfo = new UserPoolProjectResp();
            userInfo.setTargetId(entry.getKey());
            userInfo.setUserIds(entry.getValue());
            return userInfo;
        }).collect(Collectors.toList());
    }
}
