package com.yxt.talent.bk.svc.heir.bean.req;

import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
public class ConfigPageImageUpdateReq {

    private Long id;

    @NotBlank(message = BkApiErrorKeys.PARAM_NULL_ERROR_MESSAGE)
    @Schema(description = "图片地址")
    private String imageUrl;

    @Schema(description = "链接地址")
    private String linkUrl;
}
