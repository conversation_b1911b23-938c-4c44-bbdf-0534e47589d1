package com.yxt.talent.bk.core.heir.bean.open;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * DwdHeirPosUserBean
 *
 * <AUTHOR> geyan
 * @Date 14/9/23 5:23 pm
 */
@Data
public class DwdHeirPosUserBean {
    @Schema(description = "雪花id")
    private Long id;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "用户id")
    private String userId;

    private String thirdUserId;

    @Schema(description = "dwd_heir_pos pkId")
    private String posId;

    @Schema(description = "0:继任用户，1:标杆用户")
    private Integer userType;

    @Schema(description = "准备等级名称")
    private String levelName;

    @Schema(description = "准备等级颜色")
    private String levelColor;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "是否删除.0：未删除，1：已删除")
    private Integer deleted;

    @Schema(description = "准备度规则等级id")
    private Long prepareLevelId;
}
