package com.yxt.talent.bk.core.usergroup.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.bk.core.usergroup.entity.UserGroupTag;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface UserGroupTagMapper extends BaseMapper<UserGroupTag> {
	int updateBatch(List<UserGroupTag> list);

	int updateBatchSelective(List<UserGroupTag> list);

	int batchInsert(@Param("list") List<UserGroupTag> list);

	int insertOrUpdate(UserGroupTag record);

	int insertOrUpdateSelective(UserGroupTag record);

    int deleteByGroupId(@Param("orgId") String orgId, @Param("groupId") Long groupId);
}
