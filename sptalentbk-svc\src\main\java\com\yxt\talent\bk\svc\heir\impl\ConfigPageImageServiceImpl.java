package com.yxt.talent.bk.svc.heir.impl;

import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.talent.bk.common.constants.BkApiErrorKeys;
import com.yxt.talent.bk.core.heir.entity.ConfigPageImageEntity;
import com.yxt.talent.bk.core.heir.ext.ConfigPageImageExt;
import com.yxt.talent.bk.core.heir.mapper.ConfigPageImageMapper;
import com.yxt.talent.bk.svc.heir.ConfigPageImageService;
import com.yxt.talent.bk.svc.heir.bean.req.ConfigPageImageAddReq;
import com.yxt.talent.bk.svc.heir.bean.req.ConfigPageImageUpdateReq;
import com.yxt.talent.bk.svc.heir.bean.resp.ConfigPageImageResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
*  @date 2023/9/14
**/
@Slf4j
@Service
@RequiredArgsConstructor
public class ConfigPageImageServiceImpl implements ConfigPageImageService {

    private final ConfigPageImageMapper configPageImageMapper;

    @DbHintMaster
    @Override
    public void create(UserCacheBasic currentUser, ConfigPageImageAddReq req) {
        String orgId = currentUser.getOrgId();
        if (configPageImageMapper.countByOrgId(orgId) >= 10) {
            throw new ApiException(BkApiErrorKeys.CONFIG_BANNER_COUNT_OVER_LIMIT);
        }
        ConfigPageImageEntity entity = new ConfigPageImageEntity();
        entity.init(orgId, currentUser.getUserId());
        entity.setImageUrl(req.getImageUrl());
        entity.setLinkUrl(req.getLinkUrl());
        configPageImageMapper.insert(entity);
    }

    @DbHintMaster
    @Override
    public void update(UserCacheBasic currentUser, ConfigPageImageUpdateReq req) {
        String orgId = currentUser.getOrgId();
        ConfigPageImageEntity entity = new ConfigPageImageEntity();
        entity.setId(req.getId());
        entity.init(orgId, currentUser.getUserId());
        entity.setImageUrl(req.getImageUrl());
        entity.setLinkUrl(req.getLinkUrl());
        configPageImageMapper.updateById(entity);
    }

    @Override
    public CommonList<ConfigPageImageResp> list(UserCacheBasic currentUser) {
        List<ConfigPageImageExt> list = configPageImageMapper.listByOrgId(currentUser.getOrgId());
        return BeanCopierUtil.toCommonList(list, ConfigPageImageExt.class, ConfigPageImageResp.class);
    }

    @DbHintMaster
    @Override
    public void delete(UserCacheBasic currentUser, String id) {
        configPageImageMapper.deleteByOrgIdAndId(currentUser.getOrgId(), currentUser.getUserId(), id);
    }

}
