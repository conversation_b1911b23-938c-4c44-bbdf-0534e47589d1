package com.yxt.talent.bk.core.udp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.spsdk.common.bean.SpIdNameBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangDeptBean;
import com.yxt.talent.bk.common.bean.udp.UdpLangPositionBean;
import com.yxt.talent.bk.core.udp.bean.DeptFullPathBean;
import com.yxt.talent.bk.core.udp.entity.UdpDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

@Mapper
public interface UdpDeptMapper extends BaseMapper<UdpDept> {

    UdpLangDeptBean getNameById(@Param("orgId") String orgId, @Param("id") String id);

    @Select("select id,name from udp_position where org_id = #{orgId} and id = #{id} and deleted = 0")
    UdpLangPositionBean getPositionNameById(@Param("orgId") String orgId, @Param("id") String id);

    List<DeptFullPathBean> queryIdFullPathByIds(@Param("ids") List<String> ids);

    List<SpIdNameBean> queryNameByIds(@Param("ids") Collection<String> ids);

    String getRoutingPathById(@Param("orgId") String orgId, @Param("id") String id);

}
