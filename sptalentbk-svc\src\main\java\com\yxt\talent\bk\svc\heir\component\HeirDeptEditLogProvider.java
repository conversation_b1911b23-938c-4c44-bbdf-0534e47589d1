package com.yxt.talent.bk.svc.heir.component;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.bk.common.enums.HeirRiskRuleTypeEnum;
import com.yxt.talent.bk.core.heir.entity.HeirPosEntity;
import com.yxt.talent.bk.core.heir.mapper.HeirOrgLevelCfgMapper;
import com.yxt.talent.bk.core.heir.repo.HeirPosRepository;
import com.yxt.talent.bk.core.udp.repo.UdpDeptRepository;
import com.yxt.talent.bk.svc.heir.bean.HeirDept4Log;
import com.yxt.talent.bk.svc.heir.bean.req.HeirPosEditBean;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * HeirPosEditLogProvider
 *
 * <AUTHOR> geyan
 * @Date 19/3/24 10:09 am
 */
@Slf4j
@AllArgsConstructor
@Component
public class HeirDeptEditLogProvider implements AuditLogDataProvider<HeirPosEditBean, HeirDept4Log> {
    private final HeirPosRepository heirPosRepository;
    private final UdpDeptRepository udpDeptRepository;
    private final HeirOrgLevelCfgMapper heirOrgLevelCfgMapper;

    @Override
    public HeirDept4Log before(HeirPosEditBean param, AuditLogBasicBean logBasic) {
        HeirPosEntity heirPos = heirPosRepository.selectById(param.getId());
        HeirDept4Log ret = new HeirDept4Log();
        ret.setId(param.getId());
        if (heirPos == null) {
            heirPos = HeirPosEntity.createEntity(logBasic.getOrgId(), logBasic.getUserId());
        }
        ret.setHeirTargetQty(heirPos.getHeirTargetQty());
        ret.setRiskRuleType(heirPos.getRiskRuleType());
        ret.setRiskLevelId(heirPos.getRiskLevelId());
        fillDeptLogDetail(logBasic.getOrgId(), ret);
        return ret;
    }

    @Override
    public HeirDept4Log after(HeirPosEditBean param, AuditLogBasicBean logBasic) {
        HeirDept4Log ret = new HeirDept4Log();
        ret.setId(param.getId());
        ret.setHeirTargetQty(param.getHeirTargetQty());
        ret.setRiskRuleType(param.getRiskRuleType());
        ret.setRiskLevelId(param.getRiskLevelId());
        fillDeptLogDetail(logBasic.getOrgId(), ret);
        return ret;
    }

    @Override
    public Pair<String, String> entityInfo(HeirPosEditBean param, HeirDept4Log beforeObj, HeirDept4Log afterObj, AuditLogBasicBean logBasic) {
        return Pair.of(param.getId(), String.format("继任地图-%s-组织部门设置", afterObj.getDeptName()));
    }

    private void fillDeptLogDetail(String orgId, HeirDept4Log posLog) {
        if (HeirRiskRuleTypeEnum.MANUAL.getType() == posLog.getRiskRuleType()
                && posLog.getRiskLevelId() != null) {
            posLog.setRiskLevelName(heirOrgLevelCfgMapper.getNameById(posLog.getRiskLevelId()));
        }
        posLog.setRiskRuleTypeName(HeirRiskRuleTypeEnum.getNameByType(posLog.getRiskRuleType()));
        posLog.setDeptName(udpDeptRepository.getNameById(orgId, posLog.getId()));
    }
}
