package com.yxt.talent.bk.api.controller;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.export.ImportResult;
import com.yxt.talent.bk.api.component.TagValueExportComponent;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.imports.ImportRequestBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 *  标签值导入人员 控制层
 * <AUTHOR>
 * @since 2022/8/10 13:57
 * @version 1.0
 */
@Deprecated
@Tag(name = "标签值导入人员 控制层")
@AllArgsConstructor
@RestController
@RequestMapping(value = "/mgr/tagvalueexcel")
public class TagValueExportController extends BaseController{
    private final TagValueExportComponent tagValueExportComponent;

    @Deprecated
    @Parameters({             @Parameter(name = "id", description = "标签id"),             @Parameter(name = "file", description = "文件流")})
    @ResponseStatus(HttpStatus.OK)
    @Auth(value = TalentBkConstants.LOG_TALENT_BANK_TAG, action = Constants.LOG_TYPE_GETLIST, type = AuthType.TOKEN)
    @Operation(summary = "标签值人员导入")
    @PostMapping(value = "/import/{id}", produces = Constants.MEDIATYPE)
    public ImportResult importExcel(ImportRequestBean bean, @PathVariable String id,
            @RequestParam(value = "file", required = false) MultipartFile file) {
        UserCacheBasic userDetail = getUserCacheBasic();
        return tagValueExportComponent.tagValueUserImport(userDetail.getOrgId(), userDetail.getUserId(), id, bean, file);
    }
}
