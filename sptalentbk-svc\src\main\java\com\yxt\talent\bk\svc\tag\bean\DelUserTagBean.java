package com.yxt.talent.bk.svc.tag.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;

/**
 * 删除用户指定标签
 *
 * 只能删除 自建+手动更新标签下的人
 * 领导风格为特殊的内置标签，也可以删除
 *
 * <AUTHOR>
 * @since 2022/8/11
 */
@Data
@NoArgsConstructor
@SuperBuilder
public class DelUserTagBean {

    @Schema(description = "标签id")
    @NotBlank(message = "")
    private String tagId;

    @Schema(description = "标签值id")
    private String tagValueId;

    @Schema(description = "用户id列表")
    @NotEmpty(message = "")
    private List<String> userIdList;

    /**
     * 标签值id列表
     */
    private Set<String> tagValueIdSet;

}
