package com.yxt.talent.bk.core.heir.mapper;

import com.yxt.talent.bk.common.extend.mybatis.BkBaseMapper;
import com.yxt.talent.bk.core.heir.bean.HeirPrepareCfgDataBean;
import com.yxt.talent.bk.core.heir.entity.HeirPosPrepareCfgEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * HeirPosPrepareCfgMapper
 *
 * <AUTHOR> geyan
 * @Date 15/8/23 2:31 pm
 */
@Mapper
public interface HeirPosPrepareCfgMapper extends BkBaseMapper<HeirPosPrepareCfgEntity> {

    void updateRuleCfgDataByPrimaryKey(HeirPosPrepareCfgEntity heirPosPrepareCfg);

    List<HeirPosPrepareCfgEntity> selectNeedReadinessCompute();

    List<HeirPosPrepareCfgEntity> selectNeedReadinessRemind();

    HeirPosPrepareCfgEntity getPrepareCfgDetail(@Param("orgId") String orgId, @Param("posId") String posId,
        @Param("prepareLevelId") Long prepareLevelId);

    List<HeirPrepareCfgDataBean> getPrepareCfgData(@Param("orgId") String orgId,
                                                   @Param("posId") String posId,
                                                   @Param("prepareLevelIds") List<Long> prepareLevelIds);

    List<Long> selectContainPrepareCfgDetail(@Param("orgId") String orgId,@Param("plIds") List<Long> plIds);

    List<HeirPosPrepareCfgEntity> selectByPosId(@Param("orgId") String orgId,@Param("posId") String posId);
}
