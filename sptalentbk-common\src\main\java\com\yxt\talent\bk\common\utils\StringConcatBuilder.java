package com.yxt.talent.bk.common.utils;

import org.apache.commons.lang.StringUtils;

/**
 * StringConcator
 *
 * <AUTHOR> geyan
 * @Date 18/3/24 10:42 am
 */
public class StringConcatBuilder {
    private String concatStr;
    private StringBuilder strBuilder;
    private int concatCount;
    private boolean locked = false;

    public StringConcatBuilder(String concatStr) {
        this.concatStr = concatStr;
        this.strBuilder = new StringBuilder();
    }

    public StringConcatBuilder append(String str) {
        if (locked) {
            throw new IllegalCallerException("output is done");
        }
        if (str != null) {
            strBuilder.append(str);
        }
        return this;
    }

    public StringConcatBuilder appendConcat() {
        concatCount++;
        return append(concatStr);
    }

    public String getConcatStr() {
        return concatStr;
    }

    public StringBuilder builder() {
        return strBuilder;
    }

    public String output() {
        if (StringUtils.isNotEmpty(concatStr) && strBuilder.length() >= concatStr.length()) {
            strBuilder.delete(strBuilder.length() - concatStr.length(), strBuilder.length());
        }
        locked = true;
        return strBuilder.toString();
    }

    public int getConcatCount() {
        return concatCount;
    }
}
