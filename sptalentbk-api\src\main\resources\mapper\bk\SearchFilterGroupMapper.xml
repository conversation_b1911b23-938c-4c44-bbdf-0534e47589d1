<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.search.mapper.SearchFilterGroupMapper">

    <select id="findGroupNamesByOrgId" resultType="com.yxt.talent.bk.core.search.entity.SearchFilterGroup">
        select id as id,
               group_name  as groupName,
               group_type  as groupType
        from bk_search_filter_group
        where org_id = #{orgId}
        order by group_type desc, create_time desc
    </select>

</mapper>
