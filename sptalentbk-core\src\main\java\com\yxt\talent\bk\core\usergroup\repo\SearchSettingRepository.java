package com.yxt.talent.bk.core.usergroup.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.bk.core.usergroup.entity.SearchSetting;
import com.yxt.talent.bk.core.usergroup.mapper.SearchSettingMapper;
import org.springframework.stereotype.Repository;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/17 11:45
 **/
@Repository
public class SearchSettingRepository extends ServiceImpl<SearchSettingMapper, SearchSetting> {

    private LambdaQueryWrapper<SearchSetting> getQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public SearchSetting queryById(String orgId, Long id) {
        LambdaQueryWrapper<SearchSetting> queryWrapper = getQueryWrapper();
        queryWrapper.eq(SearchSetting::getOrgId, orgId);
        queryWrapper.eq(SearchSetting::getId, id);
        queryWrapper.eq(SearchSetting::getDeleted, YesOrNo.NO.getValue());
        return getOne(queryWrapper);
    }

    public SearchSetting findByOrgId(String orgId) {
        LambdaQueryWrapper<SearchSetting> wrapper = getQueryWrapper();
        wrapper.eq(SearchSetting::getOrgId, orgId);
        wrapper.eq(SearchSetting::getDeleted, YesOrNo.NO.getValue());
        return getOne(wrapper);
    }
}
