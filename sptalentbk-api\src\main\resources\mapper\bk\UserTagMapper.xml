<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.bk.core.tag.mapper.UserTagMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.bk.core.tag.entity.UserTagEntity">
        <!--@mbg.generated-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="tag_id" jdbcType="CHAR" property="tagId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="listUserTag" resultType="com.yxt.talent.bk.core.tag.bean.UserTagBaseBean">
        select tag_id tagId, user_id userId
        from
        bk_user_tag
        where org_id = #{orgId}
        and tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and deleted = 0
    </select>

    <select id="listPageTagUser" resultType="com.yxt.talent.bk.core.tag.bean.UserBaseInfoBean">
        select b.id as userId,
            a.tag_id,
            b.fullname, b.username, b.status,
            b.dept_name as departmentName,
            b.position_name as positionName
        from
        bk_user_tag a
        inner join
        udp_lite_user_sp b
        on a.user_id = b.id and a.org_id = b.org_id
        where
        a.org_id = #{orgId}
        and a.tag_id = #{tagId} and a.deleted = 0
        <if test="keyword != null and keyword != ''">
            and ((b.username like CONCAT('%', #{keyword}, '%'))
            or (b.fullname like CONCAT('%', #{keyword}, '%')))
        </if>
        <if test="tagValueId != null and tagValueId != ''">
            and exists (select 1 from bk_user_tag_value c
                where c.org_id = a.org_id and c.tag_id = a.tag_id
                AND c.user_id = a.user_id
                and c.tag_value_id = #{tagValueId} and c.deleted = 0
            )
        </if>
        and b.deleted = 0
    </select>

    <select id="listTagUser" resultType="com.yxt.talent.bk.core.tag.bean.UserBaseInfoBean">
        select b.id userId, b.fullname, b.username, b.status, b.dept_name departmentName,
        b.position_name positionName
        from
        bk_user_tag a
        inner join
        udp_lite_user_sp b
        on a.user_id = b.id and a.org_id = b.org_id
        where
        a.org_id = #{orgId}
        and a.tag_id = #{tagId} and a.deleted = 0
        <if test="keyword != null and keyword != ''">
            and ((b.username like CONCAT('%', #{keyword}, '%'))
            or (b.fullname like CONCAT('%', #{keyword}, '%')))
        </if>
    </select>

    <select id="findByOrgIdAndTagIds" resultType="com.yxt.talent.bk.core.tag.bean.UserTagCountBean">
        select
        a.user_id as userId,
        a.tag_id as tagId
        from
        bk_user_tag a
        inner join udp_lite_user_sp ulu on a.org_id =ulu.org_id and a.user_id =ulu.id
        where
        a.org_id = #{orgId}
        and a.deleted = 0 and ulu.deleted =0
        and a.tag_id in
        <foreach collection="tagIds" item="tagId" open="(" close=")" separator=",">
            #{tagId}
        </foreach>
    </select>

    <update id="delUserTag">
        update
        bk_user_tag
        set deleted = 1, update_user_id = #{operatorId}, update_time = #{now}
        where org_id = #{orgId}
        and tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

</mapper>
