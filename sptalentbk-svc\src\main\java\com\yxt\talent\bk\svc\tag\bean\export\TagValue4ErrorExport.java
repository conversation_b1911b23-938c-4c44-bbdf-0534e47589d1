package com.yxt.talent.bk.svc.tag.bean.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * : 标签值导入人员 数据结构
 * <AUTHOR>
 * @since 2022/8/10 16:27
 */
@Data
@NoArgsConstructor
public class TagValue4ErrorExport {

    @Schema(description = "用户账号")
    private String username;

    @Schema(description = "用户姓名")
    private String fullName;

    @Schema(description = "错误信息")
    private String errorMsg = "";

}
