package com.yxt.talent.bk.svc.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.bk.common.constants.TalentBkConstants;
import com.yxt.talent.bk.common.constants.TalentBkRocketMqConstant;
import com.yxt.talent.bk.core.mq.RocketMqProducerRepository;
import com.yxt.talent.bk.svc.common.DemoCopyService;
import com.yxt.talentbkfacade.bean.SpDemoInitCmd4Mq;
import com.yxt.talentbkfacade.bean.SpDemoInitStatus4Mq;
import com.yxt.talentbkfacade.constant.BkFacadeContants;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * BkDemoInitCopyConsumer
 *
 * <AUTHOR> harleyge
 * @Date 13/9/24 3:44 pm
 */
@Component
@AllArgsConstructor
@Slf4j
@RocketMQMessageListener(consumerGroup = TalentBkRocketMqConstant.GROUP_PREFIX + BkFacadeContants.TOPIC_SP_DEMO_INIT_COPY,         topic = BkFacadeContants.TOPIC_SP_DEMO_INIT_COPY, consumeThreadNumber = 2, consumeTimeout = 30)
public class BkDemoInitCopyConsumer implements RocketMQListener<SpDemoInitCmd4Mq> {
    private final DemoCopyService demoCopyService;
    private final RocketMqProducerRepository rocketMqProducerRepository;
    @Override
    public void onMessage(SpDemoInitCmd4Mq message) {
        SpDemoInitStatus4Mq status4Mq = new SpDemoInitStatus4Mq();
        status4Mq.setModule(TalentBkConstants.MODULE_CODE);
        try {
            log.info("BkDemoInitStartConsumer copy orgId {}", message.getOrgId());
            status4Mq.setOrgId(message.getOrgId());
            status4Mq.setStage(SpDemoInitStatus4Mq.STAGE_COPY);
            status4Mq.setStatus(YesOrNo.NO.getValue());
            demoCopyService.demoCopy(JSON.parseObject(message.getOrgInitData(), OrgInit4Mq.class));
            status4Mq.setStatus(YesOrNo.YES.getValue());
        } catch (Exception e) {
            log.error("BkDemoInitCopyConsumer fail orgId {} msgId {}", message.getOrgId(), message.getMsgId(), e);
        } finally {
            rocketMqProducerRepository.send(BkFacadeContants.TOPIC_SP_DEMO_INIT_STATUS, JSON.toJSONString(status4Mq));
        }
    }
}
