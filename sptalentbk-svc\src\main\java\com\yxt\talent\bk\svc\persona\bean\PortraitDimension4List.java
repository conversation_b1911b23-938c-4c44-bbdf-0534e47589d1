package com.yxt.talent.bk.svc.persona.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "人才画像-维度信息")
public class PortraitDimension4List {
    @Schema(description = "维度id")
    private String dimensionId;
    @Schema(description = "维度名称")
    private String dimensionName;
    @Schema(description = "维度对应标签值")
    private List<PortraitDimensionTag4List> tag4Lists;
    @Schema(description = "维度关联标签是否存在某个标签被贴属性 true:是(画像显示该维度) false:否(画像不显示该维度)")
    private Boolean tagValueFinished = false;
}
