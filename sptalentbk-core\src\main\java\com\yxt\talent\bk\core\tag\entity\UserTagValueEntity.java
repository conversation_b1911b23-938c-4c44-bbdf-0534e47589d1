package com.yxt.talent.bk.core.tag.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.bk.core.CreatorEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2022/8/15
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bk_user_tag_value")
public class UserTagValueEntity extends CreatorEntity {
    /**
    * 主键
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 用户id
    */
    private String userId;

    /**
    * 标签id(bk_tag.id)
    */
    private String tagId;

    /**
    * 标签值id(bk_tag_value.id)
    */
    private String tagValueId;

    /**
    * 是否删除(0-否,1-是)
    */
    private Integer deleted;

}
