package com.yxt.talent.bk.svc.heir.bean;

import com.google.common.collect.Lists;
import com.yxt.spsdk.audit.base.AuditLogCustomBean;
import com.yxt.spsdk.audit.bean.AuditLogFieldBean;
import com.yxt.talent.bk.svc.common.enums.AuditLogPointEnum;
import lombok.Data;

import java.util.List;

/**
 * HeirUserEdit4Log
 *
 * <AUTHOR> geyan
 * @Date 19/3/24 3:22 pm
 */
@Data
public class HeirUserEdit4Log implements AuditLogCustomBean {

    private String userInfoList;
    private String exitReason;
    private AuditLogPointEnum logPoint;

    public HeirUserEdit4Log(AuditLogPointEnum logPoint) {
        this.logPoint = logPoint;
    }

    @Override
    public List<AuditLogFieldBean> logFields() {
        AuditLogFieldBean userField = AuditLogFieldBean.createBy(this.getClass(),
                "userInfoList", "继任者");
        if (AuditLogPointEnum.HEIR_USER_EXIT.equals(logPoint)) {
            AuditLogFieldBean exitField = AuditLogFieldBean.createBy(this.getClass(),
                    "exitReason", "退出原因");
            exitField.setOrderIndex(1);
            return Lists.newArrayList(userField, exitField);
        } else {
            return Lists.newArrayList(userField);
        }
    }
}
