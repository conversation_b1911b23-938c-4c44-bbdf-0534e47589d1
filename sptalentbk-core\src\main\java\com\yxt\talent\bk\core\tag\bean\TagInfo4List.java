package com.yxt.talent.bk.core.tag.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "标签详情")
public class TagInfo4List {
    @Schema(description = "标签id")
    private String tagId;
    @Schema(description = "标签名称")
    private String tagName;
    @Schema(description = "标签key(sex|birthday...")
    private String tagKey;
    @Schema(description = "标签描述/定义")
    private String description;
    @Schema(description = "标签分类id/字段空是未分组")
    private String catalogId;
    @Schema(description = "标签类型(0-普通标签,1-分层标签)")
    private Integer tagType;
    @Schema(description = "标签值选择类型(0-单选,1-多选)")
    private Integer valueChooseModel;
    @Schema(description = "标签来源(0-内置,1-自建,2-固定)")
    private Integer source;
    @Schema(description = "启用状态(0-禁用,1-启用)")
    private Integer enable;
    @Schema(description = "可见状态(0-不可见,1-可见)")
    private Integer showType;
    @Schema(description = "创建方式(0-静态,1-规则,2-模型)")
    private Integer createType;
    @Schema(description = "标签更新方式(0-手动,1-自动)")
    private Integer updateType;
    @Schema(description = "创建人名称")
    private String createUserName;
}
