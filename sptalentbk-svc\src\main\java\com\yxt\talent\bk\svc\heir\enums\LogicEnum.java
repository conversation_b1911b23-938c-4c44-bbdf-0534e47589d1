package com.yxt.talent.bk.svc.heir.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum LogicEnum {
    /**
     * 逻辑类型
     */

    AND(1, "与"),
    OR(2, "或");

    private final int code;
    private final String desc;

    public static boolean isAnd(Integer code) {
        return Objects.equals(AND.getCode(), code);
    }

    public static boolean isOr(Integer code) {
        return Objects.equals(OR.getCode(), code);
    }

}
