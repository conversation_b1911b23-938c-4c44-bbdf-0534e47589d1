package com.yxt.talent.bk.core.pool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Import;
import com.yxt.talent.bk.core.pool.bean.PoolUser4List;
import com.yxt.talent.bk.core.pool.bean.PoolUser4Param;
import com.yxt.talent.bk.core.pool.bean.PoolUserExperienceInfo4List;
import com.yxt.talent.bk.core.pool.bean.PoolUserQtyBean;
import com.yxt.talent.bk.core.pool.bean.PoolUserTargetIdDto;
import com.yxt.talent.bk.core.pool.bean.UserPoolInfoBean;
import com.yxt.talent.bk.core.pool.entity.PoolUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/09/08
 */
@Mapper
public interface PoolUserMapper extends BaseMapper<PoolUser> {
    IPage<PoolUser4List> page(@Param("page") IPage<PoolUser4List> page, @Param("param") PoolUser4Param param);

    List<PoolUser4List> page(@Param("param") PoolUser4Param param);

    List<String> findUserIdByPoolIdAndUserIds(
            @Param("orgId") String orgId, @Param("poolId") String poolId, @Param("userIds") Collection<String> userIds);

    List<PoolUser4Import> findPoolUser4Import(@Param("orgId") String orgId, @Param("userNames") List<String> userNames);

    List<String> findAllPoolUser(@Param("orgId") String orgId, @Param("poolId") String poolId);

    IPage<PoolUserExperienceInfo4List> findPoolUserExperienceByUserIds(
            IPage<PoolUserExperienceInfo4List> page, @Param("orgId") String orgId, @Param("userId") String userId,
            @Param("statusList") Collection<Integer> statusList, @Param("direction") String direction);

    void updateRecentlyBy(
            @Param("orgId") String orgId, @Param("poolId") String poolId, @Param("currentUserId") String currentUserId,
            @Param("updateTime") Date updateTime, @Param("userIds") Collection<String> userIds);

    List<String> distinctOrgId();

    List<String> hasUserPoolIds(@Param("orgId")String orgId, @Param("userIds")List<String> userIds);

    int removePoolUser(@Param("orgId")String orgId, @Param("userIds")List<String> userIds);

    List<UserPoolInfoBean> userPoolList(@Param("orgId") String orgId, @Param("userId") String userId);

    Page<String> pageUserIds(Page<String> page, @Param("orgId")String orgId, @Param("poolIds")List<String> poolIds);

    List<PoolUserQtyBean> poolEnableUserQty(@Param("orgId")String orgId, @Param("poolIds")List<String> poolIds);

    List<PoolUserTargetIdDto> inPoolsRefTarget(@Param("orgId")String orgId,
                                               @Param("userIds")List<String> userIds,
                                               @Param("targetIds")List<String> targetIds);
}
