package com.yxt.talent.bk.core.heir.bean.open;

import com.yxt.talent.bk.core.heir.bean.HeirPosRiskSupport;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * DwdHeirPosBean
 *
 * <AUTHOR> g<PERSON>an
 * @Date 14/9/23 5:19 pm
 */
@Data
public class DwdHeirPosBean implements HeirPosRiskSupport {
    @Schema(description = "pk-岗位或部门id")
    private String id;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "部门或岗位id")
    private String deptOrPosId;

    @Schema(description = "0:岗位，1:部门")
    private Integer posType;

    @Schema(description = "名称")
    private String posName;

    @Schema(description = "目标继任数量,0:未设置")
    private Integer heirTargetQty;

    @Schema(description = "有效人数")
    private Integer heirValidQty;

    @Schema(description = "风险规则名称")
    private String riskLevelName;

    @Schema(description = "风险规则颜色")
    private String riskLevelColor;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "是否删除.0：未删除，1：已删除")
    private Integer deleted;

    @Schema(description = "0:自动规则，1:手动规则")
    private Integer riskRuleType;

    @Schema(description = "风险规则id")
    private Long riskLevelId;
}
