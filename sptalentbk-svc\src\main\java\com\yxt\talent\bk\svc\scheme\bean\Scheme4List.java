package com.yxt.talent.bk.svc.scheme.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.bk.common.bean.searchrule.SearchRuleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description 人才列表规则
 *
 * <AUTHOR>
 * @Date 2023/8/16 20:32
 **/
@Data
public class Scheme4List extends SearchRuleBean {

    @Schema(description = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "筛选方案名称")
    private String schemeName;


    @Schema(description = "描述")
    private String schemeDesc;
}
