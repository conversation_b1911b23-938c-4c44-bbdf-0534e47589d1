package com.yxt.talent.bk.core.bean.org;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取机构参数状态
 *
 * <AUTHOR>
 * @since 2021/8/18
 */
@Data
@NoArgsConstructor
public class OrgSettingParam4Get {

    @Schema(description = "平台开通状态 0-未开通 1-已开通", title = "1.0&2.0使用")
    private long modelStatus;

    @Schema(description = "测训项目开通状态 0-未开通 1-已开通", title = "1.0使用")
    private long trainingStatus;

    @Schema(description = "1.0测训版本，只开通测训模块： 0-未开通 1-已开通", title = "1.0使用")
    private long onlyTalentTraining;

    @Schema(description = "人才盘点是否开通： 0-未开通 1-已开通")
    private long talentRvStatus = 0;

    @Schema(description = "控制能够开启按次购买课程：0-不可以，1-可以(人才盘点不需要此字段，冗余)")
    private long kngPersontimeSwitch;

    @Schema(description = "2.0机构人才发展开通状况（0-未开通，1-基础版，2-测训版，3-高级版）", title = "2.0使用，1.0机构默认0")
    private int orgEdition;

}
